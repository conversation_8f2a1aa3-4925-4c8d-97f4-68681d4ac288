[appearance]
	# Description: Move the JEI search bar to the bottom center of the screen.
	# Valid Values: [true, false]
	# Default Value: false
	CenterSearch = false

	# Description: Max recipe GUI height.
	# Valid Values: Any integer greater than or equal to 175
	# Default Value: 350
	RecipeGuiHeight = 350


[cheat_mode]
	# Description: Choose if JEI should give ingredients directly to the inventory or pick them up with the mouse.
	# Valid Values: [INVENTORY, MOUSE_PICKUP]
	# Default Value: MOUSE_PICKUP
	GiveMode = MOUSE_PICKUP

	# Description: Enable cheating items into the hotbar by using Shift + numeric keys.
	# Valid Values: [true, false]
	# Default Value: false
	CheatToHotbarUsingHotkeysEnabled = false

	# Description: Enable showing items that are not in the creative menu.
	# Valid Values: [true, false]
	# Default Value: false
	ShowHiddenItems = false


[bookmarks]
	# Description: Add new bookmarks to the front of the bookmark list instead of the end.
	# Valid Values: [true, false]
	# Default Value: false
	AddBookmarksToFrontEnabled = false

	# Description: Extra features for bookmark tooltips.
	# Valid Values: A comma-separated list containing values of:
	# [PREVIEW, INGREDIENTS]
	# Default Value: PREVIEW
	BookmarkTooltipFeatures = PREVIEW

	# Description: Hold Shift to show bookmark tooltip features.
	# Valid Values: [true, false]
	# Default Value: true
	HoldShiftToShowBookmarkTooltipFeatures = true

	# Description: Drag bookmarks to rearrange them in the list.
	# Valid Values: [true, false]
	# Default Value: true
	DragToRearrangeBookmarksEnabled = true


[advanced]
	# Description: Set low-memory mode (makes search very slow but uses less RAM).
	# Valid Values: [true, false]
	# Default Value: false
	LowMemorySlowSearchEnabled = false

	# Description: Catch render errors from ingredients and attempt to recover from them instead of crashing.
	# Valid Values: [true, false]
	# Default Value: true
	CatchRenderErrorsEnabled = true

	# Description: When looking up recipes with items that contain fluids, also look up recipes for the fluids.
	# Valid Values: [true, false]
	# Default Value: false
	lookupFluidContentsEnabled = false

	# Description: When searching for item tags, also include tags for the default blocks contained in the items.
	# Valid Values: [true, false]
	# Default Value: true
	lookupBlockTagsEnabled = true

	# Description: Show recipes for ingredient tags like item tags and block tags.
	# Valid Values: [true, false]
	# Default Value: false
	showTagRecipesEnabled = false

	# Description: Show creative tab names in ingredient tooltips.
	# Valid Values: [true, false]
	# Default Value: false
	showCreativeTabNamesEnabled = false


[input]
	# Description: Number of milliseconds before a long mouse click is considered a drag operation.
	# Valid Values: An integer in the range [0, 1000] (inclusive)
	# Default Value: 150
	dragDelayInMilliseconds = 150

	# Description: Scroll rate for scrolling the mouse wheel in smooth-scrolling scroll boxes. Measured in pixels.
	# Valid Values: An integer in the range [1, 50] (inclusive)
	# Default Value: 9
	smoothScrollRate = 9


[sorting]
	# Description: Sorting order for the ingredient list.
	# Valid Values: A comma-separated list containing values of:
	# [MOD_NAME, INGREDIENT_TYPE, ALPHABETICAL, CREATIVE_MENU, TAG, ARMOR, MAX_DURABILITY]
	# Default Value: MOD_NAME, INGREDIENT_TYPE, CREATIVE_MENU
	IngredientSortStages = MOD_NAME, INGREDIENT_TYPE, CREATIVE_MENU

	# Description: Sorting order for displayed recipes.
	# Valid Values: A comma-separated list containing values of:
	# [BOOKMARKED, CRAFTABLE]
	# Default Value: BOOKMARKED, CRAFTABLE
	RecipeSorterStages = BOOKMARKED, CRAFTABLE


[tags]
	# Description: Show tag content in tooltips.
	# Valid Values: [true, false]
	# Default Value: true
	TagContentTooltipEnabled = true

	# Description: Hide tags that only have 1 ingredient.
	# Valid Values: [true, false]
	# Default Value: true
	HideSingleIngredientTagsEnabled = true


[search]
	# Description: Search mode for mod names (prefix: @).
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: REQUIRE_PREFIX
	ModNameSearchMode = REQUIRE_PREFIX

	# Description: Search mode for tooltips (prefix: #).
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: ENABLED
	TooltipSearchMode = ENABLED

	# Description: Search mode for tags (prefix: $).
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: REQUIRE_PREFIX
	TagSearchMode = REQUIRE_PREFIX

	# Description: Search mode for colors (prefix: ^).
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: DISABLED
	ColorSearchMode = DISABLED

	# Description: Search mode for resource locations (prefix: &).
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: DISABLED
	ResourceLocationSearchMode = DISABLED

	# Description: Search mode for creative mode tab names (prefix: %).
	# Valid Values: [ENABLED, REQUIRE_PREFIX, DISABLED]
	# Default Value: DISABLED
	CreativeTabSearchMode = DISABLED

	# Description: Search in advanced tooltips (visible with F3 + H).
	# Valid Values: [true, false]
	# Default Value: false
	SearchAdvancedTooltips = false

	# Description: Search mod IDs in addition to mod names.
	# Valid Values: [true, false]
	# Default Value: true
	SearchModIds = true

	# Description: Search by the shorthand first letters of a mod's name.
	# Valid Values: [true, false]
	# Default Value: true
	SearchShortModNames = true

	# Description: Search ingredient aliases (alternative names) that are added by plugins, in addition to ingredient names.
	# Valid Values: [true, false]
	# Default Value: true
	SearchIngredientAliases = true


[IngredientList]
	# Description: Max number of rows shown.
	# Valid Values: An integer in the range [1, 100] (inclusive)
	# Default Value: 16
	MaxRows = 16

	# Description: Max number of columns shown.
	# Valid Values: An integer in the range [4, 100] (inclusive)
	# Default Value: 9
	MaxColumns = 9

	# Description: Horizontal alignment of the ingredient grid inside the available area.
	# Valid Values: [LEFT, CENTER, RIGHT]
	# Default Value: RIGHT
	HorizontalAlignment = RIGHT

	# Description: Vertical alignment of the ingredient grid inside the available area.
	# Valid Values: [TOP, CENTER, BOTTOM]
	# Default Value: TOP
	VerticalAlignment = TOP

	# Description: Visibility of the top page buttons. Use AUTO_HIDE to only show it when there are multiple pages.
	# Valid Values: [ENABLED, AUTO_HIDE, DISABLED]
	# Default Value: ENABLED
	ButtonNavigationVisibility = ENABLED

	# Description: Enable this to draw a background texture behind the GUI.
	# Valid Values: [true, false]
	# Default Value: false
	DrawBackground = false


[BookmarkList]
	# Description: Max number of rows shown.
	# Valid Values: An integer in the range [1, 100] (inclusive)
	# Default Value: 16
	MaxRows = 16

	# Description: Max number of columns shown.
	# Valid Values: An integer in the range [4, 100] (inclusive)
	# Default Value: 9
	MaxColumns = 9

	# Description: Horizontal alignment of the ingredient grid inside the available area.
	# Valid Values: [LEFT, CENTER, RIGHT]
	# Default Value: LEFT
	HorizontalAlignment = LEFT

	# Description: Vertical alignment of the ingredient grid inside the available area.
	# Valid Values: [TOP, CENTER, BOTTOM]
	# Default Value: TOP
	VerticalAlignment = TOP

	# Description: Visibility of the top page buttons. Use AUTO_HIDE to only show it when there are multiple pages.
	# Valid Values: [ENABLED, AUTO_HIDE, DISABLED]
	# Default Value: ENABLED
	ButtonNavigationVisibility = ENABLED

	# Description: Enable this to draw a background texture behind the GUI.
	# Valid Values: [true, false]
	# Default Value: false
	DrawBackground = false


