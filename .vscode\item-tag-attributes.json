[{"id": "forge:nuggets/lead", "items": ["immersiveengineering:nugget_lead", "mekanism:nugget_lead"]}, {"id": "forge:eggs", "items": ["minecraft:egg"]}, {"id": "forge:ingots/osmium", "items": ["mekanism:ingot_osmium"]}, {"id": "mekanism:dirty_dusts/iron", "items": ["mekanism:dirty_dust_iron"]}, {"id": "forge:ingots/iron", "items": ["minecraft:iron_ingot"]}, {"id": "forge:ores/nickel", "items": ["immersiveengineering:ore_nickel", "immersiveengineering:deepslate_ore_nickel"]}, {"id": "tconstruct:melting/invar/tools_costing_1", "items": []}, {"id": "forge:fence_gates", "items": ["minecraft:oak_fence_gate", "minecraft:spruce_fence_gate", "minecraft:birch_fence_gate", "minecraft:jungle_fence_gate", "minecraft:acacia_fence_gate", "minecraft:dark_oak_fence_gate", "minecraft:crimson_fence_gate", "minecraft:warped_fence_gate", "minecraft:mangrove_fence_gate", "minecraft:bamboo_fence_gate", "minecraft:cherry_fence_gate", "tconstruct:greenheart_fence_gate", "tconstruct:skyroot_fence_gate", "tconstruct:bloodshroom_fence_gate", "tconstruct:enderbark_fence_gate"]}, {"id": "create:stone_types/deepslate", "items": ["create:cut_deepslate", "create:cut_deepslate_stairs", "create:cut_deepslate_wall", "create:polished_cut_deepslate", "create:polished_cut_deepslate_stairs", "create:polished_cut_deepslate_wall", "create:cut_deepslate_bricks", "create:cut_deepslate_brick_stairs", "create:cut_deepslate_brick_wall", "create:small_deepslate_bricks", "create:small_deepslate_brick_stairs", "create:small_deepslate_brick_wall", "create:layered_deepslate", "create:deepslate_pillar", "minecraft:deepslate"]}, {"id": "tconstruct:melting/electrum/tools_costing_3", "items": []}, {"id": "forge:ingots/cobalt", "items": ["tconstruct:cobalt_ingot"]}, {"id": "c:red_sandstone_stairs", "items": ["minecraft:red_sandstone_stairs", "minecraft:smooth_red_sandstone_stairs"]}, {"id": "c:axes", "items": ["immersiveengineering:axe_steel", "minecraft:diamond_axe", "minecraft:golden_axe", "minecraft:wooden_axe", "minecraft:stone_axe", "minecraft:iron_axe", "minecraft:netherite_axe"]}, {"id": "minecraft:freeze_immune_wearables", "items": ["minecraft:leather_boots", "minecraft:leather_leggings", "minecraft:leather_chestplate", "minecraft:leather_helmet", "minecraft:leather_horse_armor", "mekanism:mekasuit_helmet", "mekanism:mekas<PERSON>_bodyarmor", "mekanism:mekasuit_pants", "mekanism:mekasuit_boots", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots"]}, {"id": "forge:armor/leggings", "items": ["cataclysm:ignitium_leggings"]}, {"id": "minecraft:creeper_drop_music_discs", "items": ["minecraft:music_disc_13", "minecraft:music_disc_cat", "minecraft:music_disc_blocks", "minecraft:music_disc_chirp", "minecraft:music_disc_far", "minecraft:music_disc_mall", "minecraft:music_disc_mellohi", "minecraft:music_disc_stal", "minecraft:music_disc_strad", "minecraft:music_disc_ward", "minecraft:music_disc_11", "minecraft:music_disc_wait"]}, {"id": "balm:green_dyes", "items": ["minecraft:green_dye"]}, {"id": "forge:ores/copper", "items": ["minecraft:copper_ore", "minecraft:deepslate_copper_ore"]}, {"id": "forge:wrenches", "items": ["mekanism:configurator"]}, {"id": "forge:armors/helmets", "items": ["minecraft:leather_helmet", "minecraft:turtle_helmet", "minecraft:chainmail_helmet", "minecraft:iron_helmet", "minecraft:golden_helmet", "minecraft:diamond_helmet", "minecraft:netherite_helmet", "immersiveengineering:armor_steel_helmet", "immersiveengineering:armor_faraday_helmet", "create:copper_diving_helmet", "create:netherite_diving_helmet", "mekanism:hazmat_mask", "tconstruct:travelers_helmet", "tconstruct:plate_helmet", "tconstruct:slime_helmet"]}, {"id": "forge:yellow_cake_uranium", "items": ["mekanism:yellow_cake_uranium"]}, {"id": "balm:wooden_chests", "items": ["minecraft:chest", "minecraft:trapped_chest"]}, {"id": "forge:glass/light_blue", "items": ["minecraft:light_blue_stained_glass", "tconstruct:light_blue_clear_stained_glass"]}, {"id": "forge:tools/axe", "items": ["cataclysm:meat_shredder", "cataclysm:soul_render", "cataclysm:black_steel_axe"]}, {"id": "ae2:illuminated_panel", "items": ["ae2:monitor", "ae2:semi_dark_monitor", "ae2:dark_monitor"]}, {"id": "parry:excluded_shields", "items": ["tconstruct:war_pick", "tconstruct:swasher", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:travelers_shield", "tconstruct:plate_shield", "tconstruct:battlesign"]}, {"id": "tconstruct:melting/nickel/tools_costing_1", "items": []}, {"id": "minecraft:flowers", "items": ["minecraft:dandelion", "minecraft:poppy", "minecraft:blue_orchid", "minecraft:allium", "minecraft:azure_bluet", "minecraft:red_tulip", "minecraft:orange_tulip", "minecraft:white_tulip", "minecraft:pink_tulip", "minecraft:oxeye_daisy", "minecraft:cornflower", "minecraft:lily_of_the_valley", "minecraft:wither_rose", "minecraft:torchflower", "minecraft:sunflower", "minecraft:lilac", "minecraft:peony", "minecraft:rose_bush", "minecraft:pitcher_plant", "minecraft:flowering_azalea_leaves", "minecraft:flowering_azalea", "minecraft:mangrove_propagule", "minecraft:cherry_leaves", "minecraft:pink_petals"]}, {"id": "forge:stained_glass_panes", "items": ["minecraft:white_stained_glass_pane", "minecraft:orange_stained_glass_pane", "minecraft:magenta_stained_glass_pane", "minecraft:light_blue_stained_glass_pane", "minecraft:yellow_stained_glass_pane", "minecraft:lime_stained_glass_pane", "minecraft:pink_stained_glass_pane", "minecraft:gray_stained_glass_pane", "minecraft:light_gray_stained_glass_pane", "minecraft:cyan_stained_glass_pane", "minecraft:purple_stained_glass_pane", "minecraft:blue_stained_glass_pane", "minecraft:brown_stained_glass_pane", "minecraft:green_stained_glass_pane", "minecraft:red_stained_glass_pane", "minecraft:black_stained_glass_pane", "tconstruct:white_clear_stained_glass_pane", "tconstruct:orange_clear_stained_glass_pane", "tconstruct:magenta_clear_stained_glass_pane", "tconstruct:light_blue_clear_stained_glass_pane", "tconstruct:yellow_clear_stained_glass_pane", "tconstruct:lime_clear_stained_glass_pane", "tconstruct:pink_clear_stained_glass_pane", "tconstruct:gray_clear_stained_glass_pane", "tconstruct:light_gray_clear_stained_glass_pane", "tconstruct:cyan_clear_stained_glass_pane", "tconstruct:purple_clear_stained_glass_pane", "tconstruct:blue_clear_stained_glass_pane", "tconstruct:brown_clear_stained_glass_pane", "tconstruct:green_clear_stained_glass_pane", "tconstruct:red_clear_stained_glass_pane", "tconstruct:black_clear_stained_glass_pane"]}, {"id": "forge:armors/leggings/hazmat", "items": ["mekanism:hazmat_pants"]}, {"id": "forge:dusts/ender_pearl", "items": ["ae2:ender_dust"]}, {"id": "curios:hands", "items": ["cataclysm:sticky_gloves", "cataclysm:blazing_grips", "cataclysm:chitin_claw"]}, {"id": "forge:ore_rates/singular", "items": ["minecraft:ancient_debris", "minecraft:coal_ore", "minecraft:deepslate_coal_ore", "minecraft:deepslate_diamond_ore", "minecraft:deepslate_emerald_ore", "minecraft:deepslate_gold_ore", "minecraft:deepslate_iron_ore", "minecraft:diamond_ore", "minecraft:emerald_ore", "minecraft:gold_ore", "minecraft:iron_ore", "minecraft:nether_quartz_ore", "immersiveengineering:ore_aluminum", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:deepslate_ore_lead", "immersiveengineering:ore_silver", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:ore_nickel", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:ore_uranium", "immersiveengineering:deepslate_ore_uranium", "mekanism:tin_ore", "mekanism:deepslate_tin_ore", "mekanism:osmium_ore", "mekanism:deepslate_osmium_ore", "mekanism:uranium_ore", "mekanism:deepslate_uranium_ore", "mekanism:lead_ore", "mekanism:deepslate_lead_ore", "tconstruct:cobalt_ore"]}, {"id": "tconstruct:modifiable/armor", "items": ["tconstruct:travelers_boots", "tconstruct:plate_boots", "tconstruct:slime_boots", "tconstruct:travelers_leggings", "tconstruct:plate_leggings", "tconstruct:slime_leggings", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate", "tconstruct:travelers_helmet", "tconstruct:plate_helmet", "tconstruct:slime_helmet", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "forge:dyes/white", "items": ["minecraft:white_dye"]}, {"id": "forge:sheetmetals/silver", "items": ["immersiveengineering:sheetmetal_silver"]}, {"id": "mekanism:colorable/banners", "items": ["minecraft:white_banner", "minecraft:orange_banner", "minecraft:magenta_banner", "minecraft:light_blue_banner", "minecraft:yellow_banner", "minecraft:lime_banner", "minecraft:pink_banner", "minecraft:gray_banner", "minecraft:light_gray_banner", "minecraft:cyan_banner", "minecraft:purple_banner", "minecraft:blue_banner", "minecraft:brown_banner", "minecraft:green_banner", "minecraft:red_banner", "minecraft:black_banner"]}, {"id": "mekanism:enriched/redstone", "items": ["mekanism:enriched_redstone"]}, {"id": "forge:shears", "items": ["minecraft:shears", "tconstruct:kama"]}, {"id": "create:vanilla_stripped_wood", "items": ["minecraft:stripped_acacia_wood", "minecraft:stripped_birch_wood", "minecraft:stripped_crimson_hyphae", "minecraft:stripped_dark_oak_wood", "minecraft:stripped_jungle_wood", "minecraft:stripped_mangrove_wood", "minecraft:stripped_oak_wood", "minecraft:stripped_spruce_wood", "minecraft:stripped_warped_hyphae", "minecraft:stripped_cherry_wood"]}, {"id": "forge:dyes/black", "items": ["minecraft:black_dye"]}, {"id": "forge:dusts/nickel", "items": ["immersiveengineering:dust_nickel"]}, {"id": "minecraft:wooden_trapdoors", "items": ["minecraft:acacia_trapdoor", "minecraft:birch_trapdoor", "minecraft:dark_oak_trapdoor", "minecraft:jungle_trapdoor", "minecraft:oak_trapdoor", "minecraft:spruce_trapdoor", "minecraft:crimson_trapdoor", "minecraft:warped_trapdoor", "minecraft:mangrove_trapdoor", "minecraft:bamboo_trapdoor", "minecraft:cherry_trapdoor", "tconstruct:greenheart_trapdoor", "tconstruct:skyroot_trapdoor", "tconstruct:bloodshroom_trapdoor", "tconstruct:enderbark_trapdoor"]}, {"id": "mekanism:colorable/concrete", "items": ["minecraft:white_concrete", "minecraft:orange_concrete", "minecraft:magenta_concrete", "minecraft:light_blue_concrete", "minecraft:yellow_concrete", "minecraft:lime_concrete", "minecraft:pink_concrete", "minecraft:gray_concrete", "minecraft:light_gray_concrete", "minecraft:cyan_concrete", "minecraft:purple_concrete", "minecraft:blue_concrete", "minecraft:brown_concrete", "minecraft:green_concrete", "minecraft:red_concrete", "minecraft:black_concrete"]}, {"id": "tconstruct:casts/single_use/blank", "items": ["tconstruct:blank_sand_cast", "tconstruct:blank_red_sand_cast"]}, {"id": "c:green_dyes", "items": ["minecraft:green_dye"]}, {"id": "c:sandstone_slabs", "items": ["minecraft:sandstone_slab", "minecraft:cut_sandstone_slab", "minecraft:smooth_sandstone_slab", "minecraft:red_sandstone_slab", "minecraft:cut_red_sandstone_slab", "minecraft:smooth_red_sandstone_slab"]}, {"id": "forge:ingots/uranium", "items": ["immersiveengineering:ingot_uranium", "mekanism:ingot_uranium"]}, {"id": "ae2:p2p_attunements/item_p2p_tunnel", "items": ["ae2:storage_bus", "ae2:export_bus", "ae2:import_bus", "minecraft:hopper", "minecraft:chest", "minecraft:trapped_chest", "ae2:cable_interface", "ae2:interface"]}, {"id": "mekanism:dirty_dusts", "items": ["mekanism:dirty_dust_iron", "mekanism:dirty_dust_gold", "mekanism:dirty_dust_osmium", "mekanism:dirty_dust_copper", "mekanism:dirty_dust_tin", "mekanism:dirty_dust_lead", "mekanism:dirty_dust_uranium"]}, {"id": "c:lapis", "items": ["minecraft:lapis_lazuli"]}, {"id": "forge:storage_blocks/netherite", "items": ["minecraft:netherite_block"]}, {"id": "minecraft:small_flowers", "items": ["minecraft:dandelion", "minecraft:poppy", "minecraft:blue_orchid", "minecraft:allium", "minecraft:azure_bluet", "minecraft:red_tulip", "minecraft:orange_tulip", "minecraft:white_tulip", "minecraft:pink_tulip", "minecraft:oxeye_daisy", "minecraft:cornflower", "minecraft:lily_of_the_valley", "minecraft:wither_rose", "minecraft:torchflower"]}, {"id": "forge:dusts/saltpeter", "items": ["immersiveengineering:dust_saltpeter"]}, {"id": "forge:dusts/emerald", "items": ["mekanism:dust_emerald"]}, {"id": "forge:buckets/honey", "items": ["create:honey_bucket"]}, {"id": "forge:sheetmetals/electrum", "items": ["immersiveengineering:sheetmetal_electrum"]}, {"id": "cataclysm:bone_item", "items": ["minecraft:bone", "cataclysm:koboleton_bone"]}, {"id": "minecraft:trim_materials", "items": ["minecraft:iron_ingot", "minecraft:copper_ingot", "minecraft:gold_ingot", "minecraft:lapis_lazuli", "minecraft:emerald", "minecraft:diamond", "minecraft:netherite_ingot", "minecraft:redstone", "minecraft:quartz", "minecraft:amethyst_shard", "cataclysm:ignitium_ingot", "cataclysm:witherite_ingot", "cataclysm:ancient_metal_ingot", "cataclysm:cursium_ingot", "tconstruct:slimesteel_ingot", "tconstruct:amethyst_bronze_ingot", "tconstruct:pig_iron_ingot", "tconstruct:rose_gold_ingot", "tconstruct:steel_ingot", "tconstruct:cobalt_ingot", "tconstruct:manyullyn_ingot", "tconstruct:hepati<PERSON>_ingot", "tconstruct:cinderslime_ingot", "tconstruct:queens_slime_ingot", "tconstruct:earth_slime_crystal", "tconstruct:sky_slime_crystal", "tconstruct:ichor_slime_crystal", "tconstruct:ender_slime_crystal"]}, {"id": "forge:wires/lead", "items": ["immersiveengineering:wire_lead"]}, {"id": "ae2:all_fluix", "items": ["ae2:fluix_crystal"]}, {"id": "minecraft:wool_carpets", "items": ["minecraft:white_carpet", "minecraft:orange_carpet", "minecraft:magenta_carpet", "minecraft:light_blue_carpet", "minecraft:yellow_carpet", "minecraft:lime_carpet", "minecraft:pink_carpet", "minecraft:gray_carpet", "minecraft:light_gray_carpet", "minecraft:cyan_carpet", "minecraft:purple_carpet", "minecraft:blue_carpet", "minecraft:brown_carpet", "minecraft:green_carpet", "minecraft:red_carpet", "minecraft:black_carpet"]}, {"id": "create:dyed_table_cloths", "items": ["create:white_table_cloth", "create:orange_table_cloth", "create:magenta_table_cloth", "create:light_blue_table_cloth", "create:yellow_table_cloth", "create:lime_table_cloth", "create:pink_table_cloth", "create:gray_table_cloth", "create:light_gray_table_cloth", "create:cyan_table_cloth", "create:purple_table_cloth", "create:blue_table_cloth", "create:brown_table_cloth", "create:green_table_cloth", "create:red_table_cloth", "create:black_table_cloth"]}, {"id": "mekanism:crystals/gold", "items": ["mekanism:crystal_gold"]}, {"id": "c:uncolored_sandstone_stairs", "items": ["minecraft:sandstone_stairs", "minecraft:smooth_sandstone_stairs"]}, {"id": "immersiveengineering:cut_slabs/copper", "items": ["minecraft:cut_copper_slab", "minecraft:exposed_cut_copper_slab", "minecraft:weathered_cut_copper_slab", "minecraft:oxidized_cut_copper_slab", "minecraft:waxed_cut_copper_slab", "minecraft:waxed_exposed_cut_copper_slab", "minecraft:waxed_weathered_cut_copper_slab", "minecraft:waxed_oxidized_cut_copper_slab"]}, {"id": "forge:sheetmetals/constantan", "items": ["immersiveengineering:sheetmetal_constantan"]}, {"id": "forge:ingots/lead", "items": ["immersiveengineering:ingot_lead", "mekanism:ingot_lead"]}, {"id": "forge:slimeball/sky", "items": ["tconstruct:sky_slime_ball"]}, {"id": "forge:ores/aluminum", "items": ["immersiveengineering:ore_aluminum", "immersiveengineering:deepslate_ore_aluminum"]}, {"id": "mekanism:shards/copper", "items": ["mekanism:shard_copper"]}, {"id": "minecraft:beds", "items": ["minecraft:red_bed", "minecraft:black_bed", "minecraft:blue_bed", "minecraft:brown_bed", "minecraft:cyan_bed", "minecraft:gray_bed", "minecraft:green_bed", "minecraft:light_blue_bed", "minecraft:light_gray_bed", "minecraft:lime_bed", "minecraft:magenta_bed", "minecraft:orange_bed", "minecraft:pink_bed", "minecraft:purple_bed", "minecraft:white_bed", "minecraft:yellow_bed"]}, {"id": "tconstruct:modifiable/melee/primary", "items": ["tconstruct:sledge_hammer", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:battlesign", "tconstruct:swasher"]}, {"id": "forge:dusts/steel", "items": ["immersiveengineering:dust_steel", "mekanism:dust_steel"]}, {"id": "forge:tools", "items": ["minecraft:diamond_sword", "minecraft:stone_sword", "minecraft:golden_sword", "minecraft:netherite_sword", "minecraft:wooden_sword", "minecraft:iron_sword", "cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "ae2:certus_quartz_sword", "ae2:nether_quartz_sword", "ae2:fluix_sword", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:swasher", "minecraft:diamond_axe", "minecraft:stone_axe", "minecraft:golden_axe", "minecraft:netherite_axe", "minecraft:wooden_axe", "minecraft:iron_axe", "immersiveengineering:axe_steel", "ae2:certus_quartz_axe", "ae2:nether_quartz_axe", "ae2:fluix_axe", "tconstruct:mattock", "tconstruct:hand_axe", "tconstruct:broad_axe", "minecraft:diamond_pickaxe", "minecraft:stone_pickaxe", "minecraft:golden_pickaxe", "minecraft:netherite_pickaxe", "minecraft:wooden_pickaxe", "minecraft:iron_pickaxe", "cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "ae2:certus_quartz_pickaxe", "ae2:nether_quartz_pickaxe", "ae2:fluix_pickaxe", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:pickadze", "tconstruct:war_pick", "minecraft:diamond_shovel", "minecraft:stone_shovel", "minecraft:golden_shovel", "minecraft:netherite_shovel", "minecraft:wooden_shovel", "minecraft:iron_shovel", "immersiveengineering:shovel_steel", "ae2:certus_quartz_shovel", "ae2:nether_quartz_shovel", "ae2:fluix_shovel", "tconstruct:excavator", "minecraft:diamond_hoe", "minecraft:stone_hoe", "minecraft:golden_hoe", "minecraft:netherite_hoe", "minecraft:wooden_hoe", "minecraft:iron_hoe", "immersiveengineering:hoe_steel", "ae2:certus_quartz_hoe", "ae2:nether_quartz_hoe", "ae2:fluix_hoe", "tconstruct:kama", "tconstruct:scythe", "cataclysm:bulwark_of_the_flame", "cataclysm:black_steel_targe", "cataclysm:azure_sea_shield", "minecraft:shield", "immersiveengineering:shield", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield", "cataclysm:cursed_bow", "minecraft:bow", "mekanism:electric_bow", "tconstruct:longbow", "minecraft:crossbow", "tconstruct:crossbow", "minecraft:fishing_rod", "cataclysm:coral_spear", "cataclysm:<PERSON>_bar<PERSON>he", "minecraft:trident", "create:wrench", "ae2:certus_quartz_wrench", "ae2:nether_quartz_wrench", "ae2:network_tool", "immersiveengineering:hammer", "mekanism:configurator"]}, {"id": "immersiveengineering:circuits/logic", "items": ["immersiveengineering:electron_tube"]}, {"id": "forge:glass/silica", "items": ["minecraft:glass", "minecraft:black_stained_glass", "minecraft:blue_stained_glass", "minecraft:brown_stained_glass", "minecraft:cyan_stained_glass", "minecraft:gray_stained_glass", "minecraft:green_stained_glass", "minecraft:light_blue_stained_glass", "minecraft:light_gray_stained_glass", "minecraft:lime_stained_glass", "minecraft:magenta_stained_glass", "minecraft:orange_stained_glass", "minecraft:pink_stained_glass", "minecraft:purple_stained_glass", "minecraft:red_stained_glass", "minecraft:white_stained_glass", "minecraft:yellow_stained_glass", "tconstruct:clear_glass", "tconstruct:white_clear_stained_glass", "tconstruct:orange_clear_stained_glass", "tconstruct:magenta_clear_stained_glass", "tconstruct:light_blue_clear_stained_glass", "tconstruct:yellow_clear_stained_glass", "tconstruct:lime_clear_stained_glass", "tconstruct:pink_clear_stained_glass", "tconstruct:gray_clear_stained_glass", "tconstruct:light_gray_clear_stained_glass", "tconstruct:cyan_clear_stained_glass", "tconstruct:purple_clear_stained_glass", "tconstruct:blue_clear_stained_glass", "tconstruct:brown_clear_stained_glass", "tconstruct:green_clear_stained_glass", "tconstruct:red_clear_stained_glass", "tconstruct:black_clear_stained_glass"]}, {"id": "minecraft:wooden_slabs", "items": ["minecraft:oak_slab", "minecraft:spruce_slab", "minecraft:birch_slab", "minecraft:jungle_slab", "minecraft:acacia_slab", "minecraft:dark_oak_slab", "minecraft:crimson_slab", "minecraft:warped_slab", "minecraft:mangrove_slab", "minecraft:bamboo_slab", "minecraft:cherry_slab", "cataclysm:chorus_slab", "immersiveengineering:slab_treated_wood_horizontal", "immersiveengineering:slab_treated_wood_vertical", "immersiveengineering:slab_treated_wood_packaged", "tconstruct:greenheart_planks_slab", "tconstruct:skyroot_planks_slab", "tconstruct:bloodshroom_planks_slab", "tconstruct:enderbark_planks_slab"]}, {"id": "forge:nuggets/brass", "items": ["create:brass_nugget"]}, {"id": "forge:sand/red", "items": ["minecraft:red_sand"]}, {"id": "balm:nuggets", "items": ["minecraft:iron_nugget", "minecraft:gold_nugget", "immersiveengineering:nugget_copper", "create:copper_nugget", "tconstruct:copper_nugget", "immersiveengineering:nugget_aluminum", "immersiveengineering:nugget_lead", "mekanism:nugget_lead", "immersiveengineering:nugget_silver", "immersiveengineering:nugget_nickel", "immersiveengineering:nugget_uranium", "mekanism:nugget_uranium", "immersiveengineering:nugget_constantan", "immersiveengineering:nugget_electrum", "immersiveengineering:nugget_steel", "mekanism:nugget_steel", "tconstruct:steel_nugget", "create:zinc_nugget", "create:brass_nugget", "create:experience_nugget", "mekanism:nugget_osmium", "mekanism:nugget_tin", "mekanism:nugget_bronze", "mekanism:nugget_refined_glowstone", "mekanism:nugget_refined_obsidian", "tconstruct:netherite_nugget", "tconstruct:debris_nugget", "tconstruct:cobalt_nugget", "tconstruct:slimesteel_nugget", "tconstruct:amethyst_bronze_nugget", "tconstruct:rose_gold_nugget", "tconstruct:pig_iron_nugget", "tconstruct:cinderslime_nugget", "tconstruct:queens_slime_nugget", "tconstruct:manyullyn_nugget", "tconstruct:hepatizon_nugget", "tconstruct:soulsteel_nugget", "tconstruct:<PERSON><PERSON>e_nugget"]}, {"id": "tconstruct:slime_block", "items": ["minecraft:slime_block", "tconstruct:sky_slime", "tconstruct:ichor_slime", "tconstruct:ender_slime"]}, {"id": "tconstruct:copper_platforms", "items": ["tconstruct:copper_platform", "tconstruct:exposed_copper_platform", "tconstruct:weathered_copper_platform", "tconstruct:oxidized_copper_platform", "tconstruct:waxed_copper_platform", "tconstruct:waxed_exposed_copper_platform", "tconstruct:waxed_weathered_copper_platform", "tconstruct:waxed_oxidized_copper_platform"]}, {"id": "forge:dyes/brown", "items": ["minecraft:brown_dye"]}, {"id": "forge:dusts/netherite", "items": ["mekanism:dust_netherite"]}, {"id": "forge:armors/chestplate", "items": ["create:cardboard_chestplate"]}, {"id": "forge:dough/wheat", "items": ["create:dough"]}, {"id": "forge:storage_blocks/raw_iron", "items": ["minecraft:raw_iron_block"]}, {"id": "ae2:quartz_axe", "items": ["ae2:certus_quartz_axe", "ae2:nether_quartz_axe"]}, {"id": "forge:books", "items": ["tconstruct:materials_and_you", "tconstruct:tinkers_gadgetry", "tconstruct:puny_smelting", "tconstruct:mighty_smelting", "tconstruct:fantastic_foundry", "tconstruct:encyclopedia"]}, {"id": "forge:dusts", "items": ["minecraft:glowstone_dust", "minecraft:prismarine_shard", "minecraft:redstone", "immersiveengineering:dust_copper", "mekanism:dust_copper", "immersiveengineering:dust_aluminum", "immersiveengineering:dust_lead", "mekanism:dust_lead", "immersiveengineering:dust_silver", "immersiveengineering:dust_nickel", "immersiveengineering:dust_uranium", "mekanism:dust_uranium", "immersiveengineering:dust_constantan", "immersiveengineering:dust_electrum", "immersiveengineering:dust_steel", "mekanism:dust_steel", "immersiveengineering:dust_iron", "mekanism:dust_iron", "immersiveengineering:dust_gold", "mekanism:dust_gold", "ae2:certus_quartz_dust", "ae2:ender_dust", "ae2:fluix_dust", "ae2:sky_dust", "mekanism:dust_osmium", "mekanism:dust_tin", "mekanism:dust_bronze", "mekanism:dust_charcoal", "mekanism:dust_coal", "mekanism:dust_diamond", "mekanism:dust_emerald", "mekanism:dust_netherite", "mekanism:dust_lapis_lazuli", "mekanism:dust_lithium", "create:powdered_obsidian", "mekanism:dust_obsidian", "mekanism:dust_quartz", "mekanism:dust_refined_obsidian", "mekanism:salt", "immersiveengineering:dust_sulfur", "mekanism:dust_sulfur", "immersiveengineering:dust_wood", "mekanism:sawdust", "mekanism:dust_fluorite"]}, {"id": "forge:ingots/brass", "items": ["create:brass_ingot"]}, {"id": "tconstruct:casts/multi_use/ingot", "items": ["tconstruct:ingot_cast"]}, {"id": "create:modded_stripped_wood", "items": ["tconstruct:stripped_greenheart_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:stripped_bloodshroom_wood"]}, {"id": "forge:nuggets/constantan", "items": ["immersiveengineering:nugget_constantan"]}, {"id": "tconstruct:chrysophilite_ores", "items": ["minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore", "minecraft:gilded_blackstone"]}, {"id": "minecraft:fishes", "items": ["minecraft:cod", "minecraft:cooked_cod", "minecraft:salmon", "minecraft:cooked_salmon", "minecraft:pufferfish", "minecraft:tropical_fish", "cataclysm:lionfish"]}, {"id": "tconstruct:melting/diamond/tools_costing_11", "items": []}, {"id": "c:black_dyes", "items": ["minecraft:black_dye"]}, {"id": "forge:raw_materials/lead", "items": ["immersiveengineering:raw_lead", "mekanism:raw_lead"]}, {"id": "balm:pink_dyes", "items": ["minecraft:pink_dye"]}, {"id": "forge:glass_panes/black", "items": ["minecraft:black_stained_glass_pane", "tconstruct:black_clear_stained_glass_pane"]}, {"id": "forge:ore_rates/dense", "items": ["minecraft:copper_ore", "minecraft:deepslate_copper_ore", "minecraft:deepslate_lapis_ore", "minecraft:deepslate_redstone_ore", "minecraft:lapis_ore", "minecraft:redstone_ore", "mekanism:fluorite_ore", "mekanism:deepslate_fluorite_ore"]}, {"id": "c:raw_copper_blocks", "items": ["minecraft:raw_copper_block"]}, {"id": "c:ores", "items": ["minecraft:coal_ore", "minecraft:deepslate_coal_ore", "minecraft:copper_ore", "minecraft:deepslate_copper_ore", "minecraft:diamond_ore", "minecraft:deepslate_diamond_ore", "minecraft:emerald_ore", "minecraft:deepslate_emerald_ore", "minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore", "minecraft:iron_ore", "minecraft:deepslate_iron_ore", "minecraft:lapis_ore", "minecraft:deepslate_lapis_ore", "minecraft:redstone_ore", "minecraft:deepslate_redstone_ore", "minecraft:nether_quartz_ore", "minecraft:ancient_debris", "immersiveengineering:ore_aluminum", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:deepslate_ore_lead", "mekanism:lead_ore", "mekanism:deepslate_lead_ore", "immersiveengineering:ore_silver", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:ore_nickel", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:ore_uranium", "immersiveengineering:deepslate_ore_uranium", "mekanism:uranium_ore", "mekanism:deepslate_uranium_ore", "create:zinc_ore", "create:deepslate_zinc_ore", "mekanism:tin_ore", "mekanism:deepslate_tin_ore", "mekanism:osmium_ore", "mekanism:deepslate_osmium_ore", "mekanism:fluorite_ore", "mekanism:deepslate_fluorite_ore", "tconstruct:cobalt_ore"]}, {"id": "mekanism:clumps/tin", "items": ["mekanism:clump_tin"]}, {"id": "tconstruct:modifiable/armor/helmets", "items": ["tconstruct:travelers_helmet", "tconstruct:plate_helmet", "tconstruct:slime_helmet"]}, {"id": "forge:nuggets/zinc", "items": ["create:zinc_nugget"]}, {"id": "tconstruct:casts/gold", "items": ["tconstruct:ingot_cast", "tconstruct:nugget_cast", "tconstruct:gem_cast", "tconstruct:rod_cast", "tconstruct:repair_kit_cast", "tconstruct:plate_cast", "tconstruct:gear_cast", "tconstruct:coin_cast", "tconstruct:wire_cast", "tconstruct:pick_head_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_blade_cast", "tconstruct:adze_head_cast", "tconstruct:hammer_head_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_blade_cast", "tconstruct:large_plate_cast", "tconstruct:tool_binding_cast", "tconstruct:tough_binding_cast", "tconstruct:tool_handle_cast", "tconstruct:tough_handle_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_grip_cast", "tconstruct:helmet_plating_cast", "tconstruct:chestplate_plating_cast", "tconstruct:leggings_plating_cast", "tconstruct:boots_plating_cast", "tconstruct:maille_cast"]}, {"id": "forge:dusts/prismarine", "items": ["minecraft:prismarine_shard"]}, {"id": "c:milk_buckets", "items": ["minecraft:milk_bucket"]}, {"id": "tconstruct:tanks", "items": ["tconstruct:seared_fuel_tank", "tconstruct:seared_fuel_gauge", "tconstruct:seared_ingot_tank", "tconstruct:seared_ingot_gauge", "tconstruct:scorched_fuel_tank", "tconstruct:scorched_fuel_gauge", "tconstruct:scorched_ingot_tank", "tconstruct:scorched_ingot_gauge"]}, {"id": "tconstruct:casts/multi_use/tough_binding", "items": ["tconstruct:tough_binding_cast"]}, {"id": "forge:buckets/water", "items": ["minecraft:water_bucket"]}, {"id": "forge:clusters", "items": ["ae2:quartz_cluster", "minecraft:amethyst_cluster"]}, {"id": "c:lime_dyes", "items": ["minecraft:lime_dye"]}, {"id": "forge:dusts/osmium", "items": ["mekanism:dust_osmium"]}, {"id": "forge:storage_blocks/electrum", "items": ["immersiveengineering:storage_electrum"]}, {"id": "forge:crops/carrot", "items": ["minecraft:carrot"]}, {"id": "forge:glass/lime", "items": ["minecraft:lime_stained_glass", "tconstruct:lime_clear_stained_glass"]}, {"id": "forge:storage_blocks/queens_slime", "items": ["tconstruct:queens_slime_block"]}, {"id": "tconstruct:modifiable/interactable/dual", "items": ["tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan"]}, {"id": "forge:cobblestone", "items": ["minecraft:cobblestone", "minecraft:infested_cobblestone", "minecraft:mossy_cobblestone", "minecraft:cobbled_deepslate"]}, {"id": "forge:nuggets/electrum", "items": ["immersiveengineering:nugget_electrum"]}, {"id": "forge:pellets/antimatter", "items": ["mekanism:pellet_antimatter"]}, {"id": "forge:ingots/netherite_scrap", "items": ["minecraft:netherite_scrap"]}, {"id": "forge:ingots/zinc", "items": ["create:zinc_ingot"]}, {"id": "forge:sawdust", "items": ["immersiveengineering:dust_wood", "mekanism:sawdust"]}, {"id": "forge:dusts/wood", "items": ["immersiveengineering:dust_wood", "mekanism:sawdust"]}, {"id": "forge:tools/crossbows", "items": ["minecraft:crossbow", "tconstruct:crossbow", "tconstruct:war_pick"]}, {"id": "mekanism:colorable/candle", "items": ["minecraft:candle", "minecraft:white_candle", "minecraft:orange_candle", "minecraft:magenta_candle", "minecraft:light_blue_candle", "minecraft:yellow_candle", "minecraft:lime_candle", "minecraft:pink_candle", "minecraft:gray_candle", "minecraft:light_gray_candle", "minecraft:cyan_candle", "minecraft:purple_candle", "minecraft:blue_candle", "minecraft:brown_candle", "minecraft:green_candle", "minecraft:red_candle", "minecraft:black_candle"]}, {"id": "tconstruct:casts/single_use/rod", "items": ["tconstruct:rod_sand_cast", "tconstruct:rod_red_sand_cast"]}, {"id": "forge:nuggets/aluminum", "items": ["immersiveengineering:nugget_aluminum"]}, {"id": "tconstruct:casts/multi_use/chestplate_plating", "items": ["tconstruct:chestplate_plating_cast"]}, {"id": "minecraft:hoes", "items": ["minecraft:diamond_hoe", "minecraft:stone_hoe", "minecraft:golden_hoe", "minecraft:netherite_hoe", "minecraft:wooden_hoe", "minecraft:iron_hoe", "immersiveengineering:hoe_steel", "ae2:certus_quartz_hoe", "ae2:nether_quartz_hoe", "ae2:fluix_hoe", "tconstruct:kama", "tconstruct:scythe"]}, {"id": "forge:sandstone", "items": ["minecraft:sandstone", "minecraft:cut_sandstone", "minecraft:chiseled_sandstone", "minecraft:smooth_sandstone", "minecraft:red_sandstone", "minecraft:cut_red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:smooth_red_sandstone"]}, {"id": "minecraft:logs_that_burn", "items": ["minecraft:dark_oak_log", "minecraft:dark_oak_wood", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood", "minecraft:oak_log", "minecraft:oak_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood", "minecraft:acacia_log", "minecraft:acacia_wood", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood", "minecraft:birch_log", "minecraft:birch_wood", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood", "minecraft:jungle_log", "minecraft:jungle_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood", "minecraft:spruce_log", "minecraft:spruce_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood", "minecraft:mangrove_log", "minecraft:mangrove_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood", "minecraft:cherry_log", "minecraft:cherry_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood"]}, {"id": "forge:ores/diamond", "items": ["minecraft:diamond_ore", "minecraft:deepslate_diamond_ore"]}, {"id": "forge:wires/copper", "items": ["immersiveengineering:wire_copper"]}, {"id": "minecraft:axes", "items": ["minecraft:diamond_axe", "minecraft:stone_axe", "minecraft:golden_axe", "minecraft:netherite_axe", "minecraft:wooden_axe", "minecraft:iron_axe", "immersiveengineering:axe_steel", "ae2:certus_quartz_axe", "ae2:nether_quartz_axe", "ae2:fluix_axe", "tconstruct:mattock", "tconstruct:hand_axe", "tconstruct:broad_axe"]}, {"id": "forge:storage_blocks/quartz", "items": ["minecraft:quartz_block"]}, {"id": "minecraft:dirt", "items": ["minecraft:dirt", "minecraft:grass_block", "minecraft:podzol", "minecraft:coarse_dirt", "minecraft:mycelium", "minecraft:rooted_dirt", "minecraft:moss_block", "minecraft:mud", "minecraft:muddy_mangrove_roots"]}, {"id": "tconstruct:seared_tanks", "items": ["tconstruct:seared_fuel_tank", "tconstruct:seared_fuel_gauge", "tconstruct:seared_ingot_tank", "tconstruct:seared_ingot_gauge"]}, {"id": "c:raw_ores", "items": ["minecraft:raw_copper", "minecraft:raw_gold", "minecraft:raw_iron", "immersiveengineering:raw_aluminum", "immersiveengineering:raw_lead", "mekanism:raw_lead", "immersiveengineering:raw_silver", "immersiveengineering:raw_nickel", "immersiveengineering:raw_uranium", "mekanism:raw_uranium", "create:raw_zinc", "mekanism:raw_osmium", "mekanism:raw_tin", "tconstruct:raw_cobalt"]}, {"id": "forge:storage_blocks/copper", "items": ["minecraft:copper_block"]}, {"id": "forge:storage_blocks/raw_cobalt", "items": ["tconstruct:raw_cobalt_block"]}, {"id": "forge:nuggets/netherite_scrap", "items": ["tconstruct:debris_nugget"]}, {"id": "minecraft:completes_find_tree_tutorial", "items": ["minecraft:dark_oak_log", "minecraft:dark_oak_wood", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood", "minecraft:oak_log", "minecraft:oak_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood", "minecraft:acacia_log", "minecraft:acacia_wood", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood", "minecraft:birch_log", "minecraft:birch_wood", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood", "minecraft:jungle_log", "minecraft:jungle_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood", "minecraft:spruce_log", "minecraft:spruce_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood", "minecraft:mangrove_log", "minecraft:mangrove_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood", "minecraft:cherry_log", "minecraft:cherry_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood", "minecraft:crimson_stem", "minecraft:stripped_crimson_stem", "minecraft:crimson_hyphae", "minecraft:stripped_crimson_hyphae", "minecraft:warped_stem", "minecraft:stripped_warped_stem", "minecraft:warped_hyphae", "minecraft:stripped_warped_hyphae", "tconstruct:greenheart_log", "tconstruct:stripped_greenheart_log", "tconstruct:greenheart_wood", "tconstruct:stripped_greenheart_wood", "tconstruct:skyroot_log", "tconstruct:stripped_skyroot_log", "tconstruct:skyroot_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:bloodshroom_log", "tconstruct:stripped_bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:stripped_bloodshroom_wood", "tconstruct:enderbark_log", "tconstruct:stripped_enderbark_log", "tconstruct:enderbark_wood", "tconstruct:stripped_enderbark_wood", "minecraft:jungle_leaves", "minecraft:oak_leaves", "minecraft:spruce_leaves", "minecraft:dark_oak_leaves", "minecraft:acacia_leaves", "minecraft:birch_leaves", "minecraft:azalea_leaves", "minecraft:flowering_azalea_leaves", "minecraft:mangrove_leaves", "minecraft:cherry_leaves", "tconstruct:earth_slime_leaves", "tconstruct:sky_slime_leaves", "tconstruct:ender_slime_leaves", "minecraft:nether_wart_block", "minecraft:warped_wart_block"]}, {"id": "forge:nuggets/slimesteel", "items": ["tconstruct:slimesteel_nugget"]}, {"id": "tconstruct:modifiable/armor/trim", "items": ["tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:slime_helmet", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots"]}, {"id": "forge:wires/steel", "items": ["immersiveengineering:wire_steel"]}, {"id": "forge:nuggets/gold", "items": ["minecraft:gold_nugget"]}, {"id": "tconstruct:melting/gold/tools_costing_2", "items": ["minecraft:golden_sword", "minecraft:golden_hoe"]}, {"id": "createoreexcavation:drills", "items": ["createoreexcavation:drill", "createoreexcavation:diamond_drill", "createoreexcavation:netherite_drill"]}, {"id": "create:casing", "items": ["create:andesite_casing", "create:brass_casing", "create:copper_casing", "create:shadow_steel_casing", "create:refined_radiance_casing", "create:railway_casing"]}, {"id": "forge:dusts/certus_quartz", "items": ["ae2:certus_quartz_dust"]}, {"id": "create:invalid_for_track_paving", "items": ["create:wooden_bracket", "create:metal_bracket"]}, {"id": "balm:magenta_dyes", "items": ["minecraft:magenta_dye"]}, {"id": "c:diamonds", "items": ["minecraft:diamond"]}, {"id": "forge:raw_materials/zinc", "items": ["create:raw_zinc"]}, {"id": "forge:glass/brown", "items": ["minecraft:brown_stained_glass", "tconstruct:brown_clear_stained_glass"]}, {"id": "minecraft:wool", "items": ["minecraft:white_wool", "minecraft:orange_wool", "minecraft:magenta_wool", "minecraft:light_blue_wool", "minecraft:yellow_wool", "minecraft:lime_wool", "minecraft:pink_wool", "minecraft:gray_wool", "minecraft:light_gray_wool", "minecraft:cyan_wool", "minecraft:purple_wool", "minecraft:blue_wool", "minecraft:brown_wool", "minecraft:green_wool", "minecraft:red_wool", "minecraft:black_wool"]}, {"id": "tconstruct:structure_debug/general", "items": []}, {"id": "c:raw_iron_ores", "items": ["minecraft:raw_iron"]}, {"id": "tconstruct:melting/nickel/tools_costing_3", "items": []}, {"id": "tconstruct:melting/silver/tools_costing_2", "items": []}, {"id": "forge:alloys/elite", "items": ["mekanism:alloy_reinforced"]}, {"id": "forge:buckets/empty", "items": ["minecraft:bucket"]}, {"id": "tconstruct:casts/red_sand", "items": ["tconstruct:blank_red_sand_cast", "tconstruct:ingot_red_sand_cast", "tconstruct:nugget_red_sand_cast", "tconstruct:gem_red_sand_cast", "tconstruct:rod_red_sand_cast", "tconstruct:repair_kit_red_sand_cast", "tconstruct:plate_red_sand_cast", "tconstruct:gear_red_sand_cast", "tconstruct:coin_red_sand_cast", "tconstruct:wire_red_sand_cast", "tconstruct:pick_head_red_sand_cast", "tconstruct:small_axe_head_red_sand_cast", "tconstruct:small_blade_red_sand_cast", "tconstruct:adze_head_red_sand_cast", "tconstruct:hammer_head_red_sand_cast", "tconstruct:broad_axe_head_red_sand_cast", "tconstruct:broad_blade_red_sand_cast", "tconstruct:large_plate_red_sand_cast", "tconstruct:tool_binding_red_sand_cast", "tconstruct:tough_binding_red_sand_cast", "tconstruct:tool_handle_red_sand_cast", "tconstruct:tough_handle_red_sand_cast", "tconstruct:bow_limb_red_sand_cast", "tconstruct:bow_grip_red_sand_cast", "tconstruct:helmet_plating_red_sand_cast", "tconstruct:chestplate_plating_red_sand_cast", "tconstruct:leggings_plating_red_sand_cast", "tconstruct:boots_plating_red_sand_cast", "tconstruct:maille_red_sand_cast"]}, {"id": "forge:storage_blocks/soulsteel", "items": ["tconstruct:soulsteel_block"]}, {"id": "forge:fuels", "items": ["mekanism:bio_fuel"]}, {"id": "tconstruct:modifiable/ranged/bows", "items": ["tconstruct:longbow", "tconstruct:crossbow", "tconstruct:war_pick"]}, {"id": "forge:diorite", "items": ["minecraft:diorite"]}, {"id": "mekanism:clumps/osmium", "items": ["mekanism:clump_osmium"]}, {"id": "forge:dyes/magenta", "items": ["minecraft:magenta_dye"]}, {"id": "forge:buckets/lava", "items": ["minecraft:lava_bucket"]}, {"id": "tconstruct:melting/tin/tools_costing_3", "items": []}, {"id": "minecraft:banners", "items": ["minecraft:white_banner", "minecraft:orange_banner", "minecraft:magenta_banner", "minecraft:light_blue_banner", "minecraft:yellow_banner", "minecraft:lime_banner", "minecraft:pink_banner", "minecraft:gray_banner", "minecraft:light_gray_banner", "minecraft:cyan_banner", "minecraft:purple_banner", "minecraft:blue_banner", "minecraft:brown_banner", "minecraft:green_banner", "minecraft:red_banner", "minecraft:black_banner"]}, {"id": "forge:dusts/coal_coke", "items": ["immersiveengineering:dust_coke"]}, {"id": "forge:stone", "items": ["minecraft:andesite", "minecraft:diorite", "minecraft:granite", "minecraft:infested_stone", "minecraft:stone", "minecraft:polished_andesite", "minecraft:polished_diorite", "minecraft:polished_granite", "minecraft:deepslate", "minecraft:polished_deepslate", "minecraft:infested_deepslate", "minecraft:tuff"]}, {"id": "forge:stripped_wood", "items": ["minecraft:stripped_acacia_wood", "minecraft:stripped_birch_wood", "minecraft:stripped_crimson_hyphae", "minecraft:stripped_dark_oak_wood", "minecraft:stripped_jungle_wood", "minecraft:stripped_mangrove_wood", "minecraft:stripped_oak_wood", "minecraft:stripped_spruce_wood", "minecraft:stripped_warped_hyphae", "minecraft:stripped_cherry_wood", "tconstruct:stripped_greenheart_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:stripped_bloodshroom_wood"]}, {"id": "create:stone_types/galosphere/amethyst", "items": []}, {"id": "forge:ores/fluorite", "items": ["mekanism:fluorite_ore", "mekanism:deepslate_fluorite_ore"]}, {"id": "forge:armors/hazmat", "items": ["mekanism:hazmat_mask"]}, {"id": "tconstruct:melting/netherite/tools_costing_2", "items": ["minecraft:netherite_sword", "minecraft:netherite_hoe"]}, {"id": "forge:coal_coke", "items": ["immersiveengineering:coal_coke"]}, {"id": "forge:chests", "items": ["minecraft:ender_chest", "minecraft:trapped_chest", "minecraft:chest"]}, {"id": "forge:stained_glass", "items": ["minecraft:white_stained_glass", "minecraft:orange_stained_glass", "minecraft:magenta_stained_glass", "minecraft:light_blue_stained_glass", "minecraft:yellow_stained_glass", "minecraft:lime_stained_glass", "minecraft:pink_stained_glass", "minecraft:gray_stained_glass", "minecraft:light_gray_stained_glass", "minecraft:cyan_stained_glass", "minecraft:purple_stained_glass", "minecraft:blue_stained_glass", "minecraft:brown_stained_glass", "minecraft:green_stained_glass", "minecraft:red_stained_glass", "minecraft:black_stained_glass", "tconstruct:white_clear_stained_glass", "tconstruct:orange_clear_stained_glass", "tconstruct:magenta_clear_stained_glass", "tconstruct:light_blue_clear_stained_glass", "tconstruct:yellow_clear_stained_glass", "tconstruct:lime_clear_stained_glass", "tconstruct:pink_clear_stained_glass", "tconstruct:gray_clear_stained_glass", "tconstruct:light_gray_clear_stained_glass", "tconstruct:cyan_clear_stained_glass", "tconstruct:purple_clear_stained_glass", "tconstruct:blue_clear_stained_glass", "tconstruct:brown_clear_stained_glass", "tconstruct:green_clear_stained_glass", "tconstruct:red_clear_stained_glass", "tconstruct:black_clear_stained_glass"]}, {"id": "c:quartz_ores", "items": ["minecraft:nether_quartz_ore"]}, {"id": "c:cyan_dyes", "items": ["minecraft:cyan_dye"]}, {"id": "tconstruct:modifiable/harvest", "items": ["tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:swasher", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:melting_pan", "tconstruct:war_pick"]}, {"id": "forge:gems/fluorite", "items": ["mekanism:fluorite_gem"]}, {"id": "forge:ingots/tin", "items": ["mekanism:ingot_tin"]}, {"id": "c:raw_copper_ores", "items": ["minecraft:raw_copper"]}, {"id": "forge:dyes/yellow", "items": ["minecraft:yellow_dye", "mekanism:dust_sulfur"]}, {"id": "tconstruct:greenheart_logs", "items": ["tconstruct:greenheart_log", "tconstruct:stripped_greenheart_log", "tconstruct:greenheart_wood", "tconstruct:stripped_greenheart_wood"]}, {"id": "immersiveengineering:tools/wirecutters", "items": ["immersiveengineering:wirecutter"]}, {"id": "minecraft:breaks_decorated_pots", "items": ["minecraft:diamond_sword", "minecraft:stone_sword", "minecraft:golden_sword", "minecraft:netherite_sword", "minecraft:wooden_sword", "minecraft:iron_sword", "cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "ae2:certus_quartz_sword", "ae2:nether_quartz_sword", "ae2:fluix_sword", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:swasher", "minecraft:diamond_axe", "minecraft:stone_axe", "minecraft:golden_axe", "minecraft:netherite_axe", "minecraft:wooden_axe", "minecraft:iron_axe", "immersiveengineering:axe_steel", "ae2:certus_quartz_axe", "ae2:nether_quartz_axe", "ae2:fluix_axe", "tconstruct:mattock", "tconstruct:hand_axe", "tconstruct:broad_axe", "minecraft:diamond_pickaxe", "minecraft:stone_pickaxe", "minecraft:golden_pickaxe", "minecraft:netherite_pickaxe", "minecraft:wooden_pickaxe", "minecraft:iron_pickaxe", "cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "ae2:certus_quartz_pickaxe", "ae2:nether_quartz_pickaxe", "ae2:fluix_pickaxe", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:pickadze", "tconstruct:war_pick", "minecraft:diamond_shovel", "minecraft:stone_shovel", "minecraft:golden_shovel", "minecraft:netherite_shovel", "minecraft:wooden_shovel", "minecraft:iron_shovel", "immersiveengineering:shovel_steel", "ae2:certus_quartz_shovel", "ae2:nether_quartz_shovel", "ae2:fluix_shovel", "tconstruct:excavator", "minecraft:diamond_hoe", "minecraft:stone_hoe", "minecraft:golden_hoe", "minecraft:netherite_hoe", "minecraft:wooden_hoe", "minecraft:iron_hoe", "immersiveengineering:hoe_steel", "ae2:certus_quartz_hoe", "ae2:nether_quartz_hoe", "ae2:fluix_hoe", "tconstruct:kama", "tconstruct:scythe", "minecraft:trident"]}, {"id": "create:stone_types/andesite", "items": ["create:cut_andesite", "create:cut_andesite_stairs", "create:cut_andesite_wall", "create:polished_cut_andesite", "create:polished_cut_andesite_stairs", "create:polished_cut_andesite_wall", "create:cut_andesite_bricks", "create:cut_andesite_brick_stairs", "create:cut_andesite_brick_wall", "create:small_andesite_bricks", "create:small_andesite_brick_stairs", "create:small_andesite_brick_wall", "create:layered_andesite", "create:andesite_pillar", "minecraft:andesite"]}, {"id": "forge:tools/wrench", "items": ["create:wrench", "ae2:certus_quartz_wrench", "ae2:nether_quartz_wrench", "ae2:network_tool", "immersiveengineering:hammer", "mekanism:configurator"]}, {"id": "minecraft:beacon_payment_items", "items": ["minecraft:netherite_ingot", "minecraft:emerald", "minecraft:diamond", "minecraft:gold_ingot", "minecraft:iron_ingot", "create:andesite_alloy", "create:zinc_ingot", "create:brass_ingot", "mekanism:ingot_osmium", "mekanism:ingot_tin", "mekanism:ingot_lead", "mekanism:ingot_uranium", "mekanism:ingot_bronze", "mekanism:ingot_refined_obsidian", "mekanism:ingot_refined_glowstone", "mekanism:ingot_steel", "immersiveengineering:ingot_steel", "tconstruct:steel_ingot", "tconstruct:cobalt_ingot", "tconstruct:queens_slime_ingot", "tconstruct:manyullyn_ingot", "tconstruct:hepati<PERSON>_ingot"]}, {"id": "forge:storage_blocks/raw_aluminum", "items": ["immersiveengineering:raw_block_aluminum"]}, {"id": "forge:potions", "items": ["minecraft:lingering_potion", "minecraft:splash_potion", "minecraft:potion"]}, {"id": "forge:storage_blocks/diamond", "items": ["minecraft:diamond_block"]}, {"id": "forge:seeds", "items": ["minecraft:beetroot_seeds", "minecraft:melon_seeds", "minecraft:pumpkin_seeds", "minecraft:wheat_seeds", "immersiveengineering:seed"]}, {"id": "forge:dusts/hop_graphite", "items": ["immersiveengineering:dust_hop_graphite"]}, {"id": "mekanism:crystals/osmium", "items": ["mekanism:crystal_osmium"]}, {"id": "forge:ingots/soulsteel", "items": ["tconstruct:soulsteel_ingot"]}, {"id": "mekanism:colorable/glass_panes", "items": ["minecraft:glass_pane", "minecraft:white_stained_glass_pane", "minecraft:orange_stained_glass_pane", "minecraft:magenta_stained_glass_pane", "minecraft:light_blue_stained_glass_pane", "minecraft:yellow_stained_glass_pane", "minecraft:lime_stained_glass_pane", "minecraft:pink_stained_glass_pane", "minecraft:gray_stained_glass_pane", "minecraft:light_gray_stained_glass_pane", "minecraft:cyan_stained_glass_pane", "minecraft:purple_stained_glass_pane", "minecraft:blue_stained_glass_pane", "minecraft:brown_stained_glass_pane", "minecraft:green_stained_glass_pane", "minecraft:red_stained_glass_pane", "minecraft:black_stained_glass_pane"]}, {"id": "immersiveengineering:scaffoldings/aluminum", "items": ["immersiveengineering:alu_scaffolding_standard", "immersiveengineering:alu_scaffolding_grate_top", "immersiveengineering:alu_scaffolding_wooden_top"]}, {"id": "mekanism:configurators", "items": ["mekanism:configurator", "create:wrench", "ae2:certus_quartz_wrench", "ae2:nether_quartz_wrench", "ae2:network_tool", "immersiveengineering:hammer"]}, {"id": "forge:storage_blocks/raw_nickel", "items": ["immersiveengineering:raw_block_nickel"]}, {"id": "mekanism:colorable/beds", "items": ["minecraft:white_bed", "minecraft:orange_bed", "minecraft:magenta_bed", "minecraft:light_blue_bed", "minecraft:yellow_bed", "minecraft:lime_bed", "minecraft:pink_bed", "minecraft:gray_bed", "minecraft:light_gray_bed", "minecraft:cyan_bed", "minecraft:purple_bed", "minecraft:blue_bed", "minecraft:brown_bed", "minecraft:green_bed", "minecraft:red_bed", "minecraft:black_bed"]}, {"id": "tconstruct:modifiable/held", "items": ["tconstruct:war_pick", "tconstruct:swasher", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:travelers_shield", "tconstruct:plate_shield", "tconstruct:battlesign"]}, {"id": "tconstruct:casts/multi_use/hammer_head", "items": ["tconstruct:hammer_head_cast"]}, {"id": "tconstruct:melting/iron/tools_costing_7", "items": ["minecraft:iron_leggings"]}, {"id": "forge:plates/plastic", "items": ["immersiveengineering:plate_duroplast"]}, {"id": "create:stone_types/galosphere/allurite", "items": []}, {"id": "c:yellow_dyes", "items": ["minecraft:yellow_dye", "mekanism:dust_sulfur"]}, {"id": "ae2:can_remove_color", "items": ["minecraft:water_bucket", "minecraft:snowball"]}, {"id": "tconstruct:modifiable/ranged/crossbows", "items": ["tconstruct:crossbow", "tconstruct:war_pick"]}, {"id": "forge:bones", "items": ["minecraft:bone"]}, {"id": "forge:slag", "items": ["immersiveengineering:slag"]}, {"id": "forge:glass/cyan", "items": ["minecraft:cyan_stained_glass", "tconstruct:cyan_clear_stained_glass"]}, {"id": "c:emeralds", "items": ["minecraft:emerald"]}, {"id": "tconstruct:guides", "items": ["tconstruct:materials_and_you", "tconstruct:tinkers_gadgetry", "tconstruct:puny_smelting", "tconstruct:mighty_smelting", "tconstruct:fantastic_foundry", "tconstruct:encyclopedia"]}, {"id": "tconstruct:casts/multi_use/gem", "items": ["tconstruct:gem_cast"]}, {"id": "c:glass_panes", "items": ["minecraft:glass_pane", "minecraft:gray_stained_glass_pane", "minecraft:black_stained_glass_pane", "minecraft:orange_stained_glass_pane", "minecraft:blue_stained_glass_pane", "minecraft:brown_stained_glass_pane", "minecraft:cyan_stained_glass_pane", "minecraft:green_stained_glass_pane", "minecraft:light_blue_stained_glass_pane", "minecraft:light_gray_stained_glass_pane", "minecraft:lime_stained_glass_pane", "minecraft:magenta_stained_glass_pane", "minecraft:pink_stained_glass_pane", "minecraft:purple_stained_glass_pane", "minecraft:red_stained_glass_pane", "minecraft:white_stained_glass_pane", "minecraft:yellow_stained_glass_pane"]}, {"id": "minecraft:boats", "items": ["minecraft:oak_boat", "minecraft:spruce_boat", "minecraft:birch_boat", "minecraft:jungle_boat", "minecraft:acacia_boat", "minecraft:dark_oak_boat", "minecraft:mangrove_boat", "minecraft:bamboo_raft", "minecraft:cherry_boat", "minecraft:oak_chest_boat", "minecraft:spruce_chest_boat", "minecraft:birch_chest_boat", "minecraft:jungle_chest_boat", "minecraft:acacia_chest_boat", "minecraft:dark_oak_chest_boat", "minecraft:mangrove_chest_boat", "minecraft:bamboo_chest_raft", "minecraft:cherry_chest_boat"]}, {"id": "forge:alloys/advanced", "items": ["mekanism:alloy_infused"]}, {"id": "forge:dough", "items": ["create:dough"]}, {"id": "forge:armor/helmets", "items": ["cataclysm:monstrous_helm", "cataclysm:ignitium_helmet"]}, {"id": "forge:glass_panes/yellow", "items": ["minecraft:yellow_stained_glass_pane", "tconstruct:yellow_clear_stained_glass_pane"]}, {"id": "c:gems", "items": ["minecraft:amethyst_shard", "minecraft:diamond", "minecraft:emerald", "minecraft:lapis_lazuli", "minecraft:prismarine_crystals", "minecraft:quartz", "ae2:certus_quartz_crystal", "ae2:charged_certus_quartz_crystal", "ae2:fluix_crystal", "mekanism:fluorite_gem"]}, {"id": "forge:dyes/orange", "items": ["minecraft:orange_dye"]}, {"id": "forge:ores/uranium", "items": ["immersiveengineering:ore_uranium", "immersiveengineering:deepslate_ore_uranium", "mekanism:uranium_ore", "mekanism:deepslate_uranium_ore"]}, {"id": "forge:rods/steel", "items": ["immersiveengineering:stick_steel"]}, {"id": "forge:storage_blocks/constantan", "items": ["immersiveengineering:storage_constantan"]}, {"id": "forge:glass_panes/cyan", "items": ["minecraft:cyan_stained_glass_pane", "tconstruct:cyan_clear_stained_glass_pane"]}, {"id": "forge:treated_wood", "items": ["immersiveengineering:treated_wood_horizontal", "immersiveengineering:treated_wood_vertical", "immersiveengineering:treated_wood_packaged"]}, {"id": "forge:dusts/coal", "items": ["mekanism:dust_coal"]}, {"id": "tconstruct:melting/osmium/tools_costing_7", "items": []}, {"id": "c:hoes", "items": ["immersiveengineering:hoe_steel", "minecraft:diamond_hoe", "minecraft:golden_hoe", "minecraft:wooden_hoe", "minecraft:stone_hoe", "minecraft:iron_hoe", "minecraft:netherite_hoe"]}, {"id": "forge:glass/pink", "items": ["minecraft:pink_stained_glass", "tconstruct:pink_clear_stained_glass"]}, {"id": "tconstruct:modifiable/harvest/primary", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:melting_pan", "tconstruct:war_pick"]}, {"id": "immersiveengineering:blocks/copper", "items": ["minecraft:copper_block", "minecraft:exposed_copper", "minecraft:weathered_copper", "minecraft:oxidized_copper", "minecraft:waxed_copper_block", "minecraft:waxed_exposed_copper", "minecraft:waxed_weathered_copper", "minecraft:waxed_oxidized_copper"]}, {"id": "ae2:p2p_attunements/light_p2p_tunnel", "items": ["minecraft:torch", "minecraft:glowstone"]}, {"id": "tconstruct:modifiable/embellishment/wood", "items": ["tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff"]}, {"id": "tconstruct:casts/single_use/bow_limb", "items": ["tconstruct:bow_limb_sand_cast", "tconstruct:bow_limb_red_sand_cast"]}, {"id": "minecraft:swords", "items": ["minecraft:diamond_sword", "minecraft:stone_sword", "minecraft:golden_sword", "minecraft:netherite_sword", "minecraft:wooden_sword", "minecraft:iron_sword", "cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "ae2:certus_quartz_sword", "ae2:nether_quartz_sword", "ae2:fluix_sword", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:swasher"]}, {"id": "forge:granite", "items": ["minecraft:granite"]}, {"id": "tconstruct:modifiable/bonus_slots", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:slime_helmet", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "forge:glass_panes/white", "items": ["minecraft:white_stained_glass_pane", "tconstruct:white_clear_stained_glass_pane"]}, {"id": "tconstruct:modifiable/special", "items": ["tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff"]}, {"id": "forge:armor/chestplates", "items": ["cataclysm:ignitium_chestplate", "cataclysm:ignitium_elytra_chestplate"]}, {"id": "minecraft:sand", "items": ["minecraft:sand", "minecraft:red_sand", "minecraft:suspicious_sand"]}, {"id": "tconstruct:modifiable/melee/sword", "items": ["tconstruct:sword", "tconstruct:cleaver"]}, {"id": "create:vanilla_stripped_logs", "items": ["minecraft:stripped_acacia_log", "minecraft:stripped_birch_log", "minecraft:stripped_crimson_stem", "minecraft:stripped_dark_oak_log", "minecraft:stripped_jungle_log", "minecraft:stripped_mangrove_log", "minecraft:stripped_oak_log", "minecraft:stripped_spruce_log", "minecraft:stripped_warped_stem", "minecraft:stripped_cherry_log", "minecraft:stripped_bamboo_block"]}, {"id": "forge:ores/cobalt", "items": ["tconstruct:cobalt_ore"]}, {"id": "c:chests", "items": ["minecraft:ender_chest", "minecraft:trapped_chest", "minecraft:chest"]}, {"id": "tconstruct:casts/multi_use/leggings_plating", "items": ["tconstruct:leggings_plating_cast"]}, {"id": "forge:nuggets/rose_gold", "items": ["tconstruct:rose_gold_nugget"]}, {"id": "minecraft:decorated_pot_ingredients", "items": ["minecraft:brick", "minecraft:angler_pottery_sherd", "minecraft:archer_pottery_sherd", "minecraft:arms_up_pottery_sherd", "minecraft:blade_pottery_sherd", "minecraft:brewer_pottery_sherd", "minecraft:burn_pottery_sherd", "minecraft:danger_pottery_sherd", "minecraft:explorer_pottery_sherd", "minecraft:friend_pottery_sherd", "minecraft:heart_pottery_sherd", "minecraft:heartbreak_pottery_sherd", "minecraft:howl_pottery_sherd", "minecraft:miner_pottery_sherd", "minecraft:mourner_pottery_sherd", "minecraft:plenty_pottery_sherd", "minecraft:prize_pottery_sherd", "minecraft:sheaf_pottery_sherd", "minecraft:shelter_pottery_sherd", "minecraft:skull_pottery_sherd", "minecraft:snort_pottery_sherd"]}, {"id": "tconstruct:scorched_tanks", "items": ["tconstruct:scorched_fuel_tank", "tconstruct:scorched_fuel_gauge", "tconstruct:scorched_ingot_tank", "tconstruct:scorched_ingot_gauge"]}, {"id": "forge:dusts/redstone", "items": ["minecraft:redstone"]}, {"id": "tconstruct:casts/single_use/gem", "items": ["tconstruct:gem_sand_cast", "tconstruct:gem_red_sand_cast"]}, {"id": "tconstruct:melting/steel/tools_costing_7", "items": ["immersiveengineering:armor_steel_leggings"]}, {"id": "forge:slimeball/ichor", "items": ["tconstruct:ichor_slime_ball"]}, {"id": "tconstruct:scorched_blocks", "items": ["tconstruct:scorched_stone", "tconstruct:polished_scorched_stone", "tconstruct:scorched_bricks", "tconstruct:scorched_road", "tconstruct:chiseled_scorched_bricks"]}, {"id": "forge:storage_blocks/cinderslime", "items": ["tconstruct:cinderslime_block"]}, {"id": "forge:tools/tridents", "items": ["cataclysm:coral_spear", "cataclysm:<PERSON>_bar<PERSON>he", "minecraft:trident"]}, {"id": "create:valve_handles", "items": ["create:copper_valve_handle", "create:white_valve_handle", "create:orange_valve_handle", "create:magenta_valve_handle", "create:light_blue_valve_handle", "create:yellow_valve_handle", "create:lime_valve_handle", "create:pink_valve_handle", "create:gray_valve_handle", "create:light_gray_valve_handle", "create:cyan_valve_handle", "create:purple_valve_handle", "create:blue_valve_handle", "create:brown_valve_handle", "create:green_valve_handle", "create:red_valve_handle", "create:black_valve_handle"]}, {"id": "forge:pellets/polonium", "items": ["mekanism:pellet_polonium"]}, {"id": "mekanism:shards/gold", "items": ["mekanism:shard_gold"]}, {"id": "forge:sandstone/colorless", "items": ["minecraft:sandstone", "minecraft:cut_sandstone", "minecraft:chiseled_sandstone", "minecraft:smooth_sandstone"]}, {"id": "minecraft:warped_stems", "items": ["minecraft:warped_stem", "minecraft:stripped_warped_stem", "minecraft:warped_hyphae", "minecraft:stripped_warped_hyphae"]}, {"id": "forge:glass/green", "items": ["minecraft:green_stained_glass", "tconstruct:green_clear_stained_glass"]}, {"id": "tconstruct:melting/electrum/tools_costing_2", "items": []}, {"id": "tconstruct:melting/copper/tools_costing_1", "items": ["minecraft:brush"]}, {"id": "minecraft:pickaxes", "items": ["minecraft:diamond_pickaxe", "minecraft:stone_pickaxe", "minecraft:golden_pickaxe", "minecraft:netherite_pickaxe", "minecraft:wooden_pickaxe", "minecraft:iron_pickaxe", "cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "ae2:certus_quartz_pickaxe", "ae2:nether_quartz_pickaxe", "ae2:fluix_pickaxe", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:pickadze", "tconstruct:war_pick"]}, {"id": "forge:dusts/charcoal", "items": ["mekanism:dust_charcoal"]}, {"id": "forge:storage_blocks/budding", "items": ["minecraft:budding_amethyst"]}, {"id": "mekanism:enriched/diamond", "items": ["mekanism:enriched_diamond"]}, {"id": "forge:raw_materials/uranium", "items": ["immersiveengineering:raw_uranium", "mekanism:raw_uranium"]}, {"id": "forge:dusts/obsidian", "items": ["create:powdered_obsidian", "mekanism:dust_obsidian"]}, {"id": "forge:silicon", "items": ["ae2:silicon"]}, {"id": "minecraft:candles", "items": ["minecraft:candle", "minecraft:white_candle", "minecraft:orange_candle", "minecraft:magenta_candle", "minecraft:light_blue_candle", "minecraft:yellow_candle", "minecraft:lime_candle", "minecraft:pink_candle", "minecraft:gray_candle", "minecraft:light_gray_candle", "minecraft:cyan_candle", "minecraft:purple_candle", "minecraft:blue_candle", "minecraft:brown_candle", "minecraft:green_candle", "minecraft:red_candle", "minecraft:black_candle"]}, {"id": "tconstruct:slimy_grass_seeds", "items": ["tconstruct:earth_slime_grass_seeds", "tconstruct:sky_slime_grass_seeds", "tconstruct:ichor_slime_grass_seeds", "tconstruct:ender_slime_grass_seeds", "tconstruct:blood_slime_grass_seeds"]}, {"id": "c:uncolored_sandstone_blocks", "items": ["minecraft:sandstone", "minecraft:chiseled_sandstone", "minecraft:cut_sandstone", "minecraft:smooth_sandstone"]}, {"id": "forge:basalt", "items": ["minecraft:basalt"]}, {"id": "forge:wires", "items": ["immersiveengineering:wire_copper", "immersiveengineering:wire_electrum", "immersiveengineering:wire_aluminum", "immersiveengineering:wire_steel", "immersiveengineering:wire_lead"]}, {"id": "forge:spears", "items": ["minecraft:trident"]}, {"id": "forge:storage_blocks/raw_zinc", "items": ["create:raw_zinc_block"]}, {"id": "balm:red_dyes", "items": ["minecraft:red_dye"]}, {"id": "c:purple_dyes", "items": ["minecraft:purple_dye"]}, {"id": "create:stone_types/galosphere/lumiere", "items": []}, {"id": "c:lava_buckets", "items": ["minecraft:lava_bucket"]}, {"id": "forge:nuggets/nickel", "items": ["immersiveengineering:nugget_nickel"]}, {"id": "forge:storage_blocks/certus_quartz", "items": ["ae2:quartz_block"]}, {"id": "tconstruct:melting/diamond/tools_costing_7", "items": ["minecraft:diamond_leggings"]}, {"id": "tconstruct:casts/single_use/small_axe_head", "items": ["tconstruct:small_axe_head_sand_cast", "tconstruct:small_axe_head_red_sand_cast"]}, {"id": "forge:seeds/beetroot", "items": ["minecraft:beetroot_seeds"]}, {"id": "forge:nuggets/cinderslime", "items": ["tconstruct:cinderslime_nugget"]}, {"id": "forge:glass_panes/purple", "items": ["minecraft:purple_stained_glass_pane", "tconstruct:purple_clear_stained_glass_pane"]}, {"id": "minecraft:mangrove_logs", "items": ["minecraft:mangrove_log", "minecraft:mangrove_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood"]}, {"id": "c:swords", "items": ["cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "minecraft:diamond_sword", "minecraft:golden_sword", "minecraft:wooden_sword", "minecraft:stone_sword", "minecraft:iron_sword", "minecraft:netherite_sword"]}, {"id": "tconstruct:melting/silver/tools_costing_1", "items": []}, {"id": "forge:dusts/fluorite", "items": ["mekanism:dust_fluorite"]}, {"id": "tconstruct:duct_containers", "items": ["minecraft:bucket", "tconstruct:copper_can", "tconstruct:seared_lantern", "tconstruct:scorched_lantern"]}, {"id": "forge:nuggets/knightslime", "items": ["tconstruct:<PERSON><PERSON>e_nugget"]}, {"id": "forge:plates/silver", "items": ["immersiveengineering:plate_silver"]}, {"id": "create:create_ingots", "items": ["create:andesite_alloy", "create:zinc_ingot", "create:brass_ingot"]}, {"id": "forge:ores_in_ground/netherrack", "items": ["minecraft:nether_gold_ore", "minecraft:nether_quartz_ore", "tconstruct:cobalt_ore"]}, {"id": "forge:storage_blocks/raw_silver", "items": ["immersiveengineering:raw_block_silver"]}, {"id": "forge:storage_blocks/manyullyn", "items": ["tconstruct:manyullyn_block"]}, {"id": "tconstruct:melting/bronze/tools_costing_3", "items": []}, {"id": "forge:sites/villager_job", "items": ["minecraft:barrel", "minecraft:blast_furnace", "minecraft:brewing_stand", "minecraft:cartography_table", "minecraft:cauldron", "minecraft:composter", "minecraft:fletching_table", "minecraft:grindstone", "minecraft:lectern", "minecraft:loom", "minecraft:smithing_table", "minecraft:smoker", "minecraft:stonecutter"]}, {"id": "tconstruct:modifiable/small", "items": ["tconstruct:pickaxe", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:hand_axe", "tconstruct:kama", "tconstruct:dagger", "tconstruct:sword", "tconstruct:crossbow", "tconstruct:flint_and_brick"]}, {"id": "forge:tools/swords", "items": ["cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "minecraft:diamond_sword", "minecraft:golden_sword", "minecraft:wooden_sword", "minecraft:stone_sword", "minecraft:iron_sword", "minecraft:netherite_sword"]}, {"id": "minecraft:fences", "items": ["minecraft:oak_fence", "minecraft:acacia_fence", "minecraft:dark_oak_fence", "minecraft:spruce_fence", "minecraft:birch_fence", "minecraft:jungle_fence", "minecraft:crimson_fence", "minecraft:warped_fence", "minecraft:mangrove_fence", "minecraft:bamboo_fence", "minecraft:cherry_fence", "cataclysm:chorus_fence", "tconstruct:greenheart_fence", "tconstruct:skyroot_fence", "tconstruct:bloodshroom_fence", "tconstruct:enderbark_fence", "minecraft:nether_brick_fence"]}, {"id": "forge:plates/constantan", "items": ["immersiveengineering:plate_constantan"]}, {"id": "forge:dyes/lime", "items": ["minecraft:lime_dye"]}, {"id": "ae2:interface", "items": ["ae2:cable_interface", "ae2:interface"]}, {"id": "minecraft:rails", "items": ["minecraft:rail", "minecraft:powered_rail", "minecraft:detector_rail", "minecraft:activator_rail"]}, {"id": "forge:ores/coal", "items": ["minecraft:coal_ore", "minecraft:deepslate_coal_ore"]}, {"id": "forge:sand/colorless", "items": ["minecraft:sand"]}, {"id": "forge:cobblestone/mossy", "items": ["minecraft:mossy_cobblestone"]}, {"id": "create:runtime_generated/compat/cataclysm/chorus", "items": ["cataclysm:chorus_stem"]}, {"id": "mekanism:dirty_dusts/tin", "items": ["mekanism:dirty_dust_tin"]}, {"id": "forge:blackstone", "items": ["minecraft:blackstone"]}, {"id": "forge:stripped_logs", "items": ["minecraft:stripped_acacia_log", "minecraft:stripped_birch_log", "minecraft:stripped_crimson_stem", "minecraft:stripped_dark_oak_log", "minecraft:stripped_jungle_log", "minecraft:stripped_mangrove_log", "minecraft:stripped_oak_log", "minecraft:stripped_spruce_log", "minecraft:stripped_warped_stem", "minecraft:stripped_cherry_log", "minecraft:stripped_bamboo_block", "tconstruct:stripped_greenheart_log", "tconstruct:stripped_skyroot_log", "tconstruct:stripped_bloodshroom_log"]}, {"id": "forge:storage_blocks/raw_lead", "items": ["immersiveengineering:raw_block_lead", "mekanism:block_raw_lead"]}, {"id": "forge:circuits/ultimate", "items": ["mekanism:ultimate_control_circuit"]}, {"id": "forge:normal_stone", "items": ["minecraft:stone", "minecraft:cobblestone", "minecraft:mossy_cobblestone"]}, {"id": "forge:andesite", "items": ["minecraft:andesite"]}, {"id": "forge:storage_blocks/andesite_alloy", "items": ["create:andesite_alloy_block"]}, {"id": "forge:buckets/entity_water", "items": ["minecraft:axolotl_bucket", "minecraft:cod_bucket", "minecraft:pufferfish_bucket", "minecraft:tropical_fish_bucket", "minecraft:salmon_bucket", "minecraft:tadpole_bucket"]}, {"id": "tconstruct:melting/tin/tools_costing_2", "items": []}, {"id": "create:crushed_raw_materials", "items": ["create:crushed_raw_iron", "create:crushed_raw_gold", "create:crushed_raw_copper", "create:crushed_raw_zinc", "create:crushed_raw_osmium", "create:crushed_raw_platinum", "create:crushed_raw_silver", "create:crushed_raw_tin", "create:crushed_raw_lead", "create:crushed_raw_quicksilver", "create:crushed_raw_aluminum", "create:crushed_raw_uranium", "create:crushed_raw_nickel"]}, {"id": "tconstruct:melting/nickel/tools_costing_2", "items": []}, {"id": "forge:gravel", "items": ["minecraft:gravel", "immersiveengineering:slag_gravel"]}, {"id": "forge:ingots/constantan", "items": ["immersiveengineering:ingot_constantan"]}, {"id": "forge:storage_blocks/emerald", "items": ["minecraft:emerald_block"]}, {"id": "tconstruct:modifiable/durability", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:slime_helmet", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "immersiveengineering:tools/hammers", "items": ["immersiveengineering:hammer"]}, {"id": "forge:dusts/lithium", "items": ["mekanism:dust_lithium"]}, {"id": "tconstruct:casts/single_use/tool_binding", "items": ["tconstruct:tool_binding_sand_cast", "tconstruct:tool_binding_red_sand_cast"]}, {"id": "tconstruct:melting/bronze/tools_costing_2", "items": []}, {"id": "mekanism:enriched/obsidian", "items": ["mekanism:enriched_refined_obsidian"]}, {"id": "balm:stones", "items": ["minecraft:andesite", "minecraft:diorite", "minecraft:granite", "minecraft:infested_stone", "minecraft:stone", "minecraft:polished_andesite", "minecraft:polished_diorite", "minecraft:polished_granite", "minecraft:deepslate", "minecraft:polished_deepslate", "minecraft:infested_deepslate", "minecraft:tuff"]}, {"id": "tconstruct:melting/steel/tools_costing_4", "items": ["immersiveengineering:armor_steel_boots"]}, {"id": "minecraft:fence_gates", "items": ["minecraft:acacia_fence_gate", "minecraft:birch_fence_gate", "minecraft:dark_oak_fence_gate", "minecraft:jungle_fence_gate", "minecraft:oak_fence_gate", "minecraft:spruce_fence_gate", "minecraft:crimson_fence_gate", "minecraft:warped_fence_gate", "minecraft:mangrove_fence_gate", "minecraft:bamboo_fence_gate", "minecraft:cherry_fence_gate", "tconstruct:greenheart_fence_gate", "tconstruct:skyroot_fence_gate", "tconstruct:bloodshroom_fence_gate", "tconstruct:enderbark_fence_gate"]}, {"id": "c:sandstone_stairs", "items": ["minecraft:sandstone_stairs", "minecraft:smooth_sandstone_stairs", "minecraft:red_sandstone_stairs", "minecraft:smooth_red_sandstone_stairs"]}, {"id": "forge:ingots/nether_brick", "items": ["minecraft:nether_brick"]}, {"id": "tconstruct:modifiable/unrecyclable", "items": ["tconstruct:plate_shield", "tconstruct:dagger", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher"]}, {"id": "forge:storage_blocks/slimesteel", "items": ["tconstruct:slimesteel_block"]}, {"id": "forge:fiber_hemp", "items": ["immersiveengineering:hemp_fiber"]}, {"id": "forge:wither_bones", "items": ["tconstruct:necrotic_bone"]}, {"id": "create:stone_types/diorite", "items": ["create:cut_diorite", "create:cut_diorite_stairs", "create:cut_diorite_wall", "create:polished_cut_diorite", "create:polished_cut_diorite_stairs", "create:polished_cut_diorite_wall", "create:cut_diorite_bricks", "create:cut_diorite_brick_stairs", "create:cut_diorite_brick_wall", "create:small_diorite_bricks", "create:small_diorite_brick_stairs", "create:small_diorite_brick_wall", "create:layered_diorite", "create:diorite_pillar", "minecraft:diorite"]}, {"id": "mekanism:mekasuit_hud_renderer", "items": ["mekanism:mekasuit_helmet"]}, {"id": "tconstruct:casts/single_use/pick_head", "items": ["tconstruct:pick_head_sand_cast", "tconstruct:pick_head_red_sand_cast"]}, {"id": "forge:glass_panes/green", "items": ["minecraft:green_stained_glass_pane", "tconstruct:green_clear_stained_glass_pane"]}, {"id": "ae2:glass_cable", "items": ["ae2:white_glass_cable", "ae2:orange_glass_cable", "ae2:magenta_glass_cable", "ae2:light_blue_glass_cable", "ae2:yellow_glass_cable", "ae2:lime_glass_cable", "ae2:pink_glass_cable", "ae2:gray_glass_cable", "ae2:light_gray_glass_cable", "ae2:cyan_glass_cable", "ae2:purple_glass_cable", "ae2:blue_glass_cable", "ae2:brown_glass_cable", "ae2:green_glass_cable", "ae2:red_glass_cable", "ae2:black_glass_cable", "ae2:fluix_glass_cable"]}, {"id": "forge:stairs/uncolored_sandstone", "items": ["minecraft:sandstone_stairs", "minecraft:smooth_sandstone_stairs"]}, {"id": "forge:storage_blocks/uranium", "items": ["immersiveengineering:storage_uranium", "mekanism:block_uranium"]}, {"id": "forge:sheetmetals/steel", "items": ["immersiveengineering:sheetmetal_steel"]}, {"id": "forge:alloys", "items": ["mekanism:alloy_infused", "mekanism:alloy_reinforced", "mekanism:alloy_atomic"]}, {"id": "forge:ingots/knightslime", "items": ["tconstruct:<PERSON><PERSON><PERSON>_ingot"]}, {"id": "forge:circuits/advanced", "items": ["mekanism:advanced_control_circuit"]}, {"id": "minecraft:stone_tool_materials", "items": ["minecraft:cobblestone", "minecraft:blackstone", "minecraft:cobbled_deepslate"]}, {"id": "forge:nuggets/bronze", "items": ["mekanism:nugget_bronze"]}, {"id": "forge:plates/lead", "items": ["immersiveengineering:plate_lead"]}, {"id": "tconstruct:casts", "items": ["tconstruct:ingot_cast", "tconstruct:nugget_cast", "tconstruct:gem_cast", "tconstruct:rod_cast", "tconstruct:repair_kit_cast", "tconstruct:plate_cast", "tconstruct:gear_cast", "tconstruct:coin_cast", "tconstruct:wire_cast", "tconstruct:pick_head_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_blade_cast", "tconstruct:adze_head_cast", "tconstruct:hammer_head_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_blade_cast", "tconstruct:large_plate_cast", "tconstruct:tool_binding_cast", "tconstruct:tough_binding_cast", "tconstruct:tool_handle_cast", "tconstruct:tough_handle_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_grip_cast", "tconstruct:helmet_plating_cast", "tconstruct:chestplate_plating_cast", "tconstruct:leggings_plating_cast", "tconstruct:boots_plating_cast", "tconstruct:maille_cast", "tconstruct:blank_sand_cast", "tconstruct:ingot_sand_cast", "tconstruct:nugget_sand_cast", "tconstruct:gem_sand_cast", "tconstruct:rod_sand_cast", "tconstruct:repair_kit_sand_cast", "tconstruct:plate_sand_cast", "tconstruct:gear_sand_cast", "tconstruct:coin_sand_cast", "tconstruct:wire_sand_cast", "tconstruct:pick_head_sand_cast", "tconstruct:small_axe_head_sand_cast", "tconstruct:small_blade_sand_cast", "tconstruct:adze_head_sand_cast", "tconstruct:hammer_head_sand_cast", "tconstruct:broad_axe_head_sand_cast", "tconstruct:broad_blade_sand_cast", "tconstruct:large_plate_sand_cast", "tconstruct:tool_binding_sand_cast", "tconstruct:tough_binding_sand_cast", "tconstruct:tool_handle_sand_cast", "tconstruct:tough_handle_sand_cast", "tconstruct:bow_limb_sand_cast", "tconstruct:bow_grip_sand_cast", "tconstruct:helmet_plating_sand_cast", "tconstruct:chestplate_plating_sand_cast", "tconstruct:leggings_plating_sand_cast", "tconstruct:boots_plating_sand_cast", "tconstruct:maille_sand_cast", "tconstruct:blank_red_sand_cast", "tconstruct:ingot_red_sand_cast", "tconstruct:nugget_red_sand_cast", "tconstruct:gem_red_sand_cast", "tconstruct:rod_red_sand_cast", "tconstruct:repair_kit_red_sand_cast", "tconstruct:plate_red_sand_cast", "tconstruct:gear_red_sand_cast", "tconstruct:coin_red_sand_cast", "tconstruct:wire_red_sand_cast", "tconstruct:pick_head_red_sand_cast", "tconstruct:small_axe_head_red_sand_cast", "tconstruct:small_blade_red_sand_cast", "tconstruct:adze_head_red_sand_cast", "tconstruct:hammer_head_red_sand_cast", "tconstruct:broad_axe_head_red_sand_cast", "tconstruct:broad_blade_red_sand_cast", "tconstruct:large_plate_red_sand_cast", "tconstruct:tool_binding_red_sand_cast", "tconstruct:tough_binding_red_sand_cast", "tconstruct:tool_handle_red_sand_cast", "tconstruct:tough_handle_red_sand_cast", "tconstruct:bow_limb_red_sand_cast", "tconstruct:bow_grip_red_sand_cast", "tconstruct:helmet_plating_red_sand_cast", "tconstruct:chestplate_plating_red_sand_cast", "tconstruct:leggings_plating_red_sand_cast", "tconstruct:boots_plating_red_sand_cast", "tconstruct:maille_red_sand_cast", "tconstruct:gold_bars", "tconstruct:gold_platform"]}, {"id": "forge:ingots/pig_iron", "items": ["tconstruct:pig_iron_ingot"]}, {"id": "forge:sheetmetals", "items": ["immersiveengineering:sheetmetal_copper", "immersiveengineering:sheetmetal_aluminum", "immersiveengineering:sheetmetal_lead", "immersiveengineering:sheetmetal_silver", "immersiveengineering:sheetmetal_nickel", "immersiveengineering:sheetmetal_uranium", "immersiveengineering:sheetmetal_constantan", "immersiveengineering:sheetmetal_electrum", "immersiveengineering:sheetmetal_steel", "immersiveengineering:sheetmetal_iron", "immersiveengineering:sheetmetal_gold", "immersiveengineering:sheetmetal_colored_white", "immersiveengineering:sheetmetal_colored_orange", "immersiveengineering:sheetmetal_colored_magenta", "immersiveengineering:sheetmetal_colored_light_blue", "immersiveengineering:sheetmetal_colored_yellow", "immersiveengineering:sheetmetal_colored_lime", "immersiveengineering:sheetmetal_colored_pink", "immersiveengineering:sheetmetal_colored_gray", "immersiveengineering:sheetmetal_colored_light_gray", "immersiveengineering:sheetmetal_colored_cyan", "immersiveengineering:sheetmetal_colored_purple", "immersiveengineering:sheetmetal_colored_blue", "immersiveengineering:sheetmetal_colored_brown", "immersiveengineering:sheetmetal_colored_green", "immersiveengineering:sheetmetal_colored_red", "immersiveengineering:sheetmetal_colored_black"]}, {"id": "minecraft:shovels", "items": ["minecraft:diamond_shovel", "minecraft:stone_shovel", "minecraft:golden_shovel", "minecraft:netherite_shovel", "minecraft:wooden_shovel", "minecraft:iron_shovel", "immersiveengineering:shovel_steel", "ae2:certus_quartz_shovel", "ae2:nether_quartz_shovel", "ae2:fluix_shovel", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator"]}, {"id": "forge:storage_blocks/glass", "items": ["minecraft:glass", "minecraft:gray_stained_glass", "minecraft:black_stained_glass", "minecraft:orange_stained_glass", "minecraft:blue_stained_glass", "minecraft:brown_stained_glass", "minecraft:cyan_stained_glass", "minecraft:green_stained_glass", "minecraft:light_blue_stained_glass", "minecraft:light_gray_stained_glass", "minecraft:lime_stained_glass", "minecraft:magenta_stained_glass", "minecraft:pink_stained_glass", "minecraft:purple_stained_glass", "minecraft:red_stained_glass", "minecraft:tinted_glass", "minecraft:white_stained_glass", "minecraft:yellow_stained_glass"]}, {"id": "c:shears", "items": ["minecraft:shears", "tconstruct:kama"]}, {"id": "forge:tools/hoes", "items": ["immersiveengineering:hoe_steel", "minecraft:diamond_hoe", "minecraft:golden_hoe", "minecraft:wooden_hoe", "minecraft:stone_hoe", "minecraft:iron_hoe", "minecraft:netherite_hoe"]}, {"id": "create:modded_stripped_logs", "items": ["tconstruct:stripped_greenheart_log", "tconstruct:stripped_skyroot_log", "tconstruct:stripped_bloodshroom_log"]}, {"id": "forge:glass/white", "items": ["minecraft:white_stained_glass", "tconstruct:white_clear_stained_glass"]}, {"id": "minecraft:acacia_logs", "items": ["minecraft:acacia_log", "minecraft:acacia_wood", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood"]}, {"id": "forge:ore_bearing_ground/stone", "items": ["minecraft:stone"]}, {"id": "tconstruct:casts/single_use", "items": ["tconstruct:blank_sand_cast", "tconstruct:blank_red_sand_cast", "tconstruct:ingot_sand_cast", "tconstruct:ingot_red_sand_cast", "tconstruct:nugget_sand_cast", "tconstruct:nugget_red_sand_cast", "tconstruct:gem_sand_cast", "tconstruct:gem_red_sand_cast", "tconstruct:rod_sand_cast", "tconstruct:rod_red_sand_cast", "tconstruct:repair_kit_sand_cast", "tconstruct:repair_kit_red_sand_cast", "tconstruct:plate_sand_cast", "tconstruct:plate_red_sand_cast", "tconstruct:gear_sand_cast", "tconstruct:gear_red_sand_cast", "tconstruct:coin_sand_cast", "tconstruct:coin_red_sand_cast", "tconstruct:wire_sand_cast", "tconstruct:wire_red_sand_cast", "tconstruct:pick_head_sand_cast", "tconstruct:pick_head_red_sand_cast", "tconstruct:small_axe_head_sand_cast", "tconstruct:small_axe_head_red_sand_cast", "tconstruct:small_blade_sand_cast", "tconstruct:small_blade_red_sand_cast", "tconstruct:adze_head_sand_cast", "tconstruct:adze_head_red_sand_cast", "tconstruct:hammer_head_sand_cast", "tconstruct:hammer_head_red_sand_cast", "tconstruct:broad_axe_head_sand_cast", "tconstruct:broad_axe_head_red_sand_cast", "tconstruct:broad_blade_sand_cast", "tconstruct:broad_blade_red_sand_cast", "tconstruct:large_plate_sand_cast", "tconstruct:large_plate_red_sand_cast", "tconstruct:tool_binding_sand_cast", "tconstruct:tool_binding_red_sand_cast", "tconstruct:tough_binding_sand_cast", "tconstruct:tough_binding_red_sand_cast", "tconstruct:tool_handle_sand_cast", "tconstruct:tool_handle_red_sand_cast", "tconstruct:tough_handle_sand_cast", "tconstruct:tough_handle_red_sand_cast", "tconstruct:bow_limb_sand_cast", "tconstruct:bow_limb_red_sand_cast", "tconstruct:bow_grip_sand_cast", "tconstruct:bow_grip_red_sand_cast", "tconstruct:helmet_plating_sand_cast", "tconstruct:helmet_plating_red_sand_cast", "tconstruct:chestplate_plating_sand_cast", "tconstruct:chestplate_plating_red_sand_cast", "tconstruct:leggings_plating_sand_cast", "tconstruct:leggings_plating_red_sand_cast", "tconstruct:boots_plating_sand_cast", "tconstruct:boots_plating_red_sand_cast", "tconstruct:maille_sand_cast", "tconstruct:maille_red_sand_cast"]}, {"id": "forge:nuggets/copper", "items": ["immersiveengineering:nugget_copper", "create:copper_nugget", "tconstruct:copper_nugget"]}, {"id": "c:gray_dyes", "items": ["minecraft:gray_dye"]}, {"id": "forge:plates/aluminum", "items": ["immersiveengineering:plate_aluminum"]}, {"id": "balm:blue_dyes", "items": ["minecraft:blue_dye"]}, {"id": "forge:storage_blocks/knightslime", "items": ["tconstruct:knightslime_block"]}, {"id": "forge:gems/certus_quartz", "items": ["ae2:certus_quartz_crystal", "ae2:charged_certus_quartz_crystal"]}, {"id": "forge:barrels/wooden", "items": ["minecraft:barrel"]}, {"id": "forge:storage_blocks/amethyst", "items": ["minecraft:amethyst_block"]}, {"id": "balm:cyan_dyes", "items": ["minecraft:cyan_dye"]}, {"id": "forge:ores/iron", "items": ["minecraft:iron_ore", "minecraft:deepslate_iron_ore"]}, {"id": "forge:storage_blocks/amethyst_bronze", "items": ["tconstruct:amethyst_bronze_block"]}, {"id": "forge:dusts/constantan", "items": ["immersiveengineering:dust_constantan"]}, {"id": "forge:glass_panes/silica", "items": ["minecraft:glass_pane", "tconstruct:clear_glass_pane", "minecraft:black_stained_glass_pane", "minecraft:blue_stained_glass_pane", "minecraft:brown_stained_glass_pane", "minecraft:cyan_stained_glass_pane", "minecraft:gray_stained_glass_pane", "minecraft:green_stained_glass_pane", "minecraft:light_blue_stained_glass_pane", "minecraft:light_gray_stained_glass_pane", "minecraft:lime_stained_glass_pane", "minecraft:magenta_stained_glass_pane", "minecraft:orange_stained_glass_pane", "minecraft:pink_stained_glass_pane", "minecraft:purple_stained_glass_pane", "minecraft:red_stained_glass_pane", "minecraft:white_stained_glass_pane", "minecraft:yellow_stained_glass_pane", "tconstruct:white_clear_stained_glass_pane", "tconstruct:orange_clear_stained_glass_pane", "tconstruct:magenta_clear_stained_glass_pane", "tconstruct:light_blue_clear_stained_glass_pane", "tconstruct:yellow_clear_stained_glass_pane", "tconstruct:lime_clear_stained_glass_pane", "tconstruct:pink_clear_stained_glass_pane", "tconstruct:gray_clear_stained_glass_pane", "tconstruct:light_gray_clear_stained_glass_pane", "tconstruct:cyan_clear_stained_glass_pane", "tconstruct:purple_clear_stained_glass_pane", "tconstruct:blue_clear_stained_glass_pane", "tconstruct:brown_clear_stained_glass_pane", "tconstruct:green_clear_stained_glass_pane", "tconstruct:red_clear_stained_glass_pane", "tconstruct:black_clear_stained_glass_pane"]}, {"id": "balm:iron_ingots", "items": ["minecraft:iron_ingot"]}, {"id": "forge:ingots/cinderslime", "items": ["tconstruct:cinderslime_ingot"]}, {"id": "forge:storage_blocks/steel", "items": ["immersiveengineering:storage_steel", "mekanism:block_steel", "tconstruct:steel_block"]}, {"id": "minecraft:trimmable_armor", "items": ["minecraft:netherite_helmet", "minecraft:netherite_chestplate", "minecraft:netherite_leggings", "minecraft:netherite_boots", "minecraft:diamond_helmet", "minecraft:diamond_chestplate", "minecraft:diamond_leggings", "minecraft:diamond_boots", "minecraft:golden_helmet", "minecraft:golden_chestplate", "minecraft:golden_leggings", "minecraft:golden_boots", "minecraft:iron_helmet", "minecraft:iron_chestplate", "minecraft:iron_leggings", "minecraft:iron_boots", "minecraft:chainmail_helmet", "minecraft:chainmail_chestplate", "minecraft:chainmail_leggings", "minecraft:chainmail_boots", "minecraft:leather_helmet", "minecraft:leather_chestplate", "minecraft:leather_leggings", "minecraft:leather_boots", "minecraft:turtle_helmet", "immersiveengineering:armor_steel_helmet", "immersiveengineering:armor_steel_chestplate", "immersiveengineering:armor_steel_leggings", "immersiveengineering:armor_steel_boots", "create:cardboard_helmet", "create:cardboard_chestplate", "create:cardboard_leggings", "create:cardboard_boots"]}, {"id": "ae2:quartz_hoe", "items": ["ae2:certus_quartz_hoe", "ae2:nether_quartz_hoe"]}, {"id": "tconstruct:casts/multi_use/tool_binding", "items": ["tconstruct:tool_binding_cast"]}, {"id": "forge:ingots/queens_slime", "items": ["tconstruct:queens_slime_ingot"]}, {"id": "forge:dyes/red", "items": ["minecraft:red_dye"]}, {"id": "forge:dyes/light_blue", "items": ["minecraft:light_blue_dye"]}, {"id": "tconstruct:modifiable/broad", "items": ["tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:excavator", "tconstruct:broad_axe", "tconstruct:scythe", "tconstruct:cleaver", "tconstruct:longbow"]}, {"id": "minecraft:dark_oak_logs", "items": ["minecraft:dark_oak_log", "minecraft:dark_oak_wood", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood"]}, {"id": "forge:dusts/copper", "items": ["immersiveengineering:dust_copper", "mekanism:dust_copper"]}, {"id": "tconstruct:melting/copper/tools_costing_3", "items": []}, {"id": "immersiveengineering:recycling/ignored_components", "items": ["minecraft:brick", "immersiveengineering:hammer", "immersiveengineering:screwdriver", "immersiveengineering:wirecutter"]}, {"id": "tconstruct:slimy_saplings", "items": ["tconstruct:earth_slime_sapling", "tconstruct:sky_slime_sapling", "tconstruct:ender_slime_sapling"]}, {"id": "tconstruct:slimy_planks", "items": ["tconstruct:greenheart_planks", "tconstruct:skyroot_planks", "tconstruct:bloodshroom_planks", "tconstruct:enderbark_planks"]}, {"id": "forge:sawblades", "items": ["immersiveengineering:sawblade"]}, {"id": "forge:rods/iron", "items": ["immersiveengineering:stick_iron"]}, {"id": "c:shulker_boxes", "items": ["minecraft:shulker_box", "minecraft:blue_shulker_box", "minecraft:brown_shulker_box", "minecraft:cyan_shulker_box", "minecraft:gray_shulker_box", "minecraft:green_shulker_box", "minecraft:light_blue_shulker_box", "minecraft:light_gray_shulker_box", "minecraft:lime_shulker_box", "minecraft:magenta_shulker_box", "minecraft:orange_shulker_box", "minecraft:pink_shulker_box", "minecraft:purple_shulker_box", "minecraft:red_shulker_box", "minecraft:white_shulker_box", "minecraft:yellow_shulker_box", "minecraft:black_shulker_box"]}, {"id": "tconstruct:modifiable/multipart", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:slime_helmet", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "tconstruct:casts/single_use/wire", "items": ["tconstruct:wire_sand_cast", "tconstruct:wire_red_sand_cast"]}, {"id": "forge:ores/netherite_scrap", "items": ["minecraft:ancient_debris"]}, {"id": "tconstruct:modifiable/book_armor/fantastic_foundry", "items": []}, {"id": "forge:nuggets/iron", "items": ["minecraft:iron_nugget"]}, {"id": "forge:nuggets/amethyst_bronze", "items": ["tconstruct:amethyst_bronze_nugget"]}, {"id": "minecraft:logs", "items": ["minecraft:dark_oak_log", "minecraft:dark_oak_wood", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood", "minecraft:oak_log", "minecraft:oak_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood", "minecraft:acacia_log", "minecraft:acacia_wood", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood", "minecraft:birch_log", "minecraft:birch_wood", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood", "minecraft:jungle_log", "minecraft:jungle_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood", "minecraft:spruce_log", "minecraft:spruce_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood", "minecraft:mangrove_log", "minecraft:mangrove_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood", "minecraft:cherry_log", "minecraft:cherry_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood", "minecraft:crimson_stem", "minecraft:stripped_crimson_stem", "minecraft:crimson_hyphae", "minecraft:stripped_crimson_hyphae", "minecraft:warped_stem", "minecraft:stripped_warped_stem", "minecraft:warped_hyphae", "minecraft:stripped_warped_hyphae", "tconstruct:greenheart_log", "tconstruct:stripped_greenheart_log", "tconstruct:greenheart_wood", "tconstruct:stripped_greenheart_wood", "tconstruct:skyroot_log", "tconstruct:stripped_skyroot_log", "tconstruct:skyroot_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:bloodshroom_log", "tconstruct:stripped_bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:stripped_bloodshroom_wood", "tconstruct:enderbark_log", "tconstruct:stripped_enderbark_log", "tconstruct:enderbark_wood", "tconstruct:stripped_enderbark_wood"]}, {"id": "minecraft:emerald_ores", "items": ["minecraft:emerald_ore", "minecraft:deepslate_emerald_ore"]}, {"id": "tconstruct:melting/silver/tools_costing_3", "items": []}, {"id": "tconstruct:casts/multi_use/small_axe_head", "items": ["tconstruct:small_axe_head_cast"]}, {"id": "forge:dusts/gold", "items": ["immersiveengineering:dust_gold", "mekanism:dust_gold"]}, {"id": "forge:batteries", "items": ["mekanism:energy_tablet"]}, {"id": "forge:glass/yellow", "items": ["minecraft:yellow_stained_glass", "tconstruct:yellow_clear_stained_glass"]}, {"id": "forge:plates/electrum", "items": ["immersiveengineering:plate_electrum"]}, {"id": "tconstruct:casts/sand", "items": ["tconstruct:blank_sand_cast", "tconstruct:ingot_sand_cast", "tconstruct:nugget_sand_cast", "tconstruct:gem_sand_cast", "tconstruct:rod_sand_cast", "tconstruct:repair_kit_sand_cast", "tconstruct:plate_sand_cast", "tconstruct:gear_sand_cast", "tconstruct:coin_sand_cast", "tconstruct:wire_sand_cast", "tconstruct:pick_head_sand_cast", "tconstruct:small_axe_head_sand_cast", "tconstruct:small_blade_sand_cast", "tconstruct:adze_head_sand_cast", "tconstruct:hammer_head_sand_cast", "tconstruct:broad_axe_head_sand_cast", "tconstruct:broad_blade_sand_cast", "tconstruct:large_plate_sand_cast", "tconstruct:tool_binding_sand_cast", "tconstruct:tough_binding_sand_cast", "tconstruct:tool_handle_sand_cast", "tconstruct:tough_handle_sand_cast", "tconstruct:bow_limb_sand_cast", "tconstruct:bow_grip_sand_cast", "tconstruct:helmet_plating_sand_cast", "tconstruct:chestplate_plating_sand_cast", "tconstruct:leggings_plating_sand_cast", "tconstruct:boots_plating_sand_cast", "tconstruct:maille_sand_cast"]}, {"id": "mekanism:clumps", "items": ["mekanism:clump_iron", "mekanism:clump_gold", "mekanism:clump_osmium", "mekanism:clump_copper", "mekanism:clump_tin", "mekanism:clump_lead", "mekanism:clump_uranium"]}, {"id": "forge:storage_blocks/pig_iron", "items": ["tconstruct:pig_iron_block"]}, {"id": "c:bows", "items": ["cataclysm:cursed_bow", "minecraft:bow", "mekanism:electric_bow", "tconstruct:longbow", "minecraft:crossbow"]}, {"id": "tconstruct:modifiable/armor/boots", "items": ["tconstruct:travelers_boots", "tconstruct:plate_boots", "tconstruct:slime_boots"]}, {"id": "forge:fuels/bio", "items": ["mekanism:bio_fuel"]}, {"id": "mekanism:shards/osmium", "items": ["mekanism:shard_osmium"]}, {"id": "c:copper_ingots", "items": ["minecraft:copper_ingot"]}, {"id": "c:raw_iron_blocks", "items": ["minecraft:raw_iron_block"]}, {"id": "mekanism:enriched/tin", "items": ["mekanism:enriched_tin"]}, {"id": "c:pink_dyes", "items": ["minecraft:pink_dye"]}, {"id": "forge:flour", "items": ["create:wheat_flour"]}, {"id": "curios:back", "items": ["immersiveengineering:powerpack"]}, {"id": "tconstruct:melting/invar/tools_costing_2", "items": []}, {"id": "tconstruct:modifiable/interactable/left", "items": ["tconstruct:crossbow", "tconstruct:longbow", "tconstruct:travelers_shield", "tconstruct:plate_shield", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan"]}, {"id": "mekanism:alloys", "items": ["minecraft:redstone", "mekanism:alloy_infused", "mekanism:alloy_reinforced", "mekanism:alloy_atomic"]}, {"id": "forge:storage_blocks/red_sandstone", "items": ["minecraft:red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:cut_red_sandstone", "minecraft:smooth_red_sandstone"]}, {"id": "tconstruct:wood_variants/planks", "items": ["minecraft:crimson_planks", "minecraft:warped_planks", "tconstruct:greenheart_planks", "tconstruct:skyroot_planks", "tconstruct:bloodshroom_planks", "tconstruct:enderbark_planks"]}, {"id": "tconstruct:melting/constantan/tools_costing_1", "items": []}, {"id": "immersiveengineering:toolbox/tools", "items": ["immersiveengineering:railgun", "immersiveengineering:chemthrower", "immersiveengineering:revolver", "immersiveengineering:speedloader", "immersiveengineering:manual", "immersiveengineering:wirecutter", "immersiveengineering:buzzsaw", "immersiveengineering:drill", "immersiveengineering:hammer", "immersiveengineering:screwdriver", "immersiveengineering:survey_tools", "immersiveengineering:voltmeter", "immersiveengineering:earmuffs", "immersiveengineering:skyhook", "minecraft:diamond_sword", "minecraft:stone_sword", "minecraft:golden_sword", "minecraft:netherite_sword", "minecraft:wooden_sword", "minecraft:iron_sword", "cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "ae2:certus_quartz_sword", "ae2:nether_quartz_sword", "ae2:fluix_sword", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:swasher", "minecraft:diamond_axe", "minecraft:stone_axe", "minecraft:golden_axe", "minecraft:netherite_axe", "minecraft:wooden_axe", "minecraft:iron_axe", "immersiveengineering:axe_steel", "ae2:certus_quartz_axe", "ae2:nether_quartz_axe", "ae2:fluix_axe", "tconstruct:mattock", "tconstruct:hand_axe", "tconstruct:broad_axe", "minecraft:diamond_pickaxe", "minecraft:stone_pickaxe", "minecraft:golden_pickaxe", "minecraft:netherite_pickaxe", "minecraft:wooden_pickaxe", "minecraft:iron_pickaxe", "cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "ae2:certus_quartz_pickaxe", "ae2:nether_quartz_pickaxe", "ae2:fluix_pickaxe", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:pickadze", "tconstruct:war_pick", "minecraft:diamond_shovel", "minecraft:stone_shovel", "minecraft:golden_shovel", "minecraft:netherite_shovel", "minecraft:wooden_shovel", "minecraft:iron_shovel", "immersiveengineering:shovel_steel", "ae2:certus_quartz_shovel", "ae2:nether_quartz_shovel", "ae2:fluix_shovel", "tconstruct:excavator", "minecraft:diamond_hoe", "minecraft:stone_hoe", "minecraft:golden_hoe", "minecraft:netherite_hoe", "minecraft:wooden_hoe", "minecraft:iron_hoe", "immersiveengineering:hoe_steel", "ae2:certus_quartz_hoe", "ae2:nether_quartz_hoe", "ae2:fluix_hoe", "tconstruct:kama", "tconstruct:scythe", "cataclysm:bulwark_of_the_flame", "cataclysm:black_steel_targe", "cataclysm:azure_sea_shield", "minecraft:shield", "immersiveengineering:shield", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield", "cataclysm:cursed_bow", "minecraft:bow", "mekanism:electric_bow", "tconstruct:longbow", "minecraft:crossbow", "tconstruct:crossbow", "minecraft:fishing_rod", "cataclysm:coral_spear", "cataclysm:<PERSON>_bar<PERSON>he", "minecraft:trident", "create:wrench", "ae2:certus_quartz_wrench", "ae2:nether_quartz_wrench", "ae2:network_tool", "mekanism:configurator", "minecraft:spyglass", "minecraft:clock", "minecraft:compass", "minecraft:flint_and_steel", "minecraft:bucket"]}, {"id": "tconstruct:casts/multi_use/plate", "items": ["tconstruct:plate_cast"]}, {"id": "c:uncolored_sandstone_slabs", "items": ["minecraft:sandstone_slab", "minecraft:cut_sandstone_slab", "minecraft:smooth_sandstone_slab"]}, {"id": "create:upright_on_belt", "items": ["create:blaze_cake_base", "create:blaze_cake", "create:creative_blaze_cake", "create:builders_tea", "minecraft:glass_bottle", "minecraft:potion", "minecraft:splash_potion", "minecraft:lingering_potion", "minecraft:honey_bottle", "minecraft:cake", "minecraft:bowl", "minecraft:mushroom_stew", "minecraft:rabbit_stew", "minecraft:beetroot_soup", "minecraft:suspicious_stew"]}, {"id": "ae2:inscriber_presses", "items": ["ae2:calculation_processor_press", "ae2:engineering_processor_press", "ae2:logic_processor_press", "ae2:silicon_press"]}, {"id": "forge:chests/wooden", "items": ["minecraft:chest", "minecraft:trapped_chest"]}, {"id": "forge:glass_panes/pink", "items": ["minecraft:pink_stained_glass_pane", "tconstruct:pink_clear_stained_glass_pane"]}, {"id": "minecraft:jungle_logs", "items": ["minecraft:jungle_log", "minecraft:jungle_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood"]}, {"id": "tconstruct:casts/multi_use/boots_plating", "items": ["tconstruct:boots_plating_cast"]}, {"id": "forge:workbenches", "items": ["minecraft:crafting_table", "tconstruct:crafting_station"]}, {"id": "tconstruct:modifiable/ranged/longbows", "items": ["tconstruct:longbow"]}, {"id": "minecraft:chest_boats", "items": ["minecraft:oak_chest_boat", "minecraft:spruce_chest_boat", "minecraft:birch_chest_boat", "minecraft:jungle_chest_boat", "minecraft:acacia_chest_boat", "minecraft:dark_oak_chest_boat", "minecraft:mangrove_chest_boat", "minecraft:bamboo_chest_raft", "minecraft:cherry_chest_boat"]}, {"id": "forge:crops/nether_wart", "items": ["minecraft:nether_wart"]}, {"id": "balm:gold_nuggets", "items": ["minecraft:gold_nugget"]}, {"id": "tconstruct:casts/multi_use/coin", "items": ["tconstruct:coin_cast"]}, {"id": "forge:dusts/lead", "items": ["immersiveengineering:dust_lead", "mekanism:dust_lead"]}, {"id": "forge:ores_in_ground/stone", "items": ["minecraft:coal_ore", "minecraft:copper_ore", "minecraft:diamond_ore", "minecraft:emerald_ore", "minecraft:gold_ore", "minecraft:iron_ore", "minecraft:lapis_ore", "minecraft:redstone_ore", "immersiveengineering:ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:ore_silver", "immersiveengineering:ore_nickel", "immersiveengineering:ore_uranium", "create:zinc_ore", "mekanism:tin_ore", "mekanism:osmium_ore", "mekanism:uranium_ore", "mekanism:fluorite_ore", "mekanism:lead_ore"]}, {"id": "forge:ingots", "items": ["minecraft:brick", "minecraft:copper_ingot", "minecraft:gold_ingot", "minecraft:iron_ingot", "minecraft:netherite_ingot", "minecraft:nether_brick", "immersiveengineering:ingot_aluminum", "immersiveengineering:ingot_lead", "mekanism:ingot_lead", "immersiveengineering:ingot_silver", "immersiveengineering:ingot_nickel", "immersiveengineering:ingot_uranium", "mekanism:ingot_uranium", "immersiveengineering:ingot_constantan", "immersiveengineering:ingot_electrum", "immersiveengineering:ingot_steel", "mekanism:ingot_steel", "tconstruct:steel_ingot", "create:andesite_alloy", "create:zinc_ingot", "create:brass_ingot", "mekanism:ingot_osmium", "mekanism:ingot_tin", "mekanism:ingot_bronze", "mekanism:ingot_refined_glowstone", "mekanism:ingot_refined_obsidian", "tconstruct:seared_brick", "tconstruct:scorched_brick", "minecraft:netherite_scrap", "tconstruct:cobalt_ingot", "tconstruct:slimesteel_ingot", "tconstruct:amethyst_bronze_ingot", "tconstruct:rose_gold_ingot", "tconstruct:pig_iron_ingot", "tconstruct:cinderslime_ingot", "tconstruct:queens_slime_ingot", "tconstruct:manyullyn_ingot", "tconstruct:hepati<PERSON>_ingot", "tconstruct:soulsteel_ingot", "tconstruct:<PERSON><PERSON><PERSON>_ingot"]}, {"id": "forge:budding", "items": ["minecraft:budding_amethyst", "ae2:flawless_budding_quartz", "ae2:flawed_budding_quartz", "ae2:chipped_budding_quartz", "ae2:damaged_budding_quartz"]}, {"id": "ae2:all_nether_quartz", "items": ["minecraft:quartz"]}, {"id": "forge:sandstone/red", "items": ["minecraft:red_sandstone", "minecraft:cut_red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:smooth_red_sandstone"]}, {"id": "forge:salt", "items": ["mekanism:salt"]}, {"id": "forge:seeds/pumpkin", "items": ["minecraft:pumpkin_seeds"]}, {"id": "c:dyes", "items": ["minecraft:white_dye", "minecraft:orange_dye", "minecraft:magenta_dye", "minecraft:light_blue_dye", "minecraft:yellow_dye", "mekanism:dust_sulfur", "minecraft:lime_dye", "minecraft:pink_dye", "minecraft:gray_dye", "minecraft:light_gray_dye", "minecraft:cyan_dye", "minecraft:purple_dye", "minecraft:blue_dye", "minecraft:brown_dye", "minecraft:green_dye", "minecraft:red_dye", "minecraft:black_dye"]}, {"id": "forge:plates/uranium", "items": ["immersiveengineering:plate_uranium"]}, {"id": "c:white_dyes", "items": ["minecraft:white_dye"]}, {"id": "c:redstone_dusts", "items": ["minecraft:redstone"]}, {"id": "minecraft:bamboo_blocks", "items": ["minecraft:bamboo_block", "minecraft:stripped_bamboo_block"]}, {"id": "forge:glass/light_gray", "items": ["minecraft:light_gray_stained_glass", "tconstruct:light_gray_clear_stained_glass"]}, {"id": "forge:chests/ender", "items": ["minecraft:ender_chest"]}, {"id": "tconstruct:foundry", "items": ["tconstruct:scorched_stone", "tconstruct:polished_scorched_stone", "tconstruct:scorched_bricks", "tconstruct:scorched_road", "tconstruct:chiseled_scorched_bricks", "tconstruct:scorched_fuel_tank", "tconstruct:scorched_fuel_gauge", "tconstruct:scorched_ingot_tank", "tconstruct:scorched_ingot_gauge", "tconstruct:foundry_controller", "tconstruct:scorched_ladder", "tconstruct:scorched_drain", "tconstruct:scorched_chute", "tconstruct:scorched_duct", "tconstruct:scorched_glass", "tconstruct:scorched_soul_glass", "tconstruct:scorched_tinted_glass"]}, {"id": "c:dusts", "items": ["minecraft:glowstone_dust", "minecraft:prismarine_shard", "minecraft:redstone", "immersiveengineering:dust_copper", "mekanism:dust_copper", "immersiveengineering:dust_aluminum", "immersiveengineering:dust_lead", "mekanism:dust_lead", "immersiveengineering:dust_silver", "immersiveengineering:dust_nickel", "immersiveengineering:dust_uranium", "mekanism:dust_uranium", "immersiveengineering:dust_constantan", "immersiveengineering:dust_electrum", "immersiveengineering:dust_steel", "mekanism:dust_steel", "immersiveengineering:dust_iron", "mekanism:dust_iron", "immersiveengineering:dust_gold", "mekanism:dust_gold", "ae2:certus_quartz_dust", "ae2:ender_dust", "ae2:fluix_dust", "ae2:sky_dust", "mekanism:dust_osmium", "mekanism:dust_tin", "mekanism:dust_bronze", "mekanism:dust_charcoal", "mekanism:dust_coal", "mekanism:dust_diamond", "mekanism:dust_emerald", "mekanism:dust_netherite", "mekanism:dust_lapis_lazuli", "mekanism:dust_lithium", "create:powdered_obsidian", "mekanism:dust_obsidian", "mekanism:dust_quartz", "mekanism:dust_refined_obsidian", "mekanism:salt", "immersiveengineering:dust_sulfur", "mekanism:dust_sulfur", "immersiveengineering:dust_wood", "mekanism:sawdust", "mekanism:dust_fluorite"]}, {"id": "c:red_sandstone_slabs", "items": ["minecraft:red_sandstone_slab", "minecraft:cut_red_sandstone_slab", "minecraft:smooth_red_sandstone_slab"]}, {"id": "forge:rods/all_metal", "items": ["immersiveengineering:stick_aluminum", "immersiveengineering:stick_iron", "immersiveengineering:stick_steel"]}, {"id": "forge:armors/boots", "items": ["minecraft:leather_boots", "minecraft:chainmail_boots", "minecraft:iron_boots", "minecraft:golden_boots", "minecraft:diamond_boots", "minecraft:netherite_boots", "immersiveengineering:armor_steel_boots", "immersiveengineering:armor_faraday_boots", "create:copper_diving_boots", "create:netherite_diving_boots", "create:cardboard_boots", "mekanism:hazmat_boots", "tconstruct:travelers_boots", "tconstruct:plate_boots", "tconstruct:slime_boots"]}, {"id": "tconstruct:bloodshroom_logs", "items": ["tconstruct:bloodshroom_log", "tconstruct:stripped_bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:stripped_bloodshroom_wood"]}, {"id": "forge:glass_panes/colorless", "items": ["minecraft:glass_pane", "create:tiled_glass_pane", "create:framed_glass_pane", "create:horizontal_framed_glass_pane", "create:vertical_framed_glass_pane", "tconstruct:clear_glass_pane"]}, {"id": "forge:lapis", "items": ["minecraft:lapis_lazuli"]}, {"id": "forge:storage_blocks/hepatizon", "items": ["tconstruct:hepatizon_block"]}, {"id": "minecraft:coal_ores", "items": ["minecraft:coal_ore", "minecraft:deepslate_coal_ore"]}, {"id": "c:brown_dyes", "items": ["minecraft:brown_dye"]}, {"id": "forge:storage_blocks/fluorite", "items": ["mekanism:block_fluorite"]}, {"id": "immersiveengineering:scaffolding_slabs/aluminum", "items": ["immersiveengineering:slab_alu_scaffolding_standard", "immersiveengineering:slab_alu_scaffolding_grate_top", "immersiveengineering:slab_alu_scaffolding_wooden_top"]}, {"id": "balm:brown_dyes", "items": ["minecraft:brown_dye"]}, {"id": "tconstruct:modifiable/book_armor/tinkers_gadgetry", "items": ["tconstruct:slime_helmet", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots"]}, {"id": "forge:glass/red", "items": ["minecraft:red_stained_glass", "tconstruct:red_clear_stained_glass"]}, {"id": "mekanism:alloys/basic", "items": ["minecraft:redstone"]}, {"id": "tconstruct:casts/multi_use/gear", "items": ["tconstruct:gear_cast"]}, {"id": "forge:storage_blocks/redstone", "items": ["minecraft:redstone_block"]}, {"id": "forge:tools/axes", "items": ["immersiveengineering:axe_steel", "minecraft:diamond_axe", "minecraft:golden_axe", "minecraft:wooden_axe", "minecraft:stone_axe", "minecraft:iron_axe", "minecraft:netherite_axe"]}, {"id": "forge:storage_blocks/bronze", "items": ["mekanism:block_bronze"]}, {"id": "immersiveengineering:tools/screwdrivers", "items": ["immersiveengineering:screwdriver"]}, {"id": "c:netherite_ingots", "items": ["minecraft:netherite_ingot"]}, {"id": "headlight:headlight_helmets", "items": ["tconstruct:travelers_helmet", "tconstruct:plate_helmet", "tconstruct:slime_helmet"]}, {"id": "tconstruct:melting/refined_glowstone/tools_costing_2", "items": []}, {"id": "tconstruct:chest_parts", "items": ["tconstruct:repair_kit", "tconstruct:pick_head", "tconstruct:hammer_head", "tconstruct:small_axe_head", "tconstruct:broad_axe_head", "tconstruct:small_blade", "tconstruct:broad_blade", "tconstruct:adze_head", "tconstruct:large_plate", "tconstruct:tool_binding", "tconstruct:tough_binding", "tconstruct:tool_handle", "tconstruct:tough_handle", "tconstruct:bow_limb", "tconstruct:bow_grip", "tconstruct:bowstring", "tconstruct:maille", "tconstruct:shield_core", "tconstruct:helmet_plating", "tconstruct:chestplate_plating", "tconstruct:leggings_plating", "tconstruct:boots_plating", "tconstruct:helmet_plating_dummy", "tconstruct:chestplate_plating_dummy", "tconstruct:leggings_plating_dummy", "tconstruct:boots_plating_dummy"]}, {"id": "tconstruct:casts/single_use/small_blade", "items": ["tconstruct:small_blade_sand_cast", "tconstruct:small_blade_red_sand_cast"]}, {"id": "minecraft:slabs", "items": ["minecraft:oak_slab", "minecraft:spruce_slab", "minecraft:birch_slab", "minecraft:jungle_slab", "minecraft:acacia_slab", "minecraft:dark_oak_slab", "minecraft:crimson_slab", "minecraft:warped_slab", "minecraft:mangrove_slab", "minecraft:bamboo_slab", "minecraft:cherry_slab", "cataclysm:chorus_slab", "immersiveengineering:slab_treated_wood_horizontal", "immersiveengineering:slab_treated_wood_vertical", "immersiveengineering:slab_treated_wood_packaged", "tconstruct:greenheart_planks_slab", "tconstruct:skyroot_planks_slab", "tconstruct:bloodshroom_planks_slab", "tconstruct:enderbark_planks_slab", "minecraft:bamboo_mosaic_slab", "minecraft:stone_slab", "minecraft:smooth_stone_slab", "minecraft:stone_brick_slab", "minecraft:sandstone_slab", "minecraft:purpur_slab", "minecraft:quartz_slab", "minecraft:red_sandstone_slab", "minecraft:brick_slab", "minecraft:cobblestone_slab", "minecraft:nether_brick_slab", "minecraft:petrified_oak_slab", "minecraft:prismarine_slab", "minecraft:prismarine_brick_slab", "minecraft:dark_prismarine_slab", "minecraft:polished_granite_slab", "minecraft:smooth_red_sandstone_slab", "minecraft:mossy_stone_brick_slab", "minecraft:polished_diorite_slab", "minecraft:mossy_cobblestone_slab", "minecraft:end_stone_brick_slab", "minecraft:smooth_sandstone_slab", "minecraft:smooth_quartz_slab", "minecraft:granite_slab", "minecraft:andesite_slab", "minecraft:red_nether_brick_slab", "minecraft:polished_andesite_slab", "minecraft:diorite_slab", "minecraft:cut_sandstone_slab", "minecraft:cut_red_sandstone_slab", "minecraft:blackstone_slab", "minecraft:polished_blackstone_brick_slab", "minecraft:polished_blackstone_slab", "minecraft:cobbled_deepslate_slab", "minecraft:polished_deepslate_slab", "minecraft:deepslate_tile_slab", "minecraft:deepslate_brick_slab", "minecraft:waxed_weathered_cut_copper_slab", "minecraft:waxed_exposed_cut_copper_slab", "minecraft:waxed_cut_copper_slab", "minecraft:oxidized_cut_copper_slab", "minecraft:weathered_cut_copper_slab", "minecraft:exposed_cut_copper_slab", "minecraft:cut_copper_slab", "minecraft:waxed_oxidized_cut_copper_slab", "minecraft:mud_brick_slab", "cataclysm:obsidian_brick_slab", "cataclysm:polished_end_stone_slab", "immersiveengineering:slab_storage_constantan", "immersiveengineering:slab_sheetmetal_nickel", "immersiveengineering:slab_sheetmetal_colored_orange", "immersiveengineering:slab_alloybrick", "immersiveengineering:slab_alu_scaffolding_standard", "immersiveengineering:slab_storage_lead", "immersiveengineering:slab_steel_scaffolding_standard", "immersiveengineering:slab_alu_scaffolding_grate_top", "immersiveengineering:slab_steel_scaffolding_grate_top", "immersiveengineering:slab_sheetmetal_colored_red", "immersiveengineering:slab_concrete_leaded", "immersiveengineering:slab_sheetmetal_uranium", "immersiveengineering:slab_steel_scaffolding_wooden_top", "immersiveengineering:slab_sheetmetal_colored_white", "immersiveengineering:slab_storage_silver", "immersiveengineering:slab_sheetmetal_colored_magenta", "immersiveengineering:slab_sheetmetal_aluminum", "immersiveengineering:slab_sheetmetal_colored_cyan", "immersiveengineering:slab_coke", "immersiveengineering:slab_sheetmetal_iron", "immersiveengineering:slab_blastbrick", "immersiveengineering:slab_sheetmetal_colored_green", "immersiveengineering:slab_concrete_tile", "immersiveengineering:slab_sheetmetal_colored_purple", "immersiveengineering:slab_sheetmetal_gold", "immersiveengineering:slab_sheetmetal_lead", "immersiveengineering:slab_sheetmetal_colored_brown", "immersiveengineering:slab_sheetmetal_constantan", "immersiveengineering:slab_cokebrick", "immersiveengineering:slab_storage_electrum", "immersiveengineering:slab_sheetmetal_copper", "immersiveengineering:slab_sheetmetal_colored_light_gray", "immersiveengineering:slab_hempcrete", "immersiveengineering:slab_sheetmetal_colored_pink", "immersiveengineering:slab_alu_scaffolding_wooden_top", "immersiveengineering:slab_clinker_brick", "immersiveengineering:slab_insulating_glass", "immersiveengineering:slab_concrete_brick", "immersiveengineering:slab_sheetmetal_steel", "immersiveengineering:slab_sheetmetal_silver", "immersiveengineering:slab_sheetmetal_electrum", "immersiveengineering:slab_sheetmetal_colored_blue", "immersiveengineering:slab_storage_aluminum", "immersiveengineering:slab_hempcrete_brick", "immersiveengineering:slab_sheetmetal_colored_lime", "immersiveengineering:slab_sheetmetal_colored_gray", "immersiveengineering:slab_blastbrick_reinforced", "immersiveengineering:slab_concrete", "immersiveengineering:slab_sheetmetal_colored_yellow", "immersiveengineering:slab_sheetmetal_colored_light_blue", "immersiveengineering:slab_slag_brick", "immersiveengineering:slab_storage_nickel", "immersiveengineering:slab_storage_uranium", "immersiveengineering:slab_sheetmetal_colored_black", "immersiveengineering:slab_storage_steel", "create:cut_granite_slab", "create:polished_cut_granite_slab", "create:cut_granite_brick_slab", "create:small_granite_brick_slab", "create:cut_diorite_slab", "create:polished_cut_diorite_slab", "create:cut_diorite_brick_slab", "create:small_diorite_brick_slab", "create:cut_andesite_slab", "create:polished_cut_andesite_slab", "create:cut_andesite_brick_slab", "create:small_andesite_brick_slab", "create:cut_calcite_slab", "create:polished_cut_calcite_slab", "create:cut_calcite_brick_slab", "create:small_calcite_brick_slab", "create:cut_dripstone_slab", "create:polished_cut_dripstone_slab", "create:cut_dripstone_brick_slab", "create:small_dripstone_brick_slab", "create:cut_deepslate_slab", "create:polished_cut_deepslate_slab", "create:cut_deepslate_brick_slab", "create:small_deepslate_brick_slab", "create:cut_tuff_slab", "create:polished_cut_tuff_slab", "create:cut_tuff_brick_slab", "create:small_tuff_brick_slab", "create:cut_asurine_slab", "create:polished_cut_asurine_slab", "create:cut_asurine_brick_slab", "create:small_asurine_brick_slab", "create:cut_crimsite_slab", "create:polished_cut_crimsite_slab", "create:cut_crimsite_brick_slab", "create:small_crimsite_brick_slab", "create:cut_limestone_slab", "create:polished_cut_limestone_slab", "create:cut_limestone_brick_slab", "create:small_limestone_brick_slab", "create:cut_ochrum_slab", "create:polished_cut_ochrum_slab", "create:cut_ochrum_brick_slab", "create:small_ochrum_brick_slab", "create:cut_scoria_slab", "create:polished_cut_scoria_slab", "create:cut_scoria_brick_slab", "create:small_scoria_brick_slab", "create:cut_scorchia_slab", "create:polished_cut_scorchia_slab", "create:cut_scorchia_brick_slab", "create:small_scorchia_brick_slab", "create:cut_veridium_slab", "create:polished_cut_veridium_slab", "create:cut_veridium_brick_slab", "create:small_veridium_brick_slab"]}, {"id": "forge:stairs/red_sandstone", "items": ["minecraft:red_sandstone_stairs", "minecraft:smooth_red_sandstone_stairs"]}, {"id": "forge:end_stones", "items": ["minecraft:end_stone"]}, {"id": "c:magenta_dyes", "items": ["minecraft:magenta_dye"]}, {"id": "tconstruct:casts/single_use/nugget", "items": ["tconstruct:nugget_sand_cast", "tconstruct:nugget_red_sand_cast"]}, {"id": "ae2:p2p_attunements/redstone_p2p_tunnel", "items": ["minecraft:redstone", "minecraft:repeater", "minecraft:redstone_lamp", "minecraft:comparator", "minecraft:daylight_detector", "minecraft:redstone_torch", "minecraft:redstone_block", "minecraft:lever"]}, {"id": "forge:gems/lapis", "items": ["minecraft:lapis_lazuli"]}, {"id": "tconstruct:slimy_logs", "items": ["tconstruct:greenheart_log", "tconstruct:stripped_greenheart_log", "tconstruct:greenheart_wood", "tconstruct:stripped_greenheart_wood", "tconstruct:skyroot_log", "tconstruct:stripped_skyroot_log", "tconstruct:skyroot_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:bloodshroom_log", "tconstruct:stripped_bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:stripped_bloodshroom_wood", "tconstruct:enderbark_log", "tconstruct:stripped_enderbark_log", "tconstruct:enderbark_wood", "tconstruct:stripped_enderbark_wood"]}, {"id": "minecraft:smelts_to_glass", "items": ["minecraft:sand", "minecraft:red_sand"]}, {"id": "minecraft:wooden_doors", "items": ["minecraft:oak_door", "minecraft:spruce_door", "minecraft:birch_door", "minecraft:jungle_door", "minecraft:acacia_door", "minecraft:dark_oak_door", "minecraft:crimson_door", "minecraft:warped_door", "minecraft:mangrove_door", "minecraft:bamboo_door", "minecraft:cherry_door", "tconstruct:greenheart_door", "tconstruct:skyroot_door", "tconstruct:bloodshroom_door", "tconstruct:enderbark_door"]}, {"id": "forge:storage_blocks/nickel", "items": ["immersiveengineering:storage_nickel"]}, {"id": "ae2:quartz_sword", "items": ["ae2:certus_quartz_sword", "ae2:nether_quartz_sword"]}, {"id": "minecraft:sniffer_food", "items": ["minecraft:torchflower_seeds"]}, {"id": "forge:nuggets/silver", "items": ["immersiveengineering:nugget_silver"]}, {"id": "forge:storage_blocks/sandstone", "items": ["minecraft:sandstone", "minecraft:chiseled_sandstone", "minecraft:cut_sandstone", "minecraft:smooth_sandstone", "minecraft:red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:cut_red_sandstone", "minecraft:smooth_red_sandstone"]}, {"id": "balm:eggs", "items": ["minecraft:egg"]}, {"id": "tconstruct:casts/multi_use/broad_axe_head", "items": ["tconstruct:broad_axe_head_cast"]}, {"id": "forge:plates/obsidian", "items": ["create:sturdy_sheet"]}, {"id": "forge:gems/quartz", "items": ["minecraft:quartz"]}, {"id": "tconstruct:melting/lead/tools_costing_3", "items": []}, {"id": "c:red_dyes", "items": ["minecraft:red_dye"]}, {"id": "mekanism:clumps/iron", "items": ["mekanism:clump_iron"]}, {"id": "forge:storage_blocks/lead", "items": ["immersiveengineering:storage_lead", "mekanism:block_lead"]}, {"id": "balm:wooden_rods", "items": ["minecraft:stick", "immersiveengineering:stick_treated"]}, {"id": "c:buds", "items": ["ae2:small_quartz_bud", "ae2:medium_quartz_bud", "ae2:large_quartz_bud", "minecraft:small_amethyst_bud", "minecraft:medium_amethyst_bud", "minecraft:large_amethyst_bud"]}, {"id": "c:budding_blocks", "items": ["minecraft:budding_amethyst"]}, {"id": "mekanism:dirty_dusts/osmium", "items": ["mekanism:dirty_dust_osmium"]}, {"id": "tconstruct:casts/multi_use", "items": ["tconstruct:ingot_cast", "tconstruct:nugget_cast", "tconstruct:gem_cast", "tconstruct:rod_cast", "tconstruct:repair_kit_cast", "tconstruct:plate_cast", "tconstruct:gear_cast", "tconstruct:coin_cast", "tconstruct:wire_cast", "tconstruct:pick_head_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_blade_cast", "tconstruct:adze_head_cast", "tconstruct:hammer_head_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_blade_cast", "tconstruct:large_plate_cast", "tconstruct:tool_binding_cast", "tconstruct:tough_binding_cast", "tconstruct:tool_handle_cast", "tconstruct:tough_handle_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_grip_cast", "tconstruct:helmet_plating_cast", "tconstruct:chestplate_plating_cast", "tconstruct:leggings_plating_cast", "tconstruct:boots_plating_cast", "tconstruct:maille_cast"]}, {"id": "forge:nuggets/refined_obsidian", "items": ["mekanism:nugget_refined_obsidian"]}, {"id": "forge:ingots/amethyst_bronze", "items": ["tconstruct:amethyst_bronze_ingot"]}, {"id": "create:stone_types/asurine", "items": ["create:cut_asurine", "create:cut_asurine_stairs", "create:cut_asurine_wall", "create:polished_cut_asurine", "create:polished_cut_asurine_stairs", "create:polished_cut_asurine_wall", "create:cut_asurine_bricks", "create:cut_asurine_brick_stairs", "create:cut_asurine_brick_wall", "create:small_asurine_bricks", "create:small_asurine_brick_stairs", "create:small_asurine_brick_wall", "create:layered_asurine", "create:asurine_pillar", "create:asurine"]}, {"id": "c:pickaxes", "items": ["cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "minecraft:diamond_pickaxe", "minecraft:golden_pickaxe", "minecraft:wooden_pickaxe", "minecraft:stone_pickaxe", "minecraft:iron_pickaxe", "minecraft:netherite_pickaxe"]}, {"id": "create:stone_types/tuff", "items": ["create:cut_tuff", "create:cut_tuff_stairs", "create:cut_tuff_wall", "create:polished_cut_tuff", "create:polished_cut_tuff_stairs", "create:polished_cut_tuff_wall", "create:cut_tuff_bricks", "create:cut_tuff_brick_stairs", "create:cut_tuff_brick_wall", "create:small_tuff_bricks", "create:small_tuff_brick_stairs", "create:small_tuff_brick_wall", "create:layered_tuff", "create:tuff_pillar", "minecraft:tuff"]}, {"id": "forge:ingots/refined_obsidian", "items": ["mekanism:ingot_refined_obsidian"]}, {"id": "tconstruct:casts/single_use/tough_handle", "items": ["tconstruct:tough_handle_sand_cast", "tconstruct:tough_handle_red_sand_cast"]}, {"id": "c:raw_gold_ores", "items": ["minecraft:raw_gold"]}, {"id": "minecraft:noteblock_top_instruments", "items": ["minecraft:zombie_head", "minecraft:skeleton_skull", "minecraft:creeper_head", "minecraft:dragon_head", "minecraft:wither_skeleton_skull", "minecraft:piglin_head", "minecraft:player_head"]}, {"id": "tconstruct:modifiable/interactable/charge", "items": ["tconstruct:swasher", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:longbow", "tconstruct:crossbow", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "forge:fences/aluminum", "items": ["immersiveengineering:alu_fence"]}, {"id": "create:seats", "items": ["create:white_seat", "create:orange_seat", "create:magenta_seat", "create:light_blue_seat", "create:yellow_seat", "create:lime_seat", "create:pink_seat", "create:gray_seat", "create:light_gray_seat", "create:cyan_seat", "create:purple_seat", "create:blue_seat", "create:brown_seat", "create:green_seat", "create:red_seat", "create:black_seat"]}, {"id": "forge:ingots/nickel", "items": ["immersiveengineering:ingot_nickel"]}, {"id": "minecraft:walls", "items": ["minecraft:cobblestone_wall", "minecraft:mossy_cobblestone_wall", "minecraft:brick_wall", "minecraft:prismarine_wall", "minecraft:red_sandstone_wall", "minecraft:mossy_stone_brick_wall", "minecraft:granite_wall", "minecraft:stone_brick_wall", "minecraft:nether_brick_wall", "minecraft:andesite_wall", "minecraft:red_nether_brick_wall", "minecraft:sandstone_wall", "minecraft:end_stone_brick_wall", "minecraft:diorite_wall", "minecraft:blackstone_wall", "minecraft:polished_blackstone_brick_wall", "minecraft:polished_blackstone_wall", "minecraft:cobbled_deepslate_wall", "minecraft:polished_deepslate_wall", "minecraft:deepslate_tile_wall", "minecraft:deepslate_brick_wall", "minecraft:mud_brick_wall", "cataclysm:obsidian_brick_wall", "cataclysm:purpur_wall", "create:cut_granite_wall", "create:polished_cut_granite_wall", "create:cut_granite_brick_wall", "create:small_granite_brick_wall", "create:cut_diorite_wall", "create:polished_cut_diorite_wall", "create:cut_diorite_brick_wall", "create:small_diorite_brick_wall", "create:cut_andesite_wall", "create:polished_cut_andesite_wall", "create:cut_andesite_brick_wall", "create:small_andesite_brick_wall", "create:cut_calcite_wall", "create:polished_cut_calcite_wall", "create:cut_calcite_brick_wall", "create:small_calcite_brick_wall", "create:cut_dripstone_wall", "create:polished_cut_dripstone_wall", "create:cut_dripstone_brick_wall", "create:small_dripstone_brick_wall", "create:cut_deepslate_wall", "create:polished_cut_deepslate_wall", "create:cut_deepslate_brick_wall", "create:small_deepslate_brick_wall", "create:cut_tuff_wall", "create:polished_cut_tuff_wall", "create:cut_tuff_brick_wall", "create:small_tuff_brick_wall", "create:cut_asurine_wall", "create:polished_cut_asurine_wall", "create:cut_asurine_brick_wall", "create:small_asurine_brick_wall", "create:cut_crimsite_wall", "create:polished_cut_crimsite_wall", "create:cut_crimsite_brick_wall", "create:small_crimsite_brick_wall", "create:cut_limestone_wall", "create:polished_cut_limestone_wall", "create:cut_limestone_brick_wall", "create:small_limestone_brick_wall", "create:cut_ochrum_wall", "create:polished_cut_ochrum_wall", "create:cut_ochrum_brick_wall", "create:small_ochrum_brick_wall", "create:cut_scoria_wall", "create:polished_cut_scoria_wall", "create:cut_scoria_brick_wall", "create:small_scoria_brick_wall", "create:cut_scorchia_wall", "create:polished_cut_scorchia_wall", "create:cut_scorchia_brick_wall", "create:small_scorchia_brick_wall", "create:cut_veridium_wall", "create:polished_cut_veridium_wall", "create:cut_veridium_brick_wall", "create:small_veridium_brick_wall"]}, {"id": "c:bookshelves", "items": ["minecraft:bookshelf"]}, {"id": "tconstruct:casts/single_use/gear", "items": ["tconstruct:gear_sand_cast", "tconstruct:gear_red_sand_cast"]}, {"id": "tconstruct:casts/multi_use/pick_head", "items": ["tconstruct:pick_head_cast"]}, {"id": "curios:belt", "items": []}, {"id": "minecraft:soul_fire_base_blocks", "items": ["minecraft:soul_sand", "minecraft:soul_soil", "tconstruct:soul_glass", "tconstruct:seared_soul_glass", "tconstruct:scorched_soul_glass"]}, {"id": "mekanism:shards/tin", "items": ["mekanism:shard_tin"]}, {"id": "forge:dyes/purple", "items": ["minecraft:purple_dye"]}, {"id": "immersiveengineering:scaffolding_slabs/steel", "items": ["immersiveengineering:slab_steel_scaffolding_standard", "immersiveengineering:slab_steel_scaffolding_grate_top", "immersiveengineering:slab_steel_scaffolding_wooden_top"]}, {"id": "minecraft:tools", "items": ["minecraft:diamond_sword", "minecraft:stone_sword", "minecraft:golden_sword", "minecraft:netherite_sword", "minecraft:wooden_sword", "minecraft:iron_sword", "cataclysm:the_incinerator", "cataclysm:athame", "immersiveengineering:sword_steel", "ae2:certus_quartz_sword", "ae2:nether_quartz_sword", "ae2:fluix_sword", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:swasher", "minecraft:diamond_axe", "minecraft:stone_axe", "minecraft:golden_axe", "minecraft:netherite_axe", "minecraft:wooden_axe", "minecraft:iron_axe", "immersiveengineering:axe_steel", "ae2:certus_quartz_axe", "ae2:nether_quartz_axe", "ae2:fluix_axe", "tconstruct:mattock", "tconstruct:hand_axe", "tconstruct:broad_axe", "minecraft:diamond_pickaxe", "minecraft:stone_pickaxe", "minecraft:golden_pickaxe", "minecraft:netherite_pickaxe", "minecraft:wooden_pickaxe", "minecraft:iron_pickaxe", "cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "ae2:certus_quartz_pickaxe", "ae2:nether_quartz_pickaxe", "ae2:fluix_pickaxe", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:pickadze", "tconstruct:war_pick", "minecraft:diamond_shovel", "minecraft:stone_shovel", "minecraft:golden_shovel", "minecraft:netherite_shovel", "minecraft:wooden_shovel", "minecraft:iron_shovel", "immersiveengineering:shovel_steel", "ae2:certus_quartz_shovel", "ae2:nether_quartz_shovel", "ae2:fluix_shovel", "tconstruct:excavator", "minecraft:diamond_hoe", "minecraft:stone_hoe", "minecraft:golden_hoe", "minecraft:netherite_hoe", "minecraft:wooden_hoe", "minecraft:iron_hoe", "immersiveengineering:hoe_steel", "ae2:certus_quartz_hoe", "ae2:nether_quartz_hoe", "ae2:fluix_hoe", "tconstruct:kama", "tconstruct:scythe", "minecraft:trident"]}, {"id": "forge:tools/scythe", "items": ["tconstruct:kama", "tconstruct:scythe"]}, {"id": "mekanism:crystals/tin", "items": ["mekanism:crystal_tin"]}, {"id": "c:water_buckets", "items": ["minecraft:water_bucket"]}, {"id": "tconstruct:modifiable/book_armor", "items": ["tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:travelers_shield", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:plate_shield", "tconstruct:slime_helmet", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots"]}, {"id": "create:blaze_burner_fuel/special", "items": ["create:blaze_cake"]}, {"id": "forge:fabric_hemp", "items": ["immersiveengineering:hemp_fabric"]}, {"id": "create:stone_types/crimsite", "items": ["create:cut_crimsite", "create:cut_crimsite_stairs", "create:cut_crimsite_wall", "create:polished_cut_crimsite", "create:polished_cut_crimsite_stairs", "create:polished_cut_crimsite_wall", "create:cut_crimsite_bricks", "create:cut_crimsite_brick_stairs", "create:cut_crimsite_brick_wall", "create:small_crimsite_bricks", "create:small_crimsite_brick_stairs", "create:small_crimsite_brick_wall", "create:layered_crimsite", "create:crimsite_pillar", "create:crimsite"]}, {"id": "balm:ores", "items": ["minecraft:coal_ore", "minecraft:deepslate_coal_ore", "minecraft:copper_ore", "minecraft:deepslate_copper_ore", "minecraft:diamond_ore", "minecraft:deepslate_diamond_ore", "minecraft:emerald_ore", "minecraft:deepslate_emerald_ore", "minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore", "minecraft:iron_ore", "minecraft:deepslate_iron_ore", "minecraft:lapis_ore", "minecraft:deepslate_lapis_ore", "minecraft:redstone_ore", "minecraft:deepslate_redstone_ore", "minecraft:nether_quartz_ore", "minecraft:ancient_debris", "immersiveengineering:ore_aluminum", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:deepslate_ore_lead", "mekanism:lead_ore", "mekanism:deepslate_lead_ore", "immersiveengineering:ore_silver", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:ore_nickel", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:ore_uranium", "immersiveengineering:deepslate_ore_uranium", "mekanism:uranium_ore", "mekanism:deepslate_uranium_ore", "create:zinc_ore", "create:deepslate_zinc_ore", "mekanism:tin_ore", "mekanism:deepslate_tin_ore", "mekanism:osmium_ore", "mekanism:deepslate_osmium_ore", "mekanism:fluorite_ore", "mekanism:deepslate_fluorite_ore", "tconstruct:cobalt_ore"]}, {"id": "forge:crops/beetroot", "items": ["minecraft:beetroot"]}, {"id": "forge:nuggets", "items": ["minecraft:iron_nugget", "minecraft:gold_nugget", "immersiveengineering:nugget_copper", "create:copper_nugget", "tconstruct:copper_nugget", "immersiveengineering:nugget_aluminum", "immersiveengineering:nugget_lead", "mekanism:nugget_lead", "immersiveengineering:nugget_silver", "immersiveengineering:nugget_nickel", "immersiveengineering:nugget_uranium", "mekanism:nugget_uranium", "immersiveengineering:nugget_constantan", "immersiveengineering:nugget_electrum", "immersiveengineering:nugget_steel", "mekanism:nugget_steel", "tconstruct:steel_nugget", "create:zinc_nugget", "create:brass_nugget", "create:experience_nugget", "mekanism:nugget_osmium", "mekanism:nugget_tin", "mekanism:nugget_bronze", "mekanism:nugget_refined_glowstone", "mekanism:nugget_refined_obsidian", "tconstruct:netherite_nugget", "tconstruct:debris_nugget", "tconstruct:cobalt_nugget", "tconstruct:slimesteel_nugget", "tconstruct:amethyst_bronze_nugget", "tconstruct:rose_gold_nugget", "tconstruct:pig_iron_nugget", "tconstruct:cinderslime_nugget", "tconstruct:queens_slime_nugget", "tconstruct:manyullyn_nugget", "tconstruct:hepatizon_nugget", "tconstruct:soulsteel_nugget", "tconstruct:<PERSON><PERSON>e_nugget"]}, {"id": "tconstruct:casts/empty/table", "items": ["tconstruct:gold_bars"]}, {"id": "forge:nuggets/queens_slime", "items": ["tconstruct:queens_slime_nugget"]}, {"id": "forge:dusts/bronze", "items": ["mekanism:dust_bronze"]}, {"id": "tconstruct:modifiable/harvest/stone", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:pickadze", "tconstruct:war_pick"]}, {"id": "forge:dusts/refined_obsidian", "items": ["mekanism:dust_refined_obsidian"]}, {"id": "forge:glass/blue", "items": ["minecraft:blue_stained_glass", "tconstruct:blue_clear_stained_glass"]}, {"id": "mekanism:crystals", "items": ["mekanism:crystal_iron", "mekanism:crystal_gold", "mekanism:crystal_osmium", "mekanism:crystal_copper", "mekanism:crystal_tin", "mekanism:crystal_lead", "mekanism:crystal_uranium"]}, {"id": "ae2:smart_cable", "items": ["ae2:white_smart_cable", "ae2:orange_smart_cable", "ae2:magenta_smart_cable", "ae2:light_blue_smart_cable", "ae2:yellow_smart_cable", "ae2:lime_smart_cable", "ae2:pink_smart_cable", "ae2:gray_smart_cable", "ae2:light_gray_smart_cable", "ae2:cyan_smart_cable", "ae2:purple_smart_cable", "ae2:blue_smart_cable", "ae2:brown_smart_cable", "ae2:green_smart_cable", "ae2:red_smart_cable", "ae2:black_smart_cable", "ae2:fluix_smart_cable"]}, {"id": "forge:tools/shovels", "items": ["immersiveengineering:shovel_steel", "minecraft:diamond_shovel", "minecraft:golden_shovel", "minecraft:wooden_shovel", "minecraft:stone_shovel", "minecraft:iron_shovel", "minecraft:netherite_shovel"]}, {"id": "tconstruct:casts/single_use/boots_plating", "items": ["tconstruct:boots_plating_sand_cast", "tconstruct:boots_plating_red_sand_cast"]}, {"id": "forge:bottles/lingering", "items": ["tconstruct:lingering_bottle"]}, {"id": "ae2:all_certus_quartz", "items": ["ae2:certus_quartz_crystal", "ae2:charged_certus_quartz_crystal"]}, {"id": "c:hidden_from_recipe_viewers", "items": ["tconstruct:crystalshot", "tconstruct:molten_soulsteel_bucket", "tconstruct:molten_knightslime_bucket", "tconstruct:soulsteel_block", "tconstruct:soulsteel_ingot", "tconstruct:soulsteel_nugget", "tconstruct:knightslime_block", "tconstruct:<PERSON><PERSON><PERSON>_ingot", "tconstruct:<PERSON><PERSON>e_nugget", "tconstruct:ichor_slime_leaves", "tconstruct:ichor_slime_tall_grass", "tconstruct:ichor_slime_fern", "tconstruct:ichor_slime_sapling", "tconstruct:ichor_slime_grass_seeds", "tconstruct:ichor_earth_slime_grass", "tconstruct:ichor_sky_slime_grass", "tconstruct:ichor_ichor_slime_grass", "tconstruct:ichor_ender_slime_grass", "tconstruct:ichor_vanilla_slime_grass"]}, {"id": "tconstruct:melting/iron/tools_costing_2", "items": ["minecraft:iron_sword", "minecraft:iron_hoe", "minecraft:shears", "immersiveengineering:hammer"]}, {"id": "tconstruct:patterns/reusable", "items": ["tconstruct:ingot_cast", "tconstruct:nugget_cast", "tconstruct:gem_cast", "tconstruct:rod_cast", "tconstruct:repair_kit_cast", "tconstruct:plate_cast", "tconstruct:gear_cast", "tconstruct:coin_cast", "tconstruct:wire_cast", "tconstruct:pick_head_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_blade_cast", "tconstruct:adze_head_cast", "tconstruct:hammer_head_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_blade_cast", "tconstruct:large_plate_cast", "tconstruct:tool_binding_cast", "tconstruct:tough_binding_cast", "tconstruct:tool_handle_cast", "tconstruct:tough_handle_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_grip_cast", "tconstruct:helmet_plating_cast", "tconstruct:chestplate_plating_cast", "tconstruct:leggings_plating_cast", "tconstruct:boots_plating_cast", "tconstruct:maille_cast"]}, {"id": "forge:circuits", "items": ["mekanism:basic_control_circuit", "mekanism:advanced_control_circuit", "mekanism:elite_control_circuit", "mekanism:ultimate_control_circuit"]}, {"id": "tconstruct:melting/bronze/tools_costing_1", "items": []}, {"id": "forge:glass/magenta", "items": ["minecraft:magenta_stained_glass", "tconstruct:magenta_clear_stained_glass"]}, {"id": "forge:storage_blocks/cobalt", "items": ["tconstruct:cobalt_block"]}, {"id": "ae2:blacklisted/annihilation_plane", "items": []}, {"id": "c:orange_dyes", "items": ["minecraft:orange_dye"]}, {"id": "tconstruct:casts/single_use/adze_head", "items": ["tconstruct:adze_head_sand_cast", "tconstruct:adze_head_red_sand_cast"]}, {"id": "tconstruct:melting/refined_glowstone/tools_costing_3", "items": []}, {"id": "tconstruct:seeds", "items": ["minecraft:beetroot_seeds", "minecraft:melon_seeds", "minecraft:pumpkin_seeds", "minecraft:wheat_seeds", "immersiveengineering:seed", "minecraft:carrot", "minecraft:potato", "minecraft:nether_wart"]}, {"id": "mekanism:shards/iron", "items": ["mekanism:shard_iron"]}, {"id": "forge:plates", "items": ["immersiveengineering:plate_copper", "create:copper_sheet", "immersiveengineering:plate_aluminum", "immersiveengineering:plate_lead", "immersiveengineering:plate_silver", "immersiveengineering:plate_nickel", "immersiveengineering:plate_uranium", "immersiveengineering:plate_constantan", "immersiveengineering:plate_electrum", "immersiveengineering:plate_steel", "immersiveengineering:plate_iron", "create:iron_sheet", "immersiveengineering:plate_gold", "create:golden_sheet", "create:sturdy_sheet", "create:cardboard", "create:brass_sheet"]}, {"id": "forge:nuggets/pig_iron", "items": ["tconstruct:pig_iron_nugget"]}, {"id": "mekanism:colorable/wool", "items": ["minecraft:white_wool", "minecraft:orange_wool", "minecraft:magenta_wool", "minecraft:light_blue_wool", "minecraft:yellow_wool", "minecraft:lime_wool", "minecraft:pink_wool", "minecraft:gray_wool", "minecraft:light_gray_wool", "minecraft:cyan_wool", "minecraft:purple_wool", "minecraft:blue_wool", "minecraft:brown_wool", "minecraft:green_wool", "minecraft:red_wool", "minecraft:black_wool"]}, {"id": "minecraft:stone_bricks", "items": ["minecraft:stone_bricks", "minecraft:mossy_stone_bricks", "minecraft:cracked_stone_bricks", "minecraft:chiseled_stone_bricks"]}, {"id": "forge:armors/helmet", "items": ["create:cardboard_helmet"]}, {"id": "tconstruct:melting/tin/tools_costing_1", "items": []}, {"id": "immersiveengineering:circuits/solder", "items": ["immersiveengineering:wire_copper", "immersiveengineering:wire_lead"]}, {"id": "forge:nuggets/hepatizon", "items": ["tconstruct:hepatizon_nugget"]}, {"id": "forge:glass_panes/red", "items": ["minecraft:red_stained_glass_pane", "tconstruct:red_clear_stained_glass_pane"]}, {"id": "tconstruct:wood_variants/logs", "items": ["minecraft:oak_log", "minecraft:oak_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood", "minecraft:spruce_log", "minecraft:spruce_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood", "minecraft:birch_log", "minecraft:birch_wood", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood", "minecraft:jungle_log", "minecraft:jungle_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood", "minecraft:dark_oak_log", "minecraft:dark_oak_wood", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood", "minecraft:acacia_log", "minecraft:acacia_wood", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood", "minecraft:mangrove_log", "minecraft:mangrove_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood", "minecraft:cherry_log", "minecraft:cherry_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood", "minecraft:crimson_stem", "minecraft:stripped_crimson_stem", "minecraft:crimson_hyphae", "minecraft:stripped_crimson_hyphae", "minecraft:warped_stem", "minecraft:stripped_warped_stem", "minecraft:warped_hyphae", "minecraft:stripped_warped_hyphae", "tconstruct:greenheart_log", "tconstruct:stripped_greenheart_log", "tconstruct:greenheart_wood", "tconstruct:stripped_greenheart_wood", "tconstruct:skyroot_log", "tconstruct:stripped_skyroot_log", "tconstruct:skyroot_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:bloodshroom_log", "tconstruct:stripped_bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:stripped_bloodshroom_wood", "tconstruct:enderbark_log", "tconstruct:stripped_enderbark_log", "tconstruct:enderbark_wood", "tconstruct:stripped_enderbark_wood"]}, {"id": "forge:sheetmetal_slabs", "items": ["immersiveengineering:slab_sheetmetal_copper", "immersiveengineering:slab_sheetmetal_aluminum", "immersiveengineering:slab_sheetmetal_lead", "immersiveengineering:slab_sheetmetal_silver", "immersiveengineering:slab_sheetmetal_nickel", "immersiveengineering:slab_sheetmetal_uranium", "immersiveengineering:slab_sheetmetal_constantan", "immersiveengineering:slab_sheetmetal_electrum", "immersiveengineering:slab_sheetmetal_steel", "immersiveengineering:slab_sheetmetal_iron", "immersiveengineering:slab_sheetmetal_gold", "immersiveengineering:slab_sheetmetal_colored_white", "immersiveengineering:slab_sheetmetal_colored_orange", "immersiveengineering:slab_sheetmetal_colored_magenta", "immersiveengineering:slab_sheetmetal_colored_light_blue", "immersiveengineering:slab_sheetmetal_colored_yellow", "immersiveengineering:slab_sheetmetal_colored_lime", "immersiveengineering:slab_sheetmetal_colored_pink", "immersiveengineering:slab_sheetmetal_colored_gray", "immersiveengineering:slab_sheetmetal_colored_light_gray", "immersiveengineering:slab_sheetmetal_colored_cyan", "immersiveengineering:slab_sheetmetal_colored_purple", "immersiveengineering:slab_sheetmetal_colored_blue", "immersiveengineering:slab_sheetmetal_colored_brown", "immersiveengineering:slab_sheetmetal_colored_green", "immersiveengineering:slab_sheetmetal_colored_red", "immersiveengineering:slab_sheetmetal_colored_black"]}, {"id": "tconstruct:melting/steel/tools_costing_8", "items": ["immersiveengineering:armor_steel_chestplate"]}, {"id": "balm:light_blue_dyes", "items": ["minecraft:light_blue_dye"]}, {"id": "forge:armor/boots", "items": ["cataclysm:ignitium_boots"]}, {"id": "minecraft:signs", "items": ["minecraft:oak_sign", "minecraft:spruce_sign", "minecraft:birch_sign", "minecraft:acacia_sign", "minecraft:jungle_sign", "minecraft:dark_oak_sign", "minecraft:crimson_sign", "minecraft:warped_sign", "minecraft:mangrove_sign", "minecraft:bamboo_sign", "minecraft:cherry_sign", "tconstruct:greenheart_sign", "tconstruct:skyroot_sign", "tconstruct:bloodshroom_sign", "tconstruct:enderbark_sign"]}, {"id": "tconstruct:non_singular_ore_rates", "items": ["minecraft:copper_ore", "minecraft:deepslate_copper_ore", "minecraft:deepslate_lapis_ore", "minecraft:deepslate_redstone_ore", "minecraft:lapis_ore", "minecraft:redstone_ore", "mekanism:fluorite_ore", "mekanism:deepslate_fluorite_ore", "minecraft:nether_gold_ore"]}, {"id": "minecraft:fox_food", "items": ["minecraft:sweet_berries", "minecraft:glow_berries"]}, {"id": "balm:cooking_oil", "items": []}, {"id": "c:iron_ingots", "items": ["minecraft:iron_ingot"]}, {"id": "forge:ores", "items": ["minecraft:coal_ore", "minecraft:deepslate_coal_ore", "minecraft:copper_ore", "minecraft:deepslate_copper_ore", "minecraft:diamond_ore", "minecraft:deepslate_diamond_ore", "minecraft:emerald_ore", "minecraft:deepslate_emerald_ore", "minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore", "minecraft:iron_ore", "minecraft:deepslate_iron_ore", "minecraft:lapis_ore", "minecraft:deepslate_lapis_ore", "minecraft:redstone_ore", "minecraft:deepslate_redstone_ore", "minecraft:nether_quartz_ore", "minecraft:ancient_debris", "immersiveengineering:ore_aluminum", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:deepslate_ore_lead", "mekanism:lead_ore", "mekanism:deepslate_lead_ore", "immersiveengineering:ore_silver", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:ore_nickel", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:ore_uranium", "immersiveengineering:deepslate_ore_uranium", "mekanism:uranium_ore", "mekanism:deepslate_uranium_ore", "create:zinc_ore", "create:deepslate_zinc_ore", "mekanism:tin_ore", "mekanism:deepslate_tin_ore", "mekanism:osmium_ore", "mekanism:deepslate_osmium_ore", "mekanism:fluorite_ore", "mekanism:deepslate_fluorite_ore", "tconstruct:cobalt_ore"]}, {"id": "mekanism:clumps/uranium", "items": ["mekanism:clump_uranium"]}, {"id": "forge:bottles/splash", "items": ["tconstruct:splash_bottle"]}, {"id": "forge:glass_panes/light_gray", "items": ["minecraft:light_gray_stained_glass_pane", "tconstruct:light_gray_clear_stained_glass_pane"]}, {"id": "forge:tools/fishing_rods", "items": ["minecraft:fishing_rod"]}, {"id": "create:chain_rideable", "items": ["create:wrench", "ae2:certus_quartz_wrench", "ae2:nether_quartz_wrench", "ae2:network_tool", "immersiveengineering:hammer", "mekanism:configurator"]}, {"id": "forge:ingots/electrum", "items": ["immersiveengineering:ingot_electrum"]}, {"id": "tconstruct:modifiable/melee/weapon", "items": ["tconstruct:pickaxe", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:kama", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:flint_and_brick", "tconstruct:war_pick", "tconstruct:sledge_hammer", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:battlesign", "tconstruct:swasher"]}, {"id": "forge:tools/pickaxes", "items": ["cataclysm:infernal_forge", "cataclysm:void_forge", "immersiveengineering:pickaxe_steel", "minecraft:diamond_pickaxe", "minecraft:golden_pickaxe", "minecraft:wooden_pickaxe", "minecraft:stone_pickaxe", "minecraft:iron_pickaxe", "minecraft:netherite_pickaxe"]}, {"id": "create:packages", "items": ["create:cardboard_package_12x12", "create:cardboard_package_10x12", "create:cardboard_package_10x8", "create:cardboard_package_12x10", "create:rare_creeper_package", "create:rare_darcy_package", "create:rare_evan_package", "create:rare_jinx_package", "create:rare_kryppers_package", "create:rare_simi_package", "create:rare_starlotte_package", "create:rare_thunder_package", "create:rare_up_package", "create:rare_vector_package"]}, {"id": "minecraft:copper_ores", "items": ["minecraft:copper_ore", "minecraft:deepslate_copper_ore"]}, {"id": "create:blaze_burner_fuel/regular", "items": []}, {"id": "immersiveengineering:connector_insulator", "items": ["minecraft:terracotta", "minecraft:white_terracotta", "minecraft:orange_terracotta", "minecraft:magenta_terracotta", "minecraft:light_blue_terracotta", "minecraft:yellow_terracotta", "minecraft:lime_terracotta", "minecraft:pink_terracotta", "minecraft:gray_terracotta", "minecraft:light_gray_terracotta", "minecraft:cyan_terracotta", "minecraft:purple_terracotta", "minecraft:blue_terracotta", "minecraft:brown_terracotta", "minecraft:green_terracotta", "minecraft:red_terracotta", "minecraft:black_terracotta", "immersiveengineering:duroplast"]}, {"id": "mekanism:dirty_dusts/gold", "items": ["mekanism:dirty_dust_gold"]}, {"id": "c:clusters", "items": ["ae2:quartz_cluster", "minecraft:amethyst_cluster"]}, {"id": "forge:pellets/plutonium", "items": ["mekanism:pellet_plutonium"]}, {"id": "forge:buckets/milk", "items": ["minecraft:milk_bucket"]}, {"id": "forge:sheetmetals/copper", "items": ["immersiveengineering:sheetmetal_copper"]}, {"id": "ae2:p2p_attunements/me_p2p_tunnel", "items": ["ae2:white_covered_cable", "ae2:orange_covered_cable", "ae2:magenta_covered_cable", "ae2:light_blue_covered_cable", "ae2:yellow_covered_cable", "ae2:lime_covered_cable", "ae2:pink_covered_cable", "ae2:gray_covered_cable", "ae2:light_gray_covered_cable", "ae2:cyan_covered_cable", "ae2:purple_covered_cable", "ae2:blue_covered_cable", "ae2:brown_covered_cable", "ae2:green_covered_cable", "ae2:red_covered_cable", "ae2:black_covered_cable", "ae2:fluix_covered_cable", "ae2:white_covered_dense_cable", "ae2:orange_covered_dense_cable", "ae2:magenta_covered_dense_cable", "ae2:light_blue_covered_dense_cable", "ae2:yellow_covered_dense_cable", "ae2:lime_covered_dense_cable", "ae2:pink_covered_dense_cable", "ae2:gray_covered_dense_cable", "ae2:light_gray_covered_dense_cable", "ae2:cyan_covered_dense_cable", "ae2:purple_covered_dense_cable", "ae2:blue_covered_dense_cable", "ae2:brown_covered_dense_cable", "ae2:green_covered_dense_cable", "ae2:red_covered_dense_cable", "ae2:black_covered_dense_cable", "ae2:fluix_covered_dense_cable", "ae2:white_glass_cable", "ae2:orange_glass_cable", "ae2:magenta_glass_cable", "ae2:light_blue_glass_cable", "ae2:yellow_glass_cable", "ae2:lime_glass_cable", "ae2:pink_glass_cable", "ae2:gray_glass_cable", "ae2:light_gray_glass_cable", "ae2:cyan_glass_cable", "ae2:purple_glass_cable", "ae2:blue_glass_cable", "ae2:brown_glass_cable", "ae2:green_glass_cable", "ae2:red_glass_cable", "ae2:black_glass_cable", "ae2:fluix_glass_cable", "ae2:white_smart_cable", "ae2:orange_smart_cable", "ae2:magenta_smart_cable", "ae2:light_blue_smart_cable", "ae2:yellow_smart_cable", "ae2:lime_smart_cable", "ae2:pink_smart_cable", "ae2:gray_smart_cable", "ae2:light_gray_smart_cable", "ae2:cyan_smart_cable", "ae2:purple_smart_cable", "ae2:blue_smart_cable", "ae2:brown_smart_cable", "ae2:green_smart_cable", "ae2:red_smart_cable", "ae2:black_smart_cable", "ae2:fluix_smart_cable", "ae2:white_smart_dense_cable", "ae2:orange_smart_dense_cable", "ae2:magenta_smart_dense_cable", "ae2:light_blue_smart_dense_cable", "ae2:yellow_smart_dense_cable", "ae2:lime_smart_dense_cable", "ae2:pink_smart_dense_cable", "ae2:gray_smart_dense_cable", "ae2:light_gray_smart_dense_cable", "ae2:cyan_smart_dense_cable", "ae2:purple_smart_dense_cable", "ae2:blue_smart_dense_cable", "ae2:brown_smart_dense_cable", "ae2:green_smart_dense_cable", "ae2:red_smart_dense_cable", "ae2:black_smart_dense_cable", "ae2:fluix_smart_dense_cable"]}, {"id": "c:wooden_barrels", "items": ["minecraft:barrel"]}, {"id": "forge:ingots/slimesteel", "items": ["tconstruct:slimesteel_ingot"]}, {"id": "immersiveengineering:circuits/pcb", "items": ["immersiveengineering:circuit_board"]}, {"id": "forge:coal", "items": ["minecraft:coal", "minecraft:charcoal", "immersiveengineering:coal_coke"]}, {"id": "forge:leather", "items": ["minecraft:leather", "immersiveengineering:ersatz_leather"]}, {"id": "forge:cobblestone/infested", "items": ["minecraft:infested_cobblestone"]}, {"id": "balm:gems", "items": ["minecraft:amethyst_shard", "minecraft:diamond", "minecraft:emerald", "minecraft:lapis_lazuli", "minecraft:prismarine_crystals", "minecraft:quartz", "ae2:certus_quartz_crystal", "ae2:charged_certus_quartz_crystal", "ae2:fluix_crystal", "mekanism:fluorite_gem"]}, {"id": "tconstruct:casts/single_use/coin", "items": ["tconstruct:coin_sand_cast", "tconstruct:coin_red_sand_cast"]}, {"id": "minecraft:piglin_repellents", "items": ["minecraft:soul_torch", "minecraft:soul_lantern", "minecraft:soul_campfire", "tconstruct:zombified_piglin_head"]}, {"id": "forge:plates/cardboard", "items": ["create:cardboard"]}, {"id": "forge:ores/zinc", "items": ["create:zinc_ore", "create:deepslate_zinc_ore"]}, {"id": "forge:slimeball/ender", "items": ["tconstruct:ender_slime_ball"]}, {"id": "ae2:metal_ingots", "items": ["minecraft:copper_ingot", "mekanism:ingot_tin", "minecraft:iron_ingot", "minecraft:gold_ingot", "create:brass_ingot", "immersiveengineering:ingot_nickel"]}, {"id": "tconstruct:casts/single_use/tough_binding", "items": ["tconstruct:tough_binding_sand_cast", "tconstruct:tough_binding_red_sand_cast"]}, {"id": "minecraft:non_flammable_wood", "items": ["minecraft:warped_stem", "minecraft:stripped_warped_stem", "minecraft:warped_hyphae", "minecraft:stripped_warped_hyphae", "minecraft:crimson_stem", "minecraft:stripped_crimson_stem", "minecraft:crimson_hyphae", "minecraft:stripped_crimson_hyphae", "minecraft:crimson_planks", "minecraft:warped_planks", "minecraft:crimson_slab", "minecraft:warped_slab", "minecraft:crimson_pressure_plate", "minecraft:warped_pressure_plate", "minecraft:crimson_fence", "minecraft:warped_fence", "minecraft:crimson_trapdoor", "minecraft:warped_trapdoor", "minecraft:crimson_fence_gate", "minecraft:warped_fence_gate", "minecraft:crimson_stairs", "minecraft:warped_stairs", "minecraft:crimson_button", "minecraft:warped_button", "minecraft:crimson_door", "minecraft:warped_door", "minecraft:crimson_sign", "minecraft:warped_sign", "minecraft:warped_hanging_sign", "minecraft:crimson_hanging_sign", "cataclysm:chorus_planks", "cataclysm:chorus_slab", "cataclysm:chorus_stairs", "cataclysm:chorus_fence", "tconstruct:greenheart_planks", "tconstruct:greenheart_planks_slab", "tconstruct:greenheart_planks_stairs", "tconstruct:greenheart_fence", "tconstruct:greenheart_fence_gate", "tconstruct:greenheart_door", "tconstruct:greenheart_trapdoor", "tconstruct:greenheart_pressure_plate", "tconstruct:greenheart_button", "tconstruct:greenheart_log", "tconstruct:stripped_greenheart_log", "tconstruct:greenheart_wood", "tconstruct:stripped_greenheart_wood", "tconstruct:skyroot_planks", "tconstruct:skyroot_planks_slab", "tconstruct:skyroot_planks_stairs", "tconstruct:skyroot_fence", "tconstruct:skyroot_fence_gate", "tconstruct:skyroot_door", "tconstruct:skyroot_trapdoor", "tconstruct:skyroot_pressure_plate", "tconstruct:skyroot_button", "tconstruct:skyroot_log", "tconstruct:stripped_skyroot_log", "tconstruct:skyroot_wood", "tconstruct:stripped_skyroot_wood", "tconstruct:bloodshroom_planks", "tconstruct:bloodshroom_planks_slab", "tconstruct:bloodshroom_planks_stairs", "tconstruct:bloodshroom_fence", "tconstruct:bloodshroom_fence_gate", "tconstruct:bloodshroom_door", "tconstruct:bloodshroom_trapdoor", "tconstruct:bloodshroom_pressure_plate", "tconstruct:bloodshroom_button", "tconstruct:bloodshroom_log", "tconstruct:stripped_bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:stripped_bloodshroom_wood", "tconstruct:enderbark_planks", "tconstruct:enderbark_planks_slab", "tconstruct:enderbark_planks_stairs", "tconstruct:enderbark_fence", "tconstruct:enderbark_fence_gate", "tconstruct:enderbark_door", "tconstruct:enderbark_trapdoor", "tconstruct:enderbark_pressure_plate", "tconstruct:enderbark_button", "tconstruct:enderbark_log", "tconstruct:stripped_enderbark_log", "tconstruct:enderbark_wood", "tconstruct:stripped_enderbark_wood"]}, {"id": "mekanism:clumps/copper", "items": ["mekanism:clump_copper"]}, {"id": "tconstruct:melting/lead/tools_costing_2", "items": []}, {"id": "tconstruct:enderbark_logs", "items": ["tconstruct:enderbark_log", "tconstruct:stripped_enderbark_log", "tconstruct:enderbark_wood", "tconstruct:stripped_enderbark_wood"]}, {"id": "forge:dusts/tin", "items": ["mekanism:dust_tin"]}, {"id": "mekanism:enriched", "items": ["mekanism:enriched_carbon", "mekanism:enriched_diamond", "mekanism:enriched_refined_obsidian", "mekanism:enriched_redstone", "mekanism:enriched_gold", "mekanism:enriched_tin"]}, {"id": "create:upgrade_aquatic/coral", "items": []}, {"id": "forge:sheetmetals/lead", "items": ["immersiveengineering:sheetmetal_lead"]}, {"id": "tconstruct:melting/diamond/tools_costing_1", "items": ["minecraft:diamond_shovel"]}, {"id": "tconstruct:modifiable/shields", "items": ["tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "tconstruct:casts/single_use/broad_blade", "items": ["tconstruct:broad_blade_sand_cast", "tconstruct:broad_blade_red_sand_cast"]}, {"id": "create:stone_types/scoria", "items": ["create:cut_scoria", "create:cut_scoria_stairs", "create:cut_scoria_wall", "create:polished_cut_scoria", "create:polished_cut_scoria_stairs", "create:polished_cut_scoria_wall", "create:cut_scoria_bricks", "create:cut_scoria_brick_stairs", "create:cut_scoria_brick_wall", "create:small_scoria_bricks", "create:small_scoria_brick_stairs", "create:small_scoria_brick_wall", "create:layered_scoria", "create:scoria_pillar", "create:scoria"]}, {"id": "forge:storage_blocks/uncolored_sandstone", "items": ["minecraft:sandstone", "minecraft:chiseled_sandstone", "minecraft:cut_sandstone", "minecraft:smooth_sandstone"]}, {"id": "forge:glass_panes/orange", "items": ["minecraft:orange_stained_glass_pane", "tconstruct:orange_clear_stained_glass_pane"]}, {"id": "forge:storage_blocks/refined_glowstone", "items": ["mekanism:block_refined_glowstone"]}, {"id": "ae2:covered_dense_cable", "items": ["ae2:white_covered_dense_cable", "ae2:orange_covered_dense_cable", "ae2:magenta_covered_dense_cable", "ae2:light_blue_covered_dense_cable", "ae2:yellow_covered_dense_cable", "ae2:lime_covered_dense_cable", "ae2:pink_covered_dense_cable", "ae2:gray_covered_dense_cable", "ae2:light_gray_covered_dense_cable", "ae2:cyan_covered_dense_cable", "ae2:purple_covered_dense_cable", "ae2:blue_covered_dense_cable", "ae2:brown_covered_dense_cable", "ae2:green_covered_dense_cable", "ae2:red_covered_dense_cable", "ae2:black_covered_dense_cable", "ae2:fluix_covered_dense_cable"]}, {"id": "tconstruct:modifiable/armor/worn", "items": ["tconstruct:travelers_boots", "tconstruct:plate_boots", "tconstruct:slime_boots", "tconstruct:travelers_leggings", "tconstruct:plate_leggings", "tconstruct:slime_leggings", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate", "tconstruct:travelers_helmet", "tconstruct:plate_helmet", "tconstruct:slime_helmet"]}, {"id": "minecraft:planks", "items": ["minecraft:oak_planks", "minecraft:spruce_planks", "minecraft:birch_planks", "minecraft:jungle_planks", "minecraft:acacia_planks", "minecraft:dark_oak_planks", "minecraft:crimson_planks", "minecraft:warped_planks", "minecraft:mangrove_planks", "minecraft:bamboo_planks", "minecraft:cherry_planks", "cataclysm:chorus_planks", "immersiveengineering:fiberboard", "tconstruct:greenheart_planks", "tconstruct:skyroot_planks", "tconstruct:bloodshroom_planks", "tconstruct:enderbark_planks"]}, {"id": "tconstruct:modifiable/melee", "items": ["tconstruct:pickaxe", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:kama", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:flint_and_brick", "tconstruct:war_pick", "tconstruct:sledge_hammer", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate"]}, {"id": "tconstruct:melting/iron/tools_costing_3", "items": ["minecraft:iron_pickaxe", "minecraft:iron_axe"]}, {"id": "c:blue_dyes", "items": ["minecraft:blue_dye"]}, {"id": "forge:dusts/diamond", "items": ["mekanism:dust_diamond"]}, {"id": "forge:nuggets/uranium", "items": ["immersiveengineering:nugget_uranium", "mekanism:nugget_uranium"]}, {"id": "forge:cobblestone/deepslate", "items": ["minecraft:cobbled_deepslate"]}, {"id": "minecraft:spruce_logs", "items": ["minecraft:spruce_log", "minecraft:spruce_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood"]}, {"id": "tconstruct:casts/single_use/tool_handle", "items": ["tconstruct:tool_handle_sand_cast", "tconstruct:tool_handle_red_sand_cast"]}, {"id": "forge:ingots/hop_graphite", "items": ["immersiveengineering:ingot_hop_graphite"]}, {"id": "tconstruct:smeltery", "items": ["tconstruct:seared_stone", "tconstruct:seared_cracked_bricks", "tconstruct:seared_cobble", "tconstruct:seared_paver", "tconstruct:seared_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks", "tconstruct:seared_fuel_tank", "tconstruct:seared_fuel_gauge", "tconstruct:seared_ingot_tank", "tconstruct:seared_ingot_gauge", "tconstruct:smeltery_controller", "tconstruct:seared_ladder", "tconstruct:seared_drain", "tconstruct:seared_chute", "tconstruct:seared_duct", "tconstruct:seared_glass", "tconstruct:seared_soul_glass", "tconstruct:seared_tinted_glass"]}, {"id": "forge:storage_blocks/gold", "items": ["minecraft:gold_block"]}, {"id": "forge:chests/trapped", "items": ["minecraft:trapped_chest"]}, {"id": "forge:dyes/blue", "items": ["minecraft:blue_dye"]}, {"id": "minecraft:redstone_ores", "items": ["minecraft:redstone_ore", "minecraft:deepslate_redstone_ore"]}, {"id": "forge:storage_blocks/lapis", "items": ["minecraft:lapis_block"]}, {"id": "forge:clay", "items": ["minecraft:clay_ball"]}, {"id": "tconstruct:casts/single_use/helmet_plating", "items": ["tconstruct:helmet_plating_sand_cast", "tconstruct:helmet_plating_red_sand_cast"]}, {"id": "mekanism:dirty_dusts/copper", "items": ["mekanism:dirty_dust_copper"]}, {"id": "forge:ores/silver", "items": ["immersiveengineering:ore_silver", "immersiveengineering:deepslate_ore_silver"]}, {"id": "forge:rods", "items": ["minecraft:blaze_rod", "minecraft:stick", "immersiveengineering:stick_treated", "mekanism:hdpe_stick"]}, {"id": "forge:glass/colorless", "items": ["minecraft:glass", "create:tiled_glass", "create:framed_glass", "create:horizontal_framed_glass", "create:vertical_framed_glass", "tconstruct:clear_glass"]}, {"id": "forge:obsidian", "items": ["minecraft:obsidian"]}, {"id": "c:red_sandstone_blocks", "items": ["minecraft:red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:cut_red_sandstone", "minecraft:smooth_red_sandstone"]}, {"id": "forge:ingots/netherite", "items": ["minecraft:netherite_ingot"]}, {"id": "forge:glass_panes/gray", "items": ["minecraft:gray_stained_glass_pane", "tconstruct:gray_clear_stained_glass_pane"]}, {"id": "minecraft:bookshelf_books", "items": ["minecraft:book", "minecraft:written_book", "minecraft:enchanted_book", "minecraft:writable_book", "minecraft:knowledge_book", "immersiveengineering:manual", "tconstruct:materials_and_you", "tconstruct:tinkers_gadgetry", "tconstruct:puny_smelting", "tconstruct:mighty_smelting", "tconstruct:fantastic_foundry", "tconstruct:encyclopedia"]}, {"id": "mekanism:enriched/carbon", "items": ["mekanism:enriched_carbon"]}, {"id": "forge:plates/gold", "items": ["immersiveengineering:plate_gold", "create:golden_sheet"]}, {"id": "minecraft:saplings", "items": ["minecraft:oak_sapling", "minecraft:spruce_sapling", "minecraft:birch_sapling", "minecraft:jungle_sapling", "minecraft:acacia_sapling", "minecraft:dark_oak_sapling", "minecraft:azalea", "minecraft:flowering_azalea", "minecraft:mangrove_propagule", "minecraft:cherry_sapling", "tconstruct:earth_slime_sapling", "tconstruct:sky_slime_sapling", "tconstruct:ender_slime_sapling"]}, {"id": "c:spears", "items": ["minecraft:trident"]}, {"id": "tconstruct:melting/electrum/tools_costing_1", "items": []}, {"id": "forge:raw_materials", "items": ["minecraft:raw_copper", "minecraft:raw_gold", "minecraft:raw_iron", "immersiveengineering:raw_aluminum", "immersiveengineering:raw_lead", "mekanism:raw_lead", "immersiveengineering:raw_silver", "immersiveengineering:raw_nickel", "immersiveengineering:raw_uranium", "mekanism:raw_uranium", "create:raw_zinc", "mekanism:raw_osmium", "mekanism:raw_tin", "tconstruct:raw_cobalt"]}, {"id": "minecraft:wooden_buttons", "items": ["minecraft:oak_button", "minecraft:spruce_button", "minecraft:birch_button", "minecraft:jungle_button", "minecraft:acacia_button", "minecraft:dark_oak_button", "minecraft:crimson_button", "minecraft:warped_button", "minecraft:mangrove_button", "minecraft:bamboo_button", "minecraft:cherry_button", "tconstruct:greenheart_button", "tconstruct:skyroot_button", "tconstruct:bloodshroom_button", "tconstruct:enderbark_button"]}, {"id": "forge:ores_in_ground/deepslate", "items": ["minecraft:deepslate_coal_ore", "minecraft:deepslate_copper_ore", "minecraft:deepslate_diamond_ore", "minecraft:deepslate_emerald_ore", "minecraft:deepslate_gold_ore", "minecraft:deepslate_iron_ore", "minecraft:deepslate_lapis_ore", "minecraft:deepslate_redstone_ore", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:deepslate_ore_lead", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:deepslate_ore_uranium", "create:deepslate_zinc_ore", "mekanism:deepslate_tin_ore", "mekanism:deepslate_osmium_ore", "mekanism:deepslate_uranium_ore", "mekanism:deepslate_fluorite_ore", "mekanism:deepslate_lead_ore"]}, {"id": "tconstruct:casts/single_use/hammer_head", "items": ["tconstruct:hammer_head_sand_cast", "tconstruct:hammer_head_red_sand_cast"]}, {"id": "forge:circuits/elite", "items": ["mekanism:elite_control_circuit"]}, {"id": "mekanism:alloys/atomic", "items": ["mekanism:alloy_atomic"]}, {"id": "c:coal", "items": ["minecraft:coal", "minecraft:charcoal", "immersiveengineering:coal_coke"]}, {"id": "tconstruct:modifiable/wandering_trader", "items": ["tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher"]}, {"id": "minecraft:stone_crafting_materials", "items": ["minecraft:cobblestone", "minecraft:blackstone", "minecraft:cobbled_deepslate"]}, {"id": "forge:glass_panes/blue", "items": ["minecraft:blue_stained_glass_pane", "tconstruct:blue_clear_stained_glass_pane"]}, {"id": "tconstruct:casts/single_use/large_plate", "items": ["tconstruct:large_plate_sand_cast", "tconstruct:large_plate_red_sand_cast"]}, {"id": "create:stone_types/dripstone", "items": ["create:cut_dripstone", "create:cut_dripstone_stairs", "create:cut_dripstone_wall", "create:polished_cut_dripstone", "create:polished_cut_dripstone_stairs", "create:polished_cut_dripstone_wall", "create:cut_dripstone_bricks", "create:cut_dripstone_brick_stairs", "create:cut_dripstone_brick_wall", "create:small_dripstone_bricks", "create:small_dripstone_brick_stairs", "create:small_dripstone_brick_wall", "create:layered_dripstone", "create:dripstone_pillar", "minecraft:dripstone_block"]}, {"id": "forge:gems/fluix", "items": ["ae2:fluix_crystal"]}, {"id": "ae2:smart_dense_cable", "items": ["ae2:white_smart_dense_cable", "ae2:orange_smart_dense_cable", "ae2:magenta_smart_dense_cable", "ae2:light_blue_smart_dense_cable", "ae2:yellow_smart_dense_cable", "ae2:lime_smart_dense_cable", "ae2:pink_smart_dense_cable", "ae2:gray_smart_dense_cable", "ae2:light_gray_smart_dense_cable", "ae2:cyan_smart_dense_cable", "ae2:purple_smart_dense_cable", "ae2:blue_smart_dense_cable", "ae2:brown_smart_dense_cable", "ae2:green_smart_dense_cable", "ae2:red_smart_dense_cable", "ae2:black_smart_dense_cable", "ae2:fluix_smart_dense_cable"]}, {"id": "forge:armors", "items": ["minecraft:leather_helmet", "minecraft:turtle_helmet", "minecraft:chainmail_helmet", "minecraft:iron_helmet", "minecraft:golden_helmet", "minecraft:diamond_helmet", "minecraft:netherite_helmet", "immersiveengineering:armor_steel_helmet", "immersiveengineering:armor_faraday_helmet", "create:copper_diving_helmet", "create:netherite_diving_helmet", "mekanism:hazmat_mask", "tconstruct:travelers_helmet", "tconstruct:plate_helmet", "tconstruct:slime_helmet", "minecraft:leather_chestplate", "minecraft:chainmail_chestplate", "minecraft:iron_chestplate", "minecraft:golden_chestplate", "minecraft:diamond_chestplate", "minecraft:netherite_chestplate", "immersiveengineering:armor_steel_chestplate", "immersiveengineering:armor_faraday_chestplate", "create:copper_backtank", "create:netherite_backtank", "mekanism:hazmat_gown", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate", "minecraft:leather_leggings", "minecraft:chainmail_leggings", "minecraft:iron_leggings", "minecraft:golden_leggings", "minecraft:diamond_leggings", "minecraft:netherite_leggings", "immersiveengineering:armor_steel_leggings", "immersiveengineering:armor_faraday_leggings", "create:cardboard_leggings", "mekanism:hazmat_pants", "tconstruct:travelers_leggings", "tconstruct:plate_leggings", "tconstruct:slime_leggings", "minecraft:leather_boots", "minecraft:chainmail_boots", "minecraft:iron_boots", "minecraft:golden_boots", "minecraft:diamond_boots", "minecraft:netherite_boots", "immersiveengineering:armor_steel_boots", "immersiveengineering:armor_faraday_boots", "create:copper_diving_boots", "create:netherite_diving_boots", "create:cardboard_boots", "mekanism:hazmat_boots", "tconstruct:travelers_boots", "tconstruct:plate_boots", "tconstruct:slime_boots"]}, {"id": "tconstruct:melting/constantan/tools_costing_2", "items": []}, {"id": "forge:storage_blocks/osmium", "items": ["mekanism:block_osmium"]}, {"id": "forge:dyes/pink", "items": ["minecraft:pink_dye"]}, {"id": "forge:slimeball/earth", "items": ["minecraft:slime_ball"]}, {"id": "forge:nuggets/manyullyn", "items": ["tconstruct:manyullyn_nugget"]}, {"id": "c:ingots", "items": ["minecraft:brick", "minecraft:copper_ingot", "minecraft:gold_ingot", "minecraft:iron_ingot", "minecraft:netherite_ingot", "minecraft:nether_brick", "immersiveengineering:ingot_aluminum", "immersiveengineering:ingot_lead", "mekanism:ingot_lead", "immersiveengineering:ingot_silver", "immersiveengineering:ingot_nickel", "immersiveengineering:ingot_uranium", "mekanism:ingot_uranium", "immersiveengineering:ingot_constantan", "immersiveengineering:ingot_electrum", "immersiveengineering:ingot_steel", "mekanism:ingot_steel", "tconstruct:steel_ingot", "create:andesite_alloy", "create:zinc_ingot", "create:brass_ingot", "mekanism:ingot_osmium", "mekanism:ingot_tin", "mekanism:ingot_bronze", "mekanism:ingot_refined_glowstone", "mekanism:ingot_refined_obsidian", "tconstruct:seared_brick", "tconstruct:scorched_brick", "minecraft:netherite_scrap", "tconstruct:cobalt_ingot", "tconstruct:slimesteel_ingot", "tconstruct:amethyst_bronze_ingot", "tconstruct:rose_gold_ingot", "tconstruct:pig_iron_ingot", "tconstruct:cinderslime_ingot", "tconstruct:queens_slime_ingot", "tconstruct:manyullyn_ingot", "tconstruct:hepati<PERSON>_ingot", "tconstruct:soulsteel_ingot", "tconstruct:<PERSON><PERSON><PERSON>_ingot"]}, {"id": "ae2:paint_balls", "items": ["ae2:white_paint_ball", "ae2:orange_paint_ball", "ae2:magenta_paint_ball", "ae2:light_blue_paint_ball", "ae2:yellow_paint_ball", "ae2:lime_paint_ball", "ae2:pink_paint_ball", "ae2:gray_paint_ball", "ae2:light_gray_paint_ball", "ae2:cyan_paint_ball", "ae2:purple_paint_ball", "ae2:blue_paint_ball", "ae2:brown_paint_ball", "ae2:green_paint_ball", "ae2:red_paint_ball", "ae2:black_paint_ball"]}, {"id": "forge:plates/steel", "items": ["immersiveengineering:plate_steel"]}, {"id": "forge:raw_materials/gold", "items": ["minecraft:raw_gold"]}, {"id": "tconstruct:melting/osmium/tools_costing_2", "items": []}, {"id": "forge:fence_gates/wooden", "items": ["minecraft:oak_fence_gate", "minecraft:spruce_fence_gate", "minecraft:birch_fence_gate", "minecraft:jungle_fence_gate", "minecraft:acacia_fence_gate", "minecraft:dark_oak_fence_gate", "minecraft:crimson_fence_gate", "minecraft:warped_fence_gate", "minecraft:mangrove_fence_gate", "minecraft:bamboo_fence_gate", "minecraft:cherry_fence_gate", "tconstruct:greenheart_fence_gate", "tconstruct:skyroot_fence_gate", "tconstruct:bloodshroom_fence_gate", "tconstruct:enderbark_fence_gate"]}, {"id": "forge:ores/osmium", "items": ["mekanism:osmium_ore", "mekanism:deepslate_osmium_ore"]}, {"id": "minecraft:villager_plantable_seeds", "items": ["minecraft:wheat_seeds", "minecraft:potato", "minecraft:carrot", "minecraft:beetroot_seeds", "minecraft:torchflower_seeds", "minecraft:pitcher_pod"]}, {"id": "tconstruct:melting/diamond/tools_costing_3", "items": ["minecraft:diamond_pickaxe", "minecraft:diamond_axe"]}, {"id": "forge:ingots/bronze", "items": ["mekanism:ingot_bronze"]}, {"id": "minecraft:doors", "items": ["minecraft:oak_door", "minecraft:spruce_door", "minecraft:birch_door", "minecraft:jungle_door", "minecraft:acacia_door", "minecraft:dark_oak_door", "minecraft:crimson_door", "minecraft:warped_door", "minecraft:mangrove_door", "minecraft:bamboo_door", "minecraft:cherry_door", "tconstruct:greenheart_door", "tconstruct:skyroot_door", "tconstruct:bloodshroom_door", "tconstruct:enderbark_door", "minecraft:iron_door", "create:andesite_door", "create:brass_door", "create:copper_door", "create:train_door", "create:framed_glass_door"]}, {"id": "ae2:quartz_shovel", "items": ["ae2:certus_quartz_shovel", "ae2:nether_quartz_shovel"]}, {"id": "balm:purple_dyes", "items": ["minecraft:purple_dye"]}, {"id": "ae2:all_quartz", "items": ["minecraft:quartz", "ae2:certus_quartz_crystal", "ae2:charged_certus_quartz_crystal"]}, {"id": "tconstruct:stoneshields", "items": ["minecraft:andesite", "minecraft:diorite", "minecraft:granite", "minecraft:infested_stone", "minecraft:stone", "minecraft:polished_andesite", "minecraft:polished_diorite", "minecraft:polished_granite", "minecraft:deepslate", "minecraft:polished_deepslate", "minecraft:infested_deepslate", "minecraft:tuff", "minecraft:cobblestone", "minecraft:infested_cobblestone", "minecraft:mossy_cobblestone", "minecraft:cobbled_deepslate", "minecraft:sandstone", "minecraft:cut_sandstone", "minecraft:chiseled_sandstone", "minecraft:smooth_sandstone", "minecraft:red_sandstone", "minecraft:cut_red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:smooth_red_sandstone", "minecraft:end_stone", "minecraft:gravel", "immersiveengineering:slag_gravel", "minecraft:netherrack", "minecraft:basalt", "minecraft:polished_basalt", "minecraft:blackstone", "minecraft:polished_blackstone"]}, {"id": "create:stone_types/veridium", "items": ["create:cut_veridium", "create:cut_veridium_stairs", "create:cut_veridium_wall", "create:polished_cut_veridium", "create:polished_cut_veridium_stairs", "create:polished_cut_veridium_wall", "create:cut_veridium_bricks", "create:cut_veridium_brick_stairs", "create:cut_veridium_brick_wall", "create:small_veridium_bricks", "create:small_veridium_brick_stairs", "create:small_veridium_brick_wall", "create:layered_veridium", "create:veridium_pillar", "create:veridium"]}, {"id": "forge:ores/gold", "items": ["minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore"]}, {"id": "tconstruct:casts/single_use/ingot", "items": ["tconstruct:ingot_sand_cast", "tconstruct:ingot_red_sand_cast"]}, {"id": "tconstruct:modifiable/armor/leggings", "items": ["tconstruct:travelers_leggings", "tconstruct:plate_leggings", "tconstruct:slime_leggings"]}, {"id": "tconstruct:melting/refined_glowstone/tools_costing_7", "items": []}, {"id": "forge:storage_blocks/coal_coke", "items": ["immersiveengineering:coke"]}, {"id": "balm:white_dyes", "items": ["minecraft:white_dye"]}, {"id": "forge:slimeballs", "items": ["minecraft:slime_ball", "tconstruct:sky_slime_ball", "tconstruct:ichor_slime_ball", "tconstruct:ender_slime_ball"]}, {"id": "minecraft:cluster_max_harvestables", "items": ["minecraft:diamond_pickaxe", "minecraft:golden_pickaxe", "minecraft:iron_pickaxe", "minecraft:netherite_pickaxe", "minecraft:stone_pickaxe", "minecraft:wooden_pickaxe", "immersiveengineering:pickaxe_steel", "mekanism:atomic_disassembler", "mekanism:meka_tool", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:war_pick"]}, {"id": "forge:seeds/wheat", "items": ["minecraft:wheat_seeds"]}, {"id": "create:toolboxes", "items": ["create:white_toolbox", "create:orange_toolbox", "create:magenta_toolbox", "create:light_blue_toolbox", "create:yellow_toolbox", "create:lime_toolbox", "create:pink_toolbox", "create:gray_toolbox", "create:light_gray_toolbox", "create:cyan_toolbox", "create:purple_toolbox", "create:blue_toolbox", "create:brown_toolbox", "create:green_toolbox", "create:red_toolbox", "create:black_toolbox"]}, {"id": "forge:raw_materials/nickel", "items": ["immersiveengineering:raw_nickel"]}, {"id": "forge:feathers", "items": ["minecraft:feather"]}, {"id": "minecraft:stairs", "items": ["minecraft:oak_stairs", "minecraft:spruce_stairs", "minecraft:birch_stairs", "minecraft:jungle_stairs", "minecraft:acacia_stairs", "minecraft:dark_oak_stairs", "minecraft:crimson_stairs", "minecraft:warped_stairs", "minecraft:mangrove_stairs", "minecraft:bamboo_stairs", "minecraft:cherry_stairs", "cataclysm:chorus_stairs", "immersiveengineering:stairs_treated_wood_horizontal", "immersiveengineering:stairs_treated_wood_vertical", "immersiveengineering:stairs_treated_wood_packaged", "tconstruct:greenheart_planks_stairs", "tconstruct:skyroot_planks_stairs", "tconstruct:bloodshroom_planks_stairs", "tconstruct:enderbark_planks_stairs", "minecraft:bamboo_mosaic_stairs", "minecraft:cobblestone_stairs", "minecraft:sandstone_stairs", "minecraft:nether_brick_stairs", "minecraft:stone_brick_stairs", "minecraft:brick_stairs", "minecraft:purpur_stairs", "minecraft:quartz_stairs", "minecraft:red_sandstone_stairs", "minecraft:prismarine_brick_stairs", "minecraft:prismarine_stairs", "minecraft:dark_prismarine_stairs", "minecraft:polished_granite_stairs", "minecraft:smooth_red_sandstone_stairs", "minecraft:mossy_stone_brick_stairs", "minecraft:polished_diorite_stairs", "minecraft:mossy_cobblestone_stairs", "minecraft:end_stone_brick_stairs", "minecraft:stone_stairs", "minecraft:smooth_sandstone_stairs", "minecraft:smooth_quartz_stairs", "minecraft:granite_stairs", "minecraft:andesite_stairs", "minecraft:red_nether_brick_stairs", "minecraft:polished_andesite_stairs", "minecraft:diorite_stairs", "minecraft:blackstone_stairs", "minecraft:polished_blackstone_brick_stairs", "minecraft:polished_blackstone_stairs", "minecraft:cobbled_deepslate_stairs", "minecraft:polished_deepslate_stairs", "minecraft:deepslate_tile_stairs", "minecraft:deepslate_brick_stairs", "minecraft:oxidized_cut_copper_stairs", "minecraft:weathered_cut_copper_stairs", "minecraft:exposed_cut_copper_stairs", "minecraft:cut_copper_stairs", "minecraft:waxed_weathered_cut_copper_stairs", "minecraft:waxed_exposed_cut_copper_stairs", "minecraft:waxed_cut_copper_stairs", "minecraft:waxed_oxidized_cut_copper_stairs", "minecraft:mud_brick_stairs", "cataclysm:obsidian_brick_stairs", "cataclysm:polished_end_stone_stairs", "immersiveengineering:stairs_concrete_leaded", "immersiveengineering:stairs_steel_scaffolding_wooden_top", "immersiveengineering:stairs_hempcrete", "immersiveengineering:stairs_hempcrete_brick", "immersiveengineering:stairs_alu_scaffolding_wooden_top", "immersiveengineering:stairs_clinker_brick", "immersiveengineering:stairs_concrete", "immersiveengineering:stairs_concrete_tile", "immersiveengineering:stairs_alu_scaffolding_standard", "immersiveengineering:stairs_slag_brick", "immersiveengineering:stairs_concrete_brick", "immersiveengineering:stairs_steel_scaffolding_standard", "immersiveengineering:stairs_alu_scaffolding_grate_top", "immersiveengineering:stairs_steel_scaffolding_grate_top", "create:cut_granite_stairs", "create:polished_cut_granite_stairs", "create:cut_granite_brick_stairs", "create:small_granite_brick_stairs", "create:cut_diorite_stairs", "create:polished_cut_diorite_stairs", "create:cut_diorite_brick_stairs", "create:small_diorite_brick_stairs", "create:cut_andesite_stairs", "create:polished_cut_andesite_stairs", "create:cut_andesite_brick_stairs", "create:small_andesite_brick_stairs", "create:cut_calcite_stairs", "create:polished_cut_calcite_stairs", "create:cut_calcite_brick_stairs", "create:small_calcite_brick_stairs", "create:cut_dripstone_stairs", "create:polished_cut_dripstone_stairs", "create:cut_dripstone_brick_stairs", "create:small_dripstone_brick_stairs", "create:cut_deepslate_stairs", "create:polished_cut_deepslate_stairs", "create:cut_deepslate_brick_stairs", "create:small_deepslate_brick_stairs", "create:cut_tuff_stairs", "create:polished_cut_tuff_stairs", "create:cut_tuff_brick_stairs", "create:small_tuff_brick_stairs", "create:cut_asurine_stairs", "create:polished_cut_asurine_stairs", "create:cut_asurine_brick_stairs", "create:small_asurine_brick_stairs", "create:cut_crimsite_stairs", "create:polished_cut_crimsite_stairs", "create:cut_crimsite_brick_stairs", "create:small_crimsite_brick_stairs", "create:cut_limestone_stairs", "create:polished_cut_limestone_stairs", "create:cut_limestone_brick_stairs", "create:small_limestone_brick_stairs", "create:cut_ochrum_stairs", "create:polished_cut_ochrum_stairs", "create:cut_ochrum_brick_stairs", "create:small_ochrum_brick_stairs", "create:cut_scoria_stairs", "create:polished_cut_scoria_stairs", "create:cut_scoria_brick_stairs", "create:small_scoria_brick_stairs", "create:cut_scorchia_stairs", "create:polished_cut_scorchia_stairs", "create:cut_scorchia_brick_stairs", "create:small_scorchia_brick_stairs", "create:cut_veridium_stairs", "create:polished_cut_veridium_stairs", "create:cut_veridium_brick_stairs", "create:small_veridium_brick_stairs"]}, {"id": "minecraft:hanging_signs", "items": ["minecraft:oak_hanging_sign", "minecraft:spruce_hanging_sign", "minecraft:birch_hanging_sign", "minecraft:acacia_hanging_sign", "minecraft:cherry_hanging_sign", "minecraft:jungle_hanging_sign", "minecraft:dark_oak_hanging_sign", "minecraft:crimson_hanging_sign", "minecraft:warped_hanging_sign", "minecraft:mangrove_hanging_sign", "minecraft:bamboo_hanging_sign", "tconstruct:greenheart_hanging_sign", "tconstruct:skyroot_hanging_sign", "tconstruct:bloodshroom_hanging_sign", "tconstruct:enderbark_hanging_sign"]}, {"id": "tconstruct:modifiable/ranged", "items": ["tconstruct:swasher", "tconstruct:longbow", "tconstruct:crossbow", "tconstruct:war_pick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan"]}, {"id": "cataclysm:explosion_immune_item", "items": ["cataclysm:wither_assault_shoulder_weapon", "cataclysm:void_assault_shoulder_weapon", "cataclysm:void_core", "cataclysm:infernal_forge", "cataclysm:void_forge", "cataclysm:ignitium_helmet", "cataclysm:ignitium_chestplate", "cataclysm:ignitium_elytra_chestplate", "cataclysm:ignitium_leggings", "cataclysm:ignitium_boots", "cataclysm:cursium_helmet", "cataclysm:cursium_chestplate", "cataclysm:cursium_leggings", "cataclysm:cursium_boots", "cataclysm:bulwark_of_the_flame", "cataclysm:the_incinerator", "cataclysm:gauntlet_of_guard", "cataclysm:gauntlet_of_bulwark", "cataclysm:ignitium_ingot", "cataclysm:cursium_ingot", "cataclysm:cursium_block", "cataclysm:ignitium_block", "cataclysm:witherite_ingot", "cataclysm:witherite_block", "cataclysm:monstrous_horn", "cataclysm:burning_ashes", "cataclysm:monstrous_helm", "cataclysm:music_disc_netherite_monstrosity", "cataclysm:music_disc_ender_guardian", "cataclysm:music_disc_ignis", "cataclysm:music_disc_the_harbinger", "cataclysm:music_disc_maledictus", "cataclysm:music_disc_ancient_remnant", "cataclysm:music_disc_scylla", "cataclysm:music_disc_the_leviathan", "cataclysm:mechanical_fusion_anvil", "cataclysm:abyssal_egg", "cataclysm:tidal_claws", "cataclysm:sandstorm_in_a_bottle", "cataclysm:remnant_skull", "cataclysm:netherite_effigy", "cataclysm:the_baby_leviathan_bucket", "cataclysm:modern_remnant_bucket", "cataclysm:soul_render", "cataclysm:the_annihilator", "cataclysm:cursed_bow", "cataclysm:astrape", "cataclysm:cera<PERSON>us", "cataclysm:lava_power_cell", "cataclysm:netherite_ministrosity_bucket", "cataclysm:strange_key"]}, {"id": "cataclysm:ministrosity_blacklist", "items": ["cataclysm:netherite_ministrosity_bucket", "minecraft:shulker_box", "minecraft:black_shulker_box", "minecraft:blue_shulker_box", "minecraft:brown_shulker_box", "minecraft:cyan_shulker_box", "minecraft:gray_shulker_box", "minecraft:green_shulker_box", "minecraft:light_blue_shulker_box", "minecraft:light_gray_shulker_box", "minecraft:lime_shulker_box", "minecraft:magenta_shulker_box", "minecraft:orange_shulker_box", "minecraft:pink_shulker_box", "minecraft:purple_shulker_box", "minecraft:red_shulker_box", "minecraft:white_shulker_box", "minecraft:yellow_shulker_box"]}, {"id": "forge:glass_panes/lime", "items": ["minecraft:lime_stained_glass_pane", "tconstruct:lime_clear_stained_glass_pane"]}, {"id": "forge:wires/aluminum", "items": ["immersiveengineering:wire_aluminum"]}, {"id": "tconstruct:slimy_leaves", "items": ["tconstruct:earth_slime_leaves", "tconstruct:sky_slime_leaves", "tconstruct:ender_slime_leaves"]}, {"id": "forge:nuggets/soulsteel", "items": ["tconstruct:soulsteel_nugget"]}, {"id": "c:gold_ingots", "items": ["minecraft:gold_ingot"]}, {"id": "alexscaves:ferromagnetic_items", "items": ["cataclysm:ignitium_block", "cataclysm:witherite_block", "cataclysm:ignitium_ingot", "cataclysm:witherite_ingot", "cataclysm:monstrous_helm", "cataclysm:ignitium_helmet", "cataclysm:ignitium_chestplate", "cataclysm:ignitium_elytra_chestplate", "cataclysm:ignitium_leggings", "cataclysm:ignitium_boots", "cataclysm:the_incinerator", "cataclysm:bulwark_of_the_flame", "cataclysm:meat_shredder", "cataclysm:laser_gatling", "cataclysm:wither_assault_shoulder_weapon", "cataclysm:void_assault_shoulder_weapon", "cataclysm:infernal_forge", "cataclysm:void_forge"]}, {"id": "balm:gray_dyes", "items": ["minecraft:gray_dye"]}, {"id": "ae2:p2p_attunements/fluid_p2p_tunnel", "items": ["minecraft:bucket", "minecraft:milk_bucket", "minecraft:water_bucket", "minecraft:lava_bucket"]}, {"id": "minecraft:arrows", "items": ["minecraft:arrow", "minecraft:tipped_arrow", "minecraft:spectral_arrow", "cataclysm:void_scatter_arrow"]}, {"id": "forge:ingots/copper", "items": ["minecraft:copper_ingot"]}, {"id": "immersiveengineering:cut_blocks/copper", "items": ["minecraft:cut_copper", "minecraft:exposed_cut_copper", "minecraft:weathered_cut_copper", "minecraft:oxidized_cut_copper", "minecraft:waxed_cut_copper", "minecraft:waxed_exposed_cut_copper", "minecraft:waxed_weathered_cut_copper", "minecraft:waxed_oxidized_cut_copper"]}, {"id": "balm:dyes", "items": ["minecraft:white_dye", "minecraft:orange_dye", "minecraft:magenta_dye", "minecraft:light_blue_dye", "minecraft:yellow_dye", "mekanism:dust_sulfur", "minecraft:lime_dye", "minecraft:pink_dye", "minecraft:gray_dye", "minecraft:light_gray_dye", "minecraft:cyan_dye", "minecraft:purple_dye", "minecraft:blue_dye", "minecraft:brown_dye", "minecraft:green_dye", "minecraft:red_dye", "minecraft:black_dye"]}, {"id": "tconstruct:modifiable/staffs", "items": ["tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:swasher"]}, {"id": "forge:nuggets/netherite", "items": ["tconstruct:netherite_nugget"]}, {"id": "forge:sheetmetals/uranium", "items": ["immersiveengineering:sheetmetal_uranium"]}, {"id": "forge:gems/amethyst", "items": ["minecraft:amethyst_shard"]}, {"id": "tconstruct:casts/multi_use/nugget", "items": ["tconstruct:nugget_cast"]}, {"id": "forge:dusts/glowstone", "items": ["minecraft:glowstone_dust"]}, {"id": "tconstruct:casts/multi_use/wire", "items": ["tconstruct:wire_cast"]}, {"id": "mekanism:crystals/copper", "items": ["mekanism:crystal_copper"]}, {"id": "forge:ore_rates/sparse", "items": ["minecraft:nether_gold_ore"]}, {"id": "balm:lime_dyes", "items": ["minecraft:lime_dye"]}, {"id": "immersiveengineering:scaffoldings/steel", "items": ["immersiveengineering:steel_scaffolding_standard", "immersiveengineering:steel_scaffolding_grate_top", "immersiveengineering:steel_scaffolding_wooden_top"]}, {"id": "minecraft:compasses", "items": ["minecraft:compass", "minecraft:recovery_compass"]}, {"id": "tconstruct:seared_blocks", "items": ["tconstruct:seared_stone", "tconstruct:seared_cracked_bricks", "tconstruct:seared_cobble", "tconstruct:seared_paver", "tconstruct:seared_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks"]}, {"id": "create:stone_types/granite", "items": ["create:cut_granite", "create:cut_granite_stairs", "create:cut_granite_wall", "create:polished_cut_granite", "create:polished_cut_granite_stairs", "create:polished_cut_granite_wall", "create:cut_granite_bricks", "create:cut_granite_brick_stairs", "create:cut_granite_brick_wall", "create:small_granite_bricks", "create:small_granite_brick_stairs", "create:small_granite_brick_wall", "create:layered_granite", "create:granite_pillar", "minecraft:granite"]}, {"id": "tconstruct:casts/multi_use/repair_kit", "items": ["tconstruct:repair_kit_cast"]}, {"id": "forge:storage_blocks/clay", "items": ["minecraft:clay"]}, {"id": "create:pressurized_air_sources", "items": ["create:copper_backtank", "create:netherite_backtank"]}, {"id": "forge:netherrack", "items": ["minecraft:netherrack"]}, {"id": "tconstruct:proxy_tank_blacklist", "items": ["minecraft:bucket", "minecraft:glass_bottle", "minecraft:bowl", "tconstruct:copper_can"]}, {"id": "ae2:quartz_pickaxe", "items": ["ae2:certus_quartz_pickaxe", "ae2:nether_quartz_pickaxe"]}, {"id": "tconstruct:parts/bartered", "items": ["tconstruct:pick_head", "tconstruct:hammer_head", "tconstruct:small_axe_head", "tconstruct:broad_axe_head", "tconstruct:small_blade", "tconstruct:broad_blade", "tconstruct:adze_head", "tconstruct:large_plate", "tconstruct:tool_binding", "tconstruct:tough_binding", "tconstruct:tool_handle", "tconstruct:tough_handle", "tconstruct:bow_limb", "tconstruct:bow_grip", "tconstruct:bowstring", "tconstruct:maille", "tconstruct:shield_core", "tconstruct:helmet_plating", "tconstruct:chestplate_plating", "tconstruct:leggings_plating", "tconstruct:boots_plating"]}, {"id": "tconstruct:casts/multi_use/rod", "items": ["tconstruct:rod_cast"]}, {"id": "balm:light_gray_dyes", "items": ["minecraft:light_gray_dye"]}, {"id": "forge:buds", "items": ["ae2:small_quartz_bud", "ae2:medium_quartz_bud", "ae2:large_quartz_bud", "minecraft:small_amethyst_bud", "minecraft:medium_amethyst_bud", "minecraft:large_amethyst_bud"]}, {"id": "forge:nuggets/osmium", "items": ["mekanism:nugget_osmium"]}, {"id": "minecraft:crimson_stems", "items": ["minecraft:crimson_stem", "minecraft:stripped_crimson_stem", "minecraft:crimson_hyphae", "minecraft:stripped_crimson_hyphae"]}, {"id": "tconstruct:melting/netherite/tools_costing_7", "items": ["minecraft:netherite_leggings"]}, {"id": "forge:alloys/ultimate", "items": ["mekanism:alloy_atomic"]}, {"id": "minecraft:piglin_food", "items": ["minecraft:porkchop", "minecraft:cooked_porkchop"]}, {"id": "forge:tools/bows", "items": ["cataclysm:cursed_bow", "minecraft:bow", "mekanism:electric_bow", "tconstruct:longbow", "minecraft:crossbow"]}, {"id": "forge:wires/electrum", "items": ["immersiveengineering:wire_electrum"]}, {"id": "mekanism:alloys/reinforced", "items": ["mekanism:alloy_reinforced"]}, {"id": "minecraft:trapdoors", "items": ["minecraft:acacia_trapdoor", "minecraft:birch_trapdoor", "minecraft:dark_oak_trapdoor", "minecraft:jungle_trapdoor", "minecraft:oak_trapdoor", "minecraft:spruce_trapdoor", "minecraft:crimson_trapdoor", "minecraft:warped_trapdoor", "minecraft:mangrove_trapdoor", "minecraft:bamboo_trapdoor", "minecraft:cherry_trapdoor", "tconstruct:greenheart_trapdoor", "tconstruct:skyroot_trapdoor", "tconstruct:bloodshroom_trapdoor", "tconstruct:enderbark_trapdoor", "minecraft:iron_trapdoor", "create:train_trapdoor", "create:framed_glass_trapdoor"]}, {"id": "forge:heads", "items": ["minecraft:creeper_head", "minecraft:dragon_head", "minecraft:player_head", "minecraft:skeleton_skull", "minecraft:wither_skeleton_skull", "minecraft:zombie_head", "tconstruct:blaze_head", "tconstruct:end<PERSON>_head", "tconstruct:stray_head", "tconstruct:husk_head", "tconstruct:drowned_head", "tconstruct:spider_head", "tconstruct:cave_spider_head", "tconstruct:piglin_brute_head", "tconstruct:zombified_piglin_head"]}, {"id": "forge:ores/emerald", "items": ["minecraft:emerald_ore", "minecraft:deepslate_emerald_ore"]}, {"id": "tconstruct:seared_bricks", "items": ["tconstruct:seared_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks"]}, {"id": "forge:dusts/silver", "items": ["immersiveengineering:dust_silver"]}, {"id": "forge:plates/copper", "items": ["immersiveengineering:plate_copper", "create:copper_sheet"]}, {"id": "forge:ores/lapis", "items": ["minecraft:lapis_ore", "minecraft:deepslate_lapis_ore"]}, {"id": "c:raw_gold_blocks", "items": ["minecraft:raw_gold_block"]}, {"id": "mekanism:colorable/carpets", "items": ["minecraft:white_carpet", "minecraft:orange_carpet", "minecraft:magenta_carpet", "minecraft:light_blue_carpet", "minecraft:yellow_carpet", "minecraft:lime_carpet", "minecraft:pink_carpet", "minecraft:gray_carpet", "minecraft:light_gray_carpet", "minecraft:cyan_carpet", "minecraft:purple_carpet", "minecraft:blue_carpet", "minecraft:brown_carpet", "minecraft:green_carpet", "minecraft:red_carpet", "minecraft:black_carpet"]}, {"id": "forge:plates/brass", "items": ["create:brass_sheet"]}, {"id": "forge:raw_materials/copper", "items": ["minecraft:raw_copper"]}, {"id": "c:foods", "items": ["minecraft:apple", "minecraft:mushroom_stew", "minecraft:bread", "minecraft:porkchop", "minecraft:cooked_porkchop", "minecraft:golden_apple", "minecraft:enchanted_golden_apple", "minecraft:cod", "minecraft:salmon", "minecraft:tropical_fish", "minecraft:pufferfish", "minecraft:cooked_cod", "minecraft:cooked_salmon", "minecraft:cookie", "minecraft:melon_slice", "minecraft:dried_kelp", "minecraft:beef", "minecraft:cooked_beef", "minecraft:chicken", "minecraft:cooked_chicken", "minecraft:rotten_flesh", "minecraft:spider_eye", "minecraft:carrot", "minecraft:potato", "minecraft:baked_potato", "minecraft:poisonous_potato", "minecraft:golden_carrot", "minecraft:pumpkin_pie", "minecraft:rabbit", "minecraft:cooked_rabbit", "minecraft:rabbit_stew", "minecraft:mutton", "minecraft:cooked_mutton", "minecraft:chorus_fruit", "minecraft:beetroot", "minecraft:beetroot_soup", "minecraft:suspicious_stew", "minecraft:sweet_berries", "minecraft:glow_berries", "minecraft:honey_bottle"]}, {"id": "forge:storage_blocks/raw_gold", "items": ["minecraft:raw_gold_block"]}, {"id": "forge:storage_blocks/glowstone", "items": ["minecraft:glowstone"]}, {"id": "forge:seeds/melon", "items": ["minecraft:melon_seeds"]}, {"id": "tconstruct:casts/single_use/plate", "items": ["tconstruct:plate_sand_cast", "tconstruct:plate_red_sand_cast"]}, {"id": "forge:fences/steel", "items": ["immersiveengineering:steel_fence"]}, {"id": "tconstruct:melting/steel/tools_costing_1", "items": ["immersiveengineering:shovel_steel"]}, {"id": "tconstruct:casts/single_use/maille", "items": ["tconstruct:maille_sand_cast", "tconstruct:maille_red_sand_cast"]}, {"id": "tconstruct:smeltery_bricks", "items": ["tconstruct:seared_stone", "tconstruct:seared_cracked_bricks", "tconstruct:seared_cobble", "tconstruct:seared_paver", "tconstruct:seared_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks"]}, {"id": "tconstruct:enderbark/roots", "items": ["tconstruct:enderbark_roots", "tconstruct:earth_enderbark_roots", "tconstruct:sky_enderbark_roots", "tconstruct:ichor_enderbark_roots", "tconstruct:ender_enderbark_roots"]}, {"id": "forge:dyes/green", "items": ["minecraft:green_dye"]}, {"id": "minecraft:trim_templates", "items": ["minecraft:ward_armor_trim_smithing_template", "minecraft:spire_armor_trim_smithing_template", "minecraft:coast_armor_trim_smithing_template", "minecraft:eye_armor_trim_smithing_template", "minecraft:dune_armor_trim_smithing_template", "minecraft:wild_armor_trim_smithing_template", "minecraft:rib_armor_trim_smithing_template", "minecraft:tide_armor_trim_smithing_template", "minecraft:sentry_armor_trim_smithing_template", "minecraft:vex_armor_trim_smithing_template", "minecraft:snout_armor_trim_smithing_template", "minecraft:wayfinder_armor_trim_smithing_template", "minecraft:shaper_armor_trim_smithing_template", "minecraft:silence_armor_trim_smithing_template", "minecraft:raiser_armor_trim_smithing_template", "minecraft:host_armor_trim_smithing_template"]}, {"id": "tconstruct:casts/multi_use/bow_limb", "items": ["tconstruct:bow_limb_cast"]}, {"id": "tconstruct:casts/multi_use/large_plate", "items": ["tconstruct:large_plate_cast"]}, {"id": "forge:quartz", "items": ["minecraft:quartz"]}, {"id": "c:light_blue_dyes", "items": ["minecraft:light_blue_dye"]}, {"id": "forge:ingots/manyullyn", "items": ["tconstruct:manyullyn_ingot"]}, {"id": "create:stone_types/limestone", "items": ["create:cut_limestone", "create:cut_limestone_stairs", "create:cut_limestone_wall", "create:polished_cut_limestone", "create:polished_cut_limestone_stairs", "create:polished_cut_limestone_wall", "create:cut_limestone_bricks", "create:cut_limestone_brick_stairs", "create:cut_limestone_brick_wall", "create:small_limestone_bricks", "create:small_limestone_brick_stairs", "create:small_limestone_brick_wall", "create:layered_limestone", "create:limestone_pillar", "create:limestone"]}, {"id": "mekanism:shards/lead", "items": ["mekanism:shard_lead"]}, {"id": "create:dispense_behavior_wrap_blacklist", "items": []}, {"id": "create:contraption_controlled", "items": ["create:portable_fluid_interface", "create:mechanical_drill", "create:mechanical_saw", "create:deployer", "create:portable_storage_interface", "create:redstone_contact", "create:mechanical_harvester", "create:mechanical_plough", "create:mechanical_roller", "create:andesite_funnel", "create:brass_funnel", "create:peculiar_bell", "create:haunted_bell", "create:andesite_door", "create:brass_door", "create:copper_door", "create:train_door", "create:framed_glass_door", "minecraft:bell", "minecraft:campfire", "minecraft:soul_campfire", "minecraft:dispenser", "minecraft:dropper"]}, {"id": "tconstruct:workstation_rock", "items": ["minecraft:stone", "minecraft:cobblestone", "minecraft:mossy_cobblestone", "minecraft:blackstone", "minecraft:granite", "minecraft:diorite", "minecraft:andesite", "minecraft:deepslate", "minecraft:cobbled_deepslate", "minecraft:basalt", "minecraft:tuff", "minecraft:dripstone_block", "minecraft:calcite", "create:asurine", "create:crimsite", "create:limestone", "create:ochrum", "create:scoria", "create:scorchia", "create:veridium"]}, {"id": "forge:nuggets/refined_glowstone", "items": ["mekanism:nugget_refined_glowstone"]}, {"id": "mekanism:shards/uranium", "items": ["mekanism:shard_uranium"]}, {"id": "forge:ingots/brick", "items": ["minecraft:brick"]}, {"id": "mekanism:personal_storage", "items": ["mekanism:personal_barrel", "mekanism:personal_chest"]}, {"id": "forge:gems", "items": ["minecraft:amethyst_shard", "minecraft:diamond", "minecraft:emerald", "minecraft:lapis_lazuli", "minecraft:prismarine_crystals", "minecraft:quartz", "ae2:certus_quartz_crystal", "ae2:charged_certus_quartz_crystal", "ae2:fluix_crystal", "mekanism:fluorite_gem"]}, {"id": "minecraft:oak_logs", "items": ["minecraft:oak_log", "minecraft:oak_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood"]}, {"id": "c:villager_job_sites", "items": ["minecraft:barrel", "minecraft:blast_furnace", "minecraft:brewing_stand", "minecraft:cartography_table", "minecraft:cauldron", "minecraft:composter", "minecraft:fletching_table", "minecraft:grindstone", "minecraft:lectern", "minecraft:loom", "minecraft:smithing_table", "minecraft:smoker", "minecraft:stonecutter"]}, {"id": "balm:emeralds", "items": ["minecraft:emerald"]}, {"id": "forge:storage_blocks", "items": ["minecraft:amethyst_block", "minecraft:coal_block", "minecraft:copper_block", "minecraft:diamond_block", "minecraft:emerald_block", "minecraft:gold_block", "minecraft:iron_block", "minecraft:lapis_block", "minecraft:quartz_block", "minecraft:raw_copper_block", "minecraft:raw_gold_block", "minecraft:raw_iron_block", "minecraft:redstone_block", "minecraft:netherite_block", "immersiveengineering:storage_aluminum", "immersiveengineering:raw_block_aluminum", "immersiveengineering:storage_lead", "mekanism:block_lead", "immersiveengineering:raw_block_lead", "mekanism:block_raw_lead", "immersiveengineering:storage_silver", "immersiveengineering:raw_block_silver", "immersiveengineering:storage_nickel", "immersiveengineering:raw_block_nickel", "immersiveengineering:storage_uranium", "mekanism:block_uranium", "immersiveengineering:raw_block_uranium", "mekanism:block_raw_uranium", "immersiveengineering:storage_constantan", "immersiveengineering:storage_electrum", "immersiveengineering:storage_steel", "mekanism:block_steel", "tconstruct:steel_block", "create:raw_zinc_block", "create:zinc_block", "create:andesite_alloy_block", "create:brass_block", "create:cardboard_block", "create:experience_block", "ae2:quartz_block", "mekanism:block_bronze", "mekanism:block_charcoal", "mekanism:block_refined_glowstone", "mekanism:block_refined_obsidian", "mekanism:block_fluorite", "mekanism:block_osmium", "mekanism:block_raw_osmium", "mekanism:block_tin", "mekanism:block_raw_tin", "tconstruct:cobalt_block", "tconstruct:slimesteel_block", "tconstruct:amethyst_bronze_block", "tconstruct:rose_gold_block", "tconstruct:pig_iron_block", "tconstruct:cinderslime_block", "tconstruct:queens_slime_block", "tconstruct:manyullyn_block", "tconstruct:hepatizon_block", "tconstruct:soulsteel_block", "tconstruct:knightslime_block", "tconstruct:raw_cobalt_block"]}, {"id": "tconstruct:casts/multi_use/helmet_plating", "items": ["tconstruct:helmet_plating_cast"]}, {"id": "forge:dyes/light_gray", "items": ["minecraft:light_gray_dye"]}, {"id": "forge:gems/diamond", "items": ["minecraft:diamond"]}, {"id": "tconstruct:modifiable/book_armor/materials_and_you", "items": []}, {"id": "c:light_gray_dyes", "items": ["minecraft:light_gray_dye"]}, {"id": "ae2:p2p_attunements/fe_p2p_tunnel", "items": ["ae2:dense_energy_cell", "ae2:energy_acceptor", "ae2:energy_cell", "ae2:creative_energy_cell"]}, {"id": "mekanism:clumps/gold", "items": ["mekanism:clump_gold"]}, {"id": "forge:dyes", "items": ["minecraft:white_dye", "minecraft:orange_dye", "minecraft:magenta_dye", "minecraft:light_blue_dye", "minecraft:yellow_dye", "mekanism:dust_sulfur", "minecraft:lime_dye", "minecraft:pink_dye", "minecraft:gray_dye", "minecraft:light_gray_dye", "minecraft:cyan_dye", "minecraft:purple_dye", "minecraft:blue_dye", "minecraft:brown_dye", "minecraft:green_dye", "minecraft:red_dye", "minecraft:black_dye"]}, {"id": "forge:raw_materials/tin", "items": ["mekanism:raw_tin"]}, {"id": "mekanism:dirty_dusts/uranium", "items": ["mekanism:dirty_dust_uranium"]}, {"id": "forge:ore_bearing_ground/deepslate", "items": ["minecraft:deepslate"]}, {"id": "tconstruct:melting/refined_obsidian/tools_costing_3", "items": []}, {"id": "tconstruct:casts/multi_use/broad_blade", "items": ["tconstruct:broad_blade_cast"]}, {"id": "tconstruct:modifiable", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:slime_helmet", "tconstruct:travelers_shield", "tconstruct:plate_shield", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots"]}, {"id": "immersiveengineering:scaffolding_stairs/steel", "items": ["immersiveengineering:stairs_steel_scaffolding_standard", "immersiveengineering:stairs_steel_scaffolding_grate_top", "immersiveengineering:stairs_steel_scaffolding_wooden_top"]}, {"id": "tconstruct:inventory_blacklist", "items": ["minecraft:bundle", "minecraft:shulker_box", "minecraft:white_shulker_box", "minecraft:orange_shulker_box", "minecraft:magenta_shulker_box", "minecraft:light_blue_shulker_box", "minecraft:yellow_shulker_box", "minecraft:lime_shulker_box", "minecraft:pink_shulker_box", "minecraft:gray_shulker_box", "minecraft:light_gray_shulker_box", "minecraft:cyan_shulker_box", "minecraft:purple_shulker_box", "minecraft:blue_shulker_box", "minecraft:brown_shulker_box", "minecraft:green_shulker_box", "minecraft:red_shulker_box", "minecraft:black_shulker_box"]}, {"id": "forge:ingots/silver", "items": ["immersiveengineering:ingot_silver"]}, {"id": "tconstruct:melting/steel/tools_costing_5", "items": ["immersiveengineering:armor_steel_helmet"]}, {"id": "minecraft:coals", "items": ["minecraft:coal", "minecraft:charcoal", "immersiveengineering:coal_coke"]}, {"id": "forge:gunpowder", "items": ["minecraft:gunpowder"]}, {"id": "mekanism:crystals/uranium", "items": ["mekanism:crystal_uranium"]}, {"id": "minecraft:axolotl_tempt_items", "items": ["minecraft:tropical_fish_bucket"]}, {"id": "forge:raw_materials/osmium", "items": ["mekanism:raw_osmium"]}, {"id": "minecraft:music_discs", "items": ["minecraft:music_disc_13", "minecraft:music_disc_cat", "minecraft:music_disc_blocks", "minecraft:music_disc_chirp", "minecraft:music_disc_far", "minecraft:music_disc_mall", "minecraft:music_disc_mellohi", "minecraft:music_disc_stal", "minecraft:music_disc_strad", "minecraft:music_disc_ward", "minecraft:music_disc_11", "minecraft:music_disc_wait", "minecraft:music_disc_pigstep", "minecraft:music_disc_otherside", "minecraft:music_disc_5", "minecraft:music_disc_relic", "cataclysm:music_disc_netherite_monstrosity", "cataclysm:music_disc_ender_guardian", "cataclysm:music_disc_ignis", "cataclysm:music_disc_the_harbinger", "cataclysm:music_disc_the_leviathan", "cataclysm:music_disc_ancient_remnant", "cataclysm:music_disc_maledictus", "cataclysm:music_disc_scylla", "cataclysm:music_disc_the_cataclysmfarer"]}, {"id": "tconstruct:slimy_vines", "items": ["tconstruct:sky_slime_vine", "tconstruct:ender_slime_vine"]}, {"id": "forge:nuggets/steel", "items": ["immersiveengineering:nugget_steel", "mekanism:nugget_steel", "tconstruct:steel_nugget"]}, {"id": "immersiveengineering:cut_stairs/copper", "items": ["minecraft:cut_copper_stairs", "minecraft:exposed_cut_copper_stairs", "minecraft:weathered_cut_copper_stairs", "minecraft:oxidized_cut_copper_stairs", "minecraft:waxed_cut_copper_stairs", "minecraft:waxed_exposed_cut_copper_stairs", "minecraft:waxed_weathered_cut_copper_stairs", "minecraft:waxed_oxidized_cut_copper_stairs"]}, {"id": "forge:slabs/uncolored_sandstone", "items": ["minecraft:sandstone_slab", "minecraft:cut_sandstone_slab", "minecraft:smooth_sandstone_slab"]}, {"id": "forge:ores/quartz", "items": ["minecraft:nether_quartz_ore"]}, {"id": "create:tracks", "items": ["create:track"]}, {"id": "tconstruct:modifiable/embellishment/slime", "items": ["tconstruct:slime_helmet", "tconstruct:slime_chestplate", "tconstruct:slime_leggings", "tconstruct:slime_boots"]}, {"id": "tconstruct:parts", "items": ["tconstruct:repair_kit", "tconstruct:pick_head", "tconstruct:hammer_head", "tconstruct:small_axe_head", "tconstruct:broad_axe_head", "tconstruct:small_blade", "tconstruct:broad_blade", "tconstruct:adze_head", "tconstruct:large_plate", "tconstruct:tool_binding", "tconstruct:tough_binding", "tconstruct:tool_handle", "tconstruct:tough_handle", "tconstruct:bow_limb", "tconstruct:bow_grip", "tconstruct:bowstring", "tconstruct:maille", "tconstruct:shield_core", "tconstruct:helmet_plating", "tconstruct:chestplate_plating", "tconstruct:leggings_plating", "tconstruct:boots_plating"]}, {"id": "balm:orange_dyes", "items": ["minecraft:orange_dye"]}, {"id": "c:quartz", "items": ["minecraft:quartz"]}, {"id": "minecraft:lapis_ores", "items": ["minecraft:lapis_ore", "minecraft:deepslate_lapis_ore"]}, {"id": "tconstruct:melting/steel/tools_costing_3", "items": ["immersiveengineering:hoe_steel", "immersiveengineering:axe_steel", "immersiveengineering:pickaxe_steel"]}, {"id": "create:deployable_drink", "items": ["minecraft:milk_bucket", "minecraft:potion"]}, {"id": "create:sandpaper", "items": ["create:sand_paper", "create:red_sand_paper"]}, {"id": "immersiveengineering:scaffolding_stairs/aluminum", "items": ["immersiveengineering:stairs_alu_scaffolding_standard", "immersiveengineering:stairs_alu_scaffolding_grate_top", "immersiveengineering:stairs_alu_scaffolding_wooden_top"]}, {"id": "forge:nuggets/cobalt", "items": ["tconstruct:cobalt_nugget"]}, {"id": "forge:dusts/electrum", "items": ["immersiveengineering:dust_electrum"]}, {"id": "ae2:quartz_wrench", "items": ["ae2:certus_quartz_wrench", "ae2:nether_quartz_wrench"]}, {"id": "mekanism:enriched/gold", "items": ["mekanism:enriched_gold"]}, {"id": "forge:crops/potato", "items": ["minecraft:potato"]}, {"id": "tconstruct:melting/osmium/tools_costing_3", "items": []}, {"id": "balm:black_dyes", "items": ["minecraft:black_dye"]}, {"id": "minecraft:wart_blocks", "items": ["minecraft:nether_wart_block", "minecraft:warped_wart_block"]}, {"id": "forge:seeds/hemp", "items": ["immersiveengineering:seed"]}, {"id": "forge:armors/chestplates/hazmat", "items": ["mekanism:hazmat_gown"]}, {"id": "tconstruct:autosmelt_blacklist", "items": []}, {"id": "c:potions", "items": ["minecraft:lingering_potion", "minecraft:splash_potion", "minecraft:potion"]}, {"id": "tconstruct:modifiable/melee/unarmed", "items": ["tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate"]}, {"id": "tconstruct:modifiable/interactable/right", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan"]}, {"id": "minecraft:creeper_igniters", "items": ["minecraft:flint_and_steel", "minecraft:fire_charge"]}, {"id": "mekanism:alloys/infused", "items": ["mekanism:alloy_infused"]}, {"id": "forge:rods/plastic", "items": ["mekanism:hdpe_stick"]}, {"id": "forge:viewers/hidden_from_recipe", "items": ["tconstruct:crystalshot", "tconstruct:molten_soulsteel_bucket", "tconstruct:molten_knightslime_bucket", "tconstruct:soulsteel_block", "tconstruct:soulsteel_ingot", "tconstruct:soulsteel_nugget", "tconstruct:knightslime_block", "tconstruct:<PERSON><PERSON><PERSON>_ingot", "tconstruct:<PERSON><PERSON>e_nugget", "tconstruct:ichor_slime_leaves", "tconstruct:ichor_slime_tall_grass", "tconstruct:ichor_slime_fern", "tconstruct:ichor_slime_sapling", "tconstruct:ichor_slime_grass_seeds", "tconstruct:ichor_earth_slime_grass", "tconstruct:ichor_sky_slime_grass", "tconstruct:ichor_ichor_slime_grass", "tconstruct:ichor_ender_slime_grass", "tconstruct:ichor_vanilla_slime_grass"]}, {"id": "forge:charcoal", "items": ["minecraft:charcoal"]}, {"id": "tconstruct:melting/gold/tools_costing_7", "items": ["minecraft:golden_leggings"]}, {"id": "ae2:pattern_provider", "items": ["ae2:cable_pattern_provider", "ae2:pattern_provider"]}, {"id": "minecraft:wooden_stairs", "items": ["minecraft:oak_stairs", "minecraft:spruce_stairs", "minecraft:birch_stairs", "minecraft:jungle_stairs", "minecraft:acacia_stairs", "minecraft:dark_oak_stairs", "minecraft:crimson_stairs", "minecraft:warped_stairs", "minecraft:mangrove_stairs", "minecraft:bamboo_stairs", "minecraft:cherry_stairs", "cataclysm:chorus_stairs", "immersiveengineering:stairs_treated_wood_horizontal", "immersiveengineering:stairs_treated_wood_vertical", "immersiveengineering:stairs_treated_wood_packaged", "tconstruct:greenheart_planks_stairs", "tconstruct:skyroot_planks_stairs", "tconstruct:bloodshroom_planks_stairs", "tconstruct:enderbark_planks_stairs"]}, {"id": "tconstruct:modifiable/armor/held", "items": ["tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "forge:raw_materials/aluminum", "items": ["immersiveengineering:raw_aluminum"]}, {"id": "forge:glass/purple", "items": ["minecraft:purple_stained_glass", "tconstruct:purple_clear_stained_glass"]}, {"id": "tconstruct:casts/multi_use/adze_head", "items": ["tconstruct:adze_head_cast"]}, {"id": "minecraft:terracotta", "items": ["minecraft:terracotta", "minecraft:white_terracotta", "minecraft:orange_terracotta", "minecraft:magenta_terracotta", "minecraft:light_blue_terracotta", "minecraft:yellow_terracotta", "minecraft:lime_terracotta", "minecraft:pink_terracotta", "minecraft:gray_terracotta", "minecraft:light_gray_terracotta", "minecraft:cyan_terracotta", "minecraft:purple_terracotta", "minecraft:blue_terracotta", "minecraft:brown_terracotta", "minecraft:green_terracotta", "minecraft:red_terracotta", "minecraft:black_terracotta"]}, {"id": "minecraft:anvil", "items": ["minecraft:anvil", "minecraft:chipped_anvil", "minecraft:damaged_anvil"]}, {"id": "minecraft:wooden_pressure_plates", "items": ["minecraft:oak_pressure_plate", "minecraft:spruce_pressure_plate", "minecraft:birch_pressure_plate", "minecraft:jungle_pressure_plate", "minecraft:acacia_pressure_plate", "minecraft:dark_oak_pressure_plate", "minecraft:crimson_pressure_plate", "minecraft:warped_pressure_plate", "minecraft:mangrove_pressure_plate", "minecraft:bamboo_pressure_plate", "minecraft:cherry_pressure_plate", "tconstruct:greenheart_pressure_plate", "tconstruct:skyroot_pressure_plate", "tconstruct:bloodshroom_pressure_plate", "tconstruct:enderbark_pressure_plate"]}, {"id": "forge:sheetmetals/iron", "items": ["immersiveengineering:sheetmetal_iron"]}, {"id": "balm:iron_nuggets", "items": ["minecraft:iron_nugget"]}, {"id": "tconstruct:melting/refined_obsidian/tools_costing_2", "items": []}, {"id": "forge:flour/wheat", "items": ["create:wheat_flour"]}, {"id": "forge:sheetmetals/nickel", "items": ["immersiveengineering:sheetmetal_nickel"]}, {"id": "forge:glass_panes/brown", "items": ["minecraft:brown_stained_glass_pane", "tconstruct:brown_clear_stained_glass_pane"]}, {"id": "forge:barrels", "items": ["minecraft:barrel"]}, {"id": "tconstruct:modifiable/dyeable", "items": ["tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:travelers_shield"]}, {"id": "minecraft:gold_ores", "items": ["minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore"]}, {"id": "forge:rods/treated_wood", "items": ["immersiveengineering:stick_treated"]}, {"id": "forge:deepslate", "items": ["minecraft:deepslate", "minecraft:cobbled_deepslate"]}, {"id": "forge:storage_blocks/iron", "items": ["minecraft:iron_block"]}, {"id": "create:stone_types/scorchia", "items": ["create:cut_scorchia", "create:cut_scorchia_stairs", "create:cut_scorchia_wall", "create:polished_cut_scorchia", "create:polished_cut_scorchia_stairs", "create:polished_cut_scorchia_wall", "create:cut_scorchia_bricks", "create:cut_scorchia_brick_stairs", "create:cut_scorchia_brick_wall", "create:small_scorchia_bricks", "create:small_scorchia_brick_stairs", "create:small_scorchia_brick_wall", "create:layered_scorchia", "create:scorchia_pillar", "create:scorchia"]}, {"id": "forge:storage_blocks/silver", "items": ["immersiveengineering:storage_silver"]}, {"id": "forge:storage_blocks/aluminum", "items": ["immersiveengineering:storage_aluminum"]}, {"id": "forge:string", "items": ["minecraft:string"]}, {"id": "minecraft:cherry_logs", "items": ["minecraft:cherry_log", "minecraft:cherry_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood"]}, {"id": "mekanism:crystals/lead", "items": ["mekanism:crystal_lead"]}, {"id": "c:sandstone_blocks", "items": ["minecraft:sandstone", "minecraft:chiseled_sandstone", "minecraft:cut_sandstone", "minecraft:smooth_sandstone", "minecraft:red_sandstone", "minecraft:chiseled_red_sandstone", "minecraft:cut_red_sandstone", "minecraft:smooth_red_sandstone"]}, {"id": "tconstruct:foundry_bricks", "items": ["tconstruct:scorched_stone", "tconstruct:polished_scorched_stone", "tconstruct:scorched_bricks", "tconstruct:scorched_road", "tconstruct:chiseled_scorched_bricks"]}, {"id": "forge:ender_pearls", "items": ["minecraft:ender_pearl"]}, {"id": "forge:storage_blocks/raw_osmium", "items": ["mekanism:block_raw_osmium"]}, {"id": "forge:bookshelves", "items": ["minecraft:bookshelf"]}, {"id": "forge:ingots/hepatizon", "items": ["tconstruct:hepati<PERSON>_ingot"]}, {"id": "tconstruct:melting/gold/tools_costing_1", "items": ["minecraft:golden_shovel"]}, {"id": "tconstruct:casts/single_use/leggings_plating", "items": ["tconstruct:leggings_plating_sand_cast", "tconstruct:leggings_plating_red_sand_cast"]}, {"id": "minecraft:buttons", "items": ["minecraft:oak_button", "minecraft:spruce_button", "minecraft:birch_button", "minecraft:jungle_button", "minecraft:acacia_button", "minecraft:dark_oak_button", "minecraft:crimson_button", "minecraft:warped_button", "minecraft:mangrove_button", "minecraft:bamboo_button", "minecraft:cherry_button", "tconstruct:greenheart_button", "tconstruct:skyroot_button", "tconstruct:bloodshroom_button", "tconstruct:enderbark_button", "minecraft:stone_button", "minecraft:polished_blackstone_button"]}, {"id": "balm:yellow_dyes", "items": ["minecraft:yellow_dye", "mekanism:dust_sulfur"]}, {"id": "tconstruct:melting/copper/tools_costing_2", "items": []}, {"id": "tconstruct:melting/bronze/tools_costing_7", "items": []}, {"id": "forge:dusts/salt", "items": ["mekanism:salt"]}, {"id": "tconstruct:casts/single_use/broad_axe_head", "items": ["tconstruct:broad_axe_head_sand_cast", "tconstruct:broad_axe_head_red_sand_cast"]}, {"id": "tconstruct:melting/invar/tools_costing_3", "items": []}, {"id": "minecraft:piglin_loved", "items": ["minecraft:gold_ore", "minecraft:nether_gold_ore", "minecraft:deepslate_gold_ore", "minecraft:gold_block", "minecraft:gilded_blackstone", "minecraft:light_weighted_pressure_plate", "minecraft:gold_ingot", "minecraft:bell", "minecraft:clock", "minecraft:golden_carrot", "minecraft:glistering_melon_slice", "minecraft:golden_apple", "minecraft:enchanted_golden_apple", "minecraft:golden_helmet", "minecraft:golden_chestplate", "minecraft:golden_leggings", "minecraft:golden_boots", "minecraft:golden_horse_armor", "minecraft:golden_sword", "minecraft:golden_pickaxe", "minecraft:golden_shovel", "minecraft:golden_axe", "minecraft:golden_hoe", "minecraft:raw_gold", "minecraft:raw_gold_block", "create:golden_sheet", "create:crushed_raw_gold", "mekanism:block_refined_glowstone", "mekanism:ingot_refined_glowstone", "mekanism:dust_gold", "mekanism:enriched_gold", "mekanism:shard_gold", "mekanism:crystal_gold", "mekanism:dirty_dust_gold", "mekanism:clump_gold", "tconstruct:gold_reinforcement", "tconstruct:gold_item_frame", "tconstruct:reversed_gold_item_frame", "tconstruct:molten_gold_bucket", "tconstruct:gold_bars", "tconstruct:gold_platform", "tconstruct:ingot_cast", "tconstruct:nugget_cast", "tconstruct:gem_cast", "tconstruct:rod_cast", "tconstruct:repair_kit_cast", "tconstruct:plate_cast", "tconstruct:gear_cast", "tconstruct:coin_cast", "tconstruct:wire_cast", "tconstruct:pick_head_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_blade_cast", "tconstruct:adze_head_cast", "tconstruct:hammer_head_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_blade_cast", "tconstruct:large_plate_cast", "tconstruct:tool_binding_cast", "tconstruct:tough_binding_cast", "tconstruct:tool_handle_cast", "tconstruct:tough_handle_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_grip_cast", "tconstruct:helmet_plating_cast", "tconstruct:chestplate_plating_cast", "tconstruct:leggings_plating_cast", "tconstruct:boots_plating_cast", "tconstruct:maille_cast"]}, {"id": "minecraft:tall_flowers", "items": ["minecraft:sunflower", "minecraft:lilac", "minecraft:peony", "minecraft:rose_bush", "minecraft:pitcher_plant"]}, {"id": "forge:ores/lead", "items": ["immersiveengineering:ore_lead", "immersiveengineering:deepslate_ore_lead", "mekanism:lead_ore", "mekanism:deepslate_lead_ore"]}, {"id": "forge:ingots/gold", "items": ["minecraft:gold_ingot"]}, {"id": "tconstruct:congealed_slime", "items": ["tconstruct:earth_congealed_slime", "tconstruct:sky_congealed_slime", "tconstruct:ichor_congealed_slime", "tconstruct:ender_congealed_slime"]}, {"id": "ae2:covered_cable", "items": ["ae2:white_covered_cable", "ae2:orange_covered_cable", "ae2:magenta_covered_cable", "ae2:light_blue_covered_cable", "ae2:yellow_covered_cable", "ae2:lime_covered_cable", "ae2:pink_covered_cable", "ae2:gray_covered_cable", "ae2:light_gray_covered_cable", "ae2:cyan_covered_cable", "ae2:purple_covered_cable", "ae2:blue_covered_cable", "ae2:brown_covered_cable", "ae2:green_covered_cable", "ae2:red_covered_cable", "ae2:black_covered_cable", "ae2:fluix_covered_cable"]}, {"id": "forge:gems/emerald", "items": ["minecraft:emerald"]}, {"id": "forge:dusts/sulfur", "items": ["immersiveengineering:dust_sulfur", "mekanism:dust_sulfur"]}, {"id": "tconstruct:modifiable/loot_capable_tool", "items": ["tconstruct:pickaxe", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:kama", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:flint_and_brick", "tconstruct:war_pick", "tconstruct:sledge_hammer", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate", "tconstruct:melting_pan"]}, {"id": "forge:ingots/refined_glowstone", "items": ["mekanism:ingot_refined_glowstone"]}, {"id": "mekanism:colorable/terracotta", "items": ["minecraft:terracotta", "minecraft:white_terracotta", "minecraft:orange_terracotta", "minecraft:magenta_terracotta", "minecraft:light_blue_terracotta", "minecraft:yellow_terracotta", "minecraft:lime_terracotta", "minecraft:pink_terracotta", "minecraft:gray_terracotta", "minecraft:light_gray_terracotta", "minecraft:cyan_terracotta", "minecraft:purple_terracotta", "minecraft:blue_terracotta", "minecraft:brown_terracotta", "minecraft:green_terracotta", "minecraft:red_terracotta", "minecraft:black_terracotta"]}, {"id": "forge:glass/gray", "items": ["minecraft:gray_stained_glass", "tconstruct:gray_clear_stained_glass"]}, {"id": "tconstruct:modifiable/multipart/single", "items": ["tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:slime_helmet", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "forge:ores/tin", "items": ["mekanism:tin_ore", "mekanism:deepslate_tin_ore"]}, {"id": "tconstruct:casts/multi_use/maille", "items": ["tconstruct:maille_cast"]}, {"id": "forge:ores/redstone", "items": ["minecraft:redstone_ore", "minecraft:deepslate_redstone_ore"]}, {"id": "forge:dyes/cyan", "items": ["minecraft:cyan_dye"]}, {"id": "tconstruct:melting/refined_obsidian/tools_costing_7", "items": []}, {"id": "forge:dusts/uranium", "items": ["immersiveengineering:dust_uranium", "mekanism:dust_uranium"]}, {"id": "forge:armors/boots/hazmat", "items": ["mekanism:hazmat_boots"]}, {"id": "forge:glass_panes/light_blue", "items": ["minecraft:light_blue_stained_glass_pane", "tconstruct:light_blue_clear_stained_glass_pane"]}, {"id": "forge:tools/shields", "items": ["cataclysm:bulwark_of_the_flame", "cataclysm:black_steel_targe", "cataclysm:azure_sea_shield", "minecraft:shield", "immersiveengineering:shield", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "forge:stairs/sandstone", "items": ["minecraft:sandstone_stairs", "minecraft:smooth_sandstone_stairs", "minecraft:red_sandstone_stairs", "minecraft:smooth_red_sandstone_stairs"]}, {"id": "tconstruct:casts/multi_use/bow_grip", "items": ["tconstruct:bow_grip_cast"]}, {"id": "minecraft:iron_ores", "items": ["minecraft:iron_ore", "minecraft:deepslate_iron_ore"]}, {"id": "forge:storage_blocks/coal", "items": ["minecraft:coal_block"]}, {"id": "forge:storage_blocks/charcoal", "items": ["mekanism:block_charcoal"]}, {"id": "tconstruct:anvil_metal", "items": ["tconstruct:slimesteel_block", "tconstruct:amethyst_bronze_block", "tconstruct:rose_gold_block", "tconstruct:pig_iron_block", "tconstruct:cinderslime_block", "tconstruct:queens_slime_block", "tconstruct:manyullyn_block", "tconstruct:hepatizon_block", "minecraft:netherite_block", "mekanism:block_bronze", "create:brass_block", "immersiveengineering:storage_electrum", "immersiveengineering:storage_constantan", "mekanism:block_refined_glowstone", "mekanism:block_refined_obsidian"]}, {"id": "forge:circuits/basic", "items": ["mekanism:basic_control_circuit"]}, {"id": "c:shields", "items": ["cataclysm:bulwark_of_the_flame", "cataclysm:black_steel_targe", "cataclysm:azure_sea_shield", "minecraft:shield", "immersiveengineering:shield", "tconstruct:battlesign", "tconstruct:travelers_shield", "tconstruct:plate_shield"]}, {"id": "tconstruct:modifiable/unsalvageable", "items": ["tconstruct:dagger"]}, {"id": "tconstruct:casts/single_use/repair_kit", "items": ["tconstruct:repair_kit_sand_cast", "tconstruct:repair_kit_red_sand_cast"]}, {"id": "forge:nether_stars", "items": ["minecraft:nether_star"]}, {"id": "forge:boxes/shulker", "items": ["minecraft:shulker_box", "minecraft:blue_shulker_box", "minecraft:brown_shulker_box", "minecraft:cyan_shulker_box", "minecraft:gray_shulker_box", "minecraft:green_shulker_box", "minecraft:light_blue_shulker_box", "minecraft:light_gray_shulker_box", "minecraft:lime_shulker_box", "minecraft:magenta_shulker_box", "minecraft:orange_shulker_box", "minecraft:pink_shulker_box", "minecraft:purple_shulker_box", "minecraft:red_shulker_box", "minecraft:white_shulker_box", "minecraft:yellow_shulker_box", "minecraft:black_shulker_box"]}, {"id": "forge:raw_materials/cobalt", "items": ["tconstruct:raw_cobalt"]}, {"id": "tconstruct:structure_debug/smeltery", "items": ["tconstruct:seared_stone", "tconstruct:seared_cracked_bricks", "tconstruct:seared_cobble", "tconstruct:seared_paver", "tconstruct:seared_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks", "tconstruct:seared_fuel_tank", "tconstruct:seared_fuel_gauge", "tconstruct:seared_ingot_tank", "tconstruct:seared_ingot_gauge", "tconstruct:smeltery_controller", "tconstruct:seared_ladder", "tconstruct:seared_drain", "tconstruct:seared_chute", "tconstruct:seared_duct", "tconstruct:seared_glass", "tconstruct:seared_soul_glass", "tconstruct:seared_tinted_glass"]}, {"id": "tconstruct:structure_debug/foundry", "items": ["tconstruct:scorched_stone", "tconstruct:polished_scorched_stone", "tconstruct:scorched_bricks", "tconstruct:scorched_road", "tconstruct:chiseled_scorched_bricks", "tconstruct:scorched_fuel_tank", "tconstruct:scorched_fuel_gauge", "tconstruct:scorched_ingot_tank", "tconstruct:scorched_ingot_gauge", "tconstruct:foundry_controller", "tconstruct:scorched_ladder", "tconstruct:scorched_drain", "tconstruct:scorched_chute", "tconstruct:scorched_duct", "tconstruct:scorched_glass", "tconstruct:scorched_soul_glass", "tconstruct:scorched_tinted_glass"]}, {"id": "tconstruct:casts/single_use/chestplate_plating", "items": ["tconstruct:chestplate_plating_sand_cast", "tconstruct:chestplate_plating_red_sand_cast"]}, {"id": "forge:storage_blocks/tin", "items": ["mekanism:block_tin"]}, {"id": "forge:sheetmetals/aluminum", "items": ["immersiveengineering:sheetmetal_aluminum"]}, {"id": "minecraft:birch_logs", "items": ["minecraft:birch_log", "minecraft:birch_wood", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood"]}, {"id": "forge:rods/blaze", "items": ["minecraft:blaze_rod"]}, {"id": "forge:storage_blocks/raw_tin", "items": ["mekanism:block_raw_tin"]}, {"id": "tconstruct:melting/netherite/tools_costing_3", "items": ["minecraft:netherite_pickaxe", "minecraft:netherite_axe"]}, {"id": "tconstruct:melting/netherite/tools_costing_1", "items": ["minecraft:netherite_shovel"]}, {"id": "tconstruct:casts/empty/basin", "items": ["tconstruct:gold_platform"]}, {"id": "balm:ingots", "items": ["minecraft:brick", "minecraft:copper_ingot", "minecraft:gold_ingot", "minecraft:iron_ingot", "minecraft:netherite_ingot", "minecraft:nether_brick", "immersiveengineering:ingot_aluminum", "immersiveengineering:ingot_lead", "mekanism:ingot_lead", "immersiveengineering:ingot_silver", "immersiveengineering:ingot_nickel", "immersiveengineering:ingot_uranium", "mekanism:ingot_uranium", "immersiveengineering:ingot_constantan", "immersiveengineering:ingot_electrum", "immersiveengineering:ingot_steel", "mekanism:ingot_steel", "tconstruct:steel_ingot", "create:andesite_alloy", "create:zinc_ingot", "create:brass_ingot", "mekanism:ingot_osmium", "mekanism:ingot_tin", "mekanism:ingot_bronze", "mekanism:ingot_refined_glowstone", "mekanism:ingot_refined_obsidian", "tconstruct:seared_brick", "tconstruct:scorched_brick", "minecraft:netherite_scrap", "tconstruct:cobalt_ingot", "tconstruct:slimesteel_ingot", "tconstruct:amethyst_bronze_ingot", "tconstruct:rose_gold_ingot", "tconstruct:pig_iron_ingot", "tconstruct:cinderslime_ingot", "tconstruct:queens_slime_ingot", "tconstruct:manyullyn_ingot", "tconstruct:hepati<PERSON>_ingot", "tconstruct:soulsteel_ingot", "tconstruct:<PERSON><PERSON><PERSON>_ingot"]}, {"id": "forge:dusts/lapis", "items": ["mekanism:dust_lapis_lazuli"]}, {"id": "forge:storage_blocks/zinc", "items": ["create:zinc_block"]}, {"id": "forge:storage_blocks/raw_uranium", "items": ["immersiveengineering:raw_block_uranium", "mekanism:block_raw_uranium"]}, {"id": "forge:storage_blocks/rose_gold", "items": ["tconstruct:rose_gold_block"]}, {"id": "tconstruct:casts/multi_use/tool_handle", "items": ["tconstruct:tool_handle_cast"]}, {"id": "forge:panes/glass", "items": ["minecraft:glass_pane", "minecraft:gray_stained_glass_pane", "minecraft:black_stained_glass_pane", "minecraft:orange_stained_glass_pane", "minecraft:blue_stained_glass_pane", "minecraft:brown_stained_glass_pane", "minecraft:cyan_stained_glass_pane", "minecraft:green_stained_glass_pane", "minecraft:light_blue_stained_glass_pane", "minecraft:light_gray_stained_glass_pane", "minecraft:lime_stained_glass_pane", "minecraft:magenta_stained_glass_pane", "minecraft:pink_stained_glass_pane", "minecraft:purple_stained_glass_pane", "minecraft:red_stained_glass_pane", "minecraft:white_stained_glass_pane", "minecraft:yellow_stained_glass_pane"]}, {"id": "tconstruct:planklike", "items": ["minecraft:oak_planks", "minecraft:spruce_planks", "minecraft:birch_planks", "minecraft:jungle_planks", "minecraft:acacia_planks", "minecraft:dark_oak_planks", "minecraft:crimson_planks", "minecraft:warped_planks", "minecraft:mangrove_planks", "minecraft:bamboo_planks", "minecraft:cherry_planks", "cataclysm:chorus_planks", "immersiveengineering:fiberboard", "tconstruct:greenheart_planks", "tconstruct:skyroot_planks", "tconstruct:bloodshroom_planks", "tconstruct:enderbark_planks", "tconstruct:blazewood", "tconstruct:nahuatl"]}, {"id": "forge:plates/nickel", "items": ["immersiveengineering:plate_nickel"]}, {"id": "mekanism:dirty_dusts/lead", "items": ["mekanism:dirty_dust_lead"]}, {"id": "forge:storage_blocks/cardboard", "items": ["create:cardboard_block"]}, {"id": "mekanism:clumps/lead", "items": ["mekanism:clump_lead"]}, {"id": "forge:glass_panes/magenta", "items": ["minecraft:magenta_stained_glass_pane", "tconstruct:magenta_clear_stained_glass_pane"]}, {"id": "c:empty_buckets", "items": ["minecraft:bucket"]}, {"id": "forge:dyes/gray", "items": ["minecraft:gray_dye"]}, {"id": "forge:enchanting_fuels", "items": ["minecraft:lapis_lazuli"]}, {"id": "minecraft:leaves", "items": ["minecraft:jungle_leaves", "minecraft:oak_leaves", "minecraft:spruce_leaves", "minecraft:dark_oak_leaves", "minecraft:acacia_leaves", "minecraft:birch_leaves", "minecraft:azalea_leaves", "minecraft:flowering_azalea_leaves", "minecraft:mangrove_leaves", "minecraft:cherry_leaves", "tconstruct:earth_slime_leaves", "tconstruct:sky_slime_leaves", "tconstruct:ender_slime_leaves"]}, {"id": "mekanism:colorable/glass", "items": ["minecraft:glass", "minecraft:white_stained_glass", "minecraft:orange_stained_glass", "minecraft:magenta_stained_glass", "minecraft:light_blue_stained_glass", "minecraft:yellow_stained_glass", "minecraft:lime_stained_glass", "minecraft:pink_stained_glass", "minecraft:gray_stained_glass", "minecraft:light_gray_stained_glass", "minecraft:cyan_stained_glass", "minecraft:purple_stained_glass", "minecraft:blue_stained_glass", "minecraft:brown_stained_glass", "minecraft:green_stained_glass", "minecraft:red_stained_glass", "minecraft:black_stained_glass"]}, {"id": "minecraft:wooden_fences", "items": ["minecraft:oak_fence", "minecraft:acacia_fence", "minecraft:dark_oak_fence", "minecraft:spruce_fence", "minecraft:birch_fence", "minecraft:jungle_fence", "minecraft:crimson_fence", "minecraft:warped_fence", "minecraft:mangrove_fence", "minecraft:bamboo_fence", "minecraft:cherry_fence", "cataclysm:chorus_fence", "tconstruct:greenheart_fence", "tconstruct:skyroot_fence", "tconstruct:bloodshroom_fence", "tconstruct:enderbark_fence"]}, {"id": "forge:gems/prismarine", "items": ["minecraft:prismarine_crystals"]}, {"id": "forge:raw_materials/iron", "items": ["minecraft:raw_iron"]}, {"id": "balm:diamonds", "items": ["minecraft:diamond"]}, {"id": "ae2:all_quartz_dust", "items": ["ae2:certus_quartz_dust"]}, {"id": "c:shovels", "items": ["immersiveengineering:shovel_steel", "minecraft:diamond_shovel", "minecraft:golden_shovel", "minecraft:wooden_shovel", "minecraft:stone_shovel", "minecraft:iron_shovel", "minecraft:netherite_shovel"]}, {"id": "forge:armors/chestplates", "items": ["minecraft:leather_chestplate", "minecraft:chainmail_chestplate", "minecraft:iron_chestplate", "minecraft:golden_chestplate", "minecraft:diamond_chestplate", "minecraft:netherite_chestplate", "immersiveengineering:armor_steel_chestplate", "immersiveengineering:armor_faraday_chestplate", "create:copper_backtank", "create:netherite_backtank", "mekanism:hazmat_gown", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate"]}, {"id": "create:sleepers", "items": ["minecraft:stone_slab", "minecraft:smooth_stone_slab", "minecraft:andesite_slab"]}, {"id": "forge:glass/tinted", "items": ["minecraft:tinted_glass", "tconstruct:clear_tinted_glass"]}, {"id": "forge:glass/orange", "items": ["minecraft:orange_stained_glass", "tconstruct:orange_clear_stained_glass"]}, {"id": "forge:slabs/sandstone", "items": ["minecraft:sandstone_slab", "minecraft:cut_sandstone_slab", "minecraft:smooth_sandstone_slab", "minecraft:red_sandstone_slab", "minecraft:cut_red_sandstone_slab", "minecraft:smooth_red_sandstone_slab"]}, {"id": "create:stone_types/calcite", "items": ["create:cut_calcite", "create:cut_calcite_stairs", "create:cut_calcite_wall", "create:polished_cut_calcite", "create:polished_cut_calcite_stairs", "create:polished_cut_calcite_wall", "create:cut_calcite_bricks", "create:cut_calcite_brick_stairs", "create:cut_calcite_brick_wall", "create:small_calcite_bricks", "create:small_calcite_brick_stairs", "create:small_calcite_brick_wall", "create:layered_calcite", "create:calcite_pillar", "minecraft:calcite"]}, {"id": "mekanism:crystals/iron", "items": ["mekanism:crystal_iron"]}, {"id": "create:stone_types/ochrum", "items": ["create:cut_ochrum", "create:cut_ochrum_stairs", "create:cut_ochrum_wall", "create:polished_cut_ochrum", "create:polished_cut_ochrum_stairs", "create:polished_cut_ochrum_wall", "create:cut_ochrum_bricks", "create:cut_ochrum_brick_stairs", "create:cut_ochrum_brick_wall", "create:small_ochrum_bricks", "create:small_ochrum_brick_stairs", "create:small_ochrum_brick_wall", "create:layered_ochrum", "create:ochrum_pillar", "create:ochrum"]}, {"id": "c:entity_water_buckets", "items": ["minecraft:axolotl_bucket", "minecraft:cod_bucket", "minecraft:pufferfish_bucket", "minecraft:tropical_fish_bucket", "minecraft:salmon_bucket", "minecraft:tadpole_bucket"]}, {"id": "forge:bones/wither", "items": ["tconstruct:necrotic_bone"]}, {"id": "forge:glass", "items": ["minecraft:glass", "create:tiled_glass", "create:framed_glass", "create:horizontal_framed_glass", "create:vertical_framed_glass", "tconstruct:clear_glass", "minecraft:white_stained_glass", "minecraft:orange_stained_glass", "minecraft:magenta_stained_glass", "minecraft:light_blue_stained_glass", "minecraft:yellow_stained_glass", "minecraft:lime_stained_glass", "minecraft:pink_stained_glass", "minecraft:gray_stained_glass", "minecraft:light_gray_stained_glass", "minecraft:cyan_stained_glass", "minecraft:purple_stained_glass", "minecraft:blue_stained_glass", "minecraft:brown_stained_glass", "minecraft:green_stained_glass", "minecraft:red_stained_glass", "minecraft:black_stained_glass", "tconstruct:white_clear_stained_glass", "tconstruct:orange_clear_stained_glass", "tconstruct:magenta_clear_stained_glass", "tconstruct:light_blue_clear_stained_glass", "tconstruct:yellow_clear_stained_glass", "tconstruct:lime_clear_stained_glass", "tconstruct:pink_clear_stained_glass", "tconstruct:gray_clear_stained_glass", "tconstruct:light_gray_clear_stained_glass", "tconstruct:cyan_clear_stained_glass", "tconstruct:purple_clear_stained_glass", "tconstruct:blue_clear_stained_glass", "tconstruct:brown_clear_stained_glass", "tconstruct:green_clear_stained_glass", "tconstruct:red_clear_stained_glass", "tconstruct:black_clear_stained_glass", "minecraft:tinted_glass", "tconstruct:clear_tinted_glass"]}, {"id": "tconstruct:casts/multi_use/small_blade", "items": ["tconstruct:small_blade_cast"]}, {"id": "minecraft:diamond_ores", "items": ["minecraft:diamond_ore", "minecraft:deepslate_diamond_ore"]}, {"id": "c:nuggets", "items": ["minecraft:iron_nugget", "minecraft:gold_nugget", "immersiveengineering:nugget_copper", "create:copper_nugget", "tconstruct:copper_nugget", "immersiveengineering:nugget_aluminum", "immersiveengineering:nugget_lead", "mekanism:nugget_lead", "immersiveengineering:nugget_silver", "immersiveengineering:nugget_nickel", "immersiveengineering:nugget_uranium", "mekanism:nugget_uranium", "immersiveengineering:nugget_constantan", "immersiveengineering:nugget_electrum", "immersiveengineering:nugget_steel", "mekanism:nugget_steel", "tconstruct:steel_nugget", "create:zinc_nugget", "create:brass_nugget", "create:experience_nugget", "mekanism:nugget_osmium", "mekanism:nugget_tin", "mekanism:nugget_bronze", "mekanism:nugget_refined_glowstone", "mekanism:nugget_refined_obsidian", "tconstruct:netherite_nugget", "tconstruct:debris_nugget", "tconstruct:cobalt_nugget", "tconstruct:slimesteel_nugget", "tconstruct:amethyst_bronze_nugget", "tconstruct:rose_gold_nugget", "tconstruct:pig_iron_nugget", "tconstruct:cinderslime_nugget", "tconstruct:queens_slime_nugget", "tconstruct:manyullyn_nugget", "tconstruct:hepatizon_nugget", "tconstruct:soulsteel_nugget", "tconstruct:<PERSON><PERSON>e_nugget"]}, {"id": "tconstruct:casts/multi_use/tough_handle", "items": ["tconstruct:tough_handle_cast"]}, {"id": "tconstruct:skyroot_logs", "items": ["tconstruct:skyroot_log", "tconstruct:stripped_skyroot_log", "tconstruct:skyroot_wood", "tconstruct:stripped_skyroot_wood"]}, {"id": "forge:glass/black", "items": ["minecraft:black_stained_glass", "tconstruct:black_clear_stained_glass"]}, {"id": "forge:raw_materials/silver", "items": ["immersiveengineering:raw_silver"]}, {"id": "tconstruct:modifiable/armor/chestplate", "items": ["tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate"]}, {"id": "forge:dusts/iron", "items": ["immersiveengineering:dust_iron", "mekanism:dust_iron"]}, {"id": "tconstruct:tables", "items": ["tconstruct:crafting_station", "tconstruct:part_builder", "tconstruct:tinker_station"]}, {"id": "create:pulpifiable", "items": ["minecraft:bamboo", "minecraft:sugar_cane", "minecraft:oak_sapling", "minecraft:spruce_sapling", "minecraft:birch_sapling", "minecraft:jungle_sapling", "minecraft:acacia_sapling", "minecraft:dark_oak_sapling", "minecraft:azalea", "minecraft:flowering_azalea", "minecraft:mangrove_propagule", "minecraft:cherry_sapling", "tconstruct:earth_slime_sapling", "tconstruct:sky_slime_sapling", "tconstruct:ender_slime_sapling"]}, {"id": "tconstruct:patterns", "items": ["tconstruct:pattern", "tconstruct:ingot_cast", "tconstruct:nugget_cast", "tconstruct:gem_cast", "tconstruct:rod_cast", "tconstruct:repair_kit_cast", "tconstruct:plate_cast", "tconstruct:gear_cast", "tconstruct:coin_cast", "tconstruct:wire_cast", "tconstruct:pick_head_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_blade_cast", "tconstruct:adze_head_cast", "tconstruct:hammer_head_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_blade_cast", "tconstruct:large_plate_cast", "tconstruct:tool_binding_cast", "tconstruct:tough_binding_cast", "tconstruct:tool_handle_cast", "tconstruct:tough_handle_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_grip_cast", "tconstruct:helmet_plating_cast", "tconstruct:chestplate_plating_cast", "tconstruct:leggings_plating_cast", "tconstruct:boots_plating_cast", "tconstruct:maille_cast", "tconstruct:blank_sand_cast", "tconstruct:ingot_sand_cast", "tconstruct:nugget_sand_cast", "tconstruct:gem_sand_cast", "tconstruct:rod_sand_cast", "tconstruct:repair_kit_sand_cast", "tconstruct:plate_sand_cast", "tconstruct:gear_sand_cast", "tconstruct:coin_sand_cast", "tconstruct:wire_sand_cast", "tconstruct:pick_head_sand_cast", "tconstruct:small_axe_head_sand_cast", "tconstruct:small_blade_sand_cast", "tconstruct:adze_head_sand_cast", "tconstruct:hammer_head_sand_cast", "tconstruct:broad_axe_head_sand_cast", "tconstruct:broad_blade_sand_cast", "tconstruct:large_plate_sand_cast", "tconstruct:tool_binding_sand_cast", "tconstruct:tough_binding_sand_cast", "tconstruct:tool_handle_sand_cast", "tconstruct:tough_handle_sand_cast", "tconstruct:bow_limb_sand_cast", "tconstruct:bow_grip_sand_cast", "tconstruct:helmet_plating_sand_cast", "tconstruct:chestplate_plating_sand_cast", "tconstruct:leggings_plating_sand_cast", "tconstruct:boots_plating_sand_cast", "tconstruct:maille_sand_cast", "tconstruct:blank_red_sand_cast", "tconstruct:ingot_red_sand_cast", "tconstruct:nugget_red_sand_cast", "tconstruct:gem_red_sand_cast", "tconstruct:rod_red_sand_cast", "tconstruct:repair_kit_red_sand_cast", "tconstruct:plate_red_sand_cast", "tconstruct:gear_red_sand_cast", "tconstruct:coin_red_sand_cast", "tconstruct:wire_red_sand_cast", "tconstruct:pick_head_red_sand_cast", "tconstruct:small_axe_head_red_sand_cast", "tconstruct:small_blade_red_sand_cast", "tconstruct:adze_head_red_sand_cast", "tconstruct:hammer_head_red_sand_cast", "tconstruct:broad_axe_head_red_sand_cast", "tconstruct:broad_blade_red_sand_cast", "tconstruct:large_plate_red_sand_cast", "tconstruct:tool_binding_red_sand_cast", "tconstruct:tough_binding_red_sand_cast", "tconstruct:tool_handle_red_sand_cast", "tconstruct:tough_handle_red_sand_cast", "tconstruct:bow_limb_red_sand_cast", "tconstruct:bow_grip_red_sand_cast", "tconstruct:helmet_plating_red_sand_cast", "tconstruct:chestplate_plating_red_sand_cast", "tconstruct:leggings_plating_red_sand_cast", "tconstruct:boots_plating_red_sand_cast", "tconstruct:maille_red_sand_cast", "minecraft:sand", "minecraft:red_sand"]}, {"id": "forge:ingots/steel", "items": ["immersiveengineering:ingot_steel", "mekanism:ingot_steel", "tconstruct:steel_ingot"]}, {"id": "tconstruct:melting/gold/tools_costing_3", "items": ["minecraft:golden_pickaxe", "minecraft:golden_axe"]}, {"id": "tconstruct:modifiable/book_armor/puny_smelting", "items": ["tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:travelers_shield", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:plate_shield"]}, {"id": "mekanism:colorable/concrete_powder", "items": ["minecraft:white_concrete_powder", "minecraft:orange_concrete_powder", "minecraft:magenta_concrete_powder", "minecraft:light_blue_concrete_powder", "minecraft:yellow_concrete_powder", "minecraft:lime_concrete_powder", "minecraft:pink_concrete_powder", "minecraft:gray_concrete_powder", "minecraft:light_gray_concrete_powder", "minecraft:cyan_concrete_powder", "minecraft:purple_concrete_powder", "minecraft:blue_concrete_powder", "minecraft:brown_concrete_powder", "minecraft:green_concrete_powder", "minecraft:red_concrete_powder", "minecraft:black_concrete_powder"]}, {"id": "forge:fences", "items": ["minecraft:nether_brick_fence", "minecraft:oak_fence", "minecraft:acacia_fence", "minecraft:dark_oak_fence", "minecraft:spruce_fence", "minecraft:birch_fence", "minecraft:jungle_fence", "minecraft:crimson_fence", "minecraft:warped_fence", "minecraft:mangrove_fence", "minecraft:bamboo_fence", "minecraft:cherry_fence", "cataclysm:chorus_fence", "tconstruct:greenheart_fence", "tconstruct:skyroot_fence", "tconstruct:bloodshroom_fence", "tconstruct:enderbark_fence"]}, {"id": "forge:rods/aluminum", "items": ["immersiveengineering:stick_aluminum"]}, {"id": "tconstruct:casts/single_use/bow_grip", "items": ["tconstruct:bow_grip_sand_cast", "tconstruct:bow_grip_red_sand_cast"]}, {"id": "tconstruct:fireballs", "items": ["minecraft:fire_charge"]}, {"id": "forge:crops", "items": ["minecraft:beetroot", "minecraft:carrot", "minecraft:nether_wart", "minecraft:potato", "minecraft:wheat"]}, {"id": "forge:dusts/aluminum", "items": ["immersiveengineering:dust_aluminum"]}, {"id": "forge:storage_blocks/raw_copper", "items": ["minecraft:raw_copper_block"]}, {"id": "tconstruct:modifiable/ancient", "items": ["tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher"]}, {"id": "forge:fences/nether_brick", "items": ["minecraft:nether_brick_fence"]}, {"id": "forge:glass_panes", "items": ["minecraft:glass_pane", "create:tiled_glass_pane", "create:framed_glass_pane", "create:horizontal_framed_glass_pane", "create:vertical_framed_glass_pane", "tconstruct:clear_glass_pane", "minecraft:white_stained_glass_pane", "minecraft:orange_stained_glass_pane", "minecraft:magenta_stained_glass_pane", "minecraft:light_blue_stained_glass_pane", "minecraft:yellow_stained_glass_pane", "minecraft:lime_stained_glass_pane", "minecraft:pink_stained_glass_pane", "minecraft:gray_stained_glass_pane", "minecraft:light_gray_stained_glass_pane", "minecraft:cyan_stained_glass_pane", "minecraft:purple_stained_glass_pane", "minecraft:blue_stained_glass_pane", "minecraft:brown_stained_glass_pane", "minecraft:green_stained_glass_pane", "minecraft:red_stained_glass_pane", "minecraft:black_stained_glass_pane", "tconstruct:white_clear_stained_glass_pane", "tconstruct:orange_clear_stained_glass_pane", "tconstruct:magenta_clear_stained_glass_pane", "tconstruct:light_blue_clear_stained_glass_pane", "tconstruct:yellow_clear_stained_glass_pane", "tconstruct:lime_clear_stained_glass_pane", "tconstruct:pink_clear_stained_glass_pane", "tconstruct:gray_clear_stained_glass_pane", "tconstruct:light_gray_clear_stained_glass_pane", "tconstruct:cyan_clear_stained_glass_pane", "tconstruct:purple_clear_stained_glass_pane", "tconstruct:blue_clear_stained_glass_pane", "tconstruct:brown_clear_stained_glass_pane", "tconstruct:green_clear_stained_glass_pane", "tconstruct:red_clear_stained_glass_pane", "tconstruct:black_clear_stained_glass_pane", "create:oak_window_pane", "create:spruce_window_pane", "create:birch_window_pane", "create:jungle_window_pane", "create:acacia_window_pane", "create:dark_oak_window_pane", "create:mangrove_window_pane", "create:crimson_window_pane", "create:warped_window_pane", "create:cherry_window_pane", "create:bamboo_window_pane", "create:ornate_iron_window_pane", "create:industrial_iron_window_pane", "create:weathered_iron_window_pane"]}, {"id": "forge:rods/wooden", "items": ["minecraft:stick", "immersiveengineering:stick_treated"]}, {"id": "tconstruct:modifiable/book_armor/mighty_smelting", "items": []}, {"id": "tconstruct:modifiable/melee/parry", "items": ["tconstruct:dagger"]}, {"id": "tconstruct:modifiable/interactable/armor", "items": ["tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate"]}, {"id": "create:table_cloths", "items": ["create:white_table_cloth", "create:orange_table_cloth", "create:magenta_table_cloth", "create:light_blue_table_cloth", "create:yellow_table_cloth", "create:lime_table_cloth", "create:pink_table_cloth", "create:gray_table_cloth", "create:light_gray_table_cloth", "create:cyan_table_cloth", "create:purple_table_cloth", "create:blue_table_cloth", "create:brown_table_cloth", "create:green_table_cloth", "create:red_table_cloth", "create:black_table_cloth", "create:andesite_table_cloth", "create:brass_table_cloth", "create:copper_table_cloth"]}, {"id": "tconstruct:modifiable/interactable", "items": ["tconstruct:crossbow", "tconstruct:longbow", "tconstruct:travelers_shield", "tconstruct:plate_shield", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:flint_and_brick", "tconstruct:travelers_chestplate", "tconstruct:plate_chestplate", "tconstruct:slime_chestplate"]}, {"id": "minecraft:stone_buttons", "items": ["minecraft:stone_button", "minecraft:polished_blackstone_button"]}, {"id": "forge:ingots/aluminum", "items": ["immersiveengineering:ingot_aluminum"]}, {"id": "tconstruct:melting/iron/tools_costing_11", "items": []}, {"id": "create:postboxes", "items": ["create:white_postbox", "create:orange_postbox", "create:magenta_postbox", "create:light_blue_postbox", "create:yellow_postbox", "create:lime_postbox", "create:pink_postbox", "create:gray_postbox", "create:light_gray_postbox", "create:cyan_postbox", "create:purple_postbox", "create:blue_postbox", "create:brown_postbox", "create:green_postbox", "create:red_postbox", "create:black_postbox"]}, {"id": "tconstruct:melting/steel/tools_costing_2", "items": ["immersiveengineering:sword_steel"]}, {"id": "minecraft:ignored_by_piglin_babies", "items": ["minecraft:leather"]}, {"id": "mekanism:shards", "items": ["mekanism:shard_iron", "mekanism:shard_gold", "mekanism:shard_osmium", "mekanism:shard_copper", "mekanism:shard_tin", "mekanism:shard_lead", "mekanism:shard_uranium"]}, {"id": "forge:ingots/rose_gold", "items": ["tconstruct:rose_gold_ingot"]}, {"id": "forge:cobblestone/normal", "items": ["minecraft:cobblestone"]}, {"id": "tconstruct:melting/constantan/tools_costing_3", "items": []}, {"id": "forge:sheetmetals/gold", "items": ["immersiveengineering:sheetmetal_gold"]}, {"id": "forge:mushrooms", "items": ["minecraft:brown_mushroom", "minecraft:red_mushroom"]}, {"id": "forge:nuggets/tin", "items": ["mekanism:nugget_tin"]}, {"id": "minecraft:decorated_pot_sherds", "items": ["minecraft:angler_pottery_sherd", "minecraft:archer_pottery_sherd", "minecraft:arms_up_pottery_sherd", "minecraft:blade_pottery_sherd", "minecraft:brewer_pottery_sherd", "minecraft:burn_pottery_sherd", "minecraft:danger_pottery_sherd", "minecraft:explorer_pottery_sherd", "minecraft:friend_pottery_sherd", "minecraft:heart_pottery_sherd", "minecraft:heartbreak_pottery_sherd", "minecraft:howl_pottery_sherd", "minecraft:miner_pottery_sherd", "minecraft:mourner_pottery_sherd", "minecraft:plenty_pottery_sherd", "minecraft:prize_pottery_sherd", "minecraft:sheaf_pottery_sherd", "minecraft:shelter_pottery_sherd", "minecraft:skull_pottery_sherd", "minecraft:snort_pottery_sherd"]}, {"id": "tconstruct:melting/diamond/tools_costing_2", "items": ["minecraft:diamond_sword", "minecraft:diamond_hoe"]}, {"id": "tconstruct:melting/lead/tools_costing_1", "items": []}, {"id": "forge:storage_blocks/refined_obsidian", "items": ["mekanism:block_refined_obsidian"]}, {"id": "tconstruct:bones", "items": ["minecraft:bone"]}, {"id": "forge:plates/iron", "items": ["immersiveengineering:plate_iron", "create:iron_sheet"]}, {"id": "forge:foods", "items": ["minecraft:apple", "minecraft:mushroom_stew", "minecraft:bread", "minecraft:porkchop", "minecraft:cooked_porkchop", "minecraft:golden_apple", "minecraft:enchanted_golden_apple", "minecraft:cod", "minecraft:salmon", "minecraft:tropical_fish", "minecraft:pufferfish", "minecraft:cooked_cod", "minecraft:cooked_salmon", "minecraft:cookie", "minecraft:melon_slice", "minecraft:dried_kelp", "minecraft:beef", "minecraft:cooked_beef", "minecraft:chicken", "minecraft:cooked_chicken", "minecraft:rotten_flesh", "minecraft:spider_eye", "minecraft:carrot", "minecraft:potato", "minecraft:baked_potato", "minecraft:poisonous_potato", "minecraft:golden_carrot", "minecraft:pumpkin_pie", "minecraft:rabbit", "minecraft:cooked_rabbit", "minecraft:rabbit_stew", "minecraft:mutton", "minecraft:cooked_mutton", "minecraft:chorus_fruit", "minecraft:beetroot", "minecraft:beetroot_soup", "minecraft:suspicious_stew", "minecraft:sweet_berries", "minecraft:glow_berries", "minecraft:honey_bottle"]}, {"id": "forge:storage_blocks/brass", "items": ["create:brass_block"]}, {"id": "forge:armors/leggings", "items": ["minecraft:leather_leggings", "minecraft:chainmail_leggings", "minecraft:iron_leggings", "minecraft:golden_leggings", "minecraft:diamond_leggings", "minecraft:netherite_leggings", "immersiveengineering:armor_steel_leggings", "immersiveengineering:armor_faraday_leggings", "create:cardboard_leggings", "mekanism:hazmat_pants", "tconstruct:travelers_leggings", "tconstruct:plate_leggings", "tconstruct:slime_leggings"]}, {"id": "minecraft:lectern_books", "items": ["minecraft:written_book", "minecraft:writable_book", "immersiveengineering:manual", "tconstruct:materials_and_you", "tconstruct:tinkers_gadgetry", "tconstruct:puny_smelting", "tconstruct:mighty_smelting", "tconstruct:fantastic_foundry", "tconstruct:encyclopedia"]}, {"id": "forge:dusts/quartz", "items": ["mekanism:dust_quartz"]}, {"id": "forge:fences/wooden", "items": ["minecraft:oak_fence", "minecraft:acacia_fence", "minecraft:dark_oak_fence", "minecraft:spruce_fence", "minecraft:birch_fence", "minecraft:jungle_fence", "minecraft:crimson_fence", "minecraft:warped_fence", "minecraft:mangrove_fence", "minecraft:bamboo_fence", "minecraft:cherry_fence", "cataclysm:chorus_fence", "tconstruct:greenheart_fence", "tconstruct:skyroot_fence", "tconstruct:bloodshroom_fence", "tconstruct:enderbark_fence"]}, {"id": "tconstruct:melting/iron/tools_costing_1", "items": ["minecraft:iron_shovel", "minecraft:flint_and_steel", "minecraft:shield"]}, {"id": "minecraft:dampens_vibrations", "items": ["minecraft:white_wool", "minecraft:orange_wool", "minecraft:magenta_wool", "minecraft:light_blue_wool", "minecraft:yellow_wool", "minecraft:lime_wool", "minecraft:pink_wool", "minecraft:gray_wool", "minecraft:light_gray_wool", "minecraft:cyan_wool", "minecraft:purple_wool", "minecraft:blue_wool", "minecraft:brown_wool", "minecraft:green_wool", "minecraft:red_wool", "minecraft:black_wool", "minecraft:white_carpet", "minecraft:orange_carpet", "minecraft:magenta_carpet", "minecraft:light_blue_carpet", "minecraft:yellow_carpet", "minecraft:lime_carpet", "minecraft:pink_carpet", "minecraft:gray_carpet", "minecraft:light_gray_carpet", "minecraft:cyan_carpet", "minecraft:purple_carpet", "minecraft:blue_carpet", "minecraft:brown_carpet", "minecraft:green_carpet", "minecraft:red_carpet", "minecraft:black_carpet"]}, {"id": "forge:sand", "items": ["minecraft:sand", "minecraft:red_sand"]}, {"id": "curios:head", "items": ["cataclysm:kobolediator_skull", "cataclysm:aptrgangr_head", "cataclysm:draugr_head", "immersiveengineering:earmuffs", "create:goggles"]}, {"id": "forge:dusts/fluix", "items": ["ae2:fluix_dust"]}, {"id": "forge:books/guide", "items": ["tconstruct:materials_and_you", "tconstruct:tinkers_gadgetry", "tconstruct:puny_smelting", "tconstruct:mighty_smelting", "tconstruct:fantastic_foundry", "tconstruct:encyclopedia"]}, {"id": "forge:crops/wheat", "items": ["minecraft:wheat"]}, {"id": "forge:slabs/red_sandstone", "items": ["minecraft:red_sandstone_slab", "minecraft:cut_red_sandstone_slab", "minecraft:smooth_red_sandstone_slab"]}, {"id": "forge:ore_bearing_ground/netherrack", "items": ["minecraft:netherrack"]}, {"id": "immersiveengineering:toolbox/wiring", "items": ["immersiveengineering:wirecoil_copper", "immersiveengineering:wirecoil_electrum", "immersiveengineering:wirecoil_steel", "immersiveengineering:wirecoil_structure_rope", "immersiveengineering:wirecoil_structure_steel", "immersiveengineering:wirecoil_redstone", "immersiveengineering:wirecoil_copper_ins", "immersiveengineering:wirecoil_electrum_ins", "immersiveengineering:connector_hv_relay", "immersiveengineering:connector_mv_relay", "immersiveengineering:connector_lv_relay", "immersiveengineering:connector_hv", "immersiveengineering:connector_mv", "immersiveengineering:connector_lv", "immersiveengineering:connector_structural", "immersiveengineering:transformer", "immersiveengineering:transformer_hv", "immersiveengineering:breaker_switch", "immersiveengineering:redstone_breaker", "immersiveengineering:current_transformer", "immersiveengineering:connector_redstone", "immersiveengineering:connector_probe", "immersiveengineering:connector_bundled", "immersiveengineering:balloon", "immersiveengineering:razor_wire"]}, {"id": "cataclysm:sticky_item", "items": ["minecraft:slime_ball", "minecraft:slime_block", "minecraft:honey_block", "cataclysm:sticky_gloves"]}, {"id": "tconstruct:modifiable/aoe", "items": ["tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:travelers_boots", "tconstruct:plate_boots", "tconstruct:slime_boots"]}, {"id": "forge:treated_wood_slab", "items": ["immersiveengineering:slab_treated_wood_horizontal", "immersiveengineering:slab_treated_wood_vertical", "immersiveengineering:slab_treated_wood_packaged"]}, {"id": "c:glass_blocks", "items": ["minecraft:glass", "minecraft:gray_stained_glass", "minecraft:black_stained_glass", "minecraft:orange_stained_glass", "minecraft:blue_stained_glass", "minecraft:brown_stained_glass", "minecraft:cyan_stained_glass", "minecraft:green_stained_glass", "minecraft:light_blue_stained_glass", "minecraft:light_gray_stained_glass", "minecraft:lime_stained_glass", "minecraft:magenta_stained_glass", "minecraft:pink_stained_glass", "minecraft:purple_stained_glass", "minecraft:red_stained_glass", "minecraft:tinted_glass", "minecraft:white_stained_glass", "minecraft:yellow_stained_glass"]}, {"id": "ae2:knife", "items": ["ae2:certus_quartz_cutting_knife", "ae2:nether_quartz_cutting_knife"]}, {"id": "tconstruct:patterns/default", "items": ["tconstruct:pattern"]}]