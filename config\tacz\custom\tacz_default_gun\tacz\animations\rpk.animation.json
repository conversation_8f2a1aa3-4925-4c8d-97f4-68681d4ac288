{"format_version": "1.8.0", "animations": {"static_idle": {"loop": true, "bones": {"righthand": {"rotation": [95.04292, 367.4713, 180.6574], "position": [-7, -14.275, 7], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": [100.36601, 316.5798, -142.78527], "position": [7.775, -13, -6], "scale": [1, 1.5, 1]}, "constraint": {"rotation": [0.2, 0.2, 0.1], "position": [0.2, 0.2, 0.3]}}}, "draw": {"animation_length": 1.13333, "bones": {"righthand": {"rotation": [95.04292, 367.4713, 180.6574], "position": [-7, -14.275, 7], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": [100.36601, 316.5798, -142.78527], "position": [7.775, -13, -6], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": [22.5, -22.5, 32.5], "0.1667": {"pre": [16.81, -14.77, 27.74], "post": [16.81, -14.77, 27.74], "lerp_mode": "catmullrom"}, "0.2667": {"post": [10.38725, -13.74806, 10.06185], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-0.88995, -1.54338, 7.15196], "lerp_mode": "catmullrom"}, "0.5667": [1.10685, 1.42586, -1.99731], "0.6667": {"pre": [0.39065, 0.294, 2.28025], "post": [0.39065, 0.294, 2.28025], "lerp_mode": "catmullrom"}, "0.7667": {"post": [0.03946, 0.20234, -1.89002], "lerp_mode": "catmullrom"}, "0.9": {"post": [0.03, 0.12, 0.67], "lerp_mode": "catmullrom"}, "1.1333": [0, 0, 0]}, "position": {"0.0": {"post": [-5, -14, 0], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-3.79, -7.23, -0.38], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-0.3, -0.975, -0.6], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0.01, -0.3, 0.5], "lerp_mode": "catmullrom"}, "0.8": [-0.01, -0.07, -0.32], "1.0": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "group7": {"rotation": {"0.2333": [0, 0, 0], "0.3667": [1.5, 0, 0], "0.5667": [-1, 0, 0], "0.7333": [0.975, 0, 0], "0.8667": [0, 0, 0], "1.0333": [0, 0, 0]}}, "group2": {"rotation": {"0.2667": [0, 0, 0], "0.4": [1.5, 0, 0], "0.6": [-1, 0, 0], "0.7667": [0.975, 0, 0], "0.9": [0, 0, 0], "1.0667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.55, -0.05, 0.1], "lerp_mode": "catmullrom"}, "0.2333": {"post": [1.1, -0.3, 0.7], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.7, -0.2, 0.3], "lerp_mode": "catmullrom"}, "0.6": {"post": [-0.1, 0, 0.4], "lerp_mode": "catmullrom"}, "0.7": {"post": [0.2, 0.1, -0.3], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 0.1], "lerp_mode": "catmullrom"}, "0.9333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0667": {"effect": "p04_lm_rkilo_raise_first_end"}}}, "put_away": {"animation_length": 0.6, "bones": {"righthand": {"rotation": [95.04292, 367.4713, 180.6574], "position": [-7, -14.275, 7], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": {"post": [100.36601, 316.5798, -142.78527], "lerp_mode": "catmullrom"}, "0.2": {"post": [153.89111, 367.50964, -225.61909], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [7.775, -13, -6], "lerp_mode": "catmullrom"}, "0.1": {"post": [9.355, -11.45, -4.2], "lerp_mode": "catmullrom"}, "0.1667": {"post": [9.07, -9.445, -3.6], "lerp_mode": "catmullrom"}, "0.2333": {"post": [8.78, -9, -3], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.64, -3.66, -2.19], "lerp_mode": "catmullrom"}, "0.2": {"post": [1.8, -12.38, 1.96], "lerp_mode": "catmullrom"}, "0.3667": {"post": [6.24, -23.59, 10.25], "lerp_mode": "catmullrom"}, "0.5333": {"post": [11.63972, -43.66226, 20.28897], "lerp_mode": "catmullrom"}, "0.6": {"post": [13.35, -52.02, 23.22], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.45, -0.49, 0.28], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.67, -1.47, 0.34], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.435, -2.62, 0.06], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0.92, -4.65, -0.335], "lerp_mode": "catmullrom"}, "0.6": {"post": [1.97, -5.47, -0.37], "lerp_mode": "catmullrom"}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0.3, 0, -0.125], "lerp_mode": "catmullrom"}, "0.3": {"post": [0.64, 0, 0.3], "lerp_mode": "catmullrom"}, "0.4667": {"post": [0.5, 0, 0.15], "lerp_mode": "catmullrom"}, "0.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "p04_lm_rkilo_drop_rattle"}}}, "reload_tactical": {"animation_length": 3.06667, "bones": {"righthand": {"rotation": [95.04292, 367.4713, 180.6574], "position": [-7, -14.275, 7], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.4333": [100.36601, 316.5798, -142.78527], "0.5667": [112.59014, 359.11105, -202.40769], "0.6667": [115.27176, 366.47125, -197.18811], "1.3667": [115.27176, 366.47125, -197.18811], "1.4667": [124.27176, 366.47125, -197.18811], "2.1333": [124.27176, 366.47125, -197.18811], "2.2667": [100.36601, 316.5798, -142.78527]}, "position": {"0.0": [7.775, -13, -6], "0.4333": [7.775, -13, -6], "0.5333": [8.66, -15.165, -2.18], "0.6667": [7.18, -15, 0.35], "1.3667": [7.18, -15, 0.35], "1.4667": [7.18, -16.275, -0.9], "2.1333": [7.18, -16.275, -0.9], "2.2667": [7.775, -13, -6]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-1.25, 0, -3.6], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-5.37, -0.45, -5.49], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-7.79901, -0.34533, -6.8002], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-7.93988, -0.26802, -12.37905], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-7.09421, -1.49163, -18.16483], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-5.08937, -1.57806, -18.91185], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-5.10245, -1.66009, -21.07978], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-7.66415, -0.35361, -15.69746], "lerp_mode": "catmullrom"}, "0.9": {"post": [-9.00696, -0.40889, -20.49759], "lerp_mode": "catmullrom"}, "1.0": {"post": [-8.88285, -1.30105, -18.91215], "lerp_mode": "catmullrom"}, "1.1": {"post": [-6.82592, -1.44935, -21.3622], "lerp_mode": "catmullrom"}, "1.2667": {"post": [-4.8691, -0.41163, -21.57935], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-5.49, -0.23, -21.38], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-7.98102, -0.34026, -21.03393], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-12.19817, -1.74916, -20.65666], "lerp_mode": "catmullrom"}, "1.8": {"post": [-12.17757, -2.09274, -20.59958], "lerp_mode": "catmullrom"}, "1.9333": {"post": [-10.66477, -2.23004, -20.43371], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-8.56877, -0.42303, -9.60771], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-11.0932, -0.4255, -16.68604], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-11.04212, 0.71521, -9.04441], "lerp_mode": "catmullrom"}, "2.2333": {"post": [-7.45283, -0.53539, -8.27735], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-0.26433, -1.10299, -9.4432], "lerp_mode": "catmullrom"}, "2.4333": {"post": [2.6377, -0.70754, -6.76675], "lerp_mode": "catmullrom"}, "2.5667": {"post": [0.00698, -0.09976, -5.60001], "lerp_mode": "catmullrom"}, "2.6667": {"post": [0.08242, -0.19876, -2.36596], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-0.18865, -0.22812, 0.60001], "lerp_mode": "catmullrom"}, "2.8": {"post": [0.1, 0.3125, -1.5], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0.41875, 0.3125, 0.775], "lerp_mode": "catmullrom"}, "3.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0.025, -0.075, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.58, 0.225, -0.1], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.755, -0.15, 0.55], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-0.905, -0.05, 0.805], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-0.805, -0.15, 1.03], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-0.705, -0.275, 1.03], "lerp_mode": "catmullrom"}, "0.9": {"post": [-0.53, -0.625, 0.98], "lerp_mode": "catmullrom"}, "1.0": {"post": [-0.83, -0.895, 0.98], "lerp_mode": "catmullrom"}, "1.1333": {"post": [-0.84, -0.78, 1.03], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-0.975, -0.84, 1.07], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-0.995, -0.69, 1.07], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.94, 0.06, 1.32], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-0.825, 0.01, 1.365], "lerp_mode": "catmullrom"}, "1.9333": {"post": [-0.815, -0.22, 1.42], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-0.84, -0.62, 1.72], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-0.8, -0.455, 1.595], "lerp_mode": "catmullrom"}, "2.1667": {"post": [-0.515, -0.52, 1.525], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-0.39, -0.715, 1.425], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-0.34, -1.66, 0.75], "lerp_mode": "catmullrom"}, "2.4333": {"post": [-0.34, -1.76, 0.455], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-0.01, -0.575, -0.95], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.1, -0.325, -1.125], "lerp_mode": "catmullrom"}, "2.8": {"post": [0, -0.29375, 0.4875], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0, -0.0875, -0.06875], "lerp_mode": "catmullrom"}, "2.9667": {"post": [0, 0, 0.05], "lerp_mode": "catmullrom"}, "3.0667": [0, 0, 0]}, "scale": {"2.6333": {"pre": [1, 1, 1], "post": [1, 1, 1], "lerp_mode": "catmullrom"}}}, "lefthand_and_mag": {"rotation": {"0.7667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-25, 0, 0], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-33.36659, -0.88548, -27.75259], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-20.63159, -7.43797, -37.83793], "lerp_mode": "catmullrom"}, "1.1333": {"post": [9.36841, -7.43797, -37.83793], "lerp_mode": "catmullrom"}, "1.5": {"post": [9.36841, -7.43797, -37.83793], "lerp_mode": "catmullrom"}, "1.7": [-30.04292, -11.68381, -23.81804], "1.7667": [-24.24769, -6.27967, -13.64973], "1.8333": [-25, 0, 0], "1.9333": [-25, 0, 0], "1.9667": [0, 0, 0], "2.1333": [0, 0, 0]}, "position": {"0.7667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-0.04741, -0.49658, -2.5693], "lerp_mode": "catmullrom"}, "0.9333": {"post": [1.95468, -3.16027, -6.81572], "lerp_mode": "catmullrom"}, "1.0667": {"post": [3.58373, -9.25381, -2.22233], "lerp_mode": "catmullrom"}, "1.1333": {"post": [2.93308, -12.66257, 4.20662], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.93308, -12.66257, 4.20662], "lerp_mode": "catmullrom"}, "1.6333": [2.69, -7.99, -1.67], "1.7": [2.395, -1.47, -3.765], "1.7667": [1.30259, -0.49658, -2.5693], "1.8333": [-0.04741, -0.49658, -2.5693], "1.9333": [-0.04741, -0.49658, -2.5693], "1.9667": [0, 0, 0], "2.1333": [0, 0, 0]}}, "release": {"rotation": {"0.6": [0, 0, 0], "0.6667": [-17.5, 0, 0], "1.1333": [-17.5, 0, 0], "1.2333": [0, 0, 0], "1.9333": [0, 0, 0], "2.0": [-17.5, 0, 0], "2.0667": [0, 0, 0]}}, "bullet": {"scale": {"1.4": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "group7": {"rotation": {"0.1667": [0, 0, 0], "0.3": [1.5, 0, 0], "0.5": [-1, 0, 0], "0.6667": [0.975, 0, 0], "0.8": [0, 0, 0], "0.9333": [1.5, 0, 0], "1.1333": [-1, 0, 0], "1.3": [0.975, 0, 0], "1.6": [-0.475, 0, 0], "1.9333": [0, 0, 0], "2.0667": [1.5, 0, 0], "2.2667": [0, 0, 0], "2.4": [1.5, 0, 0], "2.6": [-1, 0, 0], "2.7667": [0.975, 0, 0], "2.9": [0, 0, 0], "3.0333": [0, 0, 0]}}, "group2": {"rotation": {"0.0": [0, 0, 0], "0.1333": [1.5, 0, 0], "0.3333": [-1, 0, 0], "0.5": [0.975, 0, 0], "0.6333": [0, 0, 0], "0.7667": [1.5, 0, 0], "0.9667": [-1, 0, 0], "1.1333": [0.975, 0, 0], "1.4333": [-0.475, 0, 0], "1.7667": [0, 0, 0], "1.9": [1.5, 0, 0], "2.1": [0, 0, 0], "2.2333": [1.5, 0, 0], "2.4333": [-1, 0, 0], "2.6": [0.975, 0, 0], "2.7333": [0, 0, 0], "2.8667": [0, 0, 0]}}, "constraint": {"rotation": {"2.4": [0.1, 0.1, 0.1], "2.7667": [0.2, 0.2, 0.1]}, "position": {"2.4": [0.05, 0.1, 0.1], "2.7667": [0.2, 0.2, 0.3]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.505, 0.175, -0.4], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0.425, 0.325, -0.8], "lerp_mode": "catmullrom"}, "0.6333": {"post": [0.48, 0.325, -1.2], "lerp_mode": "catmullrom"}, "0.8": {"post": [0.89, 0.33, -1.23], "lerp_mode": "catmullrom"}, "0.8667": {"post": [1.795, 0.33, -0.595], "lerp_mode": "catmullrom"}, "0.9667": {"post": [1.13, 0.325, -1.675], "lerp_mode": "catmullrom"}, "1.1": {"post": [1.43, 0.31, -0.88], "lerp_mode": "catmullrom"}, "1.2667": {"post": [1.24, 0.28, -1.24], "lerp_mode": "catmullrom"}, "1.5": {"post": [1.46, 0.25, -1.1], "lerp_mode": "catmullrom"}, "1.8": {"post": [0.465, 0.25, -1.1], "lerp_mode": "catmullrom"}, "1.9333": {"post": [0.71, 0.25, -1.1], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.46, 0.25, -0.375], "lerp_mode": "catmullrom"}, "2.1": {"post": [0.755, 0.25, -1.485], "lerp_mode": "catmullrom"}, "2.2": {"post": [0.965, 0.25, -0.925], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0.28, 0.24, -1.11], "lerp_mode": "catmullrom"}, "2.5": {"post": [-0.56, 0.2, -1], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0.325, 0, 0.3], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-0.225, 0, -0.15], "lerp_mode": "catmullrom"}, "2.9667": {"post": [0.075, 0, 0.1], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "ak47_drum_reload_twist"}, "0.4667": {"effect": "p37_lm_dblmg_reload_fast_boltcharge"}, "0.6667": {"effect": "ak47_drum_reload_grabmag"}, "1.7": {"effect": "ar_aki<PERSON>_reload_xmag_empty_maghit"}, "1.8667": {"effect": "ak47_reload_magin_01"}, "2.1667": {"effect": "ak47_drum_reload_end"}}}, "reload_tactical_xmag": {"animation_length": 3.46667, "bones": {"righthand": {"rotation": [95.04292, 367.4713, 180.6574], "position": [-7, -14.275, 7], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.4": [100.36601, 316.5798, -142.78527], "0.5": [112.66482, 332.64797, -163.88504], "0.6": [105.14061, 340.44862, -132.82415], "1.3333": [105.14061, 340.44862, -132.82415], "1.4667": [105.14061, 340.44862, -132.82415], "2.1667": [105.14, 340.45, -132.82], "2.3": [101.00401, 331.35423, -139.17068], "2.3333": [101.00401, 331.35423, -139.17068], "2.3667": [105.676, 335.62944, -134.25197], "2.4333": [105.676, 335.62944, -134.25197], "2.5333": {"pre": [101.00401, 331.35423, -139.17068], "post": [101.00401, 331.35423, -139.17068], "lerp_mode": "catmullrom"}, "2.6667": {"post": [100.36601, 316.5798, -142.78527], "lerp_mode": "catmullrom"}}, "position": {"0.0": [7.775, -13, -6], "0.4": [7.775, -13, -6], "0.4667": [9.37084, -17.94296, -5.37839], "0.6": [7.85964, -18.72371, -0.65141], "1.3333": [7.85964, -18.72371, -0.65141], "1.4667": [7.85964, -18.72371, -0.65141], "2.1667": [7.86, -18.72, -0.65], "2.3": [9.885, -24.32, -2.15], "2.3333": [9.885, -26.045, -0.875], "2.3667": [9.21, -18.72, -0.65], "2.4333": [9.21, -18.72, -0.65], "2.5333": {"pre": [10.585, -24.42, -4.6], "post": [10.585, -24.42, -4.6], "lerp_mode": "catmullrom"}, "2.6667": {"post": [7.775, -13, -6], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-1.25, 0, -3.6], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-5.37, -0.45, -5.49], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-7.79901, -0.34533, -6.8002], "lerp_mode": "catmullrom"}, "0.4": {"post": [-7.93988, -0.26802, -12.37905], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-7.09421, -1.49163, -18.16483], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-5.08937, -1.57806, -18.91185], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-5.10245, -1.66009, -21.07978], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-7.66415, -0.35361, -15.69746], "lerp_mode": "catmullrom"}, "0.9": {"post": [-9.00696, -0.40889, -20.49759], "lerp_mode": "catmullrom"}, "0.9667": {"post": [-8.96933, -0.36926, -12.98425], "lerp_mode": "catmullrom"}, "1.1": {"post": [-6.82592, -1.44935, -21.3622], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-4.88591, -0.07139, -17.59381], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-5.49, -0.23, -21.38], "lerp_mode": "catmullrom"}, "1.5": {"post": [-7.98102, -0.34026, -21.03393], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-12.19817, -1.74916, -20.65666], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-12.7, -1.27, -16.64], "lerp_mode": "catmullrom"}, "1.8": {"post": [-13.58882, -0.60475, -13.75747], "lerp_mode": "catmullrom"}, "1.9": {"post": [-9.80195, -0.28835, -14.35765], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-9.585, -0.10171, -15.08071], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-9.53931, -0.25065, -16.69803], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-7.12245, -2.61897, -24.45227], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-8.90258, 0.72915, -10.79807], "lerp_mode": "catmullrom"}, "2.2333": {"post": [-10.16865, -0.55613, -17.4341], "lerp_mode": "catmullrom"}, "2.3": {"post": [-9.96899, -0.158, -15.4902], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-8.86585, -1.05992, -19.64596], "lerp_mode": "catmullrom"}, "2.4333": {"post": [-9.14553, 1.01842, -7.37063], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-8.32131, -0.1524, -14.98148], "lerp_mode": "catmullrom"}, "2.6": {"post": [-7.33317, -1.43722, -15.22012], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-0.26433, -1.10299, -9.4432], "lerp_mode": "catmullrom"}, "2.8333": {"post": [2.6377, -0.70754, -6.76675], "lerp_mode": "catmullrom"}, "2.9667": {"post": [0.00698, -0.09976, -5.60001], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0.08242, -0.19876, -2.36596], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.18865, -0.22812, 0.60001], "lerp_mode": "catmullrom"}, "3.1667": {"post": [0.1, 0.3125, -1.5], "lerp_mode": "catmullrom"}, "3.2667": {"post": [0.41875, 0.3125, 0.775], "lerp_mode": "catmullrom"}, "3.3667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0.025, -0.075, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.58, 0.225, -0.1], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.755, -0.15, 0.55], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-0.905, -0.05, 0.805], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-0.805, -0.15, 1.03], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-0.705, 0.05, 1.03], "lerp_mode": "catmullrom"}, "0.9": {"post": [-0.53, -0.625, 0.98], "lerp_mode": "catmullrom"}, "0.9667": {"post": [-0.83, -1.1575, 0.98], "lerp_mode": "catmullrom"}, "1.1333": {"post": [-0.84, -1.07375, 1.03], "lerp_mode": "catmullrom"}, "1.3": {"post": [-0.975, -1.04, 1.07], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-0.995, -0.69, 1.07], "lerp_mode": "catmullrom"}, "1.6": {"post": [-0.94, 0.06, 1.32], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-0.825, 0.01, 1.365], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-0.81, -0.21875, 1.38], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-0.81, -0.31375, 1.38], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-0.815, -0.22, 1.42], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-0.84, -0.995, 1.72], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-0.8, -0.68, 1.595], "lerp_mode": "catmullrom"}, "2.3": {"post": [-0.755, -0.685, 1.56], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-0.57, -1.455, 1.55], "lerp_mode": "catmullrom"}, "2.4": {"post": [-0.54, -1.435, 1.54], "lerp_mode": "catmullrom"}, "2.4333": {"post": [-0.54, -0.975, 1.54], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-0.515, -0.62, 1.525], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-0.39, -0.965, 1.425], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-0.34, -2.31, 0.75], "lerp_mode": "catmullrom"}, "2.8333": {"post": [-0.34, -2.41, 0.455], "lerp_mode": "catmullrom"}, "3.0": {"post": [-0.01, -0.425, -0.95], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.1, -0.175, -1.125], "lerp_mode": "catmullrom"}, "3.2": {"post": [0, -0.41875, 0.4875], "lerp_mode": "catmullrom"}, "3.3": {"post": [0, -0.0875, -0.06875], "lerp_mode": "catmullrom"}, "3.3667": {"post": [0, 0, 0.05], "lerp_mode": "catmullrom"}, "3.4667": [0, 0, 0]}, "scale": {"3.0333": {"pre": [1, 1, 1], "post": [1, 1, 1], "lerp_mode": "catmullrom"}}}, "lefthand_and_mag": {"rotation": {"0.7333": [0, 0, 0], "0.8333": [-25, 0, 0], "0.9": [-29, 0, 0], "0.9333": [-36.36478, 1.53479, -6.46221], "0.9667": [-33.09434, 4.60437, -19.38664], "1.1333": {"pre": [-20.63159, -7.43797, -37.83793], "post": [-20.63159, -7.43797, -37.83793], "lerp_mode": "catmullrom"}, "1.2333": {"post": [9.36841, -7.43797, -37.83793], "lerp_mode": "catmullrom"}, "1.5": {"post": [9.36841, -7.43797, -37.83793], "lerp_mode": "catmullrom"}, "1.6667": [-39.65136, -4.76384, -16.72274], "1.7667": [-24.78606, -3.37192, -7.25886], "1.8333": [-24.98663, -0.8451, -1.81275], "1.8667": [-24.98663, -0.8451, -1.81275], "1.9333": [-19.66, -0.28, -0.6], "2.0333": [-19.66, -0.28, -0.6], "2.0667": [0, 0, 0], "2.3667": [0, 0, 0]}, "position": {"0.7333": [0, 0, 0], "0.8333": [-0.04741, -0.49658, -2.5693], "0.9": [-0.04741, -0.39658, -2.8943], "0.9667": [1.95468, -3.16027, -6.81572], "1.1333": {"pre": [3.58373, -9.25381, -2.22233], "post": [3.58373, -9.25381, -2.22233], "lerp_mode": "catmullrom"}, "1.2333": {"post": [2.93308, -12.66257, 4.20662], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.93308, -12.66257, 4.20662], "lerp_mode": "catmullrom"}, "1.6667": [1.54, -3.57, -1.965], "1.7667": [0.90259, -0.67158, -2.5693], "1.8333": [0.35259, -0.79658, -2.1943], "1.8667": [0.35259, -0.79658, -2.1943], "1.9333": [0.09, -0.6, -1.71], "2.0333": [0.09, -0.6, -1.71], "2.0667": [0, 0, 0], "2.3667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.505, 0.175, -0.4], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0.425, 0.325, -0.8], "lerp_mode": "catmullrom"}, "0.6333": {"post": [0.48, 0.325, -1.2], "lerp_mode": "catmullrom"}, "0.8": {"post": [0.89, 0.33, -1.23], "lerp_mode": "catmullrom"}, "0.8667": {"post": [1.895, 0.33, -0.595], "lerp_mode": "catmullrom"}, "0.9667": {"post": [1.205, 0.325, -1.675], "lerp_mode": "catmullrom"}, "1.1": {"post": [1.655, 0.31, -0.88], "lerp_mode": "catmullrom"}, "1.2667": {"post": [1.24, 0.28, -1.24], "lerp_mode": "catmullrom"}, "1.5": {"post": [1.235, 0.25, -1.1], "lerp_mode": "catmullrom"}, "1.7333": {"post": [0.02, 0.25, -1.09], "lerp_mode": "catmullrom"}, "1.9333": {"post": [0.59, 0.25, -1.1], "lerp_mode": "catmullrom"}, "2.0667": {"post": [0.71, 0.25, -1.1], "lerp_mode": "catmullrom"}, "2.1333": {"post": [1.46, 0.25, -0.375], "lerp_mode": "catmullrom"}, "2.2333": {"post": [0.755, 0.25, -1.485], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0.965, 0.25, -0.925], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.725, 0.25, -1.52], "lerp_mode": "catmullrom"}, "2.5": {"post": [0.89, 0.25, -0.345], "lerp_mode": "catmullrom"}, "2.6333": {"post": [1.075, 0.25, -1.46], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-0.195, 0.24, -0.985], "lerp_mode": "catmullrom"}, "2.9": {"post": [-1.06, 0.2, -1], "lerp_mode": "catmullrom"}, "3.1667": {"post": [0.325, 0, 0.45], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-0.225, 0, -0.375], "lerp_mode": "catmullrom"}, "3.3667": {"post": [0.075, 0, 0.1], "lerp_mode": "catmullrom"}, "3.4667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "release": {"rotation": {"0.6": [0, 0, 0], "0.6667": [-17.5, 0, 0], "1.1333": [-17.5, 0, 0], "1.2333": [0, 0, 0], "2.0333": [0, 0, 0], "2.1": [-17.5, 0, 0], "2.1667": [0, 0, 0]}}, "bullet": {"scale": {"1.5333": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "group7": {"rotation": {"0.0667": [0, 0, 0], "0.2": [1.5, 0, 0], "0.4": [-1, 0, 0], "0.5667": [0.975, 0, 0], "0.7667": [0, 0, 0], "0.9": [1.5, 0, 0], "1.1": [-1, 0, 0], "1.2667": [0.975, 0, 0], "1.5667": [-0.475, 0, 0], "1.9": [0.225, 0, 0], "2.1": [0, 0, 0], "2.2333": [1.5, 0, 0], "2.4333": [-1, 0, 0], "2.6": [0.975, 0, 0], "2.7667": [0, 0, 0], "2.9": [1.5, 0, 0], "3.1": [-1, 0, 0], "3.2667": [0.975, 0, 0], "3.4667": [0, 0, 0]}}, "group2": {"rotation": {"0.0": [0, 0, 0], "0.1333": [1.5, 0, 0], "0.3333": [-1, 0, 0], "0.5": [0.975, 0, 0], "0.7": [0, 0, 0], "0.8333": [1.5, 0, 0], "1.0333": [-1, 0, 0], "1.2": [0.975, 0, 0], "1.5": [-0.475, 0, 0], "1.8333": [0.225, 0, 0], "2.0333": [0, 0, 0], "2.1667": [1.5, 0, 0], "2.3667": [-1, 0, 0], "2.5333": [0.975, 0, 0], "2.7": [0, 0, 0], "2.8333": [1.5, 0, 0], "3.0333": [-1, 0, 0], "3.2": [0.975, 0, 0], "3.4": [0, 0, 0]}}}, "sound_effects": {"0.0": {"effect": "ak47_drum_reload_twist"}, "0.4": {"effect": "ak47_drum_reload_grabmag"}, "0.6333": {"effect": "p37_lm_dblmg_reload_fast_boltcharge"}, "1.7667": {"effect": "ak47_reload_magin_01"}, "2.5333": {"effect": "ak47_drum_reload_end"}}}, "reload_empty": {"animation_length": 4.03333, "bones": {"righthand": {"rotation": {"0.0": [95.04292, 367.4713, 180.6574], "2.3667": [95.04, 367.47, 180.66], "2.5667": [108.5922, 347.81416, 258.11891], "2.6333": [97.35129, 355.66951, 272.61968], "2.7333": [97.35129, 355.66951, 272.61968], "2.8": [110.8578, 325.95337, 268.19137], "2.9": [110.8578, 325.95337, 268.19137], "2.9667": [129.09464, 356.0725, 217.24724], "3.1": [95.04, 367.47, 180.66]}, "position": {"0.0": [-7, -14.275, 7], "2.3667": [-7, -14.27, 7], "2.4667": [-10.66, -11.47, 1.165], "2.5667": [-9.125, -10.07, -1.025], "2.6333": [-9.125, -10.07, -1.025], "2.7333": [-9.125, -10.07, -1.025], "2.8": [-9.13, -9.99, 4.645], "2.9": [-9.13, -9.99, 4.645], "2.9667": [-10.38, -12.29, 7.225], "3.1": [-7, -14.27, 7]}, "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.0333": [100.36601, 316.5798, -142.78527], "0.2": [112.59014, 359.11105, -202.40769], "0.3": [115.27176, 366.47125, -197.18811], "0.5": [115.27, 366.47, -197.19], "0.5333": [131.2702, 366.47014, -197.18978], "0.6667": {"pre": [134.60556, 348.44918, -243.25038], "post": [134.60556, 348.44918, -243.25038], "lerp_mode": "catmullrom"}, "0.8333": {"post": [131.4708, 372.20668, -216.56128], "lerp_mode": "catmullrom"}, "1.4667": [115.27, 366.47, -197.19], "2.1": [115.27, 366.47, -197.19], "2.2667": [100.36601, 316.5798, -142.78527]}, "position": {"0.0": [7.775, -13, -6], "0.0333": [7.775, -13, -6], "0.1667": [8.66, -15.165, -2.18], "0.3": [7.18, -15, 0.35], "0.5": [7.18, -15, 0.35], "0.5333": [7.34828, -15.32274, -1.35524], "0.6": [8.7557, -16.37564, -4.15704], "0.6667": {"pre": [9.50251, -16.6999, -3.89092], "post": [9.50251, -16.6999, -3.89092], "lerp_mode": "catmullrom"}, "0.8": {"post": [12.31208, -18.95782, 0.11183], "lerp_mode": "catmullrom"}, "0.9": {"post": [14.20699, -22.21559, 5.08178], "lerp_mode": "catmullrom"}, "1.0667": {"post": [14.17293, -28.48956, 11.60848], "lerp_mode": "catmullrom"}, "1.4": [14.17293, -28.48956, 11.60848], "1.4667": [7.605, -16.95, -1.65], "2.1": [7.6, -16.95, -1.65], "2.2667": [7.775, -13, -6]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-6.36488, -0.24302, -15.67905], "lerp_mode": "catmullrom"}, "0.3": {"post": [-7.12854, -1.2661, -14.0033], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-7.73045, -1.50062, -13.96963], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-6.75283, -1.67657, -15.50961], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-7.10809, -1.83692, -19.87223], "lerp_mode": "catmullrom"}, "0.7": {"post": [-5.23381, -1.14011, -11.85078], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-4.45725, -1.9431, -18.02724], "lerp_mode": "catmullrom"}, "0.9667": {"post": [-7.95737, -0.34861, -11.38843], "lerp_mode": "catmullrom"}, "1.2": {"post": [-10.75, -1.06, -13.105], "lerp_mode": "catmullrom"}, "1.4": {"post": [-11.34, -1.69, -18.535], "lerp_mode": "catmullrom"}, "1.5667": {"post": [-10.89759, -2.05396, -18.31007], "lerp_mode": "catmullrom"}, "1.7": {"post": [-11.25, -1.71, -21.245], "lerp_mode": "catmullrom"}, "1.8": {"post": [-11.24995, -2.39484, -22.8925], "lerp_mode": "catmullrom"}, "1.9": {"post": [-9.83836, -2.16266, -19.55805], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-8.33978, -0.75965, -13.3346], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-8.16038, -1.54732, -17.42649], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-10.82814, -0.54653, -14.25739], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-6.5093, 0.6993, -11.32246], "lerp_mode": "catmullrom"}, "2.4333": {"post": [-11.75415, -4.76828, 6.3371], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-7.765, -7.57, 8.88], "lerp_mode": "catmullrom"}, "2.7": {"post": [-8.275, -10.86, 8.85], "lerp_mode": "catmullrom"}, "2.8": {"post": [-8.84007, -6.40354, 9.93287], "lerp_mode": "catmullrom"}, "2.9": {"post": [-8.09478, -10.12929, 2.56626], "lerp_mode": "catmullrom"}, "2.9667": {"post": [-8.26304, -10.83854, 4.45922], "lerp_mode": "catmullrom"}, "3.0": {"post": [-8.49171, -7.41398, 9.55469], "lerp_mode": "catmullrom"}, "3.0667": {"post": [-7.265, -5.8, 7.225], "lerp_mode": "catmullrom"}, "3.1667": {"post": [-8.9418, -5.99931, 9.44808], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-4.68649, -6.18086, 6.18938], "lerp_mode": "catmullrom"}, "3.3667": {"post": [-2.09003, -3.92983, 5.63078], "lerp_mode": "catmullrom"}, "3.5": {"post": [-2.95254, -1.17612, -4.88863], "lerp_mode": "catmullrom"}, "3.6": {"post": [-1.27185, 1.22333, -2.32728], "lerp_mode": "catmullrom"}, "3.7": {"post": [0.54156, -0.10069, -0.24997], "lerp_mode": "catmullrom"}, "3.7667": {"post": [0.84472, 0.25762, 2.475], "lerp_mode": "catmullrom"}, "3.8667": {"post": [-0.1875, 0.375, -0.35], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-1.005, -0.35, -0.15], "lerp_mode": "catmullrom"}, "0.3": {"post": [-1.205, -0.575, 0.405], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-1.005, -0.5, 0.33], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-1.03, -0.55, 0.33], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-0.755, -1.45, 0.28], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-1.105, -1.67, 0.28], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-1.29, -1.03, 0.33], "lerp_mode": "catmullrom"}, "1.1333": {"post": [-1.225, 0.185, 0.37], "lerp_mode": "catmullrom"}, "1.3": {"post": [-1.365, -0.14, 0.37], "lerp_mode": "catmullrom"}, "1.4": {"post": [-1.265, -0.955, 0.46], "lerp_mode": "catmullrom"}, "1.5667": {"post": [-1.365, -1.11, 0.62], "lerp_mode": "catmullrom"}, "1.8": {"post": [-1.415, -0.715, 0.62], "lerp_mode": "catmullrom"}, "1.9": {"post": [-1.405, -0.68, 0.73], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-1.135, -1.31, 0.82], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-1.365, -1.295, 0.95], "lerp_mode": "catmullrom"}, "2.2": {"post": [-1.365, -1.54, 1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-1.33, -2.6, 1.15], "lerp_mode": "catmullrom"}, "2.4333": {"post": [-0.13, -2.15, 1.51], "lerp_mode": "catmullrom"}, "2.5": {"post": [0.385, -1.62, 1.75], "lerp_mode": "catmullrom"}, "2.6333": {"post": [0.405, -1.27, 1.76], "lerp_mode": "catmullrom"}, "2.7333": {"post": [0.405, -2.125, 1.77], "lerp_mode": "catmullrom"}, "2.8333": {"post": [0.415, -2.305, 1.78], "lerp_mode": "catmullrom"}, "2.9333": {"post": [0.415, -2.485, 1.79], "lerp_mode": "catmullrom"}, "3.0": {"post": [0.415, -1.915, 1.8], "lerp_mode": "catmullrom"}, "3.0333": {"post": [0.285, -2.27, 1.75], "lerp_mode": "catmullrom"}, "3.2": {"post": [0.285, -3.07, 1.75], "lerp_mode": "catmullrom"}, "3.3667": {"post": [-0.09, -1.65, 0.935], "lerp_mode": "catmullrom"}, "3.5": {"post": [-0.06, 0.355, -0.76], "lerp_mode": "catmullrom"}, "3.6": {"post": [0.01, 0.55, -0.8], "lerp_mode": "catmullrom"}, "3.7": {"post": [0.06, -0.52, -0.03], "lerp_mode": "catmullrom"}, "3.7667": {"post": [0, -0.15, 0.675], "lerp_mode": "catmullrom"}, "3.8333": {"post": [0, 0, -0.2], "lerp_mode": "catmullrom"}, "3.9333": {"post": [0, 0.01, 0.11], "lerp_mode": "catmullrom"}, "4.0333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_bullet": {"rotation": {"0.5": [0, 0, 0], "0.5667": {"pre": [-22.03979, -0.44355, -1.99393], "post": [-22.03979, -0.44355, -1.99393], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-42.34884, -2.67309, -6.47186], "lerp_mode": "catmullrom"}, "0.7": {"post": [-65.86313, -1.87691, -13.16818], "lerp_mode": "catmullrom"}, "0.8": {"post": [-91.48081, 14.06173, -37.44567], "lerp_mode": "catmullrom"}, "1.0": {"post": [-185.03883, 15.48834, -39.17157], "lerp_mode": "catmullrom"}, "1.1333": {"post": [-199.31626, 16.10734, -35.41906], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-19.49529, 26.19787, -19.02592], "lerp_mode": "catmullrom"}, "1.3": {"post": [-19.49529, 26.19787, -19.02592], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-19.49529, 26.19787, -19.02592], "lerp_mode": "catmullrom"}, "1.4667": [0, 0, 0]}, "position": {"0.5": [0, 0, 0], "0.5667": {"pre": [0.14, -0.28, -1.98], "post": [0.14, -0.28, -1.98], "lerp_mode": "catmullrom"}, "0.6333": {"post": [1, -0.8, -4.8], "lerp_mode": "catmullrom"}, "0.7": {"post": [2.12474, -4.09082, -5.63445], "lerp_mode": "catmullrom"}, "0.8": {"post": [3.01227, -10.6545, -5.57922], "lerp_mode": "catmullrom"}, "1.0": {"post": [7.6, -24.325, -4.9], "lerp_mode": "catmullrom"}, "1.1333": {"post": [6.35, -31.91, -2.67], "lerp_mode": "catmullrom"}, "1.2333": {"post": [8.695, -20.95, 12.91], "lerp_mode": "catmullrom"}, "1.3": {"post": [8.695, -20.95, 12.91], "lerp_mode": "catmullrom"}, "1.4333": {"post": [8.695, -20.95, 12.91], "lerp_mode": "catmullrom"}, "1.4667": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "1.1": {"pre": [1, 1, 1], "post": [0, 0, 0]}, "1.4667": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bolt": {"position": {"2.7333": [0, 0, 0], "2.8": [0, 0, 5.55], "2.9": [0, 0, 5.55], "2.9667": [0, 0, 0], "3.0333": [0, 0, 0.875], "3.1": [0, 0, 0]}}, "lefthand_and_mag": {"rotation": {"1.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5667": [4.08132, 4.1819, -69.38004], "1.6667": {"pre": [-32.27331, -6.7355, -12.30175], "post": [-32.27331, -6.7355, -12.30175], "lerp_mode": "catmullrom"}, "1.7667": [-22.63665, -3.36775, -6.15087], "1.8333": [-25.85045, -5.19155, -9.99996], "1.9": [-25.63665, -3.36775, -6.15087], "1.9333": [-29, 0, 0], "1.9667": [0, 0, 0], "2.0667": [0, 0, 0]}, "position": {"1.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.4667": {"post": [10.05, -17.93, 10.25], "lerp_mode": "catmullrom"}, "1.5667": {"post": [6.37, -11.16, 0.33], "lerp_mode": "catmullrom"}, "1.6667": {"post": [0.60477, -3.2333, -3.51912], "lerp_mode": "catmullrom"}, "1.7667": [0.6, -0.75, -2.00625], "1.8333": [0.975, -0.65, -2.545], "1.9": [0.6, -0.55, -2.225], "1.9333": [0, -0.5, -2.375], "1.9667": [0, 0, 0], "2.0667": [0, 0, 0]}}, "release": {"rotation": {"0.3667": [0, 0, 0], "0.4333": [-17.5, 0, 0], "0.9333": [-17.5, 0, 0], "1.0333": [0, 0, 0], "1.9667": [0, 0, 0], "2.0333": [-17.5, 0, 0], "2.1": [0, 0, 0]}}, "bullet": {"scale": {"1.2333": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bullet_in_barrel": {"scale": 0}, "group7": {"rotation": {"0.0": [0, 0, 0], "0.1333": [1.5, 0, 0], "0.3333": [-1, 0, 0], "0.5": [0.975, 0, 0], "0.8": [-0.475, 0, 0], "1.1333": [0.225, 0, 0], "1.5": [0, 0, 0], "1.8333": [0, 0, 0], "1.9667": [1.5, 0, 0], "2.1667": [-1, 0, 0], "2.3333": [0.975, 0, 0], "2.6333": [-0.475, 0, 0], "2.9667": [0, 0, 0], "3.1": [1.5, 0, 0], "3.3": [-1, 0, 0], "3.4667": [0.975, 0, 0], "3.7": [0, 0, 0]}}, "group2": {"rotation": {"0.0": [0, 0, 0], "0.1333": [1.5, 0, 0], "0.3333": [-1, 0, 0], "0.5": [0.975, 0, 0], "0.8": [-0.475, 0, 0], "1.1333": [0.225, 0, 0], "1.5": [0, 0, 0], "1.8333": [0, 0, 0], "1.9667": [1.5, 0, 0], "2.1667": [-1, 0, 0], "2.3333": [0.975, 0, 0], "2.6333": [-0.475, 0, 0], "2.9667": [0, 0, 0], "3.1": [1.5, 0, 0], "3.3": [-1, 0, 0], "3.4667": [0.975, 0, 0], "3.7": [0, 0, 0]}}, "constraint": {"rotation": {"3.4333": [0.1, 0.1, 0.1], "3.8667": [0.2, 0.2, 0.1]}, "position": {"3.4333": [0.05, 0.1, 0.1], "3.8667": [0.2, 0.2, 0.3]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0.195, 0.05, -0.2], "lerp_mode": "catmullrom"}, "0.2333": {"post": [1.5, 0.125, -0.475], "lerp_mode": "catmullrom"}, "0.4": {"post": [1.775, 0.17, -0.58], "lerp_mode": "catmullrom"}, "0.5667": {"post": [1.53, 0.2, -0.65], "lerp_mode": "catmullrom"}, "0.6333": {"post": [2.325, 0.075, 0.19], "lerp_mode": "catmullrom"}, "0.7333": {"post": [2.055, -0.075, -1.25], "lerp_mode": "catmullrom"}, "0.8333": {"post": [2.655, -0.05, -0.325], "lerp_mode": "catmullrom"}, "0.9667": {"post": [2.645, 0.1, -0.65], "lerp_mode": "catmullrom"}, "1.1333": {"post": [2.35, 0.2, -0.5], "lerp_mode": "catmullrom"}, "1.4667": {"post": [0.255, 0.2, -1.125], "lerp_mode": "catmullrom"}, "1.6667": {"post": [0.655, 0.2, -1.075], "lerp_mode": "catmullrom"}, "1.8333": {"post": [0.85, 0.2, -0.925], "lerp_mode": "catmullrom"}, "1.9": {"post": [1.82, 0.22, -0.065], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.2, 0.23, -1.115], "lerp_mode": "catmullrom"}, "2.1": {"post": [1.9, 0.2, -0.5], "lerp_mode": "catmullrom"}, "2.2667": {"post": [2.59, 0.03, -0.215], "lerp_mode": "catmullrom"}, "2.4333": {"post": [3.125, -0.15, 0.55], "lerp_mode": "catmullrom"}, "2.7333": {"post": [2.51, -0.21, 0.62], "lerp_mode": "catmullrom"}, "2.8333": {"post": [3.07, -0.22, 1.31], "lerp_mode": "catmullrom"}, "2.9": {"post": [2.105, -0.22, 0.075], "lerp_mode": "catmullrom"}, "2.9667": {"post": [3.42, -0.22, 1.515], "lerp_mode": "catmullrom"}, "3.0667": {"post": [2.355, -0.23, -0.12], "lerp_mode": "catmullrom"}, "3.2": {"post": [3.225, -0.24, 0.985], "lerp_mode": "catmullrom"}, "3.3667": {"post": [2.73, -0.225, 0.475], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-0.6, 0, -0.35], "lerp_mode": "catmullrom"}, "3.7333": {"post": [0.275, 0, 0.3], "lerp_mode": "catmullrom"}, "3.8": {"post": [-0.23, 0, -0.245], "lerp_mode": "catmullrom"}, "3.8667": {"post": [0.1, 0, 0.45], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "p04_lm_rkilo_reload_empty_raise"}, "0.1333": {"effect": "p04_lm_rkilo_reload_empty_magout"}, "1.2333": {"effect": "iw8_phys_mag_drop_large_drum_dirt_02"}, "1.7333": {"effect": "p04_lm_rkilo_reload_empty_raise"}, "1.7667": {"effect": "p04_lm_rkilo_reload_empty_rattle"}, "2.6667": {"effect": "p04_lm_rkilo_reload_empty_boltcharge"}, "2.8333": {"effect": "p04_lm_rkilo_reload_empty_end_end"}}}, "reload_empty_xmag": {"animation_length": 4.53333, "bones": {"righthand": {"rotation": {"0.0": [95.04292, 367.4713, 180.6574], "2.8": [95.04, 367.47, 180.66], "3.0667": [69.15891, 381.24358, 99.87891], "3.1333": [80.83786, 380.21069, 110.98463], "3.2333": [80.83786, 380.21069, 110.98463], "3.3": [86.07599, 408.90092, 116.51668], "3.4": [86.07599, 408.90092, 116.51668], "3.5": [128.1151, 370.22268, 199.68188], "3.6": [95.04, 367.47, 180.66]}, "position": {"0.0": [-7, -14.275, 7], "2.8": [-7, -14.27, 7], "2.9": [-10.66, -11.47, 1.165], "3.0667": [-9.125, -10.07, -1.025], "3.1333": [-9.125, -10.07, -1.025], "3.2333": [-9.125, -10.07, -1.025], "3.3": [-9.13, -9.99, 4.645], "3.4": [-9.13, -9.99, 4.645], "3.4667": [-10.38, -12.29, 7.225], "3.6": [-7, -14.27, 7]}, "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.1667": [100.36601, 316.5798, -142.78527], "0.3333": [112.59014, 359.11105, -202.40769], "0.4333": [115.27176, 366.47125, -197.18811], "0.6": [115.27176, 366.47125, -197.18811], "0.6333": [115.27176, 366.47125, -197.18811], "0.6667": [125.27176, 366.47125, -197.18811], "0.8": {"pre": [134.60556, 348.44918, -243.25038], "post": [134.60556, 348.44918, -243.25038], "lerp_mode": "catmullrom"}, "0.9667": {"post": [131.4708, 372.20668, -216.56128], "lerp_mode": "catmullrom"}, "1.5333": [131.4708, 372.20668, -216.56128], "1.6": [100.53377, 336.05693, -118.38141], "2.5333": [100.53377, 336.05693, -118.38141], "2.7": [100.36601, 316.5798, -142.78527]}, "position": {"0.0": [7.775, -13, -6], "0.1667": [7.775, -13, -6], "0.3": [11.41, -15.165, -2.18], "0.4333": [10.98, -15, 0.35], "0.6": [10.98, -15, 0.35], "0.6333": [10.98, -15, 0.35], "0.6667": [10.98, -15.725, -0.075], "0.7333": [11.30371, -17.26829, -4.1286], "0.8": {"pre": [12.01826, -16.53435, -3.97301], "post": [12.01826, -16.53435, -3.97301], "lerp_mode": "catmullrom"}, "0.9333": {"post": [14.21789, -19.56428, 0.10302], "lerp_mode": "catmullrom"}, "1.0333": {"post": [14.20699, -22.21559, 5.08178], "lerp_mode": "catmullrom"}, "1.2": {"post": [14.17293, -28.48956, 11.60848], "lerp_mode": "catmullrom"}, "1.5333": [14.17293, -28.48956, 11.60848], "1.6": [7.6, -18.95, -1.65], "2.5333": [7.6, -18.95, -1.65], "2.7": [7.775, -13, -6]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-5.74901, -0.32033, -7.0002], "lerp_mode": "catmullrom"}, "0.3": {"post": [-6.36488, -0.24302, -15.67905], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-7.12854, -1.2661, -14.0033], "lerp_mode": "catmullrom"}, "0.5": {"post": [-7.73045, -1.50062, -13.96963], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-6.75283, -1.67657, -15.50961], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-7.10809, -1.83692, -19.87223], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-5.23381, -1.14011, -11.85078], "lerp_mode": "catmullrom"}, "0.9": {"post": [-4.45725, -1.9431, -18.02724], "lerp_mode": "catmullrom"}, "1.1": {"post": [-7.95737, -0.34861, -11.38843], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-10.75, -1.06, -13.105], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-11.34, -1.69, -21.385], "lerp_mode": "catmullrom"}, "1.7": {"post": [-10.89759, -2.05396, -21.16007], "lerp_mode": "catmullrom"}, "1.8333": {"post": [-11.25, -1.71, -18.67], "lerp_mode": "catmullrom"}, "1.9": {"post": [-11.38712, -1.60948, -17.56793], "lerp_mode": "catmullrom"}, "2.0": {"post": [-10.20355, -1.34501, -16.83739], "lerp_mode": "catmullrom"}, "2.1": {"post": [-10.05408, -0.47982, -11.6039], "lerp_mode": "catmullrom"}, "2.2333": {"post": [-8.82776, -1.50781, -15.34809], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-10.04431, -0.73599, -10.72953], "lerp_mode": "catmullrom"}, "2.4": {"post": [-8.33978, -0.75965, -13.3346], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-8.16038, -1.54732, -17.42649], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-10.82814, -0.54653, -14.25739], "lerp_mode": "catmullrom"}, "2.7": {"post": [-7.5593, -0.3507, -11.32246], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-17.40415, -10.79328, 6.3371], "lerp_mode": "catmullrom"}, "3.0333": {"post": [-47.34, -18.52, 9.58], "lerp_mode": "catmullrom"}, "3.1333": {"post": [-53.85083, -20.54336, 10.75259], "lerp_mode": "catmullrom"}, "3.2": {"post": [-54.6, -21.235, 8.85], "lerp_mode": "catmullrom"}, "3.3": {"post": [-55.16507, -16.77854, 9.93287], "lerp_mode": "catmullrom"}, "3.4": {"post": [-54.41978, -20.50429, 2.56626], "lerp_mode": "catmullrom"}, "3.4667": {"post": [-46.11304, -21.21354, 1.98422], "lerp_mode": "catmullrom"}, "3.5": {"post": [-44.21671, -13.11398, 11.70469], "lerp_mode": "catmullrom"}, "3.5667": {"post": [-35.19, -19.4, 4.35], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-27.2918, -16.37431, 9.44808], "lerp_mode": "catmullrom"}, "3.7667": {"post": [-23.48649, -11.60586, 2.28938], "lerp_mode": "catmullrom"}, "3.8667": {"post": [-19.39003, -3.00483, -2.49422], "lerp_mode": "catmullrom"}, "4.0333": {"post": [-6.85254, -1.17612, -4.88863], "lerp_mode": "catmullrom"}, "4.1333": {"post": [-1.27185, 1.22333, -2.32728], "lerp_mode": "catmullrom"}, "4.2333": {"post": [0.54156, -0.10069, -1.59997], "lerp_mode": "catmullrom"}, "4.3": {"post": [0.84472, 0.25762, 2.475], "lerp_mode": "catmullrom"}, "4.4": {"post": [-0.1875, 0.375, -0.35], "lerp_mode": "catmullrom"}, "4.5333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.28, -0.675, -0.175], "lerp_mode": "catmullrom"}, "0.3": {"post": [-1.005, -0.35, -0.15], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-1.205, -0.575, 0.405], "lerp_mode": "catmullrom"}, "0.6": {"post": [-1.005, -0.5, 0.33], "lerp_mode": "catmullrom"}, "0.7": {"post": [-1.03, -0.55, 0.33], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-0.755, -1.45, 0.28], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-1.105, -1.67, 0.28], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-1.29, -1.03, 0.33], "lerp_mode": "catmullrom"}, "1.2667": {"post": [-1.225, 0.535, 0.37], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-0.99, 0.21, 0.37], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-0.89, -0.355, 0.46], "lerp_mode": "catmullrom"}, "1.7": {"post": [-0.99, -0.51, 0.62], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-1.02, -1.015, 0.62], "lerp_mode": "catmullrom"}, "1.9333": {"post": [-1.03, -0.93125, 0.62], "lerp_mode": "catmullrom"}, "2.0": {"post": [-1.055, -1.195, 0.62], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-1.05, -0.96, 0.61], "lerp_mode": "catmullrom"}, "2.2333": {"post": [-1.04, -0.965, 0.62], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-1.03, -1.08, 0.73], "lerp_mode": "catmullrom"}, "2.4": {"post": [-0.76, -2.185, 0.82], "lerp_mode": "catmullrom"}, "2.5": {"post": [-0.99, -2.12, 0.95], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-0.99, -2.965, 1], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-0.955, -4.3, 1.15], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-0.505, -5.325, 0.735], "lerp_mode": "catmullrom"}, "3.0333": {"post": [0.06, -7.795, -0.275], "lerp_mode": "catmullrom"}, "3.1333": {"post": [1.18, -8.995, -0.515], "lerp_mode": "catmullrom"}, "3.2333": {"post": [1.33, -8.825, -0.705], "lerp_mode": "catmullrom"}, "3.3333": {"post": [1.34, -9.005, -0.695], "lerp_mode": "catmullrom"}, "3.4333": {"post": [1.34, -9.185, -0.685], "lerp_mode": "catmullrom"}, "3.5": {"post": [1.34, -8.615, -0.675], "lerp_mode": "catmullrom"}, "3.5333": {"post": [1.21, -8.97, -0.725], "lerp_mode": "catmullrom"}, "3.7": {"post": [1.21, -9.245, -0.725], "lerp_mode": "catmullrom"}, "3.8667": {"post": [0.835, -7.625, -1.54], "lerp_mode": "catmullrom"}, "4.0333": {"post": [-0.06, -1.195, -0.76], "lerp_mode": "catmullrom"}, "4.1667": {"post": [0.01, 0.025, -0.8], "lerp_mode": "catmullrom"}, "4.2667": {"post": [0.06, -0.22, 0.545], "lerp_mode": "catmullrom"}, "4.3333": {"post": [0, -0.15, 0.675], "lerp_mode": "catmullrom"}, "4.4333": {"post": [0, 0, -0.05], "lerp_mode": "catmullrom"}, "4.5667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_bullet": {"rotation": {"0.6333": [0, 0, 0], "0.7": {"pre": [-22.03979, -0.44355, -1.99393], "post": [-22.03979, -0.44355, -1.99393], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-42.34884, -2.67309, -6.47186], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-65.86313, -1.87691, -13.16818], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-91.48081, 14.06173, -37.44567], "lerp_mode": "catmullrom"}, "1.1333": {"post": [-185.03883, 15.48834, -39.17157], "lerp_mode": "catmullrom"}, "1.2667": {"post": [-199.31626, 16.10734, -35.41906], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-19.49529, 26.19787, -19.02592], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-19.49529, 26.19787, -19.02592], "lerp_mode": "catmullrom"}, "1.5667": {"post": [-19.49529, 26.19787, -19.02592], "lerp_mode": "catmullrom"}, "1.6": [0, 0, 0]}, "position": {"0.6333": [0, 0, 0], "0.7": {"pre": [0.14, -0.28, -1.98], "post": [0.14, -0.28, -1.98], "lerp_mode": "catmullrom"}, "0.7667": {"post": [1, -0.8, -4.8], "lerp_mode": "catmullrom"}, "0.8333": {"post": [2.12474, -4.09082, -5.63445], "lerp_mode": "catmullrom"}, "0.9333": {"post": [0.4477, -9.89384, -5.58261], "lerp_mode": "catmullrom"}, "1.1333": {"post": [1.66405, -23.1639, -5.03716], "lerp_mode": "catmullrom"}, "1.2667": {"post": [6.35, -31.91, -2.67], "lerp_mode": "catmullrom"}, "1.3667": {"post": [8.695, -20.95, 12.91], "lerp_mode": "catmullrom"}, "1.4333": {"post": [8.695, -20.95, 12.91], "lerp_mode": "catmullrom"}, "1.5667": {"post": [8.695, -20.95, 12.91], "lerp_mode": "catmullrom"}, "1.6": [0, 0, 0]}, "scale": {"1.2333": {"pre": [1, 1, 1], "post": [0, 0, 0]}, "1.6": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bolt": {"position": {"3.2333": [0, 0, 0], "3.3": [0, 0, 5.55], "3.4": [0, 0, 5.55], "3.4667": [0, 0, 0], "3.5333": [0, 0, 0.875], "3.6": [0, 0, 0], "3.6667": [0, 0, 0.275], "3.7333": [0, 0, 0.1]}}, "lefthand_and_mag": {"rotation": {"1.5": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.7": [4.08132, 4.1819, -69.38004], "1.8333": {"pre": [-31.52753, -9.90832, -17.44914], "post": [-31.52753, -9.90832, -17.44914], "lerp_mode": "catmullrom"}, "1.9333": [-22.63665, -3.36775, -6.15087], "2.0": [-22.63665, -3.36775, -6.15087], "2.1": [-22.38597, -4.2537, -8.13342], "2.2": [-22.38597, -4.2537, -8.13342], "2.2667": [-24.08368, -5.0091, -9.8085], "2.3333": [-25.63665, -3.36775, -6.15087], "2.3667": [-29, 0, 0], "2.4": [0, 0, 0], "2.5": [0, 0, 0]}, "position": {"1.5": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.6": {"post": [10.05, -17.93, 10.25], "lerp_mode": "catmullrom"}, "1.7": {"post": [6.37, -11.16, 0.33], "lerp_mode": "catmullrom"}, "1.8333": {"post": [2.07977, -3.2333, -3.51912], "lerp_mode": "catmullrom"}, "1.9333": [0.6, -0.5, -2.40625], "2.0": [0.6, -0.5, -2.40625], "2.1": [0.86, -0.65, -2.43], "2.2": [0.86, -0.65, -2.43], "2.2667": [1.125, -0.49, -2.33], "2.3333": [0.6, -0.55, -2.225], "2.3667": [0, -0.5, -2.375], "2.4": [0, 0, 0], "2.5": [0, 0, 0]}}, "release": {"rotation": {"0.4667": [0, 0, 0], "0.5333": [-17.5, 0, 0], "1.0": [-17.5, 0, 0], "1.1": [0, 0, 0], "2.3667": [0, 0, 0], "2.4333": [-17.5, 0, 0], "2.5": [0, 0, 0]}}, "bullet": {"scale": {"1.2333": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bullet_in_barrel": {"scale": 0}, "group7": {"rotation": {"0.2": [0, 0, 0], "0.3333": [1.5, 0, 0], "0.5333": [-1, 0, 0], "0.7": [0.975, 0, 0], "1.0": [-0.475, 0, 0], "1.3333": [0.225, 0, 0], "1.7": [0, 0, 0], "1.9333": [0, 0, 0], "2.0667": [1.5, 0, 0], "2.2667": [-1, 0, 0], "2.4333": [0.975, 0, 0], "2.7333": [-0.475, 0, 0], "2.9667": [0, 0, 0], "3.1": [1.5, 0, 0], "3.3": [-1, 0, 0], "3.4667": [0.975, 0, 0], "3.7667": [-0.475, 0, 0], "4.1": [0.225, 0, 0], "4.4667": [0, 0, 0]}}, "group2": {"rotation": {"0.0": [0, 0, 0], "0.1333": [1.5, 0, 0], "0.3333": [-1, 0, 0], "0.5": [0.975, 0, 0], "0.8": [-0.475, 0, 0], "1.1333": [0.225, 0, 0], "1.5": [0, 0, 0], "1.7333": [0, 0, 0], "1.8667": [1.5, 0, 0], "2.0667": [-1, 0, 0], "2.2333": [0.975, 0, 0], "2.5333": [-0.475, 0, 0], "2.7667": [0, 0, 0], "2.9": [1.5, 0, 0], "3.1": [-1, 0, 0], "3.2667": [0.975, 0, 0], "3.5667": [-0.475, 0, 0], "3.9": [0.225, 0, 0], "4.2667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0.195, 0.05, -0.2], "lerp_mode": "catmullrom"}, "0.3667": {"post": [1.5, 0.125, -0.475], "lerp_mode": "catmullrom"}, "0.5333": {"post": [1.775, 0.17, -0.58], "lerp_mode": "catmullrom"}, "0.7": {"post": [1.53, 0.2, -0.65], "lerp_mode": "catmullrom"}, "0.7667": {"post": [2.325, 0.075, 0.19], "lerp_mode": "catmullrom"}, "0.8667": {"post": [2.055, -0.075, -1.25], "lerp_mode": "catmullrom"}, "0.9667": {"post": [2.655, -0.05, -0.325], "lerp_mode": "catmullrom"}, "1.1": {"post": [2.645, 0.1, -0.65], "lerp_mode": "catmullrom"}, "1.2667": {"post": [2.35, 0.2, -0.5], "lerp_mode": "catmullrom"}, "1.6": {"post": [0.255, 0.2, -1.125], "lerp_mode": "catmullrom"}, "1.8": {"post": [0.655, 0.2, -1.075], "lerp_mode": "catmullrom"}, "1.9333": {"post": [0.55, 0.2, -1.06], "lerp_mode": "catmullrom"}, "2.0": {"post": [0.945, 0.2, -0.5], "lerp_mode": "catmullrom"}, "2.1": {"post": [0.65, 0.2, -1.465], "lerp_mode": "catmullrom"}, "2.3": {"post": [0.94, 0.2, -0.955], "lerp_mode": "catmullrom"}, "2.3667": {"post": [0.85, 0.2, -0.925], "lerp_mode": "catmullrom"}, "2.4333": {"post": [1.82, 0.22, -0.065], "lerp_mode": "catmullrom"}, "2.5333": {"post": [1.2, 0.23, -1.115], "lerp_mode": "catmullrom"}, "2.6333": {"post": [1.9, 0.2, -0.5], "lerp_mode": "catmullrom"}, "2.8": {"post": [2.59, 0.03, -0.215], "lerp_mode": "catmullrom"}, "2.9667": {"post": [3.125, -0.15, 0.55], "lerp_mode": "catmullrom"}, "3.2333": {"post": [2.51, -0.21, 0.62], "lerp_mode": "catmullrom"}, "3.3333": {"post": [3.07, -0.22, 1.31], "lerp_mode": "catmullrom"}, "3.4": {"post": [2.105, -0.22, 0.075], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.42, -0.22, 1.515], "lerp_mode": "catmullrom"}, "3.5667": {"post": [2.355, -0.23, -0.12], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.225, -0.24, 0.985], "lerp_mode": "catmullrom"}, "3.8667": {"post": [2.73, -0.225, 0.475], "lerp_mode": "catmullrom"}, "4.1333": {"post": [-0.6, 0, -0.35], "lerp_mode": "catmullrom"}, "4.2333": {"post": [0.275, 0, 0.3], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-0.125, 0, -0.1], "lerp_mode": "catmullrom"}, "4.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "p04_lm_rkilo_reload_empty_raise"}, "0.3333": {"effect": "p04_lm_rkilo_reload_empty_magout"}, "1.2333": {"effect": "iw8_phys_mag_drop_large_drum_dirt_02"}, "1.9": {"effect": "p04_lm_rkilo_reload_empty_raise"}, "2.2333": {"effect": "p04_lm_rkilo_reload_empty_rattle"}, "3.1667": {"effect": "p04_lm_rkilo_reload_empty_boltcharge"}, "3.4": {"effect": "p04_lm_rkilo_reload_empty_end_end"}}}, "inspect": {"animation_length": 8.3, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.1667": {"pre": [-3.95432, -0.35524, -7.5593], "post": [-3.95432, -0.35524, -7.5593], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-18.77487, -14.73938, -42.6554], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-21.52657, -15.87239, -40.05886], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-20.25861, -16.23715, -41.02775], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-20.53075, -15.90496, -40.04584], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-20.31, -15.97, -40.25], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-19.50129, -18.05893, -44.38906], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-19.4, -17.79, -43.96], "lerp_mode": "catmullrom"}, "1.8": {"post": [-19.4, -17.79, -43.96], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-15.98698, -18.90273, -47.17888], "lerp_mode": "catmullrom"}, "2.1667": [-21.03635, -12.51673, -24.54773], "2.2333": [-21.68509, -14.72379, -30.29289], "2.3333": {"pre": [-23.93272, -16.03986, -33.0996], "post": [-23.93272, -16.03986, -33.0996], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-23.93272, -16.03986, -33.0996], "lerp_mode": "catmullrom"}, "2.4": {"post": [-23.81561, -18.75152, -38.32369], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-26.69287, -14.89123, -34.22797], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-25.15112, -14.84361, -35.22725], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-25.65312, -11.93302, -33.22913], "lerp_mode": "catmullrom"}, "2.9667": {"post": [-26.01609, -11.05842, -29.41804], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-26.01964, -11.08992, -32.35272], "lerp_mode": "catmullrom"}, "3.7333": {"post": [-26.1, -10.91, -32.005], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-25.75645, -10.60384, -32.73238], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-22.18, -10.23376, -28.68011], "lerp_mode": "catmullrom"}, "4.6333": {"post": [-21.84032, -16.35295, -42.43734], "lerp_mode": "catmullrom"}, "4.7667": {"post": [-23.01767, -14.58784, -39.21257], "lerp_mode": "catmullrom"}, "4.8667": {"post": [-25.01344, -14.55583, -37.22696], "lerp_mode": "catmullrom"}, "4.9333": {"post": [-20.36166, -9.80986, -25.96993], "lerp_mode": "catmullrom"}, "4.9667": {"post": [-19.37742, -12.97734, -35.75689], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-22.53365, -10.5086, -29.93511], "lerp_mode": "catmullrom"}, "5.1333": {"post": [-22.35954, -11.69417, -32.98666], "lerp_mode": "catmullrom"}, "5.3333": {"post": [-24.36432, -1.6775, -11.65337], "lerp_mode": "catmullrom"}, "5.5333": {"post": [-23.46652, 12.24383, 43.25953], "lerp_mode": "catmullrom"}, "5.5667": {"post": [-25.38254, 14.43725, 52.36102], "lerp_mode": "catmullrom"}, "5.7333": {"post": [-29.00682, 14.71279, 56.6102], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-29.89, 14.81, 57.05], "lerp_mode": "catmullrom"}, "5.9333": {"post": [-31.3227, 15.39962, 56.65147], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-30.7885, 14.41418, 55.14586], "lerp_mode": "catmullrom"}, "6.4333": {"post": [-30.6349, 15.48634, 57.33607], "lerp_mode": "catmullrom"}, "6.7333": {"post": [-31.25052, 14.70508, 55.68418], "lerp_mode": "catmullrom"}, "6.8667": {"post": [-31.42, 14.31, 55.95], "lerp_mode": "catmullrom"}, "6.9333": {"post": [-32.27246, 11.64382, 51.59711], "lerp_mode": "catmullrom"}, "7.1667": {"post": [-26.67396, -0.07821, 27.51275], "lerp_mode": "catmullrom"}, "7.3": {"post": [-11.69726, 0.58548, 26.88017], "lerp_mode": "catmullrom"}, "7.4333": {"post": [-4.92613, 0.4648, 9.54116], "lerp_mode": "catmullrom"}, "7.6": {"post": [-0.48719, -2.12151, -6.94069], "lerp_mode": "catmullrom"}, "7.7": {"post": [0, 0, -3], "lerp_mode": "catmullrom"}, "7.8": [1.07546, -0.05235, 0.39954], "7.9": [-0.95, 0, 1.64], "8.0333": [0, 0, 0], "8.1667": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": {"pre": [-0.3, -0.65, 0], "post": [-0.3, -0.65, 0], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.6, 0.8, -0.75], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.6, 1.2, -1.35], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-0.6, 0.835, -1.59], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-0.6, 0.55, -1.6], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-0.6, 0.98, -1.6], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-0.6, 3.105, -1.6], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.6, 3.33, -1.6], "lerp_mode": "catmullrom"}, "1.8": {"post": [-0.6, 2.7, -1.6], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-0.45, -0.64, -0.8], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.7, -1.3, 2.1], "lerp_mode": "catmullrom"}, "2.2333": {"post": [0.7, -1.35, 2.1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0.7, -1.15, 2.1], "lerp_mode": "catmullrom"}, "2.4": {"post": [0.575, -0.7, 2.1], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0.245, -0.46, 2.1], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-0.05, -0.65, 2.1], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-0.275, -1.15, 2.1], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-0.2, -1.6, 2.1], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-0.125, -1.825, 2.1], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-0.4, -1.4, 2.1], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-0.4, -1.5, 2.1], "lerp_mode": "catmullrom"}, "4.5333": {"post": [-0.21, -1.27, 2.1], "lerp_mode": "catmullrom"}, "4.6333": {"post": [0.15, -0.9, 2.1], "lerp_mode": "catmullrom"}, "4.7333": {"post": [0.3, -0.6, 2.1], "lerp_mode": "catmullrom"}, "4.8333": {"post": [0.3, -0.5, 2.1], "lerp_mode": "catmullrom"}, "4.8667": {"post": [0.2, -0.5, 2.1], "lerp_mode": "catmullrom"}, "4.9667": [0.3, -0.8, 2.1], "5.1333": {"pre": [0.3, -1.67969, 2.1], "post": [0.3, -1.67969, 2.1], "lerp_mode": "catmullrom"}, "5.3333": {"post": [0.7, -4.11016, 1.5], "lerp_mode": "catmullrom"}, "5.5333": [1.425, -3.36328, 0.64141], "5.6667": {"pre": [1.87422, -2.99297, 0.30938], "post": [1.87422, -2.99297, 0.30938], "lerp_mode": "catmullrom"}, "5.8333": {"post": [2.02656, -3.34531, 0.68047], "lerp_mode": "catmullrom"}, "5.9333": {"post": [2.02656, -3.96641, 1.22516], "lerp_mode": "catmullrom"}, "6.0333": {"post": [1.9975, -4.26297, 1.14094], "lerp_mode": "catmullrom"}, "6.2667": {"post": [1.81, -4.12625, 1.26938], "lerp_mode": "catmullrom"}, "6.6333": {"post": [1.83, -3.89797, 1.35781], "lerp_mode": "catmullrom"}, "6.8667": {"post": [1.84, -3.84469, 1.24359], "lerp_mode": "catmullrom"}, "6.9333": {"post": [1.84, -4.45609, 1.23], "lerp_mode": "catmullrom"}, "7.0": {"post": [1.84, -4.23719, 1.23359], "lerp_mode": "catmullrom"}, "7.1": {"post": [1.81, -5.45562, 0.44], "lerp_mode": "catmullrom"}, "7.2333": {"post": [1.6, -6.10031, -1.06], "lerp_mode": "catmullrom"}, "7.3667": {"post": [1.08, -4.20469, -4.00109], "lerp_mode": "catmullrom"}, "7.5": {"post": [0.52, -0.355, -4.04109], "lerp_mode": "catmullrom"}, "7.7": {"post": [0, -0.3, 0.95], "lerp_mode": "catmullrom"}, "7.8333": [0, 0.02, -0.14], "7.9667": [0, 0.12, 0.08], "8.1333": [0, 0, 0]}}, "lefthand_and_mag": {"rotation": {"2.1667": [0, 0, 0], "2.2667": [-14.87378, -0.06978, -1.99878], "2.3333": [-14.87378, -0.06978, -1.99878], "2.4": {"pre": [-17.58598, -4.88201, -7.06163], "post": [-17.58598, -4.88201, -7.06163], "lerp_mode": "catmullrom"}, "2.5": {"post": [-2.54764, -3.15869, -9.15509], "lerp_mode": "catmullrom"}, "2.6": {"post": [-2.29304, 0.51545, -7.03527], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0.11593, 1.3276, -6.41886], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.56745, 0.66867, -6.29566], "lerp_mode": "catmullrom"}, "3.2": {"post": [-0.73884, -4.95119, 7.1523], "lerp_mode": "catmullrom"}, "3.3333": {"post": [4.57279, 13.80254, 51.68818], "lerp_mode": "catmullrom"}, "3.4333": {"post": [11.39392, 23.87067, 73.5731], "lerp_mode": "catmullrom"}, "3.6": {"post": [10.62198, 24.66829, 69.27027], "lerp_mode": "catmullrom"}, "3.7": {"post": [10.16633, 24.85041, 68.18683], "lerp_mode": "catmullrom"}, "3.8": {"post": [9.71201, 25.02264, 67.10375], "lerp_mode": "catmullrom"}, "4.1667": [10.8, 25.61, 67.345], "4.2667": [11.07064, 24.47878, 61.87432], "4.3667": {"pre": [2.6316, 9.13844, -19.76362], "post": [2.6316, 9.13844, -19.76362], "lerp_mode": "catmullrom"}, "4.5": [-9.91205, 0, -26.17873], "4.6": [-16.50045, 0, -1.24916], "4.6667": [-23.925, 0, 0], "4.7": [-24.5, 0, 0], "4.7667": [-22.5, 0, 0], "4.8": [-25.25, 0, 0], "4.8667": [0, 0, 0]}, "position": {"2.1667": [0, 0, 0], "2.2667": [0, -0.3, -0.575], "2.3333": [0, -0.525, -0.95], "2.4": {"pre": [1.4, -6.7, -5.4], "post": [1.4, -6.7, -5.4], "lerp_mode": "catmullrom"}, "2.5": {"post": [2.60106, -7.37564, -1.50788], "lerp_mode": "catmullrom"}, "2.6667": {"post": [4.19814, -4.01898, 1.38632], "lerp_mode": "catmullrom"}, "2.7667": {"post": [4.90996, -1.98517, 2.02353], "lerp_mode": "catmullrom"}, "2.9": {"post": [5.13462, -1.70756, 1.84998], "lerp_mode": "catmullrom"}, "3.1": {"post": [4.95, -2.2, 1.9], "lerp_mode": "catmullrom"}, "3.2": {"post": [4.75, -2.8, 2.4], "lerp_mode": "catmullrom"}, "3.3333": {"post": [3.61, -3.505, 2.16], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.4, -3.725, 1.125], "lerp_mode": "catmullrom"}, "3.6": {"post": [3.3, -3.425, 0.875], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.2, -3.425, 0.875], "lerp_mode": "catmullrom"}, "3.8": {"post": [3.2, -3.425, 0.875], "lerp_mode": "catmullrom"}, "4.1667": [3.165, -3.7, 0.775], "4.2667": [3.3, -3.85, 0.825], "4.3667": {"pre": [2.745, -6.63, 0.885], "post": [2.745, -6.63, 0.885], "lerp_mode": "catmullrom"}, "4.5": [2.05, -2, -1.95], "4.6": [0.4, -1, -1.525], "4.6667": [0.1, -1, -2.375], "4.7": [0.1, -1, -2.3], "4.7667": [0.1, -1, -1.825], "4.8": [0.05, -0.5, -1.75], "4.8667": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.1667": [120.65263, 300.35672, -200.36724], "0.3": [111.55615, 298.47384, -188.40898], "1.6667": [111.555, 298.47, -188.405], "1.9667": [118.83163, 326.45682, -211.14883], "2.0667": [128.08894, 353.56633, -232.1361], "2.1667": [137.90708, 352.98359, -229.17653], "2.2667": [136.38376, 352.2397, -227.32289], "2.3333": [131.92598, 335.01803, -221.25165], "2.4": [124.32756, 341.90777, -207.99399], "2.5667": [117.49784, 353.33242, -204.26761], "2.7": [122.57973, 353.16258, -203.63703], "3.0667": [122.57973, 353.16258, -203.63703], "3.2": [121.015, 353.92, -204.425], "3.3": [121.6483, 350.03872, -193.05026], "4.3": [121.6483, 350.03872, -193.05026], "4.4": [128.32432, 351.9275, -221.98264], "5.1": [128.32432, 351.9275, -221.98264], "5.2": [124.115, 332.21, -174.055], "5.3": [110.9783, 319.06865, -142.09861], "5.7": [81.9783, 319.06865, -142.09861], "7.3": [83.9783, 319.06865, -142.09861], "7.4333": [100.36601, 316.5798, -142.78527]}, "position": {"0.0": [7.775, -13, -6], "0.1667": [14.3, -27.8, -3.8], "0.3": [9.9, -45.2, 22.9], "1.6667": [9.9, -45.2, 22.9], "1.9667": [7.18, -18.75, 1.05], "2.0667": [7.35, -16.5, -1.5], "2.2667": [7.35, -16.5, -1.5], "2.3333": [7.35, -16.975, -1.1], "2.5667": [7.31, -16.92, -0.745], "3.2": [7.31, -16.92, -0.745], "3.3": [8.54, -16.78, -1.6], "4.3": [8.54, -16.78, -1.6], "4.4": [7.65, -17.1, -1.275], "5.1": [7.65, -17.1, -1.275], "5.1667": [7.91, -15.06, -4.48], "5.3": [8.1, -12.1, -8.5], "7.3": [8.1, -12.1, -8.5], "7.4333": [7.775, -13, -6]}, "scale": [1, 1.5, 1]}, "righthand_and_gun": {"rotation": {"2.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0, 0, -6], "lerp_mode": "catmullrom"}, "2.6": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "2.8667": {"post": [4.25145, 1.49587, 1.11119], "lerp_mode": "catmullrom"}, "3.3333": {"post": [9.7516, 3.94902, -2.77956], "lerp_mode": "catmullrom"}, "3.6667": {"post": [11.0016, 3.94902, -2.77956], "lerp_mode": "catmullrom"}, "4.3": {"post": [8.83657, 0.51786, 5.59603], "lerp_mode": "catmullrom"}, "4.7": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"2.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.7333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.33567, -0.49419, 0.31421], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-0.40508, -0.57962, 0.36794], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-0.13019, -0.20715, 0.12851], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"5.7667": [0, 0, 0], "5.9": {"pre": [0, 0, 2], "post": [0, 0, 2], "lerp_mode": "catmullrom"}, "6.0667": {"post": [0, 0, 2.25], "lerp_mode": "catmullrom"}, "6.3": {"post": [0, 0, 2.17], "lerp_mode": "catmullrom"}, "6.5667": [0, 0, 2.19938], "6.8667": [0, 0, 2], "6.9333": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "righthand": {"rotation": {"0.0": [95.04292, 367.4713, 180.6574], "0.1": [95.04292, 367.4713, 180.6574], "0.2333": [95.04292, 367.4713, 180.6574], "1.9667": [95.04292, 367.4713, 180.6574], "2.1": [95.04292, 367.4713, 180.6574], "5.4667": [95.04292, 367.4713, 180.6574], "5.6667": [76.17064, 380.31866, 67.92926], "5.7667": [77.90318, 376.66785, 78.13254], "5.8": [79.05642, 378.34388, 85.93755], "5.9": {"pre": [83.30253, 402.22545, 102.90458], "post": [83.30253, 402.22545, 102.90458], "lerp_mode": "catmullrom"}, "6.2333": {"post": [85.04261, 405.56628, 103.17399], "lerp_mode": "catmullrom"}, "6.4667": {"post": [84.64494, 403.05678, 104.1009], "lerp_mode": "catmullrom"}, "6.8667": [83.30253, 402.22545, 102.90458], "6.9333": {"pre": [62.2494, 406.43748, 84.52542], "post": [62.2494, 406.43748, 84.52542], "lerp_mode": "catmullrom"}, "7.1333": [95.04292, 367.4713, 180.6574]}, "position": {"0.0": [-7, -14.275, 7], "0.1": [-7, -14.275, 7], "0.2333": [-8.25, -14.275, 7], "1.9667": [-8.25, -14.275, 7], "2.1": [-7, -14.275, 7], "5.4667": [-7, -14.275, 7], "5.5": {"pre": [-8.015, -13.35, 5.48], "post": [-8.015, -13.35, 5.48], "lerp_mode": "catmullrom"}, "5.6": [-9.43141, -12.18, 0.80187], "5.6667": [-9.05, -11.675, -0.15], "5.7667": [-9.05, -11.525, -0.625], "5.9": {"pre": [-9.05, -11.57, 0.65], "post": [-9.05, -11.57, 0.65], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-9.05, -11.615, 0.74719], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-9.05, -11.615, 0.79703], "lerp_mode": "catmullrom"}, "6.6333": [-9.05, -11.64, 0.93234], "6.8667": [-9.05, -11.62859, 0.72266], "6.9333": {"pre": [-9.05, -11.94, 1.58], "post": [-9.05, -11.94, 1.58], "lerp_mode": "catmullrom"}, "7.1333": [-7, -14.275, 7]}, "scale": [1, 1.5, 1]}, "bullet": {"scale": 0}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [1.72, 0.5, -0.45], "lerp_mode": "catmullrom"}, "0.3667": {"post": [3.175, 0.8, -1.1], "lerp_mode": "catmullrom"}, "0.7333": {"post": [3.125, 0.8, -1.1], "lerp_mode": "catmullrom"}, "0.9333": {"post": [3.585, 0.8, -1.1], "lerp_mode": "catmullrom"}, "1.1667": {"post": [5.295, 1.625, -1.55], "lerp_mode": "catmullrom"}, "1.3333": {"post": [7.335, 2.7, -1.55], "lerp_mode": "catmullrom"}, "1.5667": {"post": [8.425, 3.1, -1.55], "lerp_mode": "catmullrom"}, "2.0": {"post": [2.7, 1.7, -1.55], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.9, 0.8, -1.1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [1, 0.5, -1.1], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.5, -0.3, 1.2], "lerp_mode": "catmullrom"}, "2.5": {"post": [0.7, -0.5, -2.95], "lerp_mode": "catmullrom"}, "2.6": {"post": [0.9, -0.8, -1.1], "lerp_mode": "catmullrom"}, "2.8": {"post": [2.3, -1.6, -1.55], "lerp_mode": "catmullrom"}, "3.0": {"post": [3, -2, -1.55], "lerp_mode": "catmullrom"}, "3.2": {"post": [3.09, -2, -1.55], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.2, -4.1, -2.125], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.11, -4.8, -2.125], "lerp_mode": "catmullrom"}, "4.0": {"post": [3, -4.8, -2.125], "lerp_mode": "catmullrom"}, "4.3": {"post": [2.6, -4.5, -2.125], "lerp_mode": "catmullrom"}, "4.5667": {"post": [0.8, 0.2, -2], "lerp_mode": "catmullrom"}, "4.8": {"post": [0.4, 0.8, -1.55], "lerp_mode": "catmullrom"}, "4.8667": {"post": [1.5, 0.8, 0.02344], "lerp_mode": "catmullrom"}, "4.9667": {"post": [0.7, 0.8, -2.32578], "lerp_mode": "catmullrom"}, "5.0667": {"post": [1.1, 0.8, -1.2], "lerp_mode": "catmullrom"}, "5.2": {"post": [1.3, 0, -1.55], "lerp_mode": "catmullrom"}, "5.5": {"post": [3.1, -0.4, -1.55], "lerp_mode": "catmullrom"}, "5.7667": {"post": [3.5, -0.5, -1.55], "lerp_mode": "catmullrom"}, "5.8667": {"post": [4.71, -0.5, -1.52], "lerp_mode": "catmullrom"}, "6.0": {"post": [3.79, -0.49, -1.47], "lerp_mode": "catmullrom"}, "6.2667": {"post": [4.22, -0.46, -1.35], "lerp_mode": "catmullrom"}, "6.7": {"post": [3.85, -0.4, -1.1], "lerp_mode": "catmullrom"}, "6.8": {"post": [3.27031, -0.41, -1.06], "lerp_mode": "catmullrom"}, "6.8667": {"post": [3.72688, -0.42, -0.43672], "lerp_mode": "catmullrom"}, "6.9667": {"post": [2.48891, -0.43, -1.10766], "lerp_mode": "catmullrom"}, "7.1": {"post": [2.37781, -0.43, -0.99], "lerp_mode": "catmullrom"}, "7.2667": {"post": [2.54953, -0.40234, -0.88672], "lerp_mode": "catmullrom"}, "7.3667": {"post": [2.92234, -0.25734, 0.51859], "lerp_mode": "catmullrom"}, "7.4667": {"post": [1.97109, -0.1775, 0.57359], "lerp_mode": "catmullrom"}, "7.6667": {"post": [0.03, 0, 0], "lerp_mode": "catmullrom"}, "7.7": {"post": [-0.15, 0, 1.6], "lerp_mode": "catmullrom"}, "7.8": {"post": [0.05, 0, -0.63984], "lerp_mode": "catmullrom"}, "7.9667": {"post": [0, 0, 0.09375], "lerp_mode": "catmullrom"}, "8.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "group7": {"rotation": {"0.1667": [0, 0, 0], "0.3": [1.5, 0, 0], "0.5": [-1, 0, 0], "0.7": [0.975, 0, 0], "0.9667": [-0.475, 0, 0], "1.3": [0.225, 0, 0], "1.7333": [0, 0, 0], "2.1667": [0, 0, 0], "2.2667": [1.5, 0, 0], "2.4333": [-1, 0, 0], "2.5667": [0.975, 0, 0], "2.7667": [-0.475, 0, 0], "3.0333": [0.225, 0, 0], "3.3333": [0, 0, 0], "4.4333": [0, 0, 0], "4.5": [1.5, 0, 0], "4.6": [-1, 0, 0], "4.7": [0, 0, 0], "4.7667": [1.5, 0, 0], "4.8667": [-1, 0, 0], "4.9667": [0.975, 0, 0], "5.1": [-0.475, 0, 0], "5.3": [0, 0, 0], "5.4333": [1.5, 0, 0], "5.6333": [-1, 0, 0], "5.8": [0.975, 0, 0], "6.1": [-0.475, 0, 0], "6.4333": [0.225, 0, 0], "6.8": [0, 0, 0], "6.8667": [0, 0, 0], "6.9333": [1.5, 0, 0], "7.0333": [-1, 0, 0], "7.1333": [0.975, 0, 0], "7.2667": [-0.475, 0, 0], "7.4333": [0.225, 0, 0], "7.5333": [0, 0, 0], "7.6": [1.5, 0, 0], "7.7": [-1, 0, 0], "7.8": [0.975, 0, 0], "7.9333": [-0.475, 0, 0], "8.1": [0.225, 0, 0], "8.3": [0, 0, 0]}}, "group2": {"rotation": {"0.2667": [0, 0, 0], "0.4": [1.5, 0, 0], "0.6": [-1, 0, 0], "0.7667": [0.975, 0, 0], "1.0667": [-0.475, 0, 0], "1.4": [0.225, 0, 0], "1.7667": [0, 0, 0], "2.3667": [0, 0, 0], "2.4667": [1.5, 0, 0], "2.6333": [-1, 0, 0], "2.7667": [0.975, 0, 0], "2.9667": [-0.475, 0, 0], "3.2333": [0.225, 0, 0], "3.5333": [0, 0, 0], "4.5667": [0, 0, 0], "4.6333": [1.5, 0, 0], "4.7333": [-1, 0, 0], "4.8333": [0, 0, 0], "4.9": [1.5, 0, 0], "5.0": [-1, 0, 0], "5.1": [0.975, 0, 0], "5.2333": [-0.475, 0, 0], "5.3": [0, 0, 0], "5.4333": [1.5, 0, 0], "5.6333": [-1, 0, 0], "5.8": [0.975, 0, 0], "6.1": [-0.475, 0, 0], "6.4333": [0.225, 0, 0], "6.8": [0, 0, 0], "7.3333": [0.225, 0, 0], "7.4333": [0, 0, 0], "7.5": [1.5, 0, 0], "7.6": [-1, 0, 0], "7.7": [0.975, 0, 0], "7.8333": [-0.475, 0, 0], "8.0": [0.225, 0, 0], "8.2": [0, 0, 0]}}, "release": {"rotation": {"2.1333": [0, 0, 0], "2.2": [-17.5, 0, 0], "2.6667": [-17.5, 0, 0], "2.7667": [0, 0, 0], "4.8": [0, 0, 0], "4.8667": [-17.5, 0, 0], "4.9333": [-17.5, 0, 0], "5.0": [0, 0, 0]}}}, "sound_effects": {"0.0": {"effect": "ar_a<PERSON><PERSON>_inspect_up"}, "2.0333": {"effect": "ar_a<PERSON><PERSON>_raise_grab"}, "2.1333": {"effect": "ar_a<PERSON><PERSON>_inspect_magout"}, "2.9667": {"effect": "p04_ar_akilo_inspect_mvt"}, "4.4333": {"effect": "ar_a<PERSON>lo_inspect_magbump"}, "4.7667": {"effect": "ar_a<PERSON><PERSON>_inspect_magin"}, "5.0667": {"effect": "ar_a<PERSON><PERSON>_inspect_turn"}, "5.6333": {"effect": "ar_a<PERSON><PERSON>_inspect_pull"}, "6.7667": {"effect": "ar_akilo_inspect_release"}, "7.3667": {"effect": "ar_a<PERSON><PERSON>_inspect_over"}}}, "inspect_xmag": {"animation_length": 9.53333, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.1667": {"pre": [-3.95432, -0.35524, -7.5593], "post": [-3.95432, -0.35524, -7.5593], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-5.89987, -14.73938, -33.3804], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-7.07657, -15.87239, -30.78386], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-5.80861, -16.23715, -31.75275], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-7.90575, -15.90496, -30.77084], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-7.685, -15.97, -30.975], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-6.87629, -18.05893, -35.11406], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-6.775, -17.79, -34.685], "lerp_mode": "catmullrom"}, "1.8": {"post": [-8.625, -17.79, -34.685], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-10.73698, -18.90273, -37.90388], "lerp_mode": "catmullrom"}, "2.1667": [-13.16135, -12.51673, -24.54773], "2.2333": [-13.81009, -14.72379, -30.29289], "2.3333": {"pre": [-16.05772, -16.03986, -33.0996], "post": [-16.05772, -16.03986, -33.0996], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-16.05772, -16.03986, -33.0996], "lerp_mode": "catmullrom"}, "2.4": {"post": [-15.94061, -18.75152, -38.32369], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-18.81787, -14.89123, -34.22797], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-19.62612, -14.84361, -35.22725], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-20.12812, -11.93302, -33.22913], "lerp_mode": "catmullrom"}, "2.9667": {"post": [-20.49109, -11.05842, -29.41804], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-20.49464, -11.08992, -32.35272], "lerp_mode": "catmullrom"}, "3.7333": {"post": [-22.15, -10.91, -32.005], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-25.75645, -10.60384, -32.73238], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-22.18, -10.23376, -28.68011], "lerp_mode": "catmullrom"}, "4.6333": {"post": [-21.84032, -16.35295, -42.43734], "lerp_mode": "catmullrom"}, "4.7667": {"post": [-23.01767, -14.58784, -39.21257], "lerp_mode": "catmullrom"}, "4.8667": {"post": [-25.01344, -14.55583, -37.22696], "lerp_mode": "catmullrom"}, "4.9333": {"post": [-20.36166, -9.80986, -25.96993], "lerp_mode": "catmullrom"}, "4.9667": {"post": [-19.37742, -12.97734, -35.75689], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-22.53365, -10.5086, -29.93511], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-22.35954, -11.69417, -32.98666], "lerp_mode": "catmullrom"}, "5.3667": {"post": [-17.75001, -10.89178, -37.84303], "lerp_mode": "catmullrom"}, "5.5": {"post": [-18.43061, -10.40678, -34.37984], "lerp_mode": "catmullrom"}, "5.5667": {"post": [-18.33098, -8.64498, -26.51424], "lerp_mode": "catmullrom"}, "5.6333": {"post": [-16.24751, -7.88924, -31.65397], "lerp_mode": "catmullrom"}, "5.7333": {"post": [-18.86635, -8.41396, -23.27217], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-18.01676, -10.42373, -29.62376], "lerp_mode": "catmullrom"}, "6.0": {"post": [-18.84123, -8.43037, -25.82365], "lerp_mode": "catmullrom"}, "6.2333": {"post": [-18.99106, -8.19826, -28.82851], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-24.36432, -1.6775, -11.65337], "lerp_mode": "catmullrom"}, "6.5667": {"post": [-23.46652, 12.24383, 43.25953], "lerp_mode": "catmullrom"}, "6.6": {"post": [-25.38254, 14.43725, 52.36102], "lerp_mode": "catmullrom"}, "6.7667": {"post": [-29.00682, 14.71279, 56.6102], "lerp_mode": "catmullrom"}, "6.8667": {"post": [-29.89, 14.81, 57.05], "lerp_mode": "catmullrom"}, "6.9667": {"post": [-31.3227, 15.39962, 56.65147], "lerp_mode": "catmullrom"}, "7.1667": {"post": [-30.7885, 14.41418, 55.14586], "lerp_mode": "catmullrom"}, "7.4667": {"post": [-30.6349, 15.48634, 57.33607], "lerp_mode": "catmullrom"}, "7.7667": {"post": [-31.25052, 14.70508, 55.68418], "lerp_mode": "catmullrom"}, "7.9": {"post": [-31.42, 14.31, 55.95], "lerp_mode": "catmullrom"}, "7.9667": {"post": [-32.27246, 11.64382, 51.59711], "lerp_mode": "catmullrom"}, "8.2": {"post": [-26.67396, -0.07821, 27.51275], "lerp_mode": "catmullrom"}, "8.3333": {"post": [-11.69726, 0.58548, 26.88017], "lerp_mode": "catmullrom"}, "8.4667": {"post": [-4.92613, 0.4648, 9.54116], "lerp_mode": "catmullrom"}, "8.6333": {"post": [-0.48719, -2.12151, -6.94069], "lerp_mode": "catmullrom"}, "8.7333": {"post": [0, 0, -3], "lerp_mode": "catmullrom"}, "8.8333": [1.07546, -0.05235, 0.39954], "8.9333": [-0.95, 0, 1.64], "9.0667": [0, 0, 0], "9.2": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": {"pre": [-0.3, -0.65, 0], "post": [-0.3, -0.65, 0], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.6, 0.8, -0.75], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.6, 1.2, -1.35], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-0.6, 0.835, -1.59], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-0.6, 0.55, -1.6], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-0.6, 0.98, -1.6], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-0.6, 3.105, -1.6], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.6, 3.33, -1.6], "lerp_mode": "catmullrom"}, "1.8": {"post": [-0.6, 2.7, -1.6], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-0.45, -0.64, -0.8], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.7, -1.3, 2.1], "lerp_mode": "catmullrom"}, "2.2333": {"post": [0.7, -1.35, 2.1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0.7, -1.15, 2.1], "lerp_mode": "catmullrom"}, "2.4": {"post": [0.575, -0.7, 2.1], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0.245, -0.46, 2.1], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-0.05, -0.65, 2.1], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-0.275, -1.15, 2.1], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-0.2, -1.6, 2.1], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-0.125, -1.825, 2.1], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-0.4, -1.4, 2.1], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-0.4, -1.5, 2.1], "lerp_mode": "catmullrom"}, "4.5333": {"post": [-0.21, -1.27, 2.1], "lerp_mode": "catmullrom"}, "4.6333": {"post": [0.15, -0.9, 2.1], "lerp_mode": "catmullrom"}, "4.7333": {"post": [0.3, -0.6, 2.1], "lerp_mode": "catmullrom"}, "4.8333": {"post": [0.3, -0.5, 2.1], "lerp_mode": "catmullrom"}, "4.8667": {"post": [0.2, -0.5, 2.1], "lerp_mode": "catmullrom"}, "4.9667": [0.3, -0.8, 2.1], "5.1667": {"pre": [0.3, -1.67969, 2.1], "post": [0.3, -1.67969, 2.1], "lerp_mode": "catmullrom"}, "5.5": {"post": [0.29, -1.67, 2.12], "lerp_mode": "catmullrom"}, "5.7": {"post": [0.4425, -1.8675, 2.13], "lerp_mode": "catmullrom"}, "5.9333": {"post": [0.2075, -1.75375, 2.15], "lerp_mode": "catmullrom"}, "6.1667": {"post": [0.3, -1.67969, 2.1], "lerp_mode": "catmullrom"}, "6.3667": {"post": [0.7, -4.11016, 1.5], "lerp_mode": "catmullrom"}, "6.5667": [1.425, -3.36328, 0.64141], "6.7": {"pre": [1.87422, -2.99297, 0.30938], "post": [1.87422, -2.99297, 0.30938], "lerp_mode": "catmullrom"}, "6.8667": {"post": [2.02656, -3.34531, 0.68047], "lerp_mode": "catmullrom"}, "6.9667": {"post": [2.02656, -3.96641, 1.22516], "lerp_mode": "catmullrom"}, "7.0667": {"post": [1.9975, -4.26297, 1.14094], "lerp_mode": "catmullrom"}, "7.3": {"post": [1.81, -4.12625, 1.26938], "lerp_mode": "catmullrom"}, "7.6667": {"post": [1.83, -3.89797, 1.35781], "lerp_mode": "catmullrom"}, "7.9": {"post": [1.84, -3.84469, 1.24359], "lerp_mode": "catmullrom"}, "7.9667": {"post": [1.84, -4.45609, 1.23], "lerp_mode": "catmullrom"}, "8.0333": {"post": [1.84, -4.23719, 1.23359], "lerp_mode": "catmullrom"}, "8.1333": {"post": [1.81, -5.45562, 0.44], "lerp_mode": "catmullrom"}, "8.2667": {"post": [1.6, -6.10031, -1.06], "lerp_mode": "catmullrom"}, "8.4": {"post": [1.08, -4.20469, -4.00109], "lerp_mode": "catmullrom"}, "8.5333": {"post": [0.52, -0.355, -4.04109], "lerp_mode": "catmullrom"}, "8.7333": {"post": [0, -0.3, 0.95], "lerp_mode": "catmullrom"}, "8.8667": [0, 0.02, -0.14], "9.0": [0, 0.12, 0.08], "9.1667": [0, 0, 0]}}, "lefthand_and_mag": {"rotation": {"2.1667": [0, 0, 0], "2.3": [-14.87378, -0.06978, -1.99878], "2.4": [-14.87378, -0.06978, -1.99878], "2.5": {"pre": [-17.58598, -4.88201, -7.06163], "post": [-17.58598, -4.88201, -7.06163], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-2.54764, -3.15869, -9.15509], "lerp_mode": "catmullrom"}, "2.8": {"post": [-2.29304, 0.51545, -7.03527], "lerp_mode": "catmullrom"}, "3.0333": {"post": [0.11593, 1.3276, -6.41886], "lerp_mode": "catmullrom"}, "3.5": {"post": [-0.56745, 0.66867, -6.29566], "lerp_mode": "catmullrom"}, "3.8": {"post": [0.99032, -6.31791, 4.45818], "lerp_mode": "catmullrom"}, "4.1667": {"post": [0.57, -6.26, 3.38], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-2.38781, -5.40809, -2.29426], "lerp_mode": "catmullrom"}, "4.5": [-9.91205, 0, -26.17873], "4.6": [-16.5, 0, -1.25], "4.6667": [-23.925, 0, 0], "4.7": [-24.5, 0, 0], "4.7667": [-22.5, 0, 0], "4.8": [-25.25, 0, 0], "4.8667": [0, 0, 0], "5.4333": [0, 0, 0], "5.5667": [0, 0, 4], "5.7": [0, 0, 0], "5.8667": [0, 0, 4], "6.0667": [0, 0, 0], "6.2667": [0, 0, 0]}, "position": {"2.1667": [0, 0, 0], "2.3": [0, -0.3, -0.575], "2.4": [0, -0.525, -0.95], "2.5": {"pre": [1.4, -6.7, -5.4], "post": [1.4, -6.7, -5.4], "lerp_mode": "catmullrom"}, "2.6333": {"post": [2.60106, -7.37564, -1.50788], "lerp_mode": "catmullrom"}, "2.8667": {"post": [4.19814, -4.01898, 1.38632], "lerp_mode": "catmullrom"}, "3.0333": {"post": [4.90996, -1.98517, 2.02353], "lerp_mode": "catmullrom"}, "3.2": {"post": [5.13462, -1.70756, 1.84998], "lerp_mode": "catmullrom"}, "3.5": {"post": [4.95, -2.2, 1.9], "lerp_mode": "catmullrom"}, "3.7333": {"post": [5.115, -1.79, 2.11], "lerp_mode": "catmullrom"}, "4.0": {"post": [5.15, -1.76, 1.985], "lerp_mode": "catmullrom"}, "4.2": {"post": [5.1, -1.825, 1.975], "lerp_mode": "catmullrom"}, "4.3333": {"post": [4.565, -2.11, 1.115], "lerp_mode": "catmullrom"}, "4.5": [2.675, -1.875, -1.55], "4.6": [0.4, -1, -1.525], "4.6667": [0.1, -1, -2.375], "4.7": [0.1, -1, -2.3], "4.7667": [0.1, -1, -1.825], "4.8": [0.05, -0.5, -1.75], "4.8667": [0, 0, 0], "5.4333": [0, 0, 0], "5.5667": [-0.4, 0, 0], "5.7": [0, 0, 0], "5.8667": [-0.4, 0, 0], "6.0667": [0, 0, 0], "6.2667": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.2": [100.36601, 316.5798, -142.78527], "0.4333": [93.44224, 333.30693, -111.77323], "0.5667": [92.17834, 332.65068, -130.86071], "1.6667": [92.17834, 332.65068, -130.86071], "2.0667": [102.03767, 346.62865, -163.14427], "5.1333": [102.04, 346.63, -163.14], "5.2333": [105.32974, 348.65885, -185.37743], "5.3333": [108.03958, 368.06176, -202.86923], "6.0333": [108.03958, 368.06176, -202.86923], "6.2": [124.115, 332.21, -174.055], "6.3333": [110.9783, 319.06865, -142.09861], "6.7333": [81.9783, 319.06865, -142.09861], "8.3333": [83.9783, 319.06865, -142.09861], "8.4667": [100.36601, 316.5798, -142.78527]}, "position": {"0.0": [7.775, -13, -6], "0.2": [7.775, -13, -6], "0.3333": [8.50856, -18.40216, -4.37415], "0.4333": [6.95659, -20.28624, -2.35704], "0.5667": [7.6, -19.95, -1.6], "1.6667": [7.6, -19.95, -1.6], "2.0667": [9.46753, -18.74472, -1.78487], "5.1333": [9.47, -18.74, -1.78], "5.2333": [10.905, -17.03, -0.9], "5.3333": [11.295, -15.315, -0.03], "6.0333": [11.295, -15.315, -0.03], "6.2": [7.91, -15.06, -4.48], "6.3333": [8.1, -12.1, -8.5], "8.3333": [8.1, -12.1, -8.5], "8.4667": [7.775, -13, -6]}, "scale": [1, 1.5, 1]}, "righthand_and_gun": {"rotation": {"2.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0, 0, -6], "lerp_mode": "catmullrom"}, "2.6": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "2.8667": {"post": [4.25145, 1.49587, 1.11119], "lerp_mode": "catmullrom"}, "3.3333": {"post": [9.7516, 3.94902, -2.77956], "lerp_mode": "catmullrom"}, "3.6667": {"post": [11.0016, 3.94902, -2.77956], "lerp_mode": "catmullrom"}, "4.3": {"post": [8.83657, 0.51786, 5.59603], "lerp_mode": "catmullrom"}, "4.7": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"2.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.7333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.33567, -0.49419, 0.31421], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-0.40508, -0.57962, 0.36794], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-0.13019, -0.20715, 0.12851], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"6.8": [0, 0, 0], "6.9333": {"pre": [0, 0, 2], "post": [0, 0, 2], "lerp_mode": "catmullrom"}, "7.1": {"post": [0, 0, 2.25], "lerp_mode": "catmullrom"}, "7.3333": {"post": [0, 0, 2.17], "lerp_mode": "catmullrom"}, "7.6": [0, 0, 2.19938], "7.9": [0, 0, 2], "8.0": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "righthand": {"rotation": {"0.0": [95.04292, 367.4713, 180.6574], "0.1": [95.04292, 367.4713, 180.6574], "0.2333": [95.04292, 367.4713, 180.6574], "1.9667": [95.04292, 367.4713, 180.6574], "2.1": [95.04292, 367.4713, 180.6574], "6.5": [95.04292, 367.4713, 180.6574], "6.7": [76.17064, 380.31866, 67.92926], "6.8": [77.90318, 376.66785, 78.13254], "6.8333": [79.05642, 378.34388, 85.93755], "6.9333": {"pre": [83.30253, 402.22545, 102.90458], "post": [83.30253, 402.22545, 102.90458], "lerp_mode": "catmullrom"}, "7.2667": {"post": [85.04261, 405.56628, 103.17399], "lerp_mode": "catmullrom"}, "7.5": {"post": [84.64494, 403.05678, 104.1009], "lerp_mode": "catmullrom"}, "7.9": [83.30253, 402.22545, 102.90458], "8.0": [83.30253, 402.22545, 102.90458], "8.2667": [95.04292, 367.4713, 180.6574]}, "position": {"0.0": [-7, -14.275, 7], "0.1": [-7, -14.275, 7], "0.2333": [-8.25, -14.275, 7], "1.9667": [-8.25, -14.275, 7], "2.1": [-7, -14.275, 7], "6.5": [-7, -14.275, 7], "6.5333": {"pre": [-8.015, -13.35, 5.48], "post": [-8.015, -13.35, 5.48], "lerp_mode": "catmullrom"}, "6.6333": [-9.43141, -12.18, 0.80187], "6.7": [-9.05, -11.675, -0.15], "6.8": [-9.05, -11.525, -0.625], "6.9333": {"pre": [-9.05, -11.57, 0.65], "post": [-9.05, -11.57, 0.65], "lerp_mode": "catmullrom"}, "7.1667": {"post": [-9.05, -11.615, 0.74719], "lerp_mode": "catmullrom"}, "7.4": {"post": [-9.05, -11.615, 0.79703], "lerp_mode": "catmullrom"}, "7.6667": [-9.05, -11.64, 0.93234], "7.9": [-9.05, -11.62859, 0.72266], "8.0": [-9.05, -11.62859, -1.62734], "8.1333": [-9.105, -11.09, 0.695], "8.2667": [-7, -14.275, 7]}, "scale": [1, 1.5, 1]}, "bullet": {"scale": 0}, "group7": {"rotation": {"0.3333": [0, 0, 0], "0.4667": [1.5, 0, 0], "0.6667": [-1, 0, 0], "0.8667": [0.975, 0, 0], "1.1333": [-0.475, 0, 0], "1.4667": [0.225, 0, 0], "1.9": [0, 0, 0], "2.3333": [0, 0, 0], "2.4333": [1.5, 0, 0], "2.6": [-1, 0, 0], "2.7333": [0.975, 0, 0], "2.9333": [-0.475, 0, 0], "3.2": [0.225, 0, 0], "3.5": [0, 0, 0], "4.6": [0, 0, 0], "4.6667": [1.5, 0, 0], "4.7667": [-1, 0, 0], "4.8667": [0, 0, 0], "4.9333": [1.5, 0, 0], "5.0333": [-1, 0, 0], "5.1333": [0.975, 0, 0], "5.2667": [-0.475, 0, 0], "5.4333": [0, 0, 0], "5.5": [1.5, 0, 0], "5.6": [-1, 0, 0], "5.7": [0.975, 0, 0], "5.8333": [-0.475, 0, 0], "6.0": [0.225, 0, 0], "6.2": [0, 0, 0], "6.3333": [1.5, 0, 0], "6.5333": [-1, 0, 0], "6.7": [0.975, 0, 0], "7.0": [-0.475, 0, 0], "7.3333": [0.225, 0, 0], "7.7": [0, 0, 0], "7.9667": [0, 0, 0], "8.0333": [1.5, 0, 0], "8.1333": [-1, 0, 0], "8.2333": [0.975, 0, 0], "8.3667": [-0.475, 0, 0], "8.5333": [0.225, 0, 0], "8.6333": [0, 0, 0], "8.7": [1.5, 0, 0], "8.8": [-1, 0, 0], "8.9": [0.975, 0, 0], "9.0333": [-0.475, 0, 0], "9.2": [0.225, 0, 0], "9.4": [0, 0, 0]}}, "group2": {"rotation": {"0.4333": [0, 0, 0], "0.5667": [1.5, 0, 0], "0.7667": [-1, 0, 0], "0.9333": [0.975, 0, 0], "1.2333": [-0.475, 0, 0], "1.5667": [0.225, 0, 0], "1.9333": [0, 0, 0], "2.5333": [0, 0, 0], "2.6333": [1.5, 0, 0], "2.8": [-1, 0, 0], "2.9333": [0.975, 0, 0], "3.1333": [-0.475, 0, 0], "3.4": [0.225, 0, 0], "3.7": [0, 0, 0], "4.7333": [0, 0, 0], "4.8": [1.5, 0, 0], "4.9": [-1, 0, 0], "5.0": [0, 0, 0], "5.0667": [1.5, 0, 0], "5.1667": [-1, 0, 0], "5.2667": [0.975, 0, 0], "5.4": [-0.475, 0, 0], "5.5667": [0, 0, 0], "5.6333": [1.5, 0, 0], "5.7333": [-1, 0, 0], "5.8333": [0.975, 0, 0], "5.9667": [-0.475, 0, 0], "6.1333": [0.225, 0, 0], "6.3333": [0, 0, 0], "6.6667": [0, 0, 0], "6.7333": [1.5, 0, 0], "6.8333": [-1, 0, 0], "6.9333": [0.975, 0, 0], "7.0667": [-0.475, 0, 0], "7.2333": [0.225, 0, 0], "7.4333": [0, 0, 0], "8.1": [0, 0, 0], "8.1667": [1.5, 0, 0], "8.2667": [-1, 0, 0], "8.3667": [0.975, 0, 0], "8.5": [-0.475, 0, 0], "8.6667": [0.225, 0, 0], "8.7667": [0, 0, 0], "8.8333": [1.5, 0, 0], "8.9333": [-1, 0, 0], "9.0333": [0.975, 0, 0], "9.1667": [-0.475, 0, 0], "9.3333": [0.225, 0, 0], "9.5333": [0, 0, 0]}}, "release": {"rotation": {"2.1333": [0, 0, 0], "2.2": [-17.5, 0, 0], "2.6667": [-17.5, 0, 0], "2.7667": [0, 0, 0], "4.8": [0, 0, 0], "4.8667": [-17.5, 0, 0], "4.9333": [-17.5, 0, 0], "5.0": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [1.72, 0.5, -0.45], "lerp_mode": "catmullrom"}, "0.3667": {"post": [3.175, 0.8, -1.1], "lerp_mode": "catmullrom"}, "0.7333": {"post": [3.125, 0.8, -1.1], "lerp_mode": "catmullrom"}, "0.9333": {"post": [3.585, 0.8, -1.1], "lerp_mode": "catmullrom"}, "1.1667": {"post": [5.295, 1.625, -1.55], "lerp_mode": "catmullrom"}, "1.3333": {"post": [7.335, 2.7, -1.55], "lerp_mode": "catmullrom"}, "1.5667": {"post": [8.425, 3.1, -1.55], "lerp_mode": "catmullrom"}, "2.0": {"post": [2.7, 1.7, -1.55], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.9, 0.8, -1.1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [1, 0.5, -1.1], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.5, -0.3, 1.2], "lerp_mode": "catmullrom"}, "2.5": {"post": [0.7, -0.5, -2.95], "lerp_mode": "catmullrom"}, "2.6": {"post": [0.9, -0.8, -1.1], "lerp_mode": "catmullrom"}, "2.8": {"post": [2.3, -1.6, -1.55], "lerp_mode": "catmullrom"}, "3.0": {"post": [3, -2, -1.55], "lerp_mode": "catmullrom"}, "3.2": {"post": [3.09, -2, -1.55], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.2, -4.1, -2.125], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.11, -4.8, -2.125], "lerp_mode": "catmullrom"}, "4.0": {"post": [3, -4.8, -2.125], "lerp_mode": "catmullrom"}, "4.3": {"post": [2.6, -4.5, -2.125], "lerp_mode": "catmullrom"}, "4.5667": {"post": [0.8, 0.2, -2], "lerp_mode": "catmullrom"}, "4.8": {"post": [0.4, 0.8, -1.55], "lerp_mode": "catmullrom"}, "4.8667": {"post": [1.5, 0.8, 0.02344], "lerp_mode": "catmullrom"}, "4.9667": {"post": [0.7, 0.8, -2.32578], "lerp_mode": "catmullrom"}, "5.0667": {"post": [1.1, 0.8, -1.2], "lerp_mode": "catmullrom"}, "6.2333": {"post": [1.3, 0, -1.55], "lerp_mode": "catmullrom"}, "6.5333": {"post": [3.1, -0.4, -1.55], "lerp_mode": "catmullrom"}, "6.8": {"post": [3.5, -0.5, -1.55], "lerp_mode": "catmullrom"}, "6.9": {"post": [4.71, -0.5, -1.52], "lerp_mode": "catmullrom"}, "7.0333": {"post": [3.79, -0.49, -1.47], "lerp_mode": "catmullrom"}, "7.3": {"post": [4.22, -0.46, -1.35], "lerp_mode": "catmullrom"}, "7.7333": {"post": [3.85, -0.4, -1.1], "lerp_mode": "catmullrom"}, "7.8333": {"post": [3.27031, -0.41, -1.06], "lerp_mode": "catmullrom"}, "7.9": {"post": [3.72688, -0.42, -0.43672], "lerp_mode": "catmullrom"}, "8.0": {"post": [2.48891, -0.43, -1.10766], "lerp_mode": "catmullrom"}, "8.1333": {"post": [2.37781, -0.43, -0.99], "lerp_mode": "catmullrom"}, "8.3": {"post": [2.54953, -0.40234, -0.88672], "lerp_mode": "catmullrom"}, "8.4": {"post": [2.92234, -0.25734, 0.51859], "lerp_mode": "catmullrom"}, "8.5": {"post": [1.97109, -0.1775, 0.57359], "lerp_mode": "catmullrom"}, "8.7": {"post": [0.03, 0, 0], "lerp_mode": "catmullrom"}, "8.7333": {"post": [-0.15, 0, 1.6], "lerp_mode": "catmullrom"}, "8.8333": {"post": [0.05, 0, -0.63984], "lerp_mode": "catmullrom"}, "9.0": {"post": [0, 0, 0.09375], "lerp_mode": "catmullrom"}, "9.2333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "ar_a<PERSON><PERSON>_inspect_up"}, "1.1": {"effect": "ak47_drum_raise_quick"}, "2.0": {"effect": "p04_lm_r<PERSON>lo_inspect_drummaglrg_magout"}, "2.1333": {"effect": "ar_a<PERSON><PERSON>_inspect_magout"}, "4.3333": {"effect": "p04_lm_rkilo_inspect_empty_magin"}, "4.5333": {"effect": "ar_a<PERSON><PERSON>_inspect_magin"}, "4.8333": {"effect": "ar_a<PERSON><PERSON>_inspect_magin"}, "5.3": {"effect": "ar_a<PERSON><PERSON>_inspect_turn"}, "5.7667": {"effect": "ak47_drum_reload_empty_fast_rotate"}, "6.6667": {"effect": "p04_lm_rkilo_inspect_boltback"}, "7.8": {"effect": "p04_lm_rkilo_inspect_drummaglrg_boltback"}, "8.4": {"effect": "ar_a<PERSON><PERSON>_inspect_over"}}}, "shoot": {"animation_length": 0.83333, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0.203, -0.00709, 0.42499], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-1.03676, -0.22272, -1.1938], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-0.49333, 0.0345, -0.46515], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0.26625, 0.0093, 0.5883], "lerp_mode": "catmullrom"}, "0.3": {"post": [0.05, 0.15, -0.175], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.175, 0.13, -0.265], "lerp_mode": "catmullrom"}, "0.4667": {"post": [0, -0.075, 0.35], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0.05, -0.075, 0.24], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.15, -0.05, -0.3], "lerp_mode": "catmullrom"}, "0.7": {"post": [0.03, 0, -0.065], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, -0.16875, 1.42734], "lerp_mode": "catmullrom"}, "0.1": {"post": [0, -0.015, 1.14], "lerp_mode": "catmullrom"}, "0.2": {"post": [0, -0.00156, -0.09609], "lerp_mode": "catmullrom"}, "0.3": {"post": [0, -0.075, 0.05703], "lerp_mode": "catmullrom"}, "0.4": {"post": [0, 0.005, 0.04], "lerp_mode": "catmullrom"}, "0.4667": {"post": [0, 0.05, 0], "lerp_mode": "catmullrom"}, "0.5667": {"post": [0, 0.03, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.7667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"0.0": [0, 0, 0], "0.0333": [0.00074, -0.02126, 5.99996], "0.0667": [0, 0, 0]}}, "bullet_in_barrel": {"scale": {"0.0": [0, 0, 0], "0.2333": [0, 0, 0], "0.2667": [1, 1, 1]}}, "group7": {"rotation": {"0.0": [0, 0, 0], "0.0333": [-6.5, 0, 0], "0.1": [2.25, 0, 0], "0.1667": [-2, 0, 0], "0.2667": [0.75, 0, 0], "0.3667": [-0.9, 0, 0], "0.5": [0, 0, 0]}}, "group2": {"rotation": {"0.0": [0, 0, 0], "0.0667": [-6.5, 0, 0], "0.1333": [2.25, 0, 0], "0.2": [-2, 0, 0], "0.3": [0.75, 0, 0], "0.4333": [-0.9, 0, 0], "0.6": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, 0, 0.7], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, 0, -0.425], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0, 0, 0.375], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -0.15], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0, 0, 0.125], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, 0, -0.075], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "inspect_empty": {"animation_length": 8.3, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.1667": {"pre": [-3.95432, -0.35524, -7.5593], "post": [-3.95432, -0.35524, -7.5593], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-18.77487, -14.73938, -42.6554], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-21.52657, -15.87239, -40.05886], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-20.25861, -16.23715, -41.02775], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-20.53075, -15.90496, -40.04584], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-20.31, -15.97, -40.25], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-19.50129, -18.05893, -44.38906], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-19.4, -17.79, -43.96], "lerp_mode": "catmullrom"}, "1.8": {"post": [-19.4, -17.79, -43.96], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-15.98698, -18.90273, -47.17888], "lerp_mode": "catmullrom"}, "2.1667": [-21.03635, -12.51673, -24.54773], "2.2333": [-21.68509, -14.72379, -30.29289], "2.3333": {"pre": [-23.93272, -16.03986, -33.0996], "post": [-23.93272, -16.03986, -33.0996], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-23.93272, -16.03986, -33.0996], "lerp_mode": "catmullrom"}, "2.4": {"post": [-23.81561, -18.75152, -38.32369], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-26.69287, -14.89123, -34.22797], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-25.15112, -14.84361, -35.22725], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-25.65312, -11.93302, -33.22913], "lerp_mode": "catmullrom"}, "2.9667": {"post": [-26.01609, -11.05842, -29.41804], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-26.01964, -11.08992, -32.35272], "lerp_mode": "catmullrom"}, "3.7333": {"post": [-26.1, -10.91, -32.005], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-25.75645, -10.60384, -32.73238], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-22.18, -10.23376, -28.68011], "lerp_mode": "catmullrom"}, "4.6333": {"post": [-21.84032, -16.35295, -42.43734], "lerp_mode": "catmullrom"}, "4.7667": {"post": [-23.01767, -14.58784, -39.21257], "lerp_mode": "catmullrom"}, "4.8667": {"post": [-25.01344, -14.55583, -37.22696], "lerp_mode": "catmullrom"}, "4.9333": {"post": [-20.36166, -9.80986, -25.96993], "lerp_mode": "catmullrom"}, "4.9667": {"post": [-19.37742, -12.97734, -35.75689], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-22.53365, -10.5086, -29.93511], "lerp_mode": "catmullrom"}, "5.1333": {"post": [-22.35954, -11.69417, -32.98666], "lerp_mode": "catmullrom"}, "5.3333": {"post": [-24.36432, -1.6775, -11.65337], "lerp_mode": "catmullrom"}, "5.5333": {"post": [-23.46652, 12.24383, 43.25953], "lerp_mode": "catmullrom"}, "5.5667": {"post": [-25.38254, 14.43725, 52.36102], "lerp_mode": "catmullrom"}, "5.7333": {"post": [-29.00682, 14.71279, 56.6102], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-29.89, 14.81, 57.05], "lerp_mode": "catmullrom"}, "5.9333": {"post": [-31.3227, 15.39962, 56.65147], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-30.7885, 14.41418, 55.14586], "lerp_mode": "catmullrom"}, "6.4333": {"post": [-30.6349, 15.48634, 57.33607], "lerp_mode": "catmullrom"}, "6.7333": {"post": [-31.25052, 14.70508, 55.68418], "lerp_mode": "catmullrom"}, "6.8667": {"post": [-31.42, 14.31, 55.95], "lerp_mode": "catmullrom"}, "6.9333": {"post": [-32.27246, 11.64382, 51.59711], "lerp_mode": "catmullrom"}, "7.1667": {"post": [-26.67396, -0.07821, 27.51275], "lerp_mode": "catmullrom"}, "7.3": {"post": [-11.69726, 0.58548, 26.88017], "lerp_mode": "catmullrom"}, "7.4333": {"post": [-4.92613, 0.4648, 9.54116], "lerp_mode": "catmullrom"}, "7.6": {"post": [-0.48719, -2.12151, -6.94069], "lerp_mode": "catmullrom"}, "7.7": {"post": [0, 0, -3], "lerp_mode": "catmullrom"}, "7.8": [1.07546, -0.05235, 0.39954], "7.9": [-0.95, 0, 1.64], "8.0333": [0, 0, 0], "8.1667": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": {"pre": [-0.3, -0.65, 0], "post": [-0.3, -0.65, 0], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.6, 0.8, -0.75], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.6, 1.2, -1.35], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-0.6, 0.835, -1.59], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-0.6, 0.55, -1.6], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-0.6, 0.98, -1.6], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-0.6, 3.105, -1.6], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.6, 3.33, -1.6], "lerp_mode": "catmullrom"}, "1.8": {"post": [-0.6, 2.7, -1.6], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-0.45, -0.64, -0.8], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.7, -1.3, 2.1], "lerp_mode": "catmullrom"}, "2.2333": {"post": [0.7, -1.35, 2.1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0.7, -1.15, 2.1], "lerp_mode": "catmullrom"}, "2.4": {"post": [0.575, -0.7, 2.1], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0.245, -0.46, 2.1], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-0.05, -0.65, 2.1], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-0.275, -1.15, 2.1], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-0.2, -1.6, 2.1], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-0.125, -1.825, 2.1], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-0.4, -1.4, 2.1], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-0.4, -1.5, 2.1], "lerp_mode": "catmullrom"}, "4.5333": {"post": [-0.21, -1.27, 2.1], "lerp_mode": "catmullrom"}, "4.6333": {"post": [0.15, -0.9, 2.1], "lerp_mode": "catmullrom"}, "4.7333": {"post": [0.3, -0.6, 2.1], "lerp_mode": "catmullrom"}, "4.8333": {"post": [0.3, -0.5, 2.1], "lerp_mode": "catmullrom"}, "4.8667": {"post": [0.2, -0.5, 2.1], "lerp_mode": "catmullrom"}, "4.9667": [0.3, -0.8, 2.1], "5.1333": {"pre": [0.3, -1.67969, 2.1], "post": [0.3, -1.67969, 2.1], "lerp_mode": "catmullrom"}, "5.3333": {"post": [0.7, -4.11016, 1.5], "lerp_mode": "catmullrom"}, "5.5333": [1.425, -3.36328, 0.64141], "5.6667": {"pre": [1.87422, -2.99297, 0.30938], "post": [1.87422, -2.99297, 0.30938], "lerp_mode": "catmullrom"}, "5.8333": {"post": [2.02656, -3.34531, 0.68047], "lerp_mode": "catmullrom"}, "5.9333": {"post": [2.02656, -3.96641, 1.22516], "lerp_mode": "catmullrom"}, "6.0333": {"post": [1.9975, -4.26297, 1.14094], "lerp_mode": "catmullrom"}, "6.2667": {"post": [1.81, -4.12625, 1.26938], "lerp_mode": "catmullrom"}, "6.6333": {"post": [1.83, -3.89797, 1.35781], "lerp_mode": "catmullrom"}, "6.8667": {"post": [1.84, -3.84469, 1.24359], "lerp_mode": "catmullrom"}, "6.9333": {"post": [1.84, -4.45609, 1.23], "lerp_mode": "catmullrom"}, "7.0": {"post": [1.84, -4.23719, 1.23359], "lerp_mode": "catmullrom"}, "7.1": {"post": [1.81, -5.45562, 0.44], "lerp_mode": "catmullrom"}, "7.2333": {"post": [1.6, -6.10031, -1.06], "lerp_mode": "catmullrom"}, "7.3667": {"post": [1.08, -4.20469, -4.00109], "lerp_mode": "catmullrom"}, "7.5": {"post": [0.52, -0.355, -4.04109], "lerp_mode": "catmullrom"}, "7.7": {"post": [0, -0.3, 0.95], "lerp_mode": "catmullrom"}, "7.8333": [0, 0.02, -0.14], "7.9667": [0, 0.12, 0.08], "8.1333": [0, 0, 0]}}, "lefthand_and_mag": {"rotation": {"2.1667": [0, 0, 0], "2.2667": [-14.87378, -0.06978, -1.99878], "2.3333": [-14.87378, -0.06978, -1.99878], "2.4": {"pre": [-17.58598, -4.88201, -7.06163], "post": [-17.58598, -4.88201, -7.06163], "lerp_mode": "catmullrom"}, "2.5": {"post": [-2.54764, -3.15869, -9.15509], "lerp_mode": "catmullrom"}, "2.6": {"post": [-2.29304, 0.51545, -7.03527], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0.11593, 1.3276, -6.41886], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.56745, 0.66867, -6.29566], "lerp_mode": "catmullrom"}, "3.2": {"post": [-0.73884, -4.95119, 7.1523], "lerp_mode": "catmullrom"}, "3.3333": {"post": [4.57279, 13.80254, 51.68818], "lerp_mode": "catmullrom"}, "3.4333": {"post": [11.39392, 23.87067, 73.5731], "lerp_mode": "catmullrom"}, "3.6": {"post": [10.62198, 24.66829, 69.27027], "lerp_mode": "catmullrom"}, "3.7": {"post": [10.16633, 24.85041, 68.18683], "lerp_mode": "catmullrom"}, "3.8": {"post": [9.71201, 25.02264, 67.10375], "lerp_mode": "catmullrom"}, "4.1667": [10.8, 25.61, 67.345], "4.2667": [11.07064, 24.47878, 61.87432], "4.3667": {"pre": [2.6316, 9.13844, -19.76362], "post": [2.6316, 9.13844, -19.76362], "lerp_mode": "catmullrom"}, "4.5": [-9.91205, 0, -26.17873], "4.6": [-16.50045, 0, -1.24916], "4.6667": [-23.925, 0, 0], "4.7": [-24.5, 0, 0], "4.7667": [-22.5, 0, 0], "4.8": [-25.25, 0, 0], "4.8667": [0, 0, 0]}, "position": {"2.1667": [0, 0, 0], "2.2667": [0, -0.3, -0.575], "2.3333": [0, -0.525, -0.95], "2.4": {"pre": [1.4, -6.7, -5.4], "post": [1.4, -6.7, -5.4], "lerp_mode": "catmullrom"}, "2.5": {"post": [2.60106, -7.37564, -1.50788], "lerp_mode": "catmullrom"}, "2.6667": {"post": [4.19814, -4.01898, 1.38632], "lerp_mode": "catmullrom"}, "2.7667": {"post": [4.90996, -1.98517, 2.02353], "lerp_mode": "catmullrom"}, "2.9": {"post": [5.13462, -1.70756, 1.84998], "lerp_mode": "catmullrom"}, "3.1": {"post": [4.95, -2.2, 1.9], "lerp_mode": "catmullrom"}, "3.2": {"post": [4.75, -2.8, 2.4], "lerp_mode": "catmullrom"}, "3.3333": {"post": [3.61, -3.505, 2.16], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.4, -3.725, 1.125], "lerp_mode": "catmullrom"}, "3.6": {"post": [3.3, -3.425, 0.875], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.2, -3.425, 0.875], "lerp_mode": "catmullrom"}, "3.8": {"post": [3.2, -3.425, 0.875], "lerp_mode": "catmullrom"}, "4.1667": [3.165, -3.7, 0.775], "4.2667": [3.3, -3.85, 0.825], "4.3667": {"pre": [2.745, -6.63, 0.885], "post": [2.745, -6.63, 0.885], "lerp_mode": "catmullrom"}, "4.5": [2.05, -2, -1.95], "4.6": [0.4, -1, -1.525], "4.6667": [0.1, -1, -2.375], "4.7": [0.1, -1, -2.3], "4.7667": [0.1, -1, -1.825], "4.8": [0.05, -0.5, -1.75], "4.8667": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [100.36601, 316.5798, -142.78527], "0.1667": [120.65263, 300.35672, -200.36724], "0.3": [111.55615, 298.47384, -188.40898], "1.6667": [111.555, 298.47, -188.405], "1.9667": [118.83163, 326.45682, -211.14883], "2.0667": [128.08894, 353.56633, -232.1361], "2.1667": [137.90708, 352.98359, -229.17653], "2.2667": [136.38376, 352.2397, -227.32289], "2.3333": [131.92598, 335.01803, -221.25165], "2.4": [124.32756, 341.90777, -207.99399], "2.5667": [117.49784, 353.33242, -204.26761], "2.7": [122.57973, 353.16258, -203.63703], "3.0667": [122.57973, 353.16258, -203.63703], "3.2": [121.015, 353.92, -204.425], "3.3": [121.6483, 350.03872, -193.05026], "4.3": [121.6483, 350.03872, -193.05026], "4.4": [128.32432, 351.9275, -221.98264], "5.1": [128.32432, 351.9275, -221.98264], "5.2": [124.115, 332.21, -174.055], "5.3": [110.9783, 319.06865, -142.09861], "5.7": [81.9783, 319.06865, -142.09861], "7.3": [83.9783, 319.06865, -142.09861], "7.4333": [100.36601, 316.5798, -142.78527]}, "position": {"0.0": [7.775, -13, -6], "0.1667": [14.3, -27.8, -3.8], "0.3": [9.9, -45.2, 22.9], "1.6667": [9.9, -45.2, 22.9], "1.9667": [7.18, -18.75, 1.05], "2.0667": [7.35, -16.5, -1.5], "2.2667": [7.35, -16.5, -1.5], "2.3333": [7.35, -16.975, -1.1], "2.5667": [7.31, -16.92, -0.745], "3.2": [7.31, -16.92, -0.745], "3.3": [8.54, -16.78, -1.6], "4.3": [8.54, -16.78, -1.6], "4.4": [7.65, -17.1, -1.275], "5.1": [7.65, -17.1, -1.275], "5.1667": [7.91, -15.06, -4.48], "5.3": [8.1, -12.1, -8.5], "7.3": [8.1, -12.1, -8.5], "7.4333": [7.775, -13, -6]}, "scale": [1, 1.5, 1]}, "righthand_and_gun": {"rotation": {"2.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0, 0, -6], "lerp_mode": "catmullrom"}, "2.6": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "2.8667": {"post": [4.25145, 1.49587, 1.11119], "lerp_mode": "catmullrom"}, "3.3333": {"post": [9.7516, 3.94902, -2.77956], "lerp_mode": "catmullrom"}, "3.6667": {"post": [11.0016, 3.94902, -2.77956], "lerp_mode": "catmullrom"}, "4.3": {"post": [8.83657, 0.51786, 5.59603], "lerp_mode": "catmullrom"}, "4.7": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"2.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.7333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.33567, -0.49419, 0.31421], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-0.40508, -0.57962, 0.36794], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-0.13019, -0.20715, 0.12851], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"5.7667": [0, 0, 0], "5.9": {"pre": [0, 0, 2], "post": [0, 0, 2], "lerp_mode": "catmullrom"}, "6.0667": {"post": [0, 0, 2.25], "lerp_mode": "catmullrom"}, "6.3": {"post": [0, 0, 2.17], "lerp_mode": "catmullrom"}, "6.5667": [0, 0, 2.19938], "6.8667": [0, 0, 2], "6.9333": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "righthand": {"rotation": {"0.0": [95.04292, 367.4713, 180.6574], "0.1": [95.04292, 367.4713, 180.6574], "0.2333": [95.04292, 367.4713, 180.6574], "1.9667": [95.04292, 367.4713, 180.6574], "2.1": [95.04292, 367.4713, 180.6574], "5.4667": [95.04292, 367.4713, 180.6574], "5.6667": [76.17064, 380.31866, 67.92926], "5.7667": [77.90318, 376.66785, 78.13254], "5.8": [79.05642, 378.34388, 85.93755], "5.9": {"pre": [83.30253, 402.22545, 102.90458], "post": [83.30253, 402.22545, 102.90458], "lerp_mode": "catmullrom"}, "6.2333": {"post": [85.04261, 405.56628, 103.17399], "lerp_mode": "catmullrom"}, "6.4667": {"post": [84.64494, 403.05678, 104.1009], "lerp_mode": "catmullrom"}, "6.8667": [83.30253, 402.22545, 102.90458], "6.9333": {"pre": [62.2494, 406.43748, 84.52542], "post": [62.2494, 406.43748, 84.52542], "lerp_mode": "catmullrom"}, "7.1333": [95.04292, 367.4713, 180.6574]}, "position": {"0.0": [-7, -14.275, 7], "0.1": [-7, -14.275, 7], "0.2333": [-8.25, -14.275, 7], "1.9667": [-8.25, -14.275, 7], "2.1": [-7, -14.275, 7], "5.4667": [-7, -14.275, 7], "5.5": {"pre": [-8.015, -13.35, 5.48], "post": [-8.015, -13.35, 5.48], "lerp_mode": "catmullrom"}, "5.6": [-9.43141, -12.18, 0.80187], "5.6667": [-9.05, -11.675, -0.15], "5.7667": [-9.05, -11.525, -0.625], "5.9": {"pre": [-9.05, -11.57, 0.65], "post": [-9.05, -11.57, 0.65], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-9.05, -11.615, 0.74719], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-9.05, -11.615, 0.79703], "lerp_mode": "catmullrom"}, "6.6333": [-9.05, -11.64, 0.93234], "6.8667": [-9.05, -11.62859, 0.72266], "6.9333": {"pre": [-9.05, -11.94, 1.58], "post": [-9.05, -11.94, 1.58], "lerp_mode": "catmullrom"}, "7.1333": [-7, -14.275, 7]}, "scale": [1, 1.5, 1]}, "bullet": {"scale": 0}, "group7": {"rotation": {"0.1667": [0, 0, 0], "0.3": [1.5, 0, 0], "0.5": [-1, 0, 0], "0.7": [0.975, 0, 0], "0.9667": [-0.475, 0, 0], "1.3": [0.225, 0, 0], "1.7333": [0, 0, 0], "2.1667": [0, 0, 0], "2.2667": [1.5, 0, 0], "2.4333": [-1, 0, 0], "2.5667": [0.975, 0, 0], "2.7667": [-0.475, 0, 0], "3.0333": [0.225, 0, 0], "3.3333": [0, 0, 0], "4.4333": [0, 0, 0], "4.5": [1.5, 0, 0], "4.6": [-1, 0, 0], "4.7": [0, 0, 0], "4.7667": [1.5, 0, 0], "4.8667": [-1, 0, 0], "4.9667": [0.975, 0, 0], "5.1": [-0.475, 0, 0], "5.3": [0, 0, 0], "5.4333": [1.5, 0, 0], "5.6333": [-1, 0, 0], "5.8": [0.975, 0, 0], "6.1": [-0.475, 0, 0], "6.4333": [0.225, 0, 0], "6.8": [0, 0, 0], "6.8667": [0, 0, 0], "6.9333": [1.5, 0, 0], "7.0333": [-1, 0, 0], "7.1333": [0.975, 0, 0], "7.2667": [-0.475, 0, 0], "7.4333": [0.225, 0, 0], "7.5333": [0, 0, 0], "7.6": [1.5, 0, 0], "7.7": [-1, 0, 0], "7.8": [0.975, 0, 0], "7.9333": [-0.475, 0, 0], "8.1": [0.225, 0, 0], "8.3": [0, 0, 0]}}, "group2": {"rotation": {"0.2667": [0, 0, 0], "0.4": [1.5, 0, 0], "0.6": [-1, 0, 0], "0.7667": [0.975, 0, 0], "1.0667": [-0.475, 0, 0], "1.4": [0.225, 0, 0], "1.7667": [0, 0, 0], "2.3667": [0, 0, 0], "2.4667": [1.5, 0, 0], "2.6333": [-1, 0, 0], "2.7667": [0.975, 0, 0], "2.9667": [-0.475, 0, 0], "3.2333": [0.225, 0, 0], "3.5333": [0, 0, 0], "4.5667": [0, 0, 0], "4.6333": [1.5, 0, 0], "4.7333": [-1, 0, 0], "4.8333": [0, 0, 0], "4.9": [1.5, 0, 0], "5.0": [-1, 0, 0], "5.1": [0.975, 0, 0], "5.2333": [-0.475, 0, 0], "5.3": [0, 0, 0], "5.4333": [1.5, 0, 0], "5.6333": [-1, 0, 0], "5.8": [0.975, 0, 0], "6.1": [-0.475, 0, 0], "6.4333": [0.225, 0, 0], "6.8": [0, 0, 0], "7.3333": [0.225, 0, 0], "7.4333": [0, 0, 0], "7.5": [1.5, 0, 0], "7.6": [-1, 0, 0], "7.7": [0.975, 0, 0], "7.8333": [-0.475, 0, 0], "8.0": [0.225, 0, 0], "8.2": [0, 0, 0]}}, "release": {"rotation": {"2.1333": [0, 0, 0], "2.2": [-17.5, 0, 0], "2.6667": [-17.5, 0, 0], "2.7667": [0, 0, 0], "4.8": [0, 0, 0], "4.8667": [-17.5, 0, 0], "4.9333": [-17.5, 0, 0], "5.0": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [1.72, 0.5, -0.45], "lerp_mode": "catmullrom"}, "0.3667": {"post": [3.175, 0.8, -1.1], "lerp_mode": "catmullrom"}, "0.7333": {"post": [3.125, 0.8, -1.1], "lerp_mode": "catmullrom"}, "0.9333": {"post": [3.585, 0.8, -1.1], "lerp_mode": "catmullrom"}, "1.1667": {"post": [5.295, 1.625, -1.55], "lerp_mode": "catmullrom"}, "1.3333": {"post": [7.335, 2.7, -1.55], "lerp_mode": "catmullrom"}, "1.5667": {"post": [8.425, 3.1, -1.55], "lerp_mode": "catmullrom"}, "2.0": {"post": [2.7, 1.7, -1.55], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.9, 0.8, -1.1], "lerp_mode": "catmullrom"}, "2.3333": {"post": [1, 0.5, -1.1], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.5, -0.3, 1.2], "lerp_mode": "catmullrom"}, "2.5": {"post": [0.7, -0.5, -2.95], "lerp_mode": "catmullrom"}, "2.6": {"post": [0.9, -0.8, -1.1], "lerp_mode": "catmullrom"}, "2.8": {"post": [2.3, -1.6, -1.55], "lerp_mode": "catmullrom"}, "3.0": {"post": [3, -2, -1.55], "lerp_mode": "catmullrom"}, "3.2": {"post": [3.09, -2, -1.55], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.2, -4.1, -2.125], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.11, -4.8, -2.125], "lerp_mode": "catmullrom"}, "4.0": {"post": [3, -4.8, -2.125], "lerp_mode": "catmullrom"}, "4.3": {"post": [2.6, -4.5, -2.125], "lerp_mode": "catmullrom"}, "4.5667": {"post": [0.8, 0.2, -2], "lerp_mode": "catmullrom"}, "4.8": {"post": [0.4, 0.8, -1.55], "lerp_mode": "catmullrom"}, "4.8667": {"post": [1.5, 0.8, 0.02344], "lerp_mode": "catmullrom"}, "4.9667": {"post": [0.7, 0.8, -2.32578], "lerp_mode": "catmullrom"}, "5.0667": {"post": [1.1, 0.8, -1.2], "lerp_mode": "catmullrom"}, "5.2": {"post": [1.3, 0, -1.55], "lerp_mode": "catmullrom"}, "5.5": {"post": [3.1, -0.4, -1.55], "lerp_mode": "catmullrom"}, "5.7667": {"post": [3.5, -0.5, -1.55], "lerp_mode": "catmullrom"}, "5.8667": {"post": [4.71, -0.5, -1.52], "lerp_mode": "catmullrom"}, "6.0": {"post": [3.79, -0.49, -1.47], "lerp_mode": "catmullrom"}, "6.2667": {"post": [4.22, -0.46, -1.35], "lerp_mode": "catmullrom"}, "6.7": {"post": [3.85, -0.4, -1.1], "lerp_mode": "catmullrom"}, "6.8": {"post": [3.27031, -0.41, -1.06], "lerp_mode": "catmullrom"}, "6.8667": {"post": [3.72688, -0.42, -0.43672], "lerp_mode": "catmullrom"}, "6.9667": {"post": [2.48891, -0.43, -1.10766], "lerp_mode": "catmullrom"}, "7.1": {"post": [2.37781, -0.43, -0.99], "lerp_mode": "catmullrom"}, "7.2667": {"post": [2.54953, -0.40234, -0.88672], "lerp_mode": "catmullrom"}, "7.3667": {"post": [2.92234, -0.25734, 0.51859], "lerp_mode": "catmullrom"}, "7.4667": {"post": [1.97109, -0.1775, 0.57359], "lerp_mode": "catmullrom"}, "7.6667": {"post": [0.03, 0, 0], "lerp_mode": "catmullrom"}, "7.7": {"post": [-0.15, 0, 1.6], "lerp_mode": "catmullrom"}, "7.8": {"post": [0.05, 0, -0.63984], "lerp_mode": "catmullrom"}, "7.9667": {"post": [0, 0, 0.09375], "lerp_mode": "catmullrom"}, "8.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "ar_a<PERSON><PERSON>_inspect_up"}, "2.0333": {"effect": "ar_a<PERSON><PERSON>_raise_grab"}, "2.1333": {"effect": "ar_a<PERSON><PERSON>_inspect_magout"}, "2.9667": {"effect": "p04_ar_akilo_inspect_mvt"}, "4.4333": {"effect": "ar_a<PERSON>lo_inspect_magbump"}, "4.7667": {"effect": "ar_a<PERSON><PERSON>_inspect_magin"}, "5.0667": {"effect": "ar_a<PERSON><PERSON>_inspect_turn"}, "5.6333": {"effect": "ar_a<PERSON><PERSON>_inspect_pull"}, "6.7667": {"effect": "ar_akilo_inspect_release"}, "7.3667": {"effect": "ar_a<PERSON><PERSON>_inspect_over"}}}}}