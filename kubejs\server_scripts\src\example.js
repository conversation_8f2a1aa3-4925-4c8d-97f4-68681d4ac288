ItemEvents.rightClicked("stick",event=>{
    function addItemOutput(count) {
        let firstItem = []
        Ingredient.of("#forge:ores")
            .getItemIds()
            .forEach((item) => {
                firstItem.push(item)
            })
        let result = {
            type: "mm:output/simple",
            ingredient: {
                type: "mm:item",
                item: firstItem[0],
                count: count
            }
        }
        return result
    }
    addItemOutput(1)
})