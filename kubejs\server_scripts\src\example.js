ItemEvents.rightClicked("stick",event=>{
    function addItemOutput(count) {
        let firstItem = []
        Ingredient.of("#forge:ores")
            .getItemIds()
            .forEach((item) => {
                firstItem.push(item)
                console.log("找到矿物: " + item)
            })
        let result = {
            type: "mm:output/simple",
            ingredient: {
                type: "mm:item",
                item: firstItem[0],
                count: count
            }
        }
        return result
    }

    let result = addItemOutput(1)
    console.log("测试结果: " + JSON.stringify(result, null, 2))
})