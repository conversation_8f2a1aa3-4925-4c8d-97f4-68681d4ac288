{
  
  "ammo": "lradd:nail",

  "ammo_amount": 30,
  "extended_mag_ammo_amount": [
    40,
    45,
    50
  ],
  "bolt": "open_bolt",
  "rpm": 720,
  "bullet": {
    // 寿命，单位秒
    "life": 0.4,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 4.5,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.25,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 1.5,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        {"distance": 1, "damage": 5.2},
        {"distance": 6, "damage": 4.5},
        {"distance": 17, "damage": 3.7},
        {"distance": 26, "damage": 3.2},
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 2.5}
      ]
    },
    // 速度 m/s
    "speed": 120,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.35,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 1
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  "reload": {
    "type": "magazine",
    "feed": {
      "empty": 3,
      "tactical": 3
    },
    "cooldown": {
      "empty": 3.625  ,
      "tactical": 3.0417
    }
  },
  "draw_time": 0.75,
  "put_away_time": 0.5417,
  "aim_time": 0.2,
  "sprint_time": 0.3,
  "fire_mode": [
    "auto",
    "semi"
  ],
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.42, 0.42]},
      {"time": 0.26, "value": [0.14, 0.14]},
      {"time": 0.48, "value": [-0.125, -0.125]},
      {"time": 0.73, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [0.45, 0.45]},
      {"time": 0.35, "value": [0.45, 0.45]},
      {"time": 0.54, "value": [0, 0]}
    ]
  },
  "inaccuracy": {
    "stand": 1.65,
    "move": 2.1,
    "sneak": 1.23,
    "lie": 1.1,
    "aim": 0.25
  },
  "move_speed": {
    "base": 0.92,
    // 瞄准情况
    "aim": 0.87
  },
  // 开放的配件槽。未指定的槽位默认关闭。全部配件槽类型有:
  // scope, stock, muzzle, grip, laser, extended_mag
  "allow_attachment_types": [
    "scope",
    "stock",
    "muzzle",
    "extended_mag"
  ]
}