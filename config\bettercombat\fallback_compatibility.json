{"schema_version": 1, "blacklist_item_id_regex": "pickaxe", "fallback_compatibility": [{"item_id_regex": "claymore|great_sword|greatsword", "weapon_attributes": "bettercombat:claymore"}, {"item_id_regex": "great_hammer|greathammer|war_hammer|warhammer|maul", "weapon_attributes": "bettercombat:hammer"}, {"item_id_regex": "double_axe|doubleaxe|war_axe|waraxe|great_axe|greataxe", "weapon_attributes": "bettercombat:double_axe"}, {"item_id_regex": "scythe", "weapon_attributes": "bettercombat:scythe"}, {"item_id_regex": "halberd", "weapon_attributes": "bettercombat:halberd"}, {"item_id_regex": "glaive", "weapon_attributes": "bettercombat:glaive"}, {"item_id_regex": "spear", "weapon_attributes": "bettercombat:spear"}, {"item_id_regex": "lance", "weapon_attributes": "bettercombat:lance"}, {"item_id_regex": "anchor", "weapon_attributes": "bettercombat:anchor"}, {"item_id_regex": "battlestaff|battle_staff", "weapon_attributes": "bettercombat:battlestaff"}, {"item_id_regex": "claw", "weapon_attributes": "bettercombat:claw"}, {"item_id_regex": "fist|gauntlet", "weapon_attributes": "bettercombat:fist"}, {"item_id_regex": "trident|javelin|impaled", "weapon_attributes": "bettercombat:trident"}, {"item_id_regex": "katana", "weapon_attributes": "bettercombat:katana"}, {"item_id_regex": "rapier", "weapon_attributes": "bettercombat:rapier"}, {"item_id_regex": "sickle", "weapon_attributes": "bettercombat:sickle"}, {"item_id_regex": "soul_knife", "weapon_attributes": "bettercombat:soul_knife"}, {"item_id_regex": "dagger|knife", "weapon_attributes": "bettercombat:dagger"}, {"item_id_regex": "staff|wand|sceptre|stave|rod", "weapon_attributes": "bettercombat:wand"}, {"item_id_regex": "mace|hammer|flail", "weapon_attributes": "bettercombat:mace"}, {"item_id_regex": "axe", "weapon_attributes": "bettercombat:axe"}, {"item_id_regex": "coral_blade", "weapon_attributes": "bettercombat:coral_blade"}, {"item_id_regex": "twin_blade|twinblade", "weapon_attributes": "bettercombat:twin_blade"}, {"item_id_regex": "cutlass|scimitar|machete", "weapon_attributes": "bettercombat:cutlass"}, {"item_id_regex": "sword|blade", "weapon_attributes": "bettercombat:sword"}], "ranged_weapons": [{"item_id_regex": "two_handed_crossbow", "weapon_attributes": "bettercombat:crossbow_two_handed_heavy"}, {"item_id_regex": "two_handed_bow", "weapon_attributes": "bettercombat:bow_two_handed_heavy"}]}