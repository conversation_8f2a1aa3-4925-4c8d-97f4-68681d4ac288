[15:17:33] [INIT] KubeJS 2001.6.5-build.16; MC 2001 forge
[15:17:33] [INIT] Loaded plugins:
[15:17:33] [INIT] - dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgePlugin
[15:17:33] [INIT] - dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgeClientPlugin
[15:17:33] [INIT] - com.prunoideae.powerfuljs.forge.PowerfulJSPluginForge
[15:17:33] [INIT] - com.almostreliable.morejs.Plugin
[15:17:33] [INIT] - dev.latvian.kubejs.mekanism.MekanismKubeJSPlugin
[15:17:33] [INIT] - net.liopyu.menujs.MenuJSPlugin
[15:17:33] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbquests.kubejs.KubeJSIntegration
[15:17:33] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbteams.kubejs.FTBTeamsKubeJSPlugin
[15:17:33] [INIT] - net.prizowo.keyboardjs.kubejs.KeyboardJSPlugin
[15:17:33] [INIT] - com.almostreliable.lootjs.kube.LootJSPlugin
[15:17:33] [INIT] - com.probejs.ProbeJSPlugin
[15:17:33] [INIT] - com.tom.createores.kubejs.KubeJSExcavation
[15:17:33] [INIT] - net.liopyu.entityjs.EntityJSPlugin
[15:17:33] [INIT] - dev.latvian.mods.kubejs.create.KubeJSCreatePlugin
[15:17:33] [INFO] Loaded script server_scripts:debug/debug.js in 0.0 s
[15:17:33] [INFO] Loaded script server_scripts:src/example.js in 0.001 s
[15:17:33] [INFO] Loaded script server_scripts:src/lightning_iron.js in 0.001 s
[15:17:33] [INFO] Loaded script server_scripts:src/wooden_dragon_head_interaction.js in 0.001 s
[15:17:33] [INFO] Loaded 4/4 KubeJS server scripts in 0.008 s with 0 errors and 0 warnings
[15:17:33] [INFO] Scripts loaded
[15:17:34] [INFO] Server resource reload complete!
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:coal_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_coal_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:copper_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_copper_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:diamond_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_diamond_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:emerald_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_emerald_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:gold_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:nether_gold_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_gold_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:iron_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_iron_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:lapis_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_lapis_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:redstone_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:deepslate_redstone_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:nether_quartz_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: minecraft:ancient_debris
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:ore_aluminum
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:deepslate_ore_aluminum
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:ore_lead
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:deepslate_ore_lead
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:lead_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:deepslate_lead_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:ore_silver
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:deepslate_ore_silver
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:ore_nickel
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:deepslate_ore_nickel
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:ore_uranium
[15:17:36] [INFO] src/example.js#9: 找到矿物: immersiveengineering:deepslate_ore_uranium
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:uranium_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:deepslate_uranium_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: create:zinc_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: create:deepslate_zinc_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:tin_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:deepslate_tin_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:osmium_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:deepslate_osmium_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:fluorite_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: mekanism:deepslate_fluorite_ore
[15:17:36] [INFO] src/example.js#9: 找到矿物: tconstruct:cobalt_ore
[15:17:36] [INFO] src/example.js#23: 测试结果: {
  "type": "mm:output/simple",
  "ingredient": {
    "type": "mm:item",
    "item": "minecraft:coal_ore",
    "count": 1.0
  }
}
