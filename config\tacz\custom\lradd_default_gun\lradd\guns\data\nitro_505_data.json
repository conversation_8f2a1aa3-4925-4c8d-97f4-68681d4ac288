{
  // 指定枪械弹药
  "ammo": "lradd:arrow",
  // 弹容
  "ammo_amount": 1,
  // 扩容弹夹弹药总数
  // 枪栓类型：开膛待机（open_bolt）、闭膛待击（closed_bolt）、手动上膛（manual_action)
  "bolt": "open_bolt",
  // 每分钟射出弹药量，最大不应超过 1200
  "rpm": 300,
  // 子弹实体属性
  "bullet": {
    // 寿命，单位秒
    "life": 10,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 20,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.8,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 3,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        {"distance": 10, "damage": 25},
        {"distance": 30, "damage": 20},
        {"distance": 50, "damage": 18.5},
        {"distance": 60, "damage": 16},
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 15}
      ]
    },
    // 速度 m/s
    "speed": 60,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.01,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 2
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  // 换弹相关
  "reload": {
    // magazine 是弹匣供弹，manual 是手动供弹
    "type": "magazine",
    // 弹匣供弹换弹时长（秒），到达此时间点，服务端就更新枪内子弹数量
    "feed": {
      // 空仓换弹
      "empty": 0.1,
      // 战术换弹
      "tactical": 1.42
    },
    // 弹匣供弹总时长（秒），到达此时间点，枪械才可以进行开火、检视等行为。
    "cooldown": {
      // 空仓换弹
      "empty": 2.1667,
      // 战术换弹
      "tactical": 2.1667
    }
  },
  // 枪械抬起的动作时长，单位秒。
  // 抬起动作完成后，枪械可以进行开火、检视等行为
  "draw_time": 0.3,
  "put_away_time": 0.3,
  "aim_time": 0.15,
  "sprint_time": 0.25,
  // 开火模式
  "fire_mode": [
    "semi"
  ],
  // 后坐力
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.45, 0.45]},
      {"time": 0.28, "value": [0.45, 0.45]},
      {"time": 0.37, "value": [0.15, 0.15]},
      {"time": 0.52, "value": [-0.13, -0.13]},
      {"time": 0.76, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [-0.325, -0.275]},
      {"time": 0.45, "value": [-0.125, 0]},
      {"time": 0.62, "value": [0, 0]}
    ]
  },
  // 不准确度
  "inaccuracy": {
    // 站立射击散布
    "stand": 2.5,
    // 移动射击散布
    "move": 4,
    // 潜行射击散布
    "sneak": 1.75,
    // 趴下射击散布
    "lie": 1.75,
    // 瞄准射击时散布
    "aim": 0.05
  },
  "move_speed": {
    "base": 0.9,
    "aim": 0.72
  },
  // 开放的配件槽。未指定的槽位默认关闭。全部配件槽类型有:
  // scope, stock, muzzle, grip, laser, extended_mag
  "allow_attachment_types": [
    "scope"
  ],
  // 专属的配件属性
  "exclusive_attachments": {
    // 配件 ID，后面的数据和配件的 data 部分结构完全一致
    "tac:8x": {
      "weight": 2.0,
      "ads_addend": 0.04,
      "inaccuracy_addend": -0.4,
      "recoil_modifier": {
        "pitch": -0.2,
        "yaw": -0.1
      }
    }
  }
}