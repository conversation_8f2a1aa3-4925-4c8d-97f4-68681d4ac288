[{"maxStackSize": 64, "maxDamage": 0, "localized": "16k合成存储器", "block": {"crop": false}, "id": "ae2:16k_crafting_storage"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "1k合成存储器", "block": {"crop": false}, "id": "ae2:1k_crafting_storage"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "256k合成存储器", "block": {"crop": false}, "id": "ae2:256k_crafting_storage"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "4k合成存储器", "block": {"crop": false}, "id": "ae2:4k_crafting_storage"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "64k合成存储器", "block": {"crop": false}, "id": "ae2:64k_crafting_storage"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级卡", "id": "ae2:advanced_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "破坏核心", "id": "ae2:annihilation_core"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME破坏面板", "id": "ae2:annihilation_plane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础卡", "id": "ae2:basic_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色ME包层线缆", "id": "ae2:black_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色ME致密包层线缆", "id": "ae2:black_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色ME玻璃线缆", "id": "ae2:black_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色光通染色球", "id": "ae2:black_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色染色球", "id": "ae2:black_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色ME智能线缆", "id": "ae2:black_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色ME致密线缆", "id": "ae2:black_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空白样板", "id": "ae2:blank_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色ME包层线缆", "id": "ae2:blue_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色ME致密包层线缆", "id": "ae2:blue_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色ME玻璃线缆", "id": "ae2:blue_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色光通染色球", "id": "ae2:blue_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色染色球", "id": "ae2:blue_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色ME智能线缆", "id": "ae2:blue_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色ME致密线缆", "id": "ae2:blue_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色ME包层线缆", "id": "ae2:brown_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色ME致密包层线缆", "id": "ae2:brown_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色ME玻璃线缆", "id": "ae2:brown_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色光通染色球", "id": "ae2:brown_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色染色球", "id": "ae2:brown_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色ME智能线缆", "id": "ae2:brown_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色ME致密线缆", "id": "ae2:brown_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "线缆锚", "id": "ae2:cable_anchor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "AE2线缆和（或）总线", "block": {"crop": false}, "id": "ae2:cable_bus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "能源接收器", "id": "ae2:cable_energy_acceptor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME接口", "id": "ae2:cable_interface"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME样板供应器", "id": "ae2:cable_pattern_provider"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "运算处理器", "id": "ae2:calculation_processor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "运算压印模板", "id": "ae2:calculation_processor_press"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "容量卡", "id": "ae2:capacity_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "16k ME存储组件", "id": "ae2:cell_component_16k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "1k ME存储组件", "id": "ae2:cell_component_1k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "256k ME存储组件", "id": "ae2:cell_component_256k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "4k ME存储组件", "id": "ae2:cell_component_4k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "64k ME存储组件", "id": "ae2:cell_component_64k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "元件工作台", "block": {"crop": false}, "id": "ae2:cell_workbench"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "赛特斯石英斧", "id": "ae2:certus_quartz_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英水晶", "id": "ae2:certus_quartz_crystal"}, {"maxStackSize": 1, "maxDamage": 50, "localized": "赛特斯石英切割刀", "id": "ae2:certus_quartz_cutting_knife"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英粉", "id": "ae2:certus_quartz_dust"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "赛特斯石英锄", "id": "ae2:certus_quartz_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "赛特斯石英镐", "id": "ae2:certus_quartz_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "赛特斯石英锹", "id": "ae2:certus_quartz_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "赛特斯石英剑", "id": "ae2:certus_quartz_sword", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "赛特斯石英扳手", "id": "ae2:certus_quartz_wrench"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "充能赛特斯石英水晶", "id": "ae2:charged_certus_quartz_crystal"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "充能手杖", "id": "ae2:charged_staff"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "充能器", "block": {"crop": false}, "id": "ae2:charger"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME箱子", "block": {"crop": false}, "id": "ae2:chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "开裂的赛特斯石英母岩", "block": {"crop": false}, "id": "ae2:chipped_budding_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹赛特斯石英块", "block": {"crop": false}, "id": "ae2:chiseled_quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹赛特斯石英台阶", "block": {"crop": false}, "id": "ae2:chiseled_quartz_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹赛特斯石英楼梯", "block": {"crop": false}, "id": "ae2:chiseled_quartz_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹赛特斯石英墙", "block": {"crop": false}, "id": "ae2:chiseled_quartz_wall"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "染色器 - 空", "id": "ae2:color_applicator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物质聚合器", "block": {"crop": false}, "id": "ae2:condenser"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME控制器", "block": {"crop": false}, "id": "ae2:controller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME交换监控器", "id": "ae2:conversion_monitor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "并行处理单元", "block": {"crop": false}, "id": "ae2:crafting_accelerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合成卡", "id": "ae2:crafting_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合成监控器", "block": {"crop": false}, "id": "ae2:crafting_monitor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "合成样板", "id": "ae2:crafting_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME合成终端", "id": "ae2:crafting_terminal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合成单元", "block": {"crop": false}, "id": "ae2:crafting_unit"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木质曲柄", "block": {"crop": false}, "id": "ae2:crank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造能源元件", "block": {"crop": false}, "id": "ae2:creative_energy_cell"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造ME流体元件", "id": "ae2:creative_fluid_cell"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造ME物品元件", "id": "ae2:creative_item_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crystal Resonance Generator", "block": {"crop": false}, "id": "ae2:crystal_resonance_generator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赛特斯石英块", "block": {"crop": false}, "id": "ae2:cut_quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赛特斯石英台阶", "block": {"crop": false}, "id": "ae2:cut_quartz_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赛特斯石英楼梯", "block": {"crop": false}, "id": "ae2:cut_quartz_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赛特斯石英墙", "block": {"crop": false}, "id": "ae2:cut_quartz_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色ME包层线缆", "id": "ae2:cyan_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色ME致密包层线缆", "id": "ae2:cyan_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色ME玻璃线缆", "id": "ae2:cyan_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色光通染色球", "id": "ae2:cyan_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色染色球", "id": "ae2:cyan_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色ME智能线缆", "id": "ae2:cyan_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色ME致密线缆", "id": "ae2:cyan_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "损坏的赛特斯石英母岩", "block": {"crop": false}, "id": "ae2:damaged_budding_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "暗色照明面板", "id": "ae2:dark_monitor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·调试卡", "id": "ae2:debug_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·区块加载器", "block": {"crop": false}, "id": "ae2:debug_chunk_loader"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·方块生成器", "block": {"crop": false}, "id": "ae2:debug_cube_gen"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·FE能源", "block": {"crop": false}, "id": "ae2:debug_energy_gen"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·删除卡", "id": "ae2:debug_eraser"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·物品生成器", "block": {"crop": false}, "id": "ae2:debug_item_gen"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·陨石放置器", "id": "ae2:debug_meteorite_placer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·幽灵网络节点", "block": {"crop": false}, "id": "ae2:debug_phantom_node"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "调试·复制卡", "id": "ae2:debug_replicator_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "致密能源元件", "block": {"crop": false}, "id": "ae2:dense_energy_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME驱动器", "block": {"crop": false}, "id": "ae2:drive"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影粉", "id": "ae2:ender_dust"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "能源接收器", "block": {"crop": false}, "id": "ae2:energy_acceptor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "能源卡", "id": "ae2:energy_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "能源元件", "block": {"crop": false}, "id": "ae2:energy_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME能源发信器", "id": "ae2:energy_level_emitter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程处理器", "id": "ae2:engineering_processor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程压印模板", "id": "ae2:engineering_processor_press"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熵变机械臂", "id": "ae2:entropy_manipulator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "均分卡", "id": "ae2:equal_distribution_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME输出总线", "id": "ae2:export_bus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "线缆伪装板", "id": "ae2:facade"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "能源P2P通道", "id": "ae2:fe_p2p_tunnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "有瑕的赛特斯石英母岩", "block": {"crop": false}, "id": "ae2:flawed_budding_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "无瑕的赛特斯石英母岩", "block": {"crop": false}, "id": "ae2:flawless_budding_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME流体元件外壳", "id": "ae2:fluid_cell_housing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体P2P通道", "id": "ae2:fluid_p2p_tunnel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "16k ME流体存储元件", "id": "ae2:fluid_storage_cell_16k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "1k ME流体存储元件", "id": "ae2:fluid_storage_cell_1k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "256k ME流体存储元件", "id": "ae2:fluid_storage_cell_256k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "4k ME流体存储元件", "id": "ae2:fluid_storage_cell_4k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "64k ME流体存储元件", "id": "ae2:fluid_storage_cell_64k"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "福鲁伊克斯斧", "id": "ae2:fluix_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯块", "block": {"crop": false}, "id": "ae2:fluix_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯色ME包层线缆", "id": "ae2:fluix_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯色ME致密包层线缆", "id": "ae2:fluix_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯水晶", "id": "ae2:fluix_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯粉", "id": "ae2:fluix_dust"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯色ME玻璃线缆", "id": "ae2:fluix_glass_cable"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "福鲁伊克斯锄", "id": "ae2:fluix_hoe", "toolType": "hoe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯珍珠", "id": "ae2:fluix_pearl"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "福鲁伊克斯镐", "id": "ae2:fluix_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "福鲁伊克斯锹", "id": "ae2:fluix_shovel", "toolType": "shovel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯台阶", "block": {"crop": false}, "id": "ae2:fluix_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯色ME智能线缆", "id": "ae2:fluix_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯色ME致密线缆", "id": "ae2:fluix_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯楼梯", "block": {"crop": false}, "id": "ae2:fluix_stairs"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "福鲁伊克斯剑", "id": "ae2:fluix_sword", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "ae2:fluix_upgrade_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "福鲁伊克斯墙", "block": {"crop": false}, "id": "ae2:fluix_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "成型核心", "id": "ae2:formation_core"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME成型面板", "id": "ae2:formation_plane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "模糊卡", "id": "ae2:fuzzy_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色ME包层线缆", "id": "ae2:gray_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色ME致密包层线缆", "id": "ae2:gray_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色ME玻璃线缆", "id": "ae2:gray_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色光通染色球", "id": "ae2:gray_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色染色球", "id": "ae2:gray_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色ME智能线缆", "id": "ae2:gray_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色ME致密线缆", "id": "ae2:gray_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色ME包层线缆", "id": "ae2:green_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色ME致密包层线缆", "id": "ae2:green_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色ME玻璃线缆", "id": "ae2:green_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色光通染色球", "id": "ae2:green_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色染色球", "id": "ae2:green_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色ME智能线缆", "id": "ae2:green_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色ME致密线缆", "id": "ae2:green_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "催生器", "block": {"crop": false}, "id": "ae2:growth_accelerator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "指南", "id": "ae2:guide"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME输入总线", "id": "ae2:import_bus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "压印器", "block": {"crop": false}, "id": "ae2:inscriber"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME接口", "block": {"crop": false}, "id": "ae2:interface"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME反相触发总线", "id": "ae2:inverted_toggle_bus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "反相卡", "id": "ae2:inverter_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME IO端口", "block": {"crop": false}, "id": "ae2:io_port"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME物品元件外壳", "id": "ae2:item_cell_housing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物品P2P通道", "id": "ae2:item_p2p_tunnel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "16k ME物品存储元件", "id": "ae2:item_storage_cell_16k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "1k ME物品存储元件", "id": "ae2:item_storage_cell_1k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "256k ME物品存储元件", "id": "ae2:item_storage_cell_256k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "4k ME物品存储元件", "id": "ae2:item_storage_cell_4k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "64k ME物品存储元件", "id": "ae2:item_storage_cell_64k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型赛特斯石英芽", "block": {"crop": false}, "id": "ae2:large_quartz_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME标准发信器", "id": "ae2:level_emitter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色ME包层线缆", "id": "ae2:light_blue_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色ME致密包层线缆", "id": "ae2:light_blue_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色ME玻璃线缆", "id": "ae2:light_blue_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色光通染色球", "id": "ae2:light_blue_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色染色球", "id": "ae2:light_blue_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色ME智能线缆", "id": "ae2:light_blue_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浅蓝色ME致密线缆", "id": "ae2:light_blue_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光亮探测器", "block": {"crop": false}, "id": "ae2:light_detector"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME包层线缆(淡灰色)", "id": "ae2:light_gray_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME致密包层线缆(淡灰色)", "id": "ae2:light_gray_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME玻璃线缆(淡灰色)", "id": "ae2:light_gray_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光通染色球(淡灰色)", "id": "ae2:light_gray_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "染色球(淡灰色)", "id": "ae2:light_gray_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME智能线缆(淡灰色)", "id": "ae2:light_gray_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME致密线缆(淡灰色)", "id": "ae2:light_gray_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光P2P通道", "id": "ae2:light_p2p_tunnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色ME包层线缆", "id": "ae2:lime_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色ME致密包层线缆", "id": "ae2:lime_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色ME玻璃线缆", "id": "ae2:lime_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色光通染色球", "id": "ae2:lime_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色染色球", "id": "ae2:lime_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色ME智能线缆", "id": "ae2:lime_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色ME致密线缆", "id": "ae2:lime_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "逻辑处理器", "id": "ae2:logic_processor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "逻辑压印模板", "id": "ae2:logic_processor_press"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色ME包层线缆", "id": "ae2:magenta_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色ME致密包层线缆", "id": "ae2:magenta_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色ME玻璃线缆", "id": "ae2:magenta_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色光通染色球", "id": "ae2:magenta_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色染色球", "id": "ae2:magenta_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色ME智能线缆", "id": "ae2:magenta_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色ME致密线缆", "id": "ae2:magenta_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矩阵框架", "block": {"crop": false}, "id": "ae2:matrix_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物质球", "id": "ae2:matter_ball"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "物质炮", "id": "ae2:matter_cannon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME P2P通道", "id": "ae2:me_p2p_tunnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中型赛特斯石英芽", "block": {"crop": false}, "id": "ae2:medium_quartz_bud"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "内存卡", "id": "ae2:memory_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石罗盘", "id": "ae2:meteorite_compass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "分子装配室", "block": {"crop": false}, "id": "ae2:molecular_assembler"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "亮色照明面板", "id": "ae2:monitor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "神秘方块", "block": {"crop": false}, "id": "ae2:mysterious_cube"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "名称压印模板", "id": "ae2:name_press"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "下界石英斧", "id": "ae2:nether_quartz_axe", "toolType": "axe"}, {"maxStackSize": 1, "maxDamage": 50, "localized": "下界石英切割刀", "id": "ae2:nether_quartz_cutting_knife"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "下界石英锄", "id": "ae2:nether_quartz_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "下界石英镐", "id": "ae2:nether_quartz_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "下界石英锹", "id": "ae2:nether_quartz_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "下界石英剑", "id": "ae2:nether_quartz_sword", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "下界石英扳手", "id": "ae2:nether_quartz_wrench"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "网络工具", "id": "ae2:network_tool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "没那么神秘的方块", "block": {"crop": false}, "id": "ae2:not_so_mysterious_cube"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色ME包层线缆", "id": "ae2:orange_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色ME致密包层线缆", "id": "ae2:orange_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色ME玻璃线缆", "id": "ae2:orange_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色光通染色球", "id": "ae2:orange_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色染色球", "id": "ae2:orange_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色ME智能线缆", "id": "ae2:orange_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色ME致密线缆", "id": "ae2:orange_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "染色球", "block": {"crop": false}, "id": "ae2:paint"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME样板管理终端", "id": "ae2:pattern_access_terminal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME样板编码终端", "id": "ae2:pattern_encoding_terminal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME样板供应器", "block": {"crop": false}, "id": "ae2:pattern_provider"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色ME包层线缆", "id": "ae2:pink_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色ME致密包层线缆", "id": "ae2:pink_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色ME玻璃线缆", "id": "ae2:pink_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色光通染色球", "id": "ae2:pink_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色染色球", "id": "ae2:pink_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色ME智能线缆", "id": "ae2:pink_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色ME致密线缆", "id": "ae2:pink_smart_dense_cable"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "16k便携流体元件", "id": "ae2:portable_fluid_cell_16k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "1k便携流体元件", "id": "ae2:portable_fluid_cell_1k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "256k便携流体元件", "id": "ae2:portable_fluid_cell_256k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "4k便携流体元件", "id": "ae2:portable_fluid_cell_4k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "64k便携流体元件", "id": "ae2:portable_fluid_cell_64k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "16k便携物品元件", "id": "ae2:portable_item_cell_16k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "1k便携物品元件", "id": "ae2:portable_item_cell_1k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "256k便携物品元件", "id": "ae2:portable_item_cell_256k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "4k便携物品元件", "id": "ae2:portable_item_cell_4k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "64k便携物品元件", "id": "ae2:portable_item_cell_64k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "运算电路板", "id": "ae2:printed_calculation_processor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程电路板", "id": "ae2:printed_engineering_processor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "逻辑电路板", "id": "ae2:printed_logic_processor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "硅板", "id": "ae2:printed_silicon"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "处理样板", "id": "ae2:processing_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色ME包层线缆", "id": "ae2:purple_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色ME致密包层线缆", "id": "ae2:purple_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色ME玻璃线缆", "id": "ae2:purple_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色光通染色球", "id": "ae2:purple_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色染色球", "id": "ae2:purple_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色ME智能线缆", "id": "ae2:purple_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色ME致密线缆", "id": "ae2:purple_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "量子缠绕态奇点", "id": "ae2:quantum_entangled_singularity"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME量子链接仓", "block": {"crop": false}, "id": "ae2:quantum_link"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME量子环", "block": {"crop": false}, "id": "ae2:quantum_ring"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英块", "block": {"crop": false}, "id": "ae2:quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英砖台阶", "block": {"crop": false}, "id": "ae2:quartz_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英砖楼梯", "block": {"crop": false}, "id": "ae2:quartz_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英砖墙", "block": {"crop": false}, "id": "ae2:quartz_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英砖", "block": {"crop": false}, "id": "ae2:quartz_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英簇", "block": {"crop": false}, "id": "ae2:quartz_cluster"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英纤维", "id": "ae2:quartz_fiber"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "充能石英火把", "block": {"crop": false}, "id": "ae2:quartz_fixture"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英玻璃", "block": {"crop": false}, "id": "ae2:quartz_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英柱", "block": {"crop": false}, "id": "ae2:quartz_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英柱台阶", "block": {"crop": false}, "id": "ae2:quartz_pillar_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英柱楼梯", "block": {"crop": false}, "id": "ae2:quartz_pillar_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英柱墙", "block": {"crop": false}, "id": "ae2:quartz_pillar_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英台阶", "block": {"crop": false}, "id": "ae2:quartz_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英楼梯", "block": {"crop": false}, "id": "ae2:quartz_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "聚能石英玻璃", "block": {"crop": false}, "id": "ae2:quartz_vibrant_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赛特斯石英墙", "block": {"crop": false}, "id": "ae2:quartz_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色ME包层线缆", "id": "ae2:red_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色ME致密包层线缆", "id": "ae2:red_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色ME玻璃线缆", "id": "ae2:red_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色光通染色球", "id": "ae2:red_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色染色球", "id": "ae2:red_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色ME智能线缆", "id": "ae2:red_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色ME致密线缆", "id": "ae2:red_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石卡", "id": "ae2:redstone_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石P2P通道", "id": "ae2:redstone_p2p_tunnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "照明面板", "id": "ae2:semi_dark_monitor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "硅", "id": "ae2:silicon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "硅压印模板", "id": "ae2:silicon_press"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "奇点", "id": "ae2:singularity"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石粉", "id": "ae2:sky_dust"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石", "block": {"crop": false}, "id": "ae2:sky_stone_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖", "block": {"crop": false}, "id": "ae2:sky_stone_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖台阶", "block": {"crop": false}, "id": "ae2:sky_stone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖楼梯", "block": {"crop": false}, "id": "ae2:sky_stone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖墙", "block": {"crop": false}, "id": "ae2:sky_stone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石箱子", "block": {"crop": false}, "id": "ae2:sky_stone_chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石台阶", "block": {"crop": false}, "id": "ae2:sky_stone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖块", "block": {"crop": false}, "id": "ae2:sky_stone_small_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖块台阶", "block": {"crop": false}, "id": "ae2:sky_stone_small_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖块楼梯", "block": {"crop": false}, "id": "ae2:sky_stone_small_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石砖块墙", "block": {"crop": false}, "id": "ae2:sky_stone_small_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石楼梯", "block": {"crop": false}, "id": "ae2:sky_stone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石储罐", "block": {"crop": false}, "id": "ae2:sky_stone_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石墙", "block": {"crop": false}, "id": "ae2:sky_stone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型赛特斯石英芽", "block": {"crop": false}, "id": "ae2:small_quartz_bud"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "锻造台样板", "id": "ae2:smithing_table_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑赛特斯石英块", "block": {"crop": false}, "id": "ae2:smooth_quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑赛特斯石英台阶", "block": {"crop": false}, "id": "ae2:smooth_quartz_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑赛特斯石英楼梯", "block": {"crop": false}, "id": "ae2:smooth_quartz_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑赛特斯石英墙", "block": {"crop": false}, "id": "ae2:smooth_quartz_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石块", "block": {"crop": false}, "id": "ae2:smooth_sky_stone_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石块箱子", "block": {"crop": false}, "id": "ae2:smooth_sky_stone_chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石块台阶", "block": {"crop": false}, "id": "ae2:smooth_sky_stone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石块楼梯", "block": {"crop": false}, "id": "ae2:smooth_sky_stone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陨石块墙", "block": {"crop": false}, "id": "ae2:smooth_sky_stone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空间锚", "block": {"crop": false}, "id": "ae2:spatial_anchor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "128³空间组件", "id": "ae2:spatial_cell_component_128"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "16³空间组件", "id": "ae2:spatial_cell_component_16"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "2³空间组件", "id": "ae2:spatial_cell_component_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空间IO端口", "block": {"crop": false}, "id": "ae2:spatial_io_port"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空间塔", "block": {"crop": false}, "id": "ae2:spatial_pylon"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "128³空间存储元件", "id": "ae2:spatial_storage_cell_128"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "16³空间存储元件", "id": "ae2:spatial_storage_cell_16"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "2³空间存储元件", "id": "ae2:spatial_storage_cell_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "加速卡", "id": "ae2:speed_card"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "切石机样板", "id": "ae2:stonecutting_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME存储总线", "id": "ae2:storage_bus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME存储监控器", "id": "ae2:storage_monitor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME终端", "id": "ae2:terminal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "迷你TNT", "block": {"crop": false}, "id": "ae2:tiny_tnt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME触发总线", "id": "ae2:toggle_bus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "谐振仓", "block": {"crop": false}, "id": "ae2:vibration_chamber"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "显示元件", "id": "ae2:view_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "溢出销毁卡", "id": "ae2:void_card"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色ME包层线缆", "id": "ae2:white_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色ME致密包层线缆", "id": "ae2:white_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色ME玻璃线缆", "id": "ae2:white_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色光通染色球", "id": "ae2:white_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色染色球", "id": "ae2:white_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色ME智能线缆", "id": "ae2:white_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色ME致密线缆", "id": "ae2:white_smart_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME无线访问点", "block": {"crop": false}, "id": "ae2:wireless_access_point"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "无线信号增幅器", "id": "ae2:wireless_booster"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "无线合成终端", "id": "ae2:wireless_crafting_terminal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "无线接收器", "id": "ae2:wireless_receiver"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "无线终端", "id": "ae2:wireless_terminal"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Wrapped <PERSON><PERSON>", "id": "ae2:wrapped_generic_stack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色ME包层线缆", "id": "ae2:yellow_covered_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色ME致密包层线缆", "id": "ae2:yellow_covered_dense_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色ME玻璃线缆", "id": "ae2:yellow_glass_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色光通染色球", "id": "ae2:yellow_lumen_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色染色球", "id": "ae2:yellow_paint_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色ME智能线缆", "id": "ae2:yellow_smart_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色ME致密线缆", "id": "ae2:yellow_smart_dense_cable"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "16k ME深度物品存储磁盘驱动器", "id": "ae2things:disk_drive_16k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "1k ME深度物品存储磁盘驱动器", "id": "ae2things:disk_drive_1k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "256k ME深度物品存储磁盘驱动器", "id": "ae2things:disk_drive_256k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "4k ME深度物品存储磁盘驱动器", "id": "ae2things:disk_drive_4k"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "64k ME深度物品存储磁盘驱动器", "id": "ae2things:disk_drive_64k"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "ME深度物品存储磁盘外壳", "id": "ae2things:disk_housing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Acid", "id": "aerlunerpg:acid"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Rotting hand", "id": "aerlunerpg:arm", "food": {"saturation": 1.1, "nutrition": 11, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bearcandy Spawn Egg", "id": "aerlunerpg:bearcandy_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bearcandy Spawn Egg", "id": "aerlunerpg:bearcandy_3_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bearcandy Spawn Egg", "id": "aerlunerpg:bearcandy_4_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bearcandy Spawn Egg", "id": "aerlunerpg:bearcandy_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>", "id": "aerlunerpg:bearcandyitem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON> tame", "id": "aerlunerpg:bearcandytame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bedbug Spawn Egg", "id": "aerlunerpg:bedbug_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bite ", "block": {"crop": false}, "id": "aerlunerpg:bite_1"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Bite", "id": "aerlunerpg:biteitem_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Boar Spawn Egg", "id": "aerlunerpg:boar_3_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Boar Spawn Egg", "id": "aerlunerpg:boar_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Boar big Spawn Egg", "id": "aerlunerpg:boarbig_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "White Rage - miniboss Spawn Egg", "id": "aerlunerpg:boarboss_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "The Head of White Fury", "id": "aerlunerpg:boarhead"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Book guide", "id": "aerlunerpg:bookgui"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Book of Frost Spells", "id": "aerlunerpg:bookice_0"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Book of Frost Spells", "id": "aerlunerpg:bookice_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:bushsmall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Foreign Intruder Spawn Egg", "id": "aerlunerpg:butterfly_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Butterfly Spawn Egg", "id": "aerlunerpg:buttermini_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Butterfly Spawn Egg", "id": "aerlunerpg:buttermini_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:candyctickbig"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:candysblock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:candysblock_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:candysp"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:candysp_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:candysp_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Candystick", "block": {"crop": false}, "id": "aerlunerpg:candystick"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "§cCandy stick", "id": "aerlunerpg:candystick_1", "food": {"saturation": 2.8, "nutrition": 16, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Cannon Spawn Egg", "id": "aerlunerpg:cannongnome_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Capacitor of souls", "block": {"crop": false}, "id": "aerlunerpg:capacitorofsouls"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Capacitor of souls", "block": {"crop": false}, "id": "aerlunerpg:capacitorsouls"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Caramel", "id": "aerlunerpg:caramel", "food": {"saturation": 0.1, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "caramel", "block": {"crop": false}, "id": "aerlunerpg:caramelore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Foreign Intruder Spawn Egg", "id": "aerlunerpg:centipede_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chest", "block": {"crop": false}, "id": "aerlunerpg:chestbig"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chest 2", "block": {"crop": false}, "id": "aerlunerpg:chestbig_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chest", "block": {"crop": false}, "id": "aerlunerpg:chestbigwood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chest", "block": {"crop": false}, "id": "aerlunerpg:chestcandy"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "Chip ", "id": "aerlunerpg:chip_1"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "Chip ", "id": "aerlunerpg:chip_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:chocolate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:chocolates"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Christmas tree   Spawn Egg", "id": "aerlunerpg:christmastree_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "id": "aerlunerpg:co<PERSON>ie"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "Cookie recipe", "id": "aerlunerpg:coockierecipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>ie tame", "id": "aerlunerpg:coockietame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Cookie Spawn Egg", "id": "aerlunerpg:cookie_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Cookie Spawn Egg", "id": "aerlunerpg:cookie_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Core of the void", "id": "aerlunerpg:coreofthevoid"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Creator", "block": {"crop": false}, "id": "aerlunerpg:creatorvoid"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crowd Spawn Egg", "id": "aerlunerpg:crowd_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crowd block", "block": {"crop": false}, "id": "aerlunerpg:crowdblock"}, {"maxStackSize": 1, "maxDamage": 64, "localized": "Crowdd Portal Igniter", "id": "aerlunerpg:crowdd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crystalsattack", "block": {"crop": false}, "id": "aerlunerpg:crystalsattack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crystals", "block": {"crop": false}, "id": "aerlunerpg:crystalsmithg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crystals", "block": {"crop": false}, "id": "aerlunerpg:crystalsmithril"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crystals of void", "block": {"crop": false}, "id": "aerlunerpg:crystalsvoid"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "C<PERSON><PERSON>hu - Conqueror of Worlds Spawn Egg", "id": "aerlunerpg:cthulhu_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Ctp", "id": "aerlunerpg:ctp"}, {"maxStackSize": 1, "maxDamage": 400, "localized": "Cudgel", "id": "aerlunerpg:cudgel", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 400, "localized": "Cudgel ragemet", "id": "aerlunerpg:cudgelragemetsmall", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Deer Spawn Egg", "id": "aerlunerpg:deerbig_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Deer Spawn Egg", "id": "aerlunerpg:deerbig_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Deer Spawn Egg", "id": "aerlunerpg:deerevil_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Demonic Head", "id": "aerlunerpg:demongun"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Grass", "block": {"crop": false}, "id": "aerlunerpg:dirt"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Disposable bait", "id": "aerlunerpg:disposablebait"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Donut Spawn Egg", "id": "aerlunerpg:donut_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Vacuum dust", "id": "aerlunerpg:dust"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Energy of void", "id": "aerlunerpg:energybigdark"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Grain of energy", "id": "aerlunerpg:energydark"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Ent Spawn Egg", "id": "aerlunerpg:ent_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Entblock", "block": {"crop": false}, "id": "aerlunerpg:entblock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "gnome Spawn Egg", "id": "aerlunerpg:evilgnome_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "gnome Spawn Egg", "id": "aerlunerpg:evilgnome_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Eye Spawn Egg", "id": "aerlunerpg:eyebeatle_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Generous gift", "id": "aerlunerpg:filgist"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Fire met", "id": "aerlunerpg:firemetsmall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "unusual meat", "id": "aerlunerpg:foodalien_1", "food": {"saturation": 0.3, "nutrition": 4, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "unusual meat", "id": "aerlunerpg:foodalien_2", "food": {"saturation": 0.2, "nutrition": 5, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "unusual meat", "id": "aerlunerpg:foodalien_3", "food": {"saturation": 0.2, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 104, "localized": "wanderer's clothes Boots", "id": "aerlunerpg:furarmor_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 128, "localized": "wanderer's clothes Chestplate", "id": "aerlunerpg:furarmor_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 120, "localized": "wanderer's clothes Leggings", "id": "aerlunerpg:furarmor_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Small furnace ", "block": {"crop": false}, "id": "aerlunerpg:furnacesmall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Furnace", "block": {"crop": false}, "id": "aerlunerpg:furnacev"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Ghost Spawn Egg", "id": "aerlunerpg:ghost_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Moderate gift", "id": "aerlunerpg:giftblue"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Modest gift", "id": "aerlunerpg:giftsmal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:gingerblock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:gingerblock_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:gingerblock_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:gingerblocks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Gingerbreadman Spawn Egg", "id": "aerlunerpg:gingerbreadman_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON><PERSON>", "id": "aerlunerpg:gingerbreadmanitem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Gingerbreadman tame", "id": "aerlunerpg:gingerbreadmanitemtame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON><PERSON>", "id": "aerlunerpg:gingerbre<PERSON>mann", "food": {"saturation": 0.2, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "gingerbreadman small Spawn Egg", "id": "aerlunerpg:gingirmanevil_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "gingerbreadman small Spawn Egg", "id": "aerlunerpg:gingirmanevilred_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>'s head", "id": "aerlunerpg:gnomehead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Gnome home", "block": {"crop": false}, "id": "aerlunerpg:gnomehome"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "House goblin Spawn Egg", "id": "aerlunerpg:goblin_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "House goblin Spawn Egg", "id": "aerlunerpg:gobliny_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "The Mithril Golem Spawn Egg", "id": "aerlunerpg:golem_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Grass", "block": {"crop": false}, "id": "aerlunerpg:grasscold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:grasssnow"}, {"maxStackSize": 1, "maxDamage": 11, "localized": "New Year's hat", "id": "aerlunerpg:hatcc_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Hole", "block": {"crop": false}, "id": "aerlunerpg:holespawn_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "House on feet Spawn Egg", "id": "aerlunerpg:house_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Icebookd", "id": "aerlunerpg:icebookd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Ice star", "id": "aerlunerpg:icecrystal"}, {"maxStackSize": 1, "maxDamage": 64, "localized": "Iced Portal Igniter", "id": "aerlunerpg:iced"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Icefireitem", "id": "aerlunerpg:icefireitem"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "ice star", "id": "aerlunerpg:icemet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Crystals", "block": {"crop": false}, "id": "aerlunerpg:icerion"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Ice Rod", "id": "aerlunerpg:icerod"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Ice Rod", "id": "aerlunerpg:icerodfiremet"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Ice Rod", "id": "aerlunerpg:icerodmagicmet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Icerodparitem", "id": "aerlunerpg:icerodparitem"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Ice sword", "id": "aerlunerpg:icesword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Laser Spawn Egg", "id": "aerlunerpg:laser_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Leather", "id": "aerlunerpg:leather"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Life energy", "id": "aerlunerpg:lifeen"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "§bPiercing lightning", "id": "aerlunerpg:lightning", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Luck 1", "id": "aerlunerpg:luck_1"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "Small Magic met ", "id": "aerlunerpg:magicmetsmall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Magicparitem", "id": "aerlunerpg:magicparitem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Mine", "block": {"crop": false}, "id": "aerlunerpg:mine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Mithril shard", "id": "aerlunerpg:mithril"}, {"maxStackSize": 1, "maxDamage": 1100, "localized": "Mith<PERSON> drill", "id": "aerlunerpg:mithrildrill", "toolType": "pickaxe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Mushroombrothblock", "block": {"crop": false}, "id": "aerlunerpg:mushroombrothblock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Mushroom broth", "id": "aerlunerpg:mushroombrothitem", "food": {"saturation": 0.2, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 11, "localized": "New Year's hat", "id": "aerlunerpg:new_yearhat_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Oak", "block": {"crop": false}, "id": "aerlunerpg:oak"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Oak", "block": {"crop": false}, "id": "aerlunerpg:oak_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Oak", "block": {"crop": false}, "id": "aerlunerpg:oak_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Oak", "block": {"crop": false}, "id": "aerlunerpg:oak_4"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Oak", "block": {"crop": false}, "id": "aerlunerpg:oak_5"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Oldman", "block": {"crop": false}, "id": "aerlunerpg:oldmantrade"}, {"maxStackSize": 1, "maxDamage": 11, "localized": "<PERSON><PERSON><PERSON><PERSON>", "id": "aerlunerpg:owlarmor_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Owlarmorblock", "block": {"crop": false}, "id": "aerlunerpg:owlarmorblock"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Parasite", "id": "aerlunerpg:parasite"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Piercing fear", "id": "aerlunerpg:piercing<PERSON>ar"}, {"maxStackSize": 1, "maxDamage": 11, "localized": "<PERSON><PERSON>", "id": "aerlunerpg:pipe_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Pipeblock", "block": {"crop": false}, "id": "aerlunerpg:pipeblock"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Magic crystal", "id": "aerlunerpg:portalitem"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Candy pot", "id": "aerlunerpg:potcandy"}, {"maxStackSize": 1, "maxDamage": 121, "localized": "§5Rotten mask", "id": "aerlunerpg:pumphead_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Rabbit Spawn Egg", "id": "aerlunerpg:rabbit_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Rabbit hole", "block": {"crop": false}, "id": "aerlunerpg:rabbithole"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Rabbit mount Spawn Egg", "id": "aerlunerpg:rabbitmount_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>", "id": "aerlunerpg:rabpetitem"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Rage met small", "id": "aerlunerpg:ragemetsmall"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "Candy bear Recipe", "id": "aerlunerpg:recipebear"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "Gingerbreadman Recipe", "id": "aerlunerpg:recipeman"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Robot gnome Spawn Egg", "id": "aerlunerpg:robotgnome_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Scroll §sIce Fire", "id": "aerlunerpg:scrollfireice"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Scroll Ice shield", "id": "aerlunerpg:scrollicsh"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Shockwave", "block": {"crop": false}, "id": "aerlunerpg:shockwave"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Shoggots Spawn Egg", "id": "aerlunerpg:shoggotsmall_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Mini battery", "block": {"crop": false}, "id": "aerlunerpg:smallbat"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Small battery", "id": "aerlunerpg:smallbatt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Smal lbeatle Spawn Egg", "id": "aerlunerpg:smallbeatle_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Smallbeatleitem", "id": "aerlunerpg:smallbeatleitem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:smalltreeblock_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:smalltreeblock_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Grass", "block": {"crop": false}, "id": "aerlunerpg:snow_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Snowdrop", "block": {"crop": false}, "id": "aerlunerpg:snowdrop"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Snowman Spawn Egg", "id": "aerlunerpg:snowman_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Snowman Spawn Egg", "id": "aerlunerpg:snowman_3_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Snowman Spawn Egg", "id": "aerlunerpg:snowman_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Voracious rodent", "id": "aerlunerpg:squirrel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Squirrelblock", "block": {"crop": false}, "id": "aerlunerpg:squirrelblock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Steve <PERSON>wn <PERSON>", "id": "aerlunerpg:steve_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:stonesmall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Strange candy", "id": "aerlunerpg:strangecandy", "food": {"saturation": 0.3, "nutrition": 4, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:stump_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Stump Spawn Egg", "id": "aerlunerpg:stump_2_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Stump Spawn Egg", "id": "aerlunerpg:stump_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:stunning"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Sugar", "block": {"crop": false}, "id": "aerlunerpg:sugar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "unusual soup", "id": "aerlunerpg:sup", "food": {"saturation": 0.5, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Test Spawn Egg", "id": "aerlunerpg:test_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:toilet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>", "block": {"crop": false}, "id": "aerlunerpg:toilet_2"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Strange mirror ", "id": "aerlunerpg:tp"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>", "block": {"crop": false}, "id": "aerlunerpg:treesmall"}, {"maxStackSize": 6, "maxDamage": 0, "localized": "Small trophy - fangs", "id": "aerlunerpg:trophyboar"}, {"maxStackSize": 6, "maxDamage": 0, "localized": "Small trophy - mad eyes", "id": "aerlunerpg:trophyeyes"}, {"maxStackSize": 6, "maxDamage": 0, "localized": "Small trophy - rabbit's paw", "id": "aerlunerpg:trophyrabbit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "void", "id": "aerlunerpg:tryrtyry"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "key", "id": "aerlunerpg:voidkey"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Mysterious hards", "id": "aerlunerpg:voidshards"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Warm Spawn Egg", "id": "aerlunerpg:warm_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 71, "localized": "Wood shield", "id": "aerlunerpg:woodshield", "toolType": "shield"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Zombieghost Spawn Egg", "id": "aerlunerpg:zombieghost_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "1024核并行处理单元", "block": {"crop": false}, "id": "bigger_ae2:1024_core_crafting_accelerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "16核并行处理单元", "block": {"crop": false}, "id": "bigger_ae2:16_core_crafting_accelerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "256核并行处理单元", "block": {"crop": false}, "id": "bigger_ae2:256_core_crafting_accelerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "4核并行处理单元", "block": {"crop": false}, "id": "bigger_ae2:4_core_crafting_accelerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "64核并行处理单元", "block": {"crop": false}, "id": "bigger_ae2:64_core_crafting_accelerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "进阶流体元件外壳", "id": "bigger_ae2:advanced_fluid_cell_housing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "进阶物品元件外壳", "id": "bigger_ae2:advanced_item_cell_housing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "数字奇点存储组件", "id": "bigger_ae2:digital_singularity_cell_component"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "数字奇点流体存储元件", "id": "bigger_ae2:digital_singularity_fluid_storage_cell"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "数字奇点物品存储元件", "id": "bigger_ae2:digital_singularity_item_storage_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "量子存储组件", "id": "bigger_ae2:quantum_cell_component"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "量子流体存储元件", "id": "bigger_ae2:quantum_fluid_storage_cell"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "量子物品存储元件", "id": "bigger_ae2:quantum_item_storage_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深渊之眼", "id": "cataclysm:abyss_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深渊之卵", "block": {"crop": false}, "id": "cataclysm:abyssal_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深渊祭品", "id": "cataclysm:abyssal_sacrifice"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深渊祭坛", "block": {"crop": false}, "id": "cataclysm:altar_of_abyss"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶祭坛", "block": {"crop": false}, "id": "cataclysm:altar_of_amethyst"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰祭坛", "block": {"crop": false}, "id": "cataclysm:altar_of_fire"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空祭坛", "block": {"crop": false}, "id": "cataclysm:altar_of_void"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶蟹肉", "id": "cataclysm:amethyst_crab_meat", "food": {"saturation": 1.2, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶蟹壳", "id": "cataclysm:amethyst_crab_shell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶巨蟹刷怪蛋", "id": "cataclysm:amethyst_crab_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "远古金属块", "block": {"crop": false}, "id": "cataclysm:ancient_metal_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "远古金属锭", "id": "cataclysm:ancient_metal_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "远古金属粒", "id": "cataclysm:ancient_metal_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "远古遗魂刷怪蛋", "id": "cataclysm:ancient_remnant_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 1800, "localized": "远古之矛", "id": "cataclysm:ancient_spear"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "武弁的头", "block": {"crop": false}, "id": "cataclysm:aptrgangr_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "冥行武弁刷怪蛋", "id": "cataclysm:aptrgangr_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "仪式匕首", "id": "cataclysm:athame", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "黑钢斧", "id": "cataclysm:black_steel_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑钢块", "block": {"crop": false}, "id": "cataclysm:black_steel_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑钢栅栏", "block": {"crop": false}, "id": "cataclysm:black_steel_fence"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "黑钢锄", "id": "cataclysm:black_steel_hoe", "toolType": "hoe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑钢锭", "id": "cataclysm:black_steel_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑钢粒", "id": "cataclysm:black_steel_nugget"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "黑钢镐", "id": "cataclysm:black_steel_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "黑钢锹", "id": "cataclysm:black_steel_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "黑钢剑", "id": "cataclysm:black_steel_sword", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 840, "localized": "黑钢圆盾", "id": "cataclysm:black_steel_targe", "toolType": "shield"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑钢墙", "block": {"crop": false}, "id": "cataclysm:black_steel_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑石柱", "block": {"crop": false}, "id": "cataclysm:blackstone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "item.cataclysm.blazing_bone", "id": "cataclysm:blazing_bone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "受祝福的紫水晶蟹肉", "id": "cataclysm:blessed_amethyst_crab_meat", "food": {"saturation": 1.2, "nutrition": 6, "alwaysEdible": true}}, {"maxStackSize": 1, "maxDamage": 480, "localized": "花岩肩甲", "id": "cataclysm:bloom_stone_pauldrons", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 560, "localized": "骨蜥胸甲", "id": "cataclysm:bone_reptile_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 385, "localized": "骨蜥头盔", "id": "cataclysm:bone_reptile_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "火焰壁垒", "id": "cataclysm:bulwark_of_the_flame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "炽燃余烬", "id": "cataclysm:burning_ashes"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "缚魂之链", "id": "cataclysm:chain_of_soul_binding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹末地石砖", "block": {"crop": false}, "id": "cataclysm:chiseled_end_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹黑曜石砖块", "block": {"crop": false}, "id": "cataclysm:chiseled_obsidian_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹紫珀块", "block": {"crop": false}, "id": "cataclysm:chiseled_purpur_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹石砖柱", "block": {"crop": false}, "id": "cataclysm:chiseled_stone_brick_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂栅栏", "block": {"crop": false}, "id": "cataclysm:chorus_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂木板", "block": {"crop": false}, "id": "cataclysm:chorus_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂台阶", "block": {"crop": false}, "id": "cataclysm:chorus_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂楼梯", "block": {"crop": false}, "id": "cataclysm:chorus_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂茎", "block": {"crop": false}, "id": "cataclysm:chorus_stem"}, {"maxStackSize": 1, "maxDamage": 160, "localized": "珊瑚钺", "id": "cataclysm:<PERSON>_bar<PERSON>he"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "珊瑚碎块", "id": "cataclysm:coral_chunk"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "珊瑚傀儡刷怪蛋", "id": "cataclysm:coral_golem_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 110, "localized": "珊瑚长矛", "id": "cataclysm:coral_spear"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "珊瑚巨像刷怪蛋", "id": "cataclysm:coralssus_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "晶化珊瑚", "id": "cataclysm:crystallized_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "晶化珊瑚碎片", "id": "cataclysm:crystallized_coral_fragments"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "咒魂弓", "id": "cataclysm:cursed_bow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诅咒之眼", "id": "cataclysm:cursed_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "被诅咒的墓碑", "block": {"crop": false}, "id": "cataclysm:cursed_tombstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "咒魂块", "block": {"crop": false}, "id": "cataclysm:cursium_block"}, {"maxStackSize": 1, "maxDamage": 585, "localized": "咒魂靴子", "id": "cataclysm:cursium_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 720, "localized": "咒魂胸甲", "id": "cataclysm:cursium_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 495, "localized": "咒魂头盔", "id": "cataclysm:cursium_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "咒魂锭", "id": "cataclysm:cursium_ingot"}, {"maxStackSize": 1, "maxDamage": 675, "localized": "咒魂护腿", "id": "cataclysm:cursium_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "cataclysm:cursium_upgrade_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "渊灵垂钓者刷怪蛋", "id": "cataclysm:deepling_angler_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "渊灵蛮兵刷怪蛋", "id": "cataclysm:deepling_brute_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "渊灵祭司刷怪蛋", "id": "cataclysm:deepling_priest_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "渊灵刷怪蛋", "id": "cataclysm:deepling_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "渊灵术士刷怪蛋", "id": "cataclysm:deepling_warlock_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "沙漠之眼", "id": "cataclysm:desert_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "封印之门", "block": {"crop": false}, "id": "cataclysm:door_of_seal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "魂尸的头", "block": {"crop": false}, "id": "cataclysm:draugr_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "再行魂尸刷怪蛋", "id": "cataclysm:draugr_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "地牢方块", "block": {"crop": false}, "id": "cataclysm:dungeon_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "将熄余烬", "id": "cataclysm:dying_ember"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英魂尸刷怪蛋", "id": "cataclysm:elite_draugr_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电磁脉冲发射器", "block": {"crop": false}, "id": "cataclysm:emp"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石柱", "block": {"crop": false}, "id": "cataclysm:end_stone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石陷阱方块", "block": {"crop": false}, "id": "cataclysm:end_stone_teleport_trap_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影傀儡刷怪蛋", "id": "cataclysm:ender_golem_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影守卫刷怪蛋", "id": "cataclysm:ender_guardian_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影合金块", "block": {"crop": false}, "id": "cataclysm:enderite_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影合金锭", "id": "cataclysm:enderite_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影甲虫刷怪蛋", "id": "cataclysm:endermaptera_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 6666, "localized": "终极分形者", "id": "cataclysm:final_fractal", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火焰之眼", "id": "cataclysm:flame_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结霜石砖台阶", "block": {"crop": false}, "id": "cataclysm:frosted_stone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结霜石砖楼梯", "block": {"crop": false}, "id": "cataclysm:frosted_stone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结霜石砖墙", "block": {"crop": false}, "id": "cataclysm:frosted_stone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结霜石砖", "block": {"crop": false}, "id": "cataclysm:frosted_stone_bricks"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "壁垒护手", "id": "cataclysm:gauntlet_of_bulwark"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "守卫者护手", "id": "cataclysm:gauntlet_of_guard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焰魔刷怪蛋", "id": "cataclysm:ignis_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "炽燃狂魂刷怪蛋", "id": "cataclysm:ignited_berserker_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "燃魂刷怪蛋", "id": "cataclysm:ignited_revenant_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "腾炎块", "block": {"crop": false}, "id": "cataclysm:ignitium_block"}, {"maxStackSize": 1, "maxDamage": 585, "localized": "腾炎靴子", "id": "cataclysm:ignitium_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 720, "localized": "腾炎胸甲", "id": "cataclysm:ignitium_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 720, "localized": "腾炎鞘翅胸甲", "id": "cataclysm:ignitium_elytra_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 495, "localized": "腾炎头盔", "id": "cataclysm:ignitium_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "腾炎锭", "id": "cataclysm:ignitium_ingot"}, {"maxStackSize": 1, "maxDamage": 675, "localized": "腾炎护腿", "id": "cataclysm:ignitium_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "cataclysm:ignitium_upgrade_smithing_template"}, {"maxStackSize": 1, "maxDamage": 2800, "localized": "炼狱锻锤", "id": "cataclysm:infernal_forge", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 750, "localized": "镰形剑", "id": "cataclysm:<PERSON><PERSON><PERSON>", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅狗头人将军头颅", "block": {"crop": false}, "id": "cataclysm:kobolediator_skull"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅狗头人将军刷怪蛋", "id": "cataclysm:kobolediator_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅狗头人之骨", "id": "cataclysm:koboleton_bone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅狗头人刷怪蛋", "id": "cataclysm:koboleton_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 50, "localized": "激光加特林", "id": "cataclysm:laser_gatling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓑鲉", "id": "cataclysm:lionfish", "food": {"saturation": 0.1, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓑鲉刷怪蛋", "id": "cataclysm:lionfish_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓑鲉尖刺", "id": "cataclysm:lionfish_spike"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "咒翼灵骸刷怪蛋", "id": "cataclysm:maledictus_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "绞肉锯", "id": "cataclysm:meat_shredder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "机械之眼", "id": "cataclysm:mech_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "机械融合砧", "block": {"crop": false}, "id": "cataclysm:mechanical_fusion_anvil"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "现世遗魂桶", "id": "cataclysm:modern_remnant_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "现世遗魂刷怪蛋", "id": "cataclysm:modern_remnant_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "恶兽之眼", "id": "cataclysm:monstrous_eye"}, {"maxStackSize": 1, "maxDamage": 407, "localized": "恶兽头盔", "id": "cataclysm:monstrous_helm", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "恶兽犄角", "id": "cataclysm:monstrous_horn"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_ancient_remnant"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_ender_guardian"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_ignis"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_maledictus"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_netherite_monstrosity"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_the_harbinger"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_the_leviathan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "无名巫师刷怪蛋", "id": "cataclysm:nameless_sorcerer_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "沙漠项链", "id": "cataclysm:necklace_of_the_desert"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金巨兽刷怪蛋", "id": "cataclysm:netherite_monstrosity_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石砖台阶", "block": {"crop": false}, "id": "cataclysm:obsidian_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石砖楼梯", "block": {"crop": false}, "id": "cataclysm:obsidian_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石砖墙", "block": {"crop": false}, "id": "cataclysm:obsidian_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石砖块", "block": {"crop": false}, "id": "cataclysm:obsidian_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石陷阱方块", "block": {"crop": false}, "id": "cataclysm:obsidian_explosion_trap_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水冰锥", "block": {"crop": false}, "id": "cataclysm:pointed_icicle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制末地石", "block": {"crop": false}, "id": "cataclysm:polished_end_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制末地石台阶", "block": {"crop": false}, "id": "cataclysm:polished_end_stone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制末地石楼梯", "block": {"crop": false}, "id": "cataclysm:polished_end_stone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制砂岩", "block": {"crop": false}, "id": "cataclysm:polished_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫珀陷阱方块", "block": {"crop": false}, "id": "cataclysm:purpur_void_rune_trap_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫珀墙", "block": {"crop": false}, "id": "cataclysm:purpur_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英砖墙", "block": {"crop": false}, "id": "cataclysm:quartz_brick_wall"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "遗魂头骨", "id": "cataclysm:remnant_skull"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皇家魂尸刷怪蛋", "id": "cataclysm:royal_draugr_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩陷阱", "block": {"crop": false}, "id": "cataclysm:sandstone_falling_trap"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩引燃陷阱", "block": {"crop": false}, "id": "cataclysm:sandstone_ignite_trap"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩毒镖陷阱", "block": {"crop": false}, "id": "cataclysm:sandstone_poison_dart_trap"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "沙暴瓶", "id": "cataclysm:sandstorm_in_a_bottle"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "断魂战戟", "id": "cataclysm:soul_render"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黏性手套", "id": "cataclysm:sticky_gloves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石柱", "block": {"crop": false}, "id": "cataclysm:stone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石瓦台阶", "block": {"crop": false}, "id": "cataclysm:stone_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石瓦楼梯", "block": {"crop": false}, "id": "cataclysm:stone_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石瓦墙", "block": {"crop": false}, "id": "cataclysm:stone_tile_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石瓦", "block": {"crop": false}, "id": "cataclysm:stone_tiles"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "歼灭战锤", "id": "cataclysm:the_annihilator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "幼年利维坦桶", "id": "cataclysm:the_baby_leviathan_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幼年利维坦刷怪蛋", "id": "cataclysm:the_baby_leviathan_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "先驱者刷怪蛋", "id": "cataclysm:the_harbinger_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "炎葬", "id": "cataclysm:the_incinerator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "利维坦刷怪蛋", "id": "cataclysm:the_leviathan_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "徘徊者刷怪蛋", "id": "cataclysm:the_prowler_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "观测者刷怪蛋", "id": "cataclysm:the_watcher_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "潮汐利爪", "id": "cataclysm:tidal_claws"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "虚空突击肩炮", "id": "cataclysm:void_assault_shoulder_weapon"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "虚空核心", "id": "cataclysm:void_core"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空之眼", "id": "cataclysm:void_eye"}, {"maxStackSize": 1, "maxDamage": 2800, "localized": "虚空锻锤", "id": "cataclysm:void_forge", "toolType": "pickaxe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空灌注末地石砖", "block": {"crop": false}, "id": "cataclysm:void_infused_end_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空之颚", "id": "cataclysm:void_jaw"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空灯", "block": {"crop": false}, "id": "cataclysm:void_lantern_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空散射箭", "id": "cataclysm:void_scatter_arrow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空碎片", "id": "cataclysm:void_shard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空石", "block": {"crop": false}, "id": "cataclysm:void_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蛇形妖骸刷怪蛋", "id": "cataclysm:wadjet_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "凋灵突击肩炮", "id": "cataclysm:wither_assault_shoulder_weapon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凋灵合金块", "block": {"crop": false}, "id": "cataclysm:witherite_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凋灵合金锭", "id": "cataclysm:witherite_ingot"}, {"maxStackSize": 1, "maxDamage": 6666, "localized": "末影双手剑", "id": "cataclysm:zwei<PERSON>", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木窗户", "block": {"crop": false}, "id": "create:acacia_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木窗户板", "block": {"crop": false}, "id": "create:acacia_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可调节链式传动箱", "block": {"crop": false}, "id": "create:adjustable_chain_gearshift"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "模拟拉杆", "block": {"crop": false}, "id": "create:analog_lever"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山合金", "id": "create:andesite_alloy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山合金块", "block": {"crop": false}, "id": "create:andesite_alloy_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山栏杆", "block": {"crop": false}, "id": "create:andesite_bars"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山机壳", "block": {"crop": false}, "id": "create:andesite_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山门", "block": {"crop": false}, "id": "create:andesite_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山齿轮箱", "block": {"crop": false}, "id": "create:andesite_encased_cogwheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山大齿轮箱", "block": {"crop": false}, "id": "create:andesite_encased_large_cogwheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山传动杆箱", "block": {"crop": false}, "id": "create:andesite_encased_shaft"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山漏斗", "block": {"crop": false}, "id": "create:andesite_funnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山梯子", "block": {"crop": false}, "id": "create:andesite_ladder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩柱", "block": {"crop": false}, "id": "create:andesite_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山脚手架", "block": {"crop": false}, "id": "create:andesite_scaffolding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山隧道", "block": {"crop": false}, "id": "create:andesite_tunnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皓蓝石", "block": {"crop": false}, "id": "create:asurine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皓蓝石柱", "block": {"crop": false}, "id": "create:asurine_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "属性过滤器", "id": "create:attribute_filter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "巧克力棒", "id": "create:bar_of_chocolate", "food": {"saturation": 0.3, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工作盆", "block": {"crop": false}, "id": "create:basin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "传送带", "block": {"crop": false}, "id": "create:belt_connector"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木窗户", "block": {"crop": false}, "id": "create:birch_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木窗户板", "block": {"crop": false}, "id": "create:birch_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色坐垫", "block": {"crop": false}, "id": "create:black_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色工具箱", "block": {"crop": false}, "id": "create:black_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色阀门手轮", "block": {"crop": false}, "id": "create:black_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰人燃烧室", "block": {"crop": false}, "id": "create:blaze_burner"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰蛋糕", "id": "create:blaze_cake"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰蛋糕胚", "id": "create:blaze_cake_base"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色坐垫", "block": {"crop": false}, "id": "create:blue_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色工具箱", "block": {"crop": false}, "id": "create:blue_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色阀门手轮", "block": {"crop": false}, "id": "create:blue_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜栏杆", "block": {"crop": false}, "id": "create:brass_bars"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜块", "block": {"crop": false}, "id": "create:brass_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜机壳", "block": {"crop": false}, "id": "create:brass_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜门", "block": {"crop": false}, "id": "create:brass_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜齿轮箱", "block": {"crop": false}, "id": "create:brass_encased_cogwheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜大齿轮箱", "block": {"crop": false}, "id": "create:brass_encased_large_cogwheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜传动杆箱", "block": {"crop": false}, "id": "create:brass_encased_shaft"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜漏斗", "block": {"crop": false}, "id": "create:brass_funnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜手部零件", "id": "create:brass_hand"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜锭", "id": "create:brass_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜梯子", "block": {"crop": false}, "id": "create:brass_ladder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜粒", "id": "create:brass_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜脚手架", "block": {"crop": false}, "id": "create:brass_scaffolding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜板", "id": "create:brass_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜隧道", "block": {"crop": false}, "id": "create:brass_tunnel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色坐垫", "block": {"crop": false}, "id": "create:brown_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色工具箱", "block": {"crop": false}, "id": "create:brown_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色阀门手轮", "block": {"crop": false}, "id": "create:brown_valve_handle"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "建筑工茶饮", "id": "create:builders_tea", "food": {"saturation": 0.6, "nutrition": 1, "alwaysEdible": true}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "方解石柱", "block": {"crop": false}, "id": "create:calcite_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矿车装配站", "block": {"crop": false}, "id": "create:cart_assembler"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "装配过的矿车", "id": "create:chest_minecart_contraption"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "巧克力桶", "id": "create:chocolate_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "巧克力包层浆果", "id": "create:chocolate_glazed_berries", "food": {"saturation": 0.8, "nutrition": 7, "alwaysEdible": false}}, {"maxStackSize": 16, "maxDamage": 0, "localized": "异彩化合物", "id": "create:chromatic_compound"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "溜槽", "block": {"crop": false}, "id": "create:chute"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "余烬面粉", "id": "create:cinder_flour"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "剪贴板", "block": {"crop": false}, "id": "create:clipboard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "时钟轴承", "block": {"crop": false}, "id": "create:clockwork_bearing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "离合器", "block": {"crop": false}, "id": "create:clutch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "齿轮", "block": {"crop": false}, "id": "create:cogwheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "智能侦测器", "block": {"crop": false}, "id": "create:content_observer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动态结构控制器", "block": {"crop": false}, "id": "create:contraption_controls"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "控制铁轨", "block": {"crop": false}, "id": "create:controller_rail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车驾驶台", "block": {"crop": false}, "id": "create:controls"}, {"maxStackSize": 1, "maxDamage": 112, "localized": "铜背罐", "id": "create:copper_backtank", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可放置的铜背罐", "block": {"crop": false}, "id": "create:copper_backtank_placeable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜栏杆", "block": {"crop": false}, "id": "create:copper_bars"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜机壳", "block": {"crop": false}, "id": "create:copper_casing"}, {"maxStackSize": 1, "maxDamage": 91, "localized": "铜潜水靴", "id": "create:copper_diving_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 77, "localized": "铜潜水头盔", "id": "create:copper_diving_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜门", "block": {"crop": false}, "id": "create:copper_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜梯子", "block": {"crop": false}, "id": "create:copper_ladder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜粒", "id": "create:copper_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜脚手架", "block": {"crop": false}, "id": "create:copper_scaffolding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜板", "id": "create:copper_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜砖瓦台阶", "block": {"crop": false}, "id": "create:copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜砖瓦楼梯", "block": {"crop": false}, "id": "create:copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜砖瓦", "block": {"crop": false}, "id": "create:copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜瓦台阶", "block": {"crop": false}, "id": "create:copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜瓦楼梯", "block": {"crop": false}, "id": "create:copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜瓦", "block": {"crop": false}, "id": "create:copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜阀门手轮", "block": {"crop": false}, "id": "create:copper_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "伪装板", "block": {"crop": false}, "id": "create:copycat_panel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "伪装半阶", "block": {"crop": false}, "id": "create:copycat_step"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合成槽盖板", "id": "create:crafter_slot_cover"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合成蓝图", "id": "create:crafting_blueprint"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造烈焰蛋糕", "id": "create:creative_blaze_cake"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造板条箱", "block": {"crop": false}, "id": "create:creative_crate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造流体储罐", "block": {"crop": false}, "id": "create:creative_fluid_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造马达", "block": {"crop": false}, "id": "create:creative_motor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红岩", "block": {"crop": false}, "id": "create:crimsite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红岩柱", "block": {"crop": false}, "id": "create:crimsite_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木窗户", "block": {"crop": false}, "id": "create:crimson_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木窗户板", "block": {"crop": false}, "id": "create:crimson_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎铝矿石", "id": "create:crushed_raw_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎铜矿石", "id": "create:crushed_raw_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎金矿石", "id": "create:crushed_raw_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎铁矿石", "id": "create:crushed_raw_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎铅矿石", "id": "create:crushed_raw_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎镍矿石", "id": "create:crushed_raw_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎锇矿石", "id": "create:crushed_raw_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎铂矿石", "id": "create:crushed_raw_platinum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎水银矿石", "id": "create:crushed_raw_quicksilver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎银矿石", "id": "create:crushed_raw_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎锡矿石", "id": "create:crushed_raw_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎铀矿石", "id": "create:crushed_raw_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎锌矿石", "id": "create:crushed_raw_zinc"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎轮", "block": {"crop": false}, "id": "create:crushing_wheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "布谷鸟闹钟", "block": {"crop": false}, "id": "create:cuckoo_clock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩", "block": {"crop": false}, "id": "create:cut_andesite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩砖台阶", "block": {"crop": false}, "id": "create:cut_andesite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_andesite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩砖块墙", "block": {"crop": false}, "id": "create:cut_andesite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩砖块", "block": {"crop": false}, "id": "create:cut_andesite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩台阶", "block": {"crop": false}, "id": "create:cut_andesite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩楼梯", "block": {"crop": false}, "id": "create:cut_andesite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制安山岩墙", "block": {"crop": false}, "id": "create:cut_andesite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石", "block": {"crop": false}, "id": "create:cut_asurine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石砖块台阶", "block": {"crop": false}, "id": "create:cut_asurine_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石砖块楼梯", "block": {"crop": false}, "id": "create:cut_asurine_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石砖块墙", "block": {"crop": false}, "id": "create:cut_asurine_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石砖块", "block": {"crop": false}, "id": "create:cut_asurine_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石台阶", "block": {"crop": false}, "id": "create:cut_asurine_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石楼梯", "block": {"crop": false}, "id": "create:cut_asurine_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制皓蓝石墙", "block": {"crop": false}, "id": "create:cut_asurine_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石", "block": {"crop": false}, "id": "create:cut_calcite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石砖块台阶", "block": {"crop": false}, "id": "create:cut_calcite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石砖块楼梯", "block": {"crop": false}, "id": "create:cut_calcite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石砖块墙", "block": {"crop": false}, "id": "create:cut_calcite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石砖块", "block": {"crop": false}, "id": "create:cut_calcite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石台阶", "block": {"crop": false}, "id": "create:cut_calcite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石楼梯", "block": {"crop": false}, "id": "create:cut_calcite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制方解石墙", "block": {"crop": false}, "id": "create:cut_calcite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩", "block": {"crop": false}, "id": "create:cut_crimsite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩砖块台阶", "block": {"crop": false}, "id": "create:cut_crimsite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_crimsite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩砖块墙", "block": {"crop": false}, "id": "create:cut_crimsite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩砖块", "block": {"crop": false}, "id": "create:cut_crimsite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩台阶", "block": {"crop": false}, "id": "create:cut_crimsite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩楼梯", "block": {"crop": false}, "id": "create:cut_crimsite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制绯红岩墙", "block": {"crop": false}, "id": "create:cut_crimsite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩", "block": {"crop": false}, "id": "create:cut_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩砖块台阶", "block": {"crop": false}, "id": "create:cut_deepslate_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_deepslate_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩砖块墙", "block": {"crop": false}, "id": "create:cut_deepslate_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩砖块", "block": {"crop": false}, "id": "create:cut_deepslate_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩台阶", "block": {"crop": false}, "id": "create:cut_deepslate_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩楼梯", "block": {"crop": false}, "id": "create:cut_deepslate_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制深板岩墙", "block": {"crop": false}, "id": "create:cut_deepslate_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩", "block": {"crop": false}, "id": "create:cut_diorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩砖块台阶", "block": {"crop": false}, "id": "create:cut_diorite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_diorite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩砖块墙", "block": {"crop": false}, "id": "create:cut_diorite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩砖块", "block": {"crop": false}, "id": "create:cut_diorite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩台阶", "block": {"crop": false}, "id": "create:cut_diorite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩楼梯", "block": {"crop": false}, "id": "create:cut_diorite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制闪长岩墙", "block": {"crop": false}, "id": "create:cut_diorite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石块", "block": {"crop": false}, "id": "create:cut_dripstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石砖块台阶", "block": {"crop": false}, "id": "create:cut_dripstone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石砖块楼梯", "block": {"crop": false}, "id": "create:cut_dripstone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石砖块墙", "block": {"crop": false}, "id": "create:cut_dripstone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石砖块", "block": {"crop": false}, "id": "create:cut_dripstone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石台阶", "block": {"crop": false}, "id": "create:cut_dripstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石楼梯", "block": {"crop": false}, "id": "create:cut_dripstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制滴水石墙", "block": {"crop": false}, "id": "create:cut_dripstone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩", "block": {"crop": false}, "id": "create:cut_granite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩砖块台阶", "block": {"crop": false}, "id": "create:cut_granite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_granite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩砖块墙", "block": {"crop": false}, "id": "create:cut_granite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩砖块", "block": {"crop": false}, "id": "create:cut_granite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩台阶", "block": {"crop": false}, "id": "create:cut_granite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩楼梯", "block": {"crop": false}, "id": "create:cut_granite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制花岗岩墙", "block": {"crop": false}, "id": "create:cut_granite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩", "block": {"crop": false}, "id": "create:cut_limestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩砖块台阶", "block": {"crop": false}, "id": "create:cut_limestone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_limestone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩砖块墙", "block": {"crop": false}, "id": "create:cut_limestone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩砖块", "block": {"crop": false}, "id": "create:cut_limestone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩台阶", "block": {"crop": false}, "id": "create:cut_limestone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩楼梯", "block": {"crop": false}, "id": "create:cut_limestone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制石灰岩墙", "block": {"crop": false}, "id": "create:cut_limestone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂", "block": {"crop": false}, "id": "create:cut_ochrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂砖块台阶", "block": {"crop": false}, "id": "create:cut_ochrum_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂砖块楼梯", "block": {"crop": false}, "id": "create:cut_ochrum_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂砖块墙", "block": {"crop": false}, "id": "create:cut_ochrum_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂砖块", "block": {"crop": false}, "id": "create:cut_ochrum_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂台阶", "block": {"crop": false}, "id": "create:cut_ochrum_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂楼梯", "block": {"crop": false}, "id": "create:cut_ochrum_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制赭金砂墙", "block": {"crop": false}, "id": "create:cut_ochrum_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣", "block": {"crop": false}, "id": "create:cut_scorchia"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣砖块台阶", "block": {"crop": false}, "id": "create:cut_scorchia_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣砖块楼梯", "block": {"crop": false}, "id": "create:cut_scorchia_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣砖块墙", "block": {"crop": false}, "id": "create:cut_scorchia_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣砖块", "block": {"crop": false}, "id": "create:cut_scorchia_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣台阶", "block": {"crop": false}, "id": "create:cut_scorchia_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣楼梯", "block": {"crop": false}, "id": "create:cut_scorchia_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制焦黑熔渣墙", "block": {"crop": false}, "id": "create:cut_scorchia_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣", "block": {"crop": false}, "id": "create:cut_scoria"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣砖块台阶", "block": {"crop": false}, "id": "create:cut_scoria_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣砖块楼梯", "block": {"crop": false}, "id": "create:cut_scoria_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣砖块墙", "block": {"crop": false}, "id": "create:cut_scoria_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣砖块", "block": {"crop": false}, "id": "create:cut_scoria_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣台阶", "block": {"crop": false}, "id": "create:cut_scoria_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣楼梯", "block": {"crop": false}, "id": "create:cut_scoria_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制熔渣墙", "block": {"crop": false}, "id": "create:cut_scoria_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩", "block": {"crop": false}, "id": "create:cut_tuff"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩砖块台阶", "block": {"crop": false}, "id": "create:cut_tuff_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_tuff_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩砖块墙", "block": {"crop": false}, "id": "create:cut_tuff_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩砖块", "block": {"crop": false}, "id": "create:cut_tuff_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩台阶", "block": {"crop": false}, "id": "create:cut_tuff_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩楼梯", "block": {"crop": false}, "id": "create:cut_tuff_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制凝灰岩墙", "block": {"crop": false}, "id": "create:cut_tuff_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩", "block": {"crop": false}, "id": "create:cut_veridium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩砖块台阶", "block": {"crop": false}, "id": "create:cut_veridium_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩砖块楼梯", "block": {"crop": false}, "id": "create:cut_veridium_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩砖块墙", "block": {"crop": false}, "id": "create:cut_veridium_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩砖块", "block": {"crop": false}, "id": "create:cut_veridium_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩台阶", "block": {"crop": false}, "id": "create:cut_veridium_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩楼梯", "block": {"crop": false}, "id": "create:cut_veridium_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制辉绿岩墙", "block": {"crop": false}, "id": "create:cut_veridium_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色坐垫", "block": {"crop": false}, "id": "create:cyan_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色工具箱", "block": {"crop": false}, "id": "create:cyan_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色阀门手轮", "block": {"crop": false}, "id": "create:cyan_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木窗户", "block": {"crop": false}, "id": "create:dark_oak_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木窗户板", "block": {"crop": false}, "id": "create:dark_oak_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩柱", "block": {"crop": false}, "id": "create:deepslate_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层锌矿石", "block": {"crop": false}, "id": "create:deepslate_zinc_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "机械手", "block": {"crop": false}, "id": "create:deployer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "置物台", "block": {"crop": false}, "id": "create:depot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩柱", "block": {"crop": false}, "id": "create:diorite_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "翻牌显示器", "block": {"crop": false}, "id": "create:display_board"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "显示链接器", "block": {"crop": false}, "id": "create:display_link"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "面团", "id": "create:dough"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石柱", "block": {"crop": false}, "id": "create:dripstone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电子管", "id": "create:electron_tube"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "升降机锚点", "block": {"crop": false}, "id": "create:elevator_contact"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "升降机滑轮", "block": {"crop": false}, "id": "create:elevator_pulley"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空的烈焰人燃烧室", "block": {"crop": false}, "id": "create:empty_blaze_burner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "空白蓝图", "id": "create:empty_schematic"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "链式传动箱", "block": {"crop": false}, "id": "create:encased_chain_drive"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鼓风机", "block": {"crop": false}, "id": "create:encased_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "经验块", "block": {"crop": false}, "id": "create:experience_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "经验颗粒", "id": "create:experience_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜砖瓦台阶", "block": {"crop": false}, "id": "create:exposed_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜砖瓦楼梯", "block": {"crop": false}, "id": "create:exposed_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜砖瓦", "block": {"crop": false}, "id": "create:exposed_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜瓦台阶", "block": {"crop": false}, "id": "create:exposed_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜瓦楼梯", "block": {"crop": false}, "id": "create:exposed_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜瓦", "block": {"crop": false}, "id": "create:exposed_copper_tiles"}, {"maxStackSize": 1, "maxDamage": 200, "localized": "伸缩机械手", "id": "create:extendo_grip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列表过滤器", "id": "create:filter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体管道", "block": {"crop": false}, "id": "create:fluid_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体储罐", "block": {"crop": false}, "id": "create:fluid_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体阀门", "block": {"crop": false}, "id": "create:fluid_valve"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "飞轮", "block": {"crop": false}, "id": "create:flywheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "边框玻璃", "block": {"crop": false}, "id": "create:framed_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "边框玻璃门", "block": {"crop": false}, "id": "create:framed_glass_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "边框玻璃板", "block": {"crop": false}, "id": "create:framed_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "边框玻璃活板门", "block": {"crop": false}, "id": "create:framed_glass_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "装配过的矿车", "id": "create:furnace_minecart_contraption"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "起重机取物器", "block": {"crop": false}, "id": "create:gantry_carriage"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "起重机杆", "block": {"crop": false}, "id": "create:gantry_shaft"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "十字齿轮箱", "block": {"crop": false}, "id": "create:gearbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "反转齿轮箱", "block": {"crop": false}, "id": "create:gearshift"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师护目镜", "id": "create:goggles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金板", "id": "create:golden_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩柱", "block": {"crop": false}, "id": "create:granite_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色坐垫", "block": {"crop": false}, "id": "create:gray_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色工具箱", "block": {"crop": false}, "id": "create:gray_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色阀门手轮", "block": {"crop": false}, "id": "create:gray_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色坐垫", "block": {"crop": false}, "id": "create:green_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色工具箱", "block": {"crop": false}, "id": "create:green_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色阀门手轮", "block": {"crop": false}, "id": "create:green_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "手摇曲柄", "block": {"crop": false}, "id": "create:hand_crank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造模式世界塑形器", "id": "create:handheld_worldshaper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "缠魂钟", "block": {"crop": false}, "id": "create:haunted_bell"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蜂蜜桶", "id": "create:honey_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜜渍苹果", "id": "create:honeyed_apple", "food": {"saturation": 0.8, "nutrition": 8, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水平边框玻璃", "block": {"crop": false}, "id": "create:horizontal_framed_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水平边框玻璃板", "block": {"crop": false}, "id": "create:horizontal_framed_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "软管滑轮", "block": {"crop": false}, "id": "create:hose_pulley"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精密构件（半成品）", "id": "create:incomplete_precision_mechanism"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "列车轨道（半成品）", "id": "create:incomplete_track"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业铁块", "block": {"crop": false}, "id": "create:industrial_iron_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁板", "id": "create:iron_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "分液池", "block": {"crop": false}, "id": "create:item_drain"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物品保险库", "block": {"crop": false}, "id": "create:item_vault"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木窗户", "block": {"crop": false}, "id": "create:jungle_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木窗户板", "block": {"crop": false}, "id": "create:jungle_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大齿轮", "block": {"crop": false}, "id": "create:large_cogwheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型水车", "block": {"crop": false}, "id": "create:large_water_wheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠安山岩", "block": {"crop": false}, "id": "create:layered_andesite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠皓蓝石", "block": {"crop": false}, "id": "create:layered_asurine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠方解石", "block": {"crop": false}, "id": "create:layered_calcite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠绯红岩", "block": {"crop": false}, "id": "create:layered_crimsite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠深板岩", "block": {"crop": false}, "id": "create:layered_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠闪长岩", "block": {"crop": false}, "id": "create:layered_diorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠滴水石", "block": {"crop": false}, "id": "create:layered_dripstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠花岗岩", "block": {"crop": false}, "id": "create:layered_granite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠石灰岩", "block": {"crop": false}, "id": "create:layered_limestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠赭金砂", "block": {"crop": false}, "id": "create:layered_ochrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠焦黑熔渣", "block": {"crop": false}, "id": "create:layered_scorchia"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠熔渣", "block": {"crop": false}, "id": "create:layered_scoria"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠凝灰岩", "block": {"crop": false}, "id": "create:layered_tuff"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "层叠辉绿岩", "block": {"crop": false}, "id": "create:layered_veridium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色坐垫", "block": {"crop": false}, "id": "create:light_blue_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色工具箱", "block": {"crop": false}, "id": "create:light_blue_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色阀门手轮", "block": {"crop": false}, "id": "create:light_blue_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色坐垫", "block": {"crop": false}, "id": "create:light_gray_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色工具箱", "block": {"crop": false}, "id": "create:light_gray_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色阀门手轮", "block": {"crop": false}, "id": "create:light_gray_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色坐垫", "block": {"crop": false}, "id": "create:lime_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色工具箱", "block": {"crop": false}, "id": "create:lime_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色阀门手轮", "block": {"crop": false}, "id": "create:lime_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石灰岩", "block": {"crop": false}, "id": "create:limestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石灰岩柱", "block": {"crop": false}, "id": "create:limestone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "轴向底盘", "block": {"crop": false}, "id": "create:linear_chassis"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "无线红石遥控器", "id": "create:linked_controller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色坐垫", "block": {"crop": false}, "id": "create:magenta_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色工具箱", "block": {"crop": false}, "id": "create:magenta_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色阀门手轮", "block": {"crop": false}, "id": "create:magenta_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木窗户", "block": {"crop": false}, "id": "create:mangrove_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木窗户板", "block": {"crop": false}, "id": "create:mangrove_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力臂", "block": {"crop": false}, "id": "create:mechanical_arm"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力轴承", "block": {"crop": false}, "id": "create:mechanical_bearing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力合成器", "block": {"crop": false}, "id": "create:mechanical_crafter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力钻头", "block": {"crop": false}, "id": "create:mechanical_drill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力收割机", "block": {"crop": false}, "id": "create:mechanical_harvester"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力搅拌器", "block": {"crop": false}, "id": "create:mechanical_mixer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力活塞", "block": {"crop": false}, "id": "create:mechanical_piston"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力犁", "block": {"crop": false}, "id": "create:mechanical_plough"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力冲压机", "block": {"crop": false}, "id": "create:mechanical_press"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力泵", "block": {"crop": false}, "id": "create:mechanical_pump"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力压路机", "block": {"crop": false}, "id": "create:mechanical_roller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力锯", "block": {"crop": false}, "id": "create:mechanical_saw"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金属支架", "block": {"crop": false}, "id": "create:metal_bracket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金属梁", "block": {"crop": false}, "id": "create:metal_girder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石磨", "block": {"crop": false}, "id": "create:millstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "装配过的矿车", "id": "create:minecart_contraption"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矿车连轴器", "id": "create:minecart_coupling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "布谷鸟闹钟", "block": {"crop": false}, "id": "create:mysterious_cuckoo_clock"}, {"maxStackSize": 1, "maxDamage": 592, "localized": "下界合金背罐", "id": "create:netherite_backtank", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可放置的下界合金背罐", "block": {"crop": false}, "id": "create:netherite_backtank_placeable"}, {"maxStackSize": 1, "maxDamage": 481, "localized": "下界合金潜水靴", "id": "create:netherite_diving_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 407, "localized": "下界合金潜水头盔", "id": "create:netherite_diving_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉光管", "block": {"crop": false}, "id": "create:nixie_tube"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "分散网", "block": {"crop": false}, "id": "create:nozzle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木窗户", "block": {"crop": false}, "id": "create:oak_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木窗户板", "block": {"crop": false}, "id": "create:oak_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭金砂", "block": {"crop": false}, "id": "create:ochrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭金砂柱", "block": {"crop": false}, "id": "create:ochrum_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色坐垫", "block": {"crop": false}, "id": "create:orange_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色工具箱", "block": {"crop": false}, "id": "create:orange_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色阀门手轮", "block": {"crop": false}, "id": "create:orange_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "华丽铁窗户", "block": {"crop": false}, "id": "create:ornate_iron_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "华丽铁窗户板", "block": {"crop": false}, "id": "create:ornate_iron_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜砖瓦台阶", "block": {"crop": false}, "id": "create:oxidized_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜砖瓦楼梯", "block": {"crop": false}, "id": "create:oxidized_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜砖瓦", "block": {"crop": false}, "id": "create:oxidized_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜瓦台阶", "block": {"crop": false}, "id": "create:oxidized_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜瓦楼梯", "block": {"crop": false}, "id": "create:oxidized_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜瓦", "block": {"crop": false}, "id": "create:oxidized_copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "奇异钟", "block": {"crop": false}, "id": "create:peculiar_bell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色坐垫", "block": {"crop": false}, "id": "create:pink_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色工具箱", "block": {"crop": false}, "id": "create:pink_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色阀门手轮", "block": {"crop": false}, "id": "create:pink_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "活塞杆", "block": {"crop": false}, "id": "create:piston_extension_pole"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "置物板", "block": {"crop": false}, "id": "create:placard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制安山岩", "block": {"crop": false}, "id": "create:polished_cut_andesite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制安山岩台阶", "block": {"crop": false}, "id": "create:polished_cut_andesite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制安山岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_andesite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制安山岩墙", "block": {"crop": false}, "id": "create:polished_cut_andesite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制皓蓝石", "block": {"crop": false}, "id": "create:polished_cut_asurine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制皓蓝石台阶", "block": {"crop": false}, "id": "create:polished_cut_asurine_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制皓蓝石楼梯", "block": {"crop": false}, "id": "create:polished_cut_asurine_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制皓蓝石墙", "block": {"crop": false}, "id": "create:polished_cut_asurine_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制方解石", "block": {"crop": false}, "id": "create:polished_cut_calcite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制方解石台阶", "block": {"crop": false}, "id": "create:polished_cut_calcite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制方解石楼梯", "block": {"crop": false}, "id": "create:polished_cut_calcite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制方解石墙", "block": {"crop": false}, "id": "create:polished_cut_calcite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制绯红岩", "block": {"crop": false}, "id": "create:polished_cut_crimsite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制绯红岩台阶", "block": {"crop": false}, "id": "create:polished_cut_crimsite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制绯红岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_crimsite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制绯红岩墙", "block": {"crop": false}, "id": "create:polished_cut_crimsite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制深板岩", "block": {"crop": false}, "id": "create:polished_cut_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制深板岩台阶", "block": {"crop": false}, "id": "create:polished_cut_deepslate_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制深板岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_deepslate_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制深板岩墙", "block": {"crop": false}, "id": "create:polished_cut_deepslate_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制闪长岩", "block": {"crop": false}, "id": "create:polished_cut_diorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制闪长岩台阶", "block": {"crop": false}, "id": "create:polished_cut_diorite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制闪长岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_diorite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制闪长岩墙", "block": {"crop": false}, "id": "create:polished_cut_diorite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制滴水石块", "block": {"crop": false}, "id": "create:polished_cut_dripstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制滴水石台阶", "block": {"crop": false}, "id": "create:polished_cut_dripstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制滴水石楼梯", "block": {"crop": false}, "id": "create:polished_cut_dripstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制滴水石墙", "block": {"crop": false}, "id": "create:polished_cut_dripstone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制花岗岩", "block": {"crop": false}, "id": "create:polished_cut_granite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制花岗岩台阶", "block": {"crop": false}, "id": "create:polished_cut_granite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制花岗岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_granite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制花岗岩墙", "block": {"crop": false}, "id": "create:polished_cut_granite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制石灰岩", "block": {"crop": false}, "id": "create:polished_cut_limestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制石灰岩台阶", "block": {"crop": false}, "id": "create:polished_cut_limestone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制石灰岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_limestone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制石灰岩墙", "block": {"crop": false}, "id": "create:polished_cut_limestone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制赭金砂", "block": {"crop": false}, "id": "create:polished_cut_ochrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制赭金砂台阶", "block": {"crop": false}, "id": "create:polished_cut_ochrum_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制赭金砂楼梯", "block": {"crop": false}, "id": "create:polished_cut_ochrum_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制赭金砂墙", "block": {"crop": false}, "id": "create:polished_cut_ochrum_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制焦黑熔渣", "block": {"crop": false}, "id": "create:polished_cut_scorchia"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制焦黑熔渣台阶", "block": {"crop": false}, "id": "create:polished_cut_scorchia_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制焦黑熔渣楼梯", "block": {"crop": false}, "id": "create:polished_cut_scorchia_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制焦黑熔渣墙", "block": {"crop": false}, "id": "create:polished_cut_scorchia_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制熔渣", "block": {"crop": false}, "id": "create:polished_cut_scoria"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制熔渣台阶", "block": {"crop": false}, "id": "create:polished_cut_scoria_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制熔渣楼梯", "block": {"crop": false}, "id": "create:polished_cut_scoria_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制熔渣墙", "block": {"crop": false}, "id": "create:polished_cut_scoria_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制凝灰岩", "block": {"crop": false}, "id": "create:polished_cut_tuff"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制凝灰岩台阶", "block": {"crop": false}, "id": "create:polished_cut_tuff_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制凝灰岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_tuff_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制凝灰岩墙", "block": {"crop": false}, "id": "create:polished_cut_tuff_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制辉绿岩", "block": {"crop": false}, "id": "create:polished_cut_veridium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制辉绿岩台阶", "block": {"crop": false}, "id": "create:polished_cut_veridium_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制辉绿岩楼梯", "block": {"crop": false}, "id": "create:polished_cut_veridium_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制切制辉绿岩墙", "block": {"crop": false}, "id": "create:polished_cut_veridium_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制玫瑰石英", "id": "create:polished_rose_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "移动式流体接口", "block": {"crop": false}, "id": "create:portable_fluid_interface"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "移动式存储接口", "block": {"crop": false}, "id": "create:portable_storage_interface"}, {"maxStackSize": 1, "maxDamage": 100, "localized": "土豆加农炮", "id": "create:potato_cannon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石粉末", "id": "create:powdered_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁存器", "block": {"crop": false}, "id": "create:powered_latch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "转换锁存器", "block": {"crop": false}, "id": "create:powered_toggle_latch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精密构件", "id": "create:precision_mechanism"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "扇叶", "id": "create:propeller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脉冲延长器", "block": {"crop": false}, "id": "create:pulse_extender"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脉冲中继器", "block": {"crop": false}, "id": "create:pulse_repeater"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色坐垫", "block": {"crop": false}, "id": "create:purple_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色工具箱", "block": {"crop": false}, "id": "create:purple_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色阀门手轮", "block": {"crop": false}, "id": "create:purple_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "径向底盘", "block": {"crop": false}, "id": "create:radial_chassis"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车机壳", "block": {"crop": false}, "id": "create:railway_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗锌", "id": "create:raw_zinc"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗锌块", "block": {"crop": false}, "id": "create:raw_zinc_block"}, {"maxStackSize": 1, "maxDamage": 8, "localized": "红沙砂纸", "id": "create:red_sand_paper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色坐垫", "block": {"crop": false}, "id": "create:red_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色工具箱", "block": {"crop": false}, "id": "create:red_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色阀门手轮", "block": {"crop": false}, "id": "create:red_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "接触式红石信号发生器", "block": {"crop": false}, "id": "create:redstone_contact"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "无线红石信号终端", "block": {"crop": false}, "id": "create:redstone_link"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光辉石", "id": "create:refined_radiance"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光辉机壳", "block": {"crop": false}, "id": "create:refined_radiance_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绳索滑轮", "block": {"crop": false}, "id": "create:rope_pulley"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰石英", "id": "create:rose_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰石英块", "block": {"crop": false}, "id": "create:rose_quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰石英灯", "block": {"crop": false}, "id": "create:rose_quartz_lamp"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰石英瓦", "block": {"crop": false}, "id": "create:rose_quartz_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "转速控制器", "block": {"crop": false}, "id": "create:rotation_speed_controller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风帆框架", "block": {"crop": false}, "id": "create:sail_frame"}, {"maxStackSize": 1, "maxDamage": 8, "localized": "砂纸", "id": "create:sand_paper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车时刻表", "id": "create:schedule"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蓝图", "id": "create:schematic"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蓝图与笔", "id": "create:schematic_and_quill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝图桌", "block": {"crop": false}, "id": "create:schematic_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝图加农炮", "block": {"crop": false}, "id": "create:schematic<PERSON><PERSON>"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔渣", "block": {"crop": false}, "id": "create:scorchia"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔渣柱", "block": {"crop": false}, "id": "create:scorchia_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔渣", "block": {"crop": false}, "id": "create:scoria"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔渣柱", "block": {"crop": false}, "id": "create:scoria_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "轴向底盘二号", "block": {"crop": false}, "id": "create:secondary_linear_chassis"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可编程齿轮箱", "block": {"crop": false}, "id": "create:sequenced_gearshift"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "暗影钢", "id": "create:shadow_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "暗影机壳", "block": {"crop": false}, "id": "create:shadow_steel_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "传动杆", "block": {"crop": false}, "id": "create:shaft"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩小砖块台阶", "block": {"crop": false}, "id": "create:small_andesite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_andesite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩小砖块墙", "block": {"crop": false}, "id": "create:small_andesite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩小砖块", "block": {"crop": false}, "id": "create:small_andesite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皓蓝石小砖块台阶", "block": {"crop": false}, "id": "create:small_asurine_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皓蓝石小砖块楼梯", "block": {"crop": false}, "id": "create:small_asurine_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皓蓝石小砖块墙", "block": {"crop": false}, "id": "create:small_asurine_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皓蓝石小砖块", "block": {"crop": false}, "id": "create:small_asurine_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "方解石小砖块台阶", "block": {"crop": false}, "id": "create:small_calcite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "方解石小砖块楼梯", "block": {"crop": false}, "id": "create:small_calcite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "方解石小砖块墙", "block": {"crop": false}, "id": "create:small_calcite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "方解石小砖块", "block": {"crop": false}, "id": "create:small_calcite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红岩小砖块台阶", "block": {"crop": false}, "id": "create:small_crimsite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_crimsite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红岩小砖块墙", "block": {"crop": false}, "id": "create:small_crimsite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红岩小砖块", "block": {"crop": false}, "id": "create:small_crimsite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩小砖块台阶", "block": {"crop": false}, "id": "create:small_deepslate_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_deepslate_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩小砖块墙", "block": {"crop": false}, "id": "create:small_deepslate_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩小砖块", "block": {"crop": false}, "id": "create:small_deepslate_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩小砖块台阶", "block": {"crop": false}, "id": "create:small_diorite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_diorite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩小砖块墙", "block": {"crop": false}, "id": "create:small_diorite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩小砖块", "block": {"crop": false}, "id": "create:small_diorite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石小砖块台阶", "block": {"crop": false}, "id": "create:small_dripstone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石小砖块楼梯", "block": {"crop": false}, "id": "create:small_dripstone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石小砖块墙", "block": {"crop": false}, "id": "create:small_dripstone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石小砖块", "block": {"crop": false}, "id": "create:small_dripstone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩小砖块台阶", "block": {"crop": false}, "id": "create:small_granite_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_granite_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩小砖块墙", "block": {"crop": false}, "id": "create:small_granite_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩小砖块", "block": {"crop": false}, "id": "create:small_granite_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石灰岩小砖块台阶", "block": {"crop": false}, "id": "create:small_limestone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石灰岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_limestone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石灰岩小砖块墙", "block": {"crop": false}, "id": "create:small_limestone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石灰岩小砖块", "block": {"crop": false}, "id": "create:small_limestone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭金砂小砖块台阶", "block": {"crop": false}, "id": "create:small_ochrum_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭金砂小砖块楼梯", "block": {"crop": false}, "id": "create:small_ochrum_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭金砂小砖块墙", "block": {"crop": false}, "id": "create:small_ochrum_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭金砂小砖块", "block": {"crop": false}, "id": "create:small_ochrum_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰石英细瓦", "block": {"crop": false}, "id": "create:small_rose_quartz_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔渣小砖块台阶", "block": {"crop": false}, "id": "create:small_scorchia_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔渣小砖块楼梯", "block": {"crop": false}, "id": "create:small_scorchia_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔渣小砖块墙", "block": {"crop": false}, "id": "create:small_scorchia_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔渣小砖块", "block": {"crop": false}, "id": "create:small_scorchia_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔渣小砖块台阶", "block": {"crop": false}, "id": "create:small_scoria_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔渣小砖块楼梯", "block": {"crop": false}, "id": "create:small_scoria_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔渣小砖块墙", "block": {"crop": false}, "id": "create:small_scoria_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔渣小砖块", "block": {"crop": false}, "id": "create:small_scoria_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝灰岩小砖块台阶", "block": {"crop": false}, "id": "create:small_tuff_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝灰岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_tuff_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝灰岩小砖块墙", "block": {"crop": false}, "id": "create:small_tuff_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝灰岩小砖块", "block": {"crop": false}, "id": "create:small_tuff_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉绿岩小砖块台阶", "block": {"crop": false}, "id": "create:small_veridium_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉绿岩小砖块楼梯", "block": {"crop": false}, "id": "create:small_veridium_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉绿岩小砖块墙", "block": {"crop": false}, "id": "create:small_veridium_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉绿岩小砖块", "block": {"crop": false}, "id": "create:small_veridium_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "智能溜槽", "block": {"crop": false}, "id": "create:smart_chute"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "智能流体管道", "block": {"crop": false}, "id": "create:smart_fluid_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "转速表", "block": {"crop": false}, "id": "create:speedometer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "注液器", "block": {"crop": false}, "id": "create:spout"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木窗户", "block": {"crop": false}, "id": "create:spruce_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木窗户板", "block": {"crop": false}, "id": "create:spruce_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蒸汽引擎", "block": {"crop": false}, "id": "create:steam_engine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蒸汽笛", "block": {"crop": false}, "id": "create:steam_whistle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏着器", "block": {"crop": false}, "id": "create:sticker"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏性动力活塞", "block": {"crop": false}, "id": "create:sticky_mechanical_piston"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "存量转信器", "block": {"crop": false}, "id": "create:stockpile_switch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "应力表", "block": {"crop": false}, "id": "create:stressometer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚固板", "id": "create:sturdy_sheet"}, {"maxStackSize": 1, "maxDamage": 99, "localized": "强力胶", "id": "create:super_glue"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "甜甜卷", "id": "create:sweet_roll", "food": {"saturation": 0.8, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "十字玻璃", "block": {"crop": false}, "id": "create:tiled_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "十字玻璃板", "block": {"crop": false}, "id": "create:tiled_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车轨道", "block": {"crop": false}, "id": "create:track"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车侦测器", "block": {"crop": false}, "id": "create:track_observer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车信号机", "block": {"crop": false}, "id": "create:track_signal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车站点", "block": {"crop": false}, "id": "create:track_station"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车门", "block": {"crop": false}, "id": "create:train_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "列车活板门", "block": {"crop": false}, "id": "create:train_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "树木肥料", "id": "create:tree_fertilizer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝灰岩柱", "block": {"crop": false}, "id": "create:tuff_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "转盘", "block": {"crop": false}, "id": "create:turntable"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "未加工的黑曜石板", "id": "create:unprocessed_obsidian_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉绿岩", "block": {"crop": false}, "id": "create:veridium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辉绿岩柱", "block": {"crop": false}, "id": "create:veridium_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竖直边框玻璃", "block": {"crop": false}, "id": "create:vertical_framed_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竖直边框玻璃板", "block": {"crop": false}, "id": "create:vertical_framed_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竖直十字齿轮箱", "block": {"crop": false}, "id": "create:vertical_gearbox"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "对称之杖", "id": "create:wand_of_symmetry"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木窗户", "block": {"crop": false}, "id": "create:warped_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木窗户板", "block": {"crop": false}, "id": "create:warped_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水车", "block": {"crop": false}, "id": "create:water_wheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜砖瓦台阶", "block": {"crop": false}, "id": "create:waxed_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜砖瓦楼梯", "block": {"crop": false}, "id": "create:waxed_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜砖瓦", "block": {"crop": false}, "id": "create:waxed_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜瓦台阶", "block": {"crop": false}, "id": "create:waxed_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜瓦楼梯", "block": {"crop": false}, "id": "create:waxed_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜瓦", "block": {"crop": false}, "id": "create:waxed_copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的涂蜡铜砖瓦台阶", "block": {"crop": false}, "id": "create:waxed_exposed_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的涂蜡铜砖瓦楼梯", "block": {"crop": false}, "id": "create:waxed_exposed_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的涂蜡铜砖瓦", "block": {"crop": false}, "id": "create:waxed_exposed_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的涂蜡铜瓦台阶", "block": {"crop": false}, "id": "create:waxed_exposed_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的涂蜡铜瓦楼梯", "block": {"crop": false}, "id": "create:waxed_exposed_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的涂蜡铜瓦", "block": {"crop": false}, "id": "create:waxed_exposed_copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的涂蜡铜砖瓦台阶", "block": {"crop": false}, "id": "create:waxed_oxidized_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的涂蜡铜砖瓦楼梯", "block": {"crop": false}, "id": "create:waxed_oxidized_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的涂蜡铜砖瓦", "block": {"crop": false}, "id": "create:waxed_oxidized_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的涂蜡铜瓦台阶", "block": {"crop": false}, "id": "create:waxed_oxidized_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的涂蜡铜瓦楼梯", "block": {"crop": false}, "id": "create:waxed_oxidized_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的涂蜡铜瓦", "block": {"crop": false}, "id": "create:waxed_oxidized_copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的涂蜡铜砖瓦台阶", "block": {"crop": false}, "id": "create:waxed_weathered_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的涂蜡铜砖瓦楼梯", "block": {"crop": false}, "id": "create:waxed_weathered_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的涂蜡铜砖瓦", "block": {"crop": false}, "id": "create:waxed_weathered_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的涂蜡铜瓦台阶", "block": {"crop": false}, "id": "create:waxed_weathered_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的涂蜡铜瓦楼梯", "block": {"crop": false}, "id": "create:waxed_weathered_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的涂蜡铜瓦", "block": {"crop": false}, "id": "create:waxed_weathered_copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜砖瓦台阶", "block": {"crop": false}, "id": "create:weathered_copper_shingle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜砖瓦楼梯", "block": {"crop": false}, "id": "create:weathered_copper_shingle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜砖瓦", "block": {"crop": false}, "id": "create:weathered_copper_shingles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜瓦台阶", "block": {"crop": false}, "id": "create:weathered_copper_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜瓦楼梯", "block": {"crop": false}, "id": "create:weathered_copper_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜瓦", "block": {"crop": false}, "id": "create:weathered_copper_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弹射置物台", "block": {"crop": false}, "id": "create:weighted_ejector"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小麦粉", "id": "create:wheat_flour"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "搅拌器", "id": "create:whisk"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风帆", "block": {"crop": false}, "id": "create:white_sail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色坐垫", "block": {"crop": false}, "id": "create:white_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色工具箱", "block": {"crop": false}, "id": "create:white_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色阀门手轮", "block": {"crop": false}, "id": "create:white_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风车轴承", "block": {"crop": false}, "id": "create:windmill_bearing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木质支架", "block": {"crop": false}, "id": "create:wooden_bracket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "扳手", "id": "create:wrench"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色坐垫", "block": {"crop": false}, "id": "create:yellow_seat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色工具箱", "block": {"crop": false}, "id": "create:yellow_toolbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色阀门手轮", "block": {"crop": false}, "id": "create:yellow_valve_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锌块", "block": {"crop": false}, "id": "create:zinc_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锌锭", "id": "create:zinc_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锌粒", "id": "create:zinc_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锌矿石", "block": {"crop": false}, "id": "create:zinc_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "钻石钻头", "id": "createoreexcavation:diamond_drill"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "铁钻头", "id": "createoreexcavation:drill"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "矿物钻井", "block": {"crop": false}, "id": "createoreexcavation:drilling_machine"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "流体提取井", "block": {"crop": false}, "id": "createoreexcavation:extractor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "下界合金钻头", "id": "createoreexcavation:netherite_drill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钻石原石", "id": "createoreexcavation:raw_diamond"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿宝石原石", "id": "createoreexcavation:raw_emerald"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石原石", "id": "createoreexcavation:raw_redstone"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "样本钻井", "block": {"crop": false}, "id": "createoreexcavation:sample_drill"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "矿脉图鉴", "id": "createoreexcavation:vein_atlas"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "探矿杖", "id": "createoreexcavation:vein_finder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "任务屏障", "block": {"crop": false}, "id": "ftbquests:barrier"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "任务书", "id": "ftbquests:book"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "自定义图标", "id": "ftbquests:custom_icon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "任务检测器", "block": {"crop": false}, "id": "ftbquests:detector"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "战利品箱开启器", "block": {"crop": false}, "id": "ftbquests:loot_crate_opener"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "战利品箱", "id": "ftbquests:lootcrate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "缺失物品", "id": "ftbquests:missing_item"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "目标显示器（1x1）", "block": {"crop": false}, "id": "ftbquests:screen_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "目标显示器（3x3）", "block": {"crop": false}, "id": "ftbquests:screen_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "目标显示器（5x5）", "block": {"crop": false}, "id": "ftbquests:screen_5"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "目标显示器（7x7）", "block": {"crop": false}, "id": "ftbquests:screen_7"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "阶段屏障", "block": {"crop": false}, "id": "ftbquests:stage_barrier"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Task Screen Configurator", "id": "ftbquests:task_screen_configurator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "乙醛桶", "id": "immersiveengineering:acetaldehyde_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化高炉", "block": {"crop": false}, "id": "immersiveengineering:advanced_blast_furnace"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合金窑", "block": {"crop": false}, "id": "immersiveengineering:alloy_smelter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "窑砖", "block": {"crop": false}, "id": "immersiveengineering:alloybrick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝栅栏", "block": {"crop": false}, "id": "immersiveengineering:alu_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝杆", "block": {"crop": false}, "id": "immersiveengineering:alu_post"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架", "block": {"crop": false}, "id": "immersiveengineering:alu_scaffolding_grate_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架", "block": {"crop": false}, "id": "immersiveengineering:alu_scaffolding_standard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架", "block": {"crop": false}, "id": "immersiveengineering:alu_scaffolding_wooden_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝结构臂", "block": {"crop": false}, "id": "immersiveengineering:alu_slope"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝壁挂支架", "block": {"crop": false}, "id": "immersiveengineering:alu_wallmount"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电弧炉", "block": {"crop": false}, "id": "immersiveengineering:arc_furnace"}, {"maxStackSize": 1, "maxDamage": 13, "localized": "法拉第靴子", "id": "immersiveengineering:armor_faraday_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 16, "localized": "法拉第胸甲", "id": "immersiveengineering:armor_faraday_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 11, "localized": "法拉第头盔", "id": "immersiveengineering:armor_faraday_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 15, "localized": "法拉第护腿", "id": "immersiveengineering:armor_faraday_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "穿甲弹", "id": "immersiveengineering:armor_piercing"}, {"maxStackSize": 1, "maxDamage": 273, "localized": "钢靴子", "id": "immersiveengineering:armor_steel_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 336, "localized": "钢胸甲", "id": "immersiveengineering:armor_steel_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 231, "localized": "钢头盔", "id": "immersiveengineering:armor_steel_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 315, "localized": "钢护腿", "id": "immersiveengineering:armor_steel_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "装配机", "block": {"crop": false}, "id": "immersiveengineering:assembler"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "自动化工程师装配台", "block": {"crop": false}, "id": "immersiveengineering:auto_workbench"}, {"maxStackSize": 1, "maxDamage": 641, "localized": "钢斧", "id": "immersiveengineering:axe_steel", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "气球", "block": {"crop": false}, "id": "immersiveengineering:balloon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:bannerpattern_bevels"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:banner<PERSON><PERSON>n_hammer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:bannerpattern_ornate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:bannerpattern_treated_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:bannerpattern_windmill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:banner<PERSON><PERSON><PERSON>_wolf"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:banner<PERSON><PERSON><PERSON>_wolf_l"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旗帜图案", "id": "immersiveengineering:banner<PERSON><PERSON><PERSON>_wolf_r"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "生物柴油桶", "id": "immersiveengineering:biodiesel_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高炉", "block": {"crop": false}, "id": "immersiveengineering:blast_furnace"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高炉砖", "block": {"crop": false}, "id": "immersiveengineering:blastbrick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化高炉砖", "block": {"crop": false}, "id": "immersiveengineering:blastbrick_reinforced"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高炉预热器", "block": {"crop": false}, "id": "immersiveengineering:blastfurnace_preheater"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师蓝图", "id": "immersiveengineering:blueprint"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灌装机", "block": {"crop": false}, "id": "immersiveengineering:bottling_machine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "断路刀闸", "block": {"crop": false}, "id": "immersiveengineering:breaker_switch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斗轮", "block": {"crop": false}, "id": "immersiveengineering:bucket_wheel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鹿弹", "id": "immersiveengineering:buckshot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bulwark Spawn Egg", "id": "immersiveengineering:bulwark_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "伐木机械锯", "id": "immersiveengineering:buzzsaw"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造蓄电池", "block": {"crop": false}, "id": "immersiveengineering:capacitor_creative"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高压蓄电池", "block": {"crop": false}, "id": "immersiveengineering:capacitor_hv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "低压蓄电池", "block": {"crop": false}, "id": "immersiveengineering:capacitor_lv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中压蓄电池", "block": {"crop": false}, "id": "immersiveengineering:capacitor_mv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "卡苏尔弹", "id": "immersiveengineering:casull"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "充电站", "block": {"crop": false}, "id": "immersiveengineering:charging_station"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学喷射器", "id": "immersiveengineering:chemthrower"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝板金属滑道", "block": {"crop": false}, "id": "immersiveengineering:chute_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜板金属滑道", "block": {"crop": false}, "id": "immersiveengineering:chute_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁板金属滑道", "block": {"crop": false}, "id": "immersiveengineering:chute_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢板金属滑道", "block": {"crop": false}, "id": "immersiveengineering:chute_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电路板", "id": "immersiveengineering:circuit_board"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程师电路台", "block": {"crop": false}, "id": "immersiveengineering:circuit_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Clinker Bricks", "block": {"crop": false}, "id": "immersiveengineering:clinker_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Clinker Brick Quoin", "block": {"crop": false}, "id": "immersiveengineering:clinker_brick_quoin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Clinker Brick Sill", "block": {"crop": false}, "id": "immersiveengineering:clinker_brick_sill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "园艺玻璃罩", "block": {"crop": false}, "id": "immersiveengineering:cloche"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦煤", "id": "immersiveengineering:coal_coke"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高压线圈", "block": {"crop": false}, "id": "immersiveengineering:coil_hv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜线圈", "block": {"crop": false}, "id": "immersiveengineering:coil_lv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金线圈", "block": {"crop": false}, "id": "immersiveengineering:coil_mv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦煤块", "block": {"crop": false}, "id": "immersiveengineering:coke"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦炉", "block": {"crop": false}, "id": "immersiveengineering:coke_oven"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦炉砖", "block": {"crop": false}, "id": "immersiveengineering:cokebrick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Commando Spawn Egg", "id": "immersiveengineering:commando_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电子元件", "id": "immersiveengineering:component_electronic"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级电子元件", "id": "immersiveengineering:component_electronic_adv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁机械零件", "id": "immersiveengineering:component_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢机械零件", "id": "immersiveengineering:component_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土", "block": {"crop": false}, "id": "immersiveengineering:concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Concrete Brick", "block": {"crop": false}, "id": "immersiveengineering:concrete_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Cracked Concrete Brick", "block": {"crop": false}, "id": "immersiveengineering:concrete_brick_cracked"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "混凝土桶", "id": "immersiveengineering:concrete_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chiseled Concrete", "block": {"crop": false}, "id": "immersiveengineering:concrete_chiseled"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镀铅混凝土", "block": {"crop": false}, "id": "immersiveengineering:concrete_leaded"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON>", "block": {"crop": false}, "id": "immersiveengineering:concrete_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土板", "block": {"crop": false}, "id": "immersiveengineering:concrete_quarter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土薄板", "block": {"crop": false}, "id": "immersiveengineering:concrete_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "速干混凝土", "block": {"crop": false}, "id": "immersiveengineering:concrete_sprayed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土块", "block": {"crop": false}, "id": "immersiveengineering:concrete_three_quarter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土瓦", "block": {"crop": false}, "id": "immersiveengineering:concrete_tile"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石接口连接器", "block": {"crop": false}, "id": "immersiveengineering:connector_bundled"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高压接线器", "block": {"crop": false}, "id": "immersiveengineering:connector_hv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高压继电器", "block": {"crop": false}, "id": "immersiveengineering:connector_hv_relay"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "低压接线器", "block": {"crop": false}, "id": "immersiveengineering:connector_lv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "低压继电器", "block": {"crop": false}, "id": "immersiveengineering:connector_lv_relay"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中压接线器", "block": {"crop": false}, "id": "immersiveengineering:connector_mv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中压继电器", "block": {"crop": false}, "id": "immersiveengineering:connector_mv_relay"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石探测接线器", "block": {"crop": false}, "id": "immersiveengineering:connector_probe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石接线器", "block": {"crop": false}, "id": "immersiveengineering:connector_redstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结构性接线器", "block": {"crop": false}, "id": "immersiveengineering:connector_structural"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "传送带", "block": {"crop": false}, "id": "immersiveengineering:conveyor_basic"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "卸货传送带", "block": {"crop": false}, "id": "immersiveengineering:conveyor_dropper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "抽取传送带", "block": {"crop": false}, "id": "immersiveengineering:conveyor_extract"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石控制传送带", "block": {"crop": false}, "id": "immersiveengineering:conveyor_redstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "分流传送带", "block": {"crop": false}, "id": "immersiveengineering:conveyor_splitter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "垂直传送带", "block": {"crop": false}, "id": "immersiveengineering:conveyor_vertical"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "岩芯样本", "id": "immersiveengineering:coresample"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程师工作台", "block": {"crop": false}, "id": "immersiveengineering:craftingtable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木制储物箱", "block": {"crop": false}, "id": "immersiveengineering:crate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "杂酚油桶", "id": "immersiveengineering:creosote_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉碎机", "block": {"crop": false}, "id": "immersiveengineering:crusher"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电流互感器", "block": {"crop": false}, "id": "immersiveengineering:current_transformer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "气垫", "block": {"crop": false}, "id": "immersiveengineering:cushion"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铝土矿石", "block": {"crop": false}, "id": "immersiveengineering:deepslate_ore_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铅矿石", "block": {"crop": false}, "id": "immersiveengineering:deepslate_ore_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层镍矿石", "block": {"crop": false}, "id": "immersiveengineering:deepslate_ore_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层银矿石", "block": {"crop": false}, "id": "immersiveengineering:deepslate_ore_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铀矿石", "block": {"crop": false}, "id": "immersiveengineering:deepslate_ore_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "柴油发电机", "block": {"crop": false}, "id": "immersiveengineering:diesel_generator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "龙息弹", "id": "immersiveengineering:dragons_breath"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "采矿机械钻", "id": "immersiveengineering:drill"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "铁钻头", "id": "immersiveengineering:drillhead_iron"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "钢钻头", "id": "immersiveengineering:drillhead_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程塑胶块", "block": {"crop": false}, "id": "immersiveengineering:duroplast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝粉", "id": "immersiveengineering:dust_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦煤粉", "id": "immersiveengineering:dust_coke"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜粉", "id": "immersiveengineering:dust_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜粉", "id": "immersiveengineering:dust_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金粉", "id": "immersiveengineering:dust_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金粉", "id": "immersiveengineering:dust_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高定向热解石墨粉", "id": "immersiveengineering:dust_hop_graphite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁粉", "id": "immersiveengineering:dust_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅粉", "id": "immersiveengineering:dust_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍粉", "id": "immersiveengineering:dust_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "硝酸盐粉", "id": "immersiveengineering:dust_saltpeter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银粉", "id": "immersiveengineering:dust_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢粉", "id": "immersiveengineering:dust_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "硫磺粉", "id": "immersiveengineering:dust_sulfur"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀粉", "id": "immersiveengineering:dust_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锯末", "id": "immersiveengineering:dust_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动能发电机", "block": {"crop": false}, "id": "immersiveengineering:dynamo"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "隔音耳罩", "id": "immersiveengineering:earmuffs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电灯", "block": {"crop": false}, "id": "immersiveengineering:electric_lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Electromagnet", "block": {"crop": false}, "id": "immersiveengineering:electromagnet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "真空管", "id": "immersiveengineering:electron_tube"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型空弹壳", "id": "immersiveengineering:empty_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型空弹壳", "id": "immersiveengineering:empty_shell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "人造皮革", "id": "immersiveengineering:ersatz_leather"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "乙醇桶", "id": "immersiveengineering:ethanol_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "挖掘机", "block": {"crop": false}, "id": "immersiveengineering:excavator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Birthday", "id": "immersiveengineering:fake_icon_birthday"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "BTTF", "id": "immersiveengineering:fake_icon_bttf"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Drillbreak", "id": "immersiveengineering:fake_icon_drillbreak"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "<PERSON><PERSON>", "id": "immersiveengineering:fake_icon_fried"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Lucky", "id": "immersiveengineering:fake_icon_lucky"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Ravenholm", "id": "immersiveengineering:fake_icon_ravenholm"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "通透绝缘体", "block": {"crop": false}, "id": "immersiveengineering:feedthrough"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业发酵机", "block": {"crop": false}, "id": "immersiveengineering:fermenter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "肥料", "id": "immersiveengineering:fertilizer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纤维块", "block": {"crop": false}, "id": "immersiveengineering:fiberboard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烟花弹", "id": "immersiveengineering:firework"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪光弹", "id": "immersiveengineering:flare"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泛光灯", "block": {"crop": false}, "id": "immersiveengineering:floodlight"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体管道", "block": {"crop": false}, "id": "immersiveengineering:fluid_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体阀", "block": {"crop": false}, "id": "immersiveengineering:fluid_placer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体泵", "block": {"crop": false}, "id": "immersiveengineering:fluid_pump"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体分配器", "block": {"crop": false}, "id": "immersiveengineering:fluid_sorter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "荧光管", "id": "immersiveengineering:fluorescent_tube"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "外置加热器", "block": {"crop": false}, "id": "immersiveengineering:furnace_heater"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Fusilier Spawn Egg", "id": "immersiveengineering:fusilier_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发电机模块", "block": {"crop": false}, "id": "immersiveengineering:generator"}, {"maxStackSize": 1, "maxDamage": 216, "localized": "可折叠滑翔翼", "id": "immersiveengineering:glider"}, {"maxStackSize": 16, "maxDamage": 96000, "localized": "石墨电极", "id": "immersiveengineering:graphite_electrode"}, {"maxStackSize": 1, "maxDamage": 4000, "localized": "研磨盘", "id": "immersiveengineering:grindingdisk"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Grit <PERSON>", "block": {"crop": false}, "id": "immersiveengineering:grit_sand"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "枪管", "id": "immersiveengineering:gunpart_barrel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "左筒", "id": "immersiveengineering:gunpart_drum"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "击锤", "id": "immersiveengineering:gunpart_hammer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火药桶", "block": {"crop": false}, "id": "immersiveengineering:gunpowder_barrel"}, {"maxStackSize": 1, "maxDamage": 100, "localized": "工程师锤", "id": "immersiveengineering:hammer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高爆弹", "id": "immersiveengineering:he"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "重型工程块", "block": {"crop": false}, "id": "immersiveengineering:heavy_engineering"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧布料", "id": "immersiveengineering:hemp_fabric"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业大麻纤维", "id": "immersiveengineering:hemp_fiber"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "麻凝土", "block": {"crop": false}, "id": "immersiveengineering:hempcrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Hempcrete Brick", "block": {"crop": false}, "id": "immersiveengineering:hempcrete_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Cracked Hempcrete Brick", "block": {"crop": false}, "id": "immersiveengineering:hempcrete_brick_cracked"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chiseled Hempcrete", "block": {"crop": false}, "id": "immersiveengineering:hempcrete_chiseled"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON><PERSON><PERSON><PERSON>", "block": {"crop": false}, "id": "immersiveengineering:hempcrete_pillar"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "除草剂桶", "id": "immersiveengineering:herbicide_bucket"}, {"maxStackSize": 1, "maxDamage": 641, "localized": "钢锄", "id": "immersiveengineering:hoe_steel", "toolType": "hoe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "追踪弹", "id": "immersiveengineering:homing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝锭", "id": "immersiveengineering:ingot_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜锭", "id": "immersiveengineering:ingot_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金锭", "id": "immersiveengineering:ingot_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高定向热解石墨锭", "id": "immersiveengineering:ingot_hop_graphite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅锭", "id": "immersiveengineering:ingot_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍锭", "id": "immersiveengineering:ingot_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银锭", "id": "immersiveengineering:ingot_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢锭", "id": "immersiveengineering:ingot_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀锭", "id": "immersiveengineering:ingot_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绝缘玻璃", "block": {"crop": false}, "id": "immersiveengineering:insulating_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "计量分配器", "block": {"crop": false}, "id": "immersiveengineering:item_batcher"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "油罐", "id": "immersiveengineering:jerrycan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灯", "block": {"crop": false}, "id": "immersiveengineering:lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白炽灯泡", "id": "immersiveengineering:light_bulb"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "轻型工程块", "block": {"crop": false}, "id": "immersiveengineering:light_engineering"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "避雷针", "block": {"crop": false}, "id": "immersiveengineering:lightning_rod"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "逻辑电路板", "id": "immersiveengineering:logic_circuit"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "逻辑单元", "block": {"crop": false}, "id": "immersiveengineering:logic_unit"}, {"maxStackSize": 1, "maxDamage": 50, "localized": "维护工具包", "id": "immersiveengineering:maintenance_kit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师手册", "id": "immersiveengineering:manual"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金属桶", "block": {"crop": false}, "id": "immersiveengineering:metal_barrel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脚手架梯子", "block": {"crop": false}, "id": "immersiveengineering:metal_ladder_alu"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金属梯子", "block": {"crop": false}, "id": "immersiveengineering:metal_ladder_none"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脚手架梯子", "block": {"crop": false}, "id": "immersiveengineering:metal_ladder_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金属冲压机", "block": {"crop": false}, "id": "immersiveengineering:metal_press"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属桶矿车", "id": "immersiveengineering:minecart_metalbarrel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "强化储物箱矿车", "id": "immersiveengineering:minecart_reinforcedcrate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "木桶矿车", "id": "immersiveengineering:minecart_woodenbarrel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "木制储物箱矿车", "id": "immersiveengineering:minecart_woodencrate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混合器", "block": {"crop": false}, "id": "immersiveengineering:mixer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：弹壳", "id": "immersiveengineering:mold_bullet_casing"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：齿轮", "id": "immersiveengineering:mold_gear"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：2x2压缩包", "id": "immersiveengineering:mold_packing_4"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：3x3压缩包", "id": "immersiveengineering:mold_packing_9"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：板材", "id": "immersiveengineering:mold_plate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：金属棒", "id": "immersiveengineering:mold_rod"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：解压包", "id": "immersiveengineering:mold_unpacking"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金属模具：线缆", "id": "immersiveengineering:mold_wire"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝粒", "id": "immersiveengineering:nugget_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜粒", "id": "immersiveengineering:nugget_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜粒", "id": "immersiveengineering:nugget_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金粒", "id": "immersiveengineering:nugget_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅粒", "id": "immersiveengineering:nugget_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍粒", "id": "immersiveengineering:nugget_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银粒", "id": "immersiveengineering:nugget_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢粒", "id": "immersiveengineering:nugget_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀粒", "id": "immersiveengineering:nugget_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝土矿石", "block": {"crop": false}, "id": "immersiveengineering:ore_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅矿石", "block": {"crop": false}, "id": "immersiveengineering:ore_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍矿石", "block": {"crop": false}, "id": "immersiveengineering:ore_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银矿石", "block": {"crop": false}, "id": "immersiveengineering:ore_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀矿石", "block": {"crop": false}, "id": "immersiveengineering:ore_uranium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "酚醛树脂桶", "id": "immersiveengineering:phenolic_resin_bucket"}, {"maxStackSize": 1, "maxDamage": 641, "localized": "钢镐", "id": "immersiveengineering:pickaxe_steel", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "植物油桶", "id": "immersiveengineering:plantoil_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝板", "id": "immersiveengineering:plate_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜板", "id": "immersiveengineering:plate_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜板", "id": "immersiveengineering:plate_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程塑胶板", "id": "immersiveengineering:plate_duroplast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金板", "id": "immersiveengineering:plate_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金板", "id": "immersiveengineering:plate_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁板", "id": "immersiveengineering:plate_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅板", "id": "immersiveengineering:plate_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍板", "id": "immersiveengineering:plate_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银板", "id": "immersiveengineering:plate_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢板", "id": "immersiveengineering:plate_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀板", "id": "immersiveengineering:plate_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "药水弹", "id": "immersiveengineering:potion"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "水瓶桶", "id": "immersiveengineering:potion_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蓄电池背包", "id": "immersiveengineering:powerpack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "散热器模块", "block": {"crop": false}, "id": "immersiveengineering:radiator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "磁轨炮", "id": "immersiveengineering:railgun"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铝土", "id": "immersiveengineering:raw_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铝土块", "block": {"crop": false}, "id": "immersiveengineering:raw_block_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铅块", "block": {"crop": false}, "id": "immersiveengineering:raw_block_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗镍块", "block": {"crop": false}, "id": "immersiveengineering:raw_block_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗银块", "block": {"crop": false}, "id": "immersiveengineering:raw_block_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铀块", "block": {"crop": false}, "id": "immersiveengineering:raw_block_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铅", "id": "immersiveengineering:raw_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗镍", "id": "immersiveengineering:raw_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗银", "id": "immersiveengineering:raw_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铀", "id": "immersiveengineering:raw_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "刀片刺网", "block": {"crop": false}, "id": "immersiveengineering:razor_wire"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "红石酸桶", "id": "immersiveengineering:redstone_acid_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石控制断路刀闸", "block": {"crop": false}, "id": "immersiveengineering:redstone_breaker"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "炼油厂", "block": {"crop": false}, "id": "immersiveengineering:refinery"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化储物箱", "block": {"crop": false}, "id": "immersiveengineering:reinforced_crate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "左轮手枪", "id": "immersiveengineering:revolver"}, {"maxStackSize": 1, "maxDamage": 5000, "localized": "凿岩机刀片", "id": "immersiveengineering:rockcutter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石工程块", "block": {"crop": false}, "id": "immersiveengineering:rs_engineering"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "岩芯钻井", "block": {"crop": false}, "id": "immersiveengineering:sample_drill"}, {"maxStackSize": 1, "maxDamage": 10000, "localized": "锯条", "id": "immersiveengineering:sawblade"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锯末地板", "block": {"crop": false}, "id": "immersiveengineering:sawdust"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锯木机", "block": {"crop": false}, "id": "immersiveengineering:sawmill"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师螺丝刀", "id": "immersiveengineering:screwdriver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业大麻种子", "block": {"crop": true}, "id": "immersiveengineering:seed"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "皮肤", "id": "immersiveengineering:shader"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "常见 皮肤奖励袋", "id": "immersiveengineering:shader_bag_common"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "史诗 皮肤奖励袋", "id": "immersiveengineering:shader_bag_epic"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "杰作 皮肤奖励袋", "id": "immersiveengineering:shader_bag_ie_masterwork"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "稀有 皮肤奖励袋", "id": "immersiveengineering:shader_bag_rare"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "良好 皮肤奖励袋", "id": "immersiveengineering:shader_bag_uncommon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_black"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_blue"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_brown"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_cyan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_gray"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_green"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_light_blue"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_light_gray"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_lime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_magenta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_orange"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_pink"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_purple"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_red"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_white"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_colored_yellow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀板金属", "block": {"crop": false}, "id": "immersiveengineering:sheetmetal_uranium"}, {"maxStackSize": 1, "maxDamage": 1024, "localized": "重型盾牌", "id": "immersiveengineering:shield"}, {"maxStackSize": 1, "maxDamage": 641, "localized": "钢锹", "id": "immersiveengineering:shovel_steel", "toolType": "shovel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "储物筒仓", "block": {"crop": false}, "id": "immersiveengineering:silo"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银弹", "id": "immersiveengineering:silver"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师天钩", "id": "immersiveengineering:skyhook"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "窑砖台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_alloybrick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_alu_scaffolding_grate_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_alu_scaffolding_standard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_alu_scaffolding_wooden_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高炉砖台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_blastbrick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化高炉砖台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_blastbrick_reinforced"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Clinker Brick Slab", "block": {"crop": false}, "id": "immersiveengineering:slab_clinker_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦煤台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_coke"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦炉砖台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_cokebrick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Concrete Brick Slab", "block": {"crop": false}, "id": "immersiveengineering:slab_concrete_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镀铅混凝土台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_concrete_leaded"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土瓦台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_concrete_tile"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "麻凝土台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_hempcrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Hempcrete Brick Slab", "block": {"crop": false}, "id": "immersiveengineering:slab_hempcrete_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绝缘玻璃台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_insulating_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_black"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_blue"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_brown"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_cyan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_gray"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_green"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_light_blue"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_light_gray"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_lime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_magenta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_orange"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_pink"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_purple"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_red"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_white"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_colored_yellow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀板金属台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_sheetmetal_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Slag Brick Slab", "block": {"crop": false}, "id": "immersiveengineering:slab_slag_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_steel_scaffolding_grate_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_steel_scaffolding_standard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_steel_scaffolding_wooden_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_storage_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_treated_wood_horizontal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_treated_wood_packaged"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木台阶", "block": {"crop": false}, "id": "immersiveengineering:slab_treated_wood_vertical"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矿渣", "id": "immersiveengineering:slag"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Slag Bricks", "block": {"crop": false}, "id": "immersiveengineering:slag_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矿渣玻璃", "block": {"crop": false}, "id": "immersiveengineering:slag_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矿渣砂砾", "block": {"crop": false}, "id": "immersiveengineering:slag_gravel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物品分配器", "block": {"crop": false}, "id": "immersiveengineering:sorter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "左轮快速装弹器", "id": "immersiveengineering:speedloader"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业挤压机", "block": {"crop": false}, "id": "immersiveengineering:squeezer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_alu_scaffolding_grate_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_alu_scaffolding_standard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝脚手架楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_alu_scaffolding_wooden_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Clinker Brick Stairs", "block": {"crop": false}, "id": "immersiveengineering:stairs_clinker_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Concrete Brick Stairs", "block": {"crop": false}, "id": "immersiveengineering:stairs_concrete_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镀铅混凝土楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_concrete_leaded"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "混凝土瓦楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_concrete_tile"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "麻凝土楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_hempcrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Hempcrete Brick Stairs", "block": {"crop": false}, "id": "immersiveengineering:stairs_hempcrete_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Slag Brick Stairs", "block": {"crop": false}, "id": "immersiveengineering:stairs_slag_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_steel_scaffolding_grate_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_steel_scaffolding_standard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_steel_scaffolding_wooden_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_treated_wood_horizontal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_treated_wood_packaged"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木楼梯", "block": {"crop": false}, "id": "immersiveengineering:stairs_treated_wood_vertical"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢栅栏", "block": {"crop": false}, "id": "immersiveengineering:steel_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢杆", "block": {"crop": false}, "id": "immersiveengineering:steel_post"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架", "block": {"crop": false}, "id": "immersiveengineering:steel_scaffolding_grate_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架", "block": {"crop": false}, "id": "immersiveengineering:steel_scaffolding_standard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢脚手架", "block": {"crop": false}, "id": "immersiveengineering:steel_scaffolding_wooden_top"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢结构臂", "block": {"crop": false}, "id": "immersiveengineering:steel_slope"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢壁挂支架", "block": {"crop": false}, "id": "immersiveengineering:steel_wallmount"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝棒", "id": "immersiveengineering:stick_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁棒", "id": "immersiveengineering:stick_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢棒", "id": "immersiveengineering:stick_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木棍", "id": "immersiveengineering:stick_treated"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝块", "block": {"crop": false}, "id": "immersiveengineering:storage_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "康铜块", "block": {"crop": false}, "id": "immersiveengineering:storage_constantan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金块", "block": {"crop": false}, "id": "immersiveengineering:storage_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅块", "block": {"crop": false}, "id": "immersiveengineering:storage_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镍块", "block": {"crop": false}, "id": "immersiveengineering:storage_nickel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "银块", "block": {"crop": false}, "id": "immersiveengineering:storage_silver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢块", "block": {"crop": false}, "id": "immersiveengineering:storage_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀块", "block": {"crop": false}, "id": "immersiveengineering:storage_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "条形窗帘", "block": {"crop": false}, "id": "immersiveengineering:strip_curtain"}, {"maxStackSize": 1, "maxDamage": 300, "localized": "矿物探测器", "id": "immersiveengineering:survey_tools"}, {"maxStackSize": 1, "maxDamage": 641, "localized": "钢剑", "id": "immersiveengineering:sword_steel", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流体储罐", "block": {"crop": false}, "id": "immersiveengineering:tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "特斯拉线圈", "block": {"crop": false}, "id": "immersiveengineering:tesla_coil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "热传导发电机", "block": {"crop": false}, "id": "immersiveengineering:thermoelectric_generator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师工具箱", "id": "immersiveengineering:toolbox"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "刀片盒子", "id": "immersiveengineering:toolupgrade_buzzsaw_spareblades"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "聚焦喷嘴", "id": "immersiveengineering:toolupgrade_chemthrower_focus"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学流体罐", "id": "immersiveengineering:toolupgrade_chemthrower_multitank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "大型燃料缸", "id": "immersiveengineering:toolupgrade_drill_capacity"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "附加螺旋钻", "id": "immersiveengineering:toolupgrade_drill_damage"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "岩石软化酸", "id": "immersiveengineering:toolupgrade_drill_fortune"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级润滑系统", "id": "immersiveengineering:toolupgrade_drill_lube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "压缩空气罐", "id": "immersiveengineering:toolupgrade_drill_waterproof"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "充电天线", "id": "immersiveengineering:toolupgrade_powerpack_antenna"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "感应充电器", "id": "immersiveengineering:toolupgrade_powerpack_induction"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Portable Electromagnet", "id": "immersiveengineering:toolupgrade_powerpack_magnet"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "微型特斯拉线圈", "id": "immersiveengineering:toolupgrade_powerpack_tesla"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "附加电容", "id": "immersiveengineering:toolupgrade_railgun_capacitors"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "瞄准镜", "id": "immersiveengineering:toolupgrade_railgun_scope"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "刺刀", "id": "immersiveengineering:toolupgrade_revolver_bayonet"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "加强电子管", "id": "immersiveengineering:toolupgrade_revolver_electro"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "扩充弹夹", "id": "immersiveengineering:toolupgrade_revolver_magazine"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "闪光灯", "id": "immersiveengineering:toolupgrade_shield_flash"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "磁力手套", "id": "immersiveengineering:toolupgrade_shield_magnet"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "震击发生器", "id": "immersiveengineering:toolupgrade_shield_shock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "变压器", "block": {"crop": false}, "id": "immersiveengineering:transformer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高压变压器", "block": {"crop": false}, "id": "immersiveengineering:transformer_hv"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木栅栏", "block": {"crop": false}, "id": "immersiveengineering:treated_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木杆", "block": {"crop": false}, "id": "immersiveengineering:treated_post"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木脚手架", "block": {"crop": false}, "id": "immersiveengineering:treated_scaffold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木制壁挂支架", "block": {"crop": false}, "id": "immersiveengineering:treated_wallmount"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木板", "block": {"crop": false}, "id": "immersiveengineering:treated_wood_horizontal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木板", "block": {"crop": false}, "id": "immersiveengineering:treated_wood_packaged"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "防腐木板", "block": {"crop": false}, "id": "immersiveengineering:treated_wood_vertical"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "旋转台", "block": {"crop": false}, "id": "immersiveengineering:turntable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "化学喷射炮塔", "block": {"crop": false}, "id": "immersiveengineering:turret_chem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "机枪炮塔", "block": {"crop": false}, "id": "immersiveengineering:turret_gun"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工程师多用表", "id": "immersiveengineering:voltmeter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Clinker Brick Wall", "block": {"crop": false}, "id": "immersiveengineering:wall_clinker_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Slag Brick Wall", "block": {"crop": false}, "id": "immersiveengineering:wall_slag_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水车", "block": {"crop": false}, "id": "immersiveengineering:watermill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水车部件", "id": "immersiveengineering:waterwheel_segment"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风车", "block": {"crop": false}, "id": "immersiveengineering:windmill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风车叶片", "id": "immersiveengineering:windmill_blade"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "改良风车叶片", "id": "immersiveengineering:windmill_sail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铝线", "id": "immersiveengineering:wire_aluminum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜线", "id": "immersiveengineering:wire_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "琥珀金线", "id": "immersiveengineering:wire_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅线", "id": "immersiveengineering:wire_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢线", "id": "immersiveengineering:wire_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "低压线圈", "id": "immersiveengineering:wirecoil_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绝缘低压线圈", "id": "immersiveengineering:wirecoil_copper_ins"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中压线圈", "id": "immersiveengineering:wirecoil_electrum"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绝缘中压线圈", "id": "immersiveengineering:wirecoil_electrum_ins"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石线圈", "id": "immersiveengineering:wirecoil_redstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高压线圈", "id": "immersiveengineering:wirecoil_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "麻绳线圈", "id": "immersiveengineering:wirecoil_structure_rope"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢线圈", "id": "immersiveengineering:wirecoil_structure_steel"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "工程师剪线钳", "id": "immersiveengineering:wirecutter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "狼群弹", "id": "immersiveengineering:wolfpack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木桶", "block": {"crop": false}, "id": "immersiveengineering:wooden_barrel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木握柄", "id": "immersiveengineering:wooden_grip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工程师装配台", "block": {"crop": false}, "id": "immersiveengineering:workbench"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "耐热砖", "id": "iron_making_furnace:iron_making_furnace_brick"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级箱柜", "block": {"crop": false}, "id": "mekanism:advanced_bin"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级化学品储罐", "block": {"crop": false}, "id": "mekanism:advanced_chemical_tank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级融合工厂", "block": {"crop": false}, "id": "mekanism:advanced_combining_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级压缩工厂", "block": {"crop": false}, "id": "mekanism:advanced_compressing_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级控制电路", "id": "mekanism:advanced_control_circuit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级粉碎工厂", "block": {"crop": false}, "id": "mekanism:advanced_crushing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级能量立方", "block": {"crop": false}, "id": "mekanism:advanced_energy_cube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级富集工厂", "block": {"crop": false}, "id": "mekanism:advanced_enriching_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级流体储罐", "block": {"crop": false}, "id": "mekanism:advanced_fluid_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级输导元件", "block": {"crop": false}, "id": "mekanism:advanced_induction_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级输导供应器", "block": {"crop": false}, "id": "mekanism:advanced_induction_provider"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级灌注工厂", "block": {"crop": false}, "id": "mekanism:advanced_infusing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级压射工厂", "block": {"crop": false}, "id": "mekanism:advanced_injecting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级物流管道", "block": {"crop": false}, "id": "mekanism:advanced_logistical_transporter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级机械管道", "block": {"crop": false}, "id": "mekanism:advanced_mechanical_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级加压管道", "block": {"crop": false}, "id": "mekanism:advanced_pressurized_tube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级提纯工厂", "block": {"crop": false}, "id": "mekanism:advanced_purifying_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级锯木工厂", "block": {"crop": false}, "id": "mekanism:advanced_sawing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "高级熔炼工厂", "block": {"crop": false}, "id": "mekanism:advanced_smelting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级热导线缆", "block": {"crop": false}, "id": "mekanism:advanced_thermodynamic_conductor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级工厂安装器", "id": "mekanism:advanced_tier_installer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高级通用线缆", "block": {"crop": false}, "id": "mekanism:advanced_universal_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "原子合金", "id": "mekanism:alloy_atomic"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灌注合金", "id": "mekanism:alloy_infused"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化合金", "id": "mekanism:alloy_reinforced"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "反质子核合成器", "block": {"crop": false}, "id": "mekanism:antiprotonic_nucleosynthesizer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "原子分解机", "id": "mekanism:atomic_disassembler"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础箱柜", "block": {"crop": false}, "id": "mekanism:basic_bin"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础化学品储罐", "block": {"crop": false}, "id": "mekanism:basic_chemical_tank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础融合工厂", "block": {"crop": false}, "id": "mekanism:basic_combining_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础压缩工厂", "block": {"crop": false}, "id": "mekanism:basic_compressing_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础控制电路", "id": "mekanism:basic_control_circuit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础粉碎工厂", "block": {"crop": false}, "id": "mekanism:basic_crushing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础能量立方", "block": {"crop": false}, "id": "mekanism:basic_energy_cube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础富集工厂", "block": {"crop": false}, "id": "mekanism:basic_enriching_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础流体储罐", "block": {"crop": false}, "id": "mekanism:basic_fluid_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础输导元件", "block": {"crop": false}, "id": "mekanism:basic_induction_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础输导供应器", "block": {"crop": false}, "id": "mekanism:basic_induction_provider"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础灌注工厂", "block": {"crop": false}, "id": "mekanism:basic_infusing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础压射工厂", "block": {"crop": false}, "id": "mekanism:basic_injecting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础物流管道", "block": {"crop": false}, "id": "mekanism:basic_logistical_transporter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础机械管道", "block": {"crop": false}, "id": "mekanism:basic_mechanical_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础加压管道", "block": {"crop": false}, "id": "mekanism:basic_pressurized_tube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础提纯工厂", "block": {"crop": false}, "id": "mekanism:basic_purifying_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础锯木工厂", "block": {"crop": false}, "id": "mekanism:basic_sawing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "基础熔炼工厂", "block": {"crop": false}, "id": "mekanism:basic_smelting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础热导线缆", "block": {"crop": false}, "id": "mekanism:basic_thermodynamic_conductor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础工厂安装器", "id": "mekanism:basic_tier_installer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基础通用线缆", "block": {"crop": false}, "id": "mekanism:basic_universal_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生物燃料", "id": "mekanism:bio_fuel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青铜块", "block": {"crop": false}, "id": "mekanism:block_bronze"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木炭块", "block": {"crop": false}, "id": "mekanism:block_charcoal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氟石块", "block": {"crop": false}, "id": "mekanism:block_fluorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅块", "block": {"crop": false}, "id": "mekanism:block_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇块", "block": {"crop": false}, "id": "mekanism:block_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铅块", "block": {"crop": false}, "id": "mekanism:block_raw_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗锇块", "block": {"crop": false}, "id": "mekanism:block_raw_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗锡块", "block": {"crop": false}, "id": "mekanism:block_raw_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铀块", "block": {"crop": false}, "id": "mekanism:block_raw_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化荧石", "block": {"crop": false}, "id": "mekanism:block_refined_glowstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化黑曜石", "block": {"crop": false}, "id": "mekanism:block_refined_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "盐块", "block": {"crop": false}, "id": "mekanism:block_salt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢块", "block": {"crop": false}, "id": "mekanism:block_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡块", "block": {"crop": false}, "id": "mekanism:block_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀块", "block": {"crop": false}, "id": "mekanism:block_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锅炉外壳", "block": {"crop": false}, "id": "mekanism:boiler_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锅炉阀门", "block": {"crop": false}, "id": "mekanism:boiler_valve"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绑定方块", "block": {"crop": false}, "id": "mekanism:bounding_block"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "盐水桶", "id": "mekanism:brine_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "水壶", "id": "mekanism:canteen"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "纸箱", "block": {"crop": false}, "id": "mekanism:cardboard_box"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "充电台", "block": {"crop": false}, "id": "mekanism:chargepad"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学结晶器", "block": {"crop": false}, "id": "mekanism:chemical_crystallizer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学溶解室", "block": {"crop": false}, "id": "mekanism:chemical_dissolution_chamber"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学灌注器", "block": {"crop": false}, "id": "mekanism:chemical_infuser"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学压射室", "block": {"crop": false}, "id": "mekanism:chemical_injection_chamber"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学氧化机", "block": {"crop": false}, "id": "mekanism:chemical_oxidizer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "化学清洗机", "block": {"crop": false}, "id": "mekanism:chemical_washer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态氯桶", "id": "mekanism:chlorine_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜碎块", "id": "mekanism:clump_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金碎块", "id": "mekanism:clump_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁碎块", "id": "mekanism:clump_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅碎块", "id": "mekanism:clump_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇碎块", "id": "mekanism:clump_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡碎块", "id": "mekanism:clump_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀碎块", "id": "mekanism:clump_uranium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "融合机", "block": {"crop": false}, "id": "mekanism:combiner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "配置卡", "id": "mekanism:configuration_card"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "配置器", "id": "mekanism:configurator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "合成公式", "id": "mekanism:crafting_formula"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造箱柜", "block": {"crop": false}, "id": "mekanism:creative_bin"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造化学品储罐", "block": {"crop": false}, "id": "mekanism:creative_chemical_tank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造能量立方", "block": {"crop": false}, "id": "mekanism:creative_energy_cube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "创造流体储罐", "block": {"crop": false}, "id": "mekanism:creative_fluid_tank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "粉碎机", "block": {"crop": false}, "id": "mekanism:crusher"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜结晶", "id": "mekanism:crystal_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金结晶", "id": "mekanism:crystal_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁结晶", "id": "mekanism:crystal_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅结晶", "id": "mekanism:crystal_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇结晶", "id": "mekanism:crystal_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡结晶", "id": "mekanism:crystal_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀结晶", "id": "mekanism:crystal_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层氟石矿石", "block": {"crop": false}, "id": "mekanism:deepslate_fluorite_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铅矿石", "block": {"crop": false}, "id": "mekanism:deepslate_lead_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层锇矿石", "block": {"crop": false}, "id": "mekanism:deepslate_osmium_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层锡矿石", "block": {"crop": false}, "id": "mekanism:deepslate_tin_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铀矿石", "block": {"crop": false}, "id": "mekanism:deepslate_uranium_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "辞典", "id": "mekanism:dictionary"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "数字型采矿机", "block": {"crop": false}, "id": "mekanism:digital_miner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "维度稳定锚", "block": {"crop": false}, "id": "mekanism:dimensional_stabilizer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊铜粉", "id": "mekanism:dirty_dust_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊金粉", "id": "mekanism:dirty_dust_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊铁粉", "id": "mekanism:dirty_dust_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊铅粉", "id": "mekanism:dirty_dust_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊锇粉", "id": "mekanism:dirty_dust_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊锡粉", "id": "mekanism:dirty_dust_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊铀粉", "id": "mekanism:dirty_dust_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "污浊下界合金碎片", "id": "mekanism:dirty_netherite_scrap"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "转运管道", "block": {"crop": false}, "id": "mekanism:diversion_transporter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "放射量测定器", "id": "mekanism:dosimeter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青铜粉", "id": "mekanism:dust_bronze"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木炭粉", "id": "mekanism:dust_charcoal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "煤粉", "id": "mekanism:dust_coal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜粉", "id": "mekanism:dust_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钻石粉", "id": "mekanism:dust_diamond"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿宝石粉", "id": "mekanism:dust_emerald"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氟石粉", "id": "mekanism:dust_fluorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金粉", "id": "mekanism:dust_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁粉", "id": "mekanism:dust_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青金石粉", "id": "mekanism:dust_lapis_lazuli"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅粉", "id": "mekanism:dust_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锂粉", "id": "mekanism:dust_lithium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金粉", "id": "mekanism:dust_netherite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石粉", "id": "mekanism:dust_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇粉", "id": "mekanism:dust_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英粉", "id": "mekanism:dust_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化黑曜石粉", "id": "mekanism:dust_refined_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢粉", "id": "mekanism:dust_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "硫粉", "id": "mekanism:dust_sulfur"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡粉", "id": "mekanism:dust_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀粉", "id": "mekanism:dust_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "染料基础", "id": "mekanism:dye_base"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动态储罐", "block": {"crop": false}, "id": "mekanism:dynamic_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动态储罐阀门", "block": {"crop": false}, "id": "mekanism:dynamic_valve"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "电动弓", "id": "mekanism:electric_bow", "toolType": "bow"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "电动泵", "block": {"crop": false}, "id": "mekanism:electric_pump"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "电解核心", "id": "mekanism:electrolytic_core"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "电解分离器", "block": {"crop": false}, "id": "mekanism:electrolytic_separator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英箱柜", "block": {"crop": false}, "id": "mekanism:elite_bin"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英化学品储罐", "block": {"crop": false}, "id": "mekanism:elite_chemical_tank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英融合工厂", "block": {"crop": false}, "id": "mekanism:elite_combining_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英压缩工厂", "block": {"crop": false}, "id": "mekanism:elite_compressing_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英控制电路", "id": "mekanism:elite_control_circuit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英粉碎工厂", "block": {"crop": false}, "id": "mekanism:elite_crushing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英能量立方", "block": {"crop": false}, "id": "mekanism:elite_energy_cube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英富集工厂", "block": {"crop": false}, "id": "mekanism:elite_enriching_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英流体储罐", "block": {"crop": false}, "id": "mekanism:elite_fluid_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英输导元件", "block": {"crop": false}, "id": "mekanism:elite_induction_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英输导供应器", "block": {"crop": false}, "id": "mekanism:elite_induction_provider"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英灌注工厂", "block": {"crop": false}, "id": "mekanism:elite_infusing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英压射工厂", "block": {"crop": false}, "id": "mekanism:elite_injecting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英物流管道", "block": {"crop": false}, "id": "mekanism:elite_logistical_transporter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英机械管道", "block": {"crop": false}, "id": "mekanism:elite_mechanical_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英加压管道", "block": {"crop": false}, "id": "mekanism:elite_pressurized_tube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英提纯工厂", "block": {"crop": false}, "id": "mekanism:elite_purifying_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英锯木工厂", "block": {"crop": false}, "id": "mekanism:elite_sawing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精英熔炼工厂", "block": {"crop": false}, "id": "mekanism:elite_smelting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英热导线缆", "block": {"crop": false}, "id": "mekanism:elite_thermodynamic_conductor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英工厂安装器", "id": "mekanism:elite_tier_installer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精英通用线缆", "block": {"crop": false}, "id": "mekanism:elite_universal_cable"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "电力熔炼炉", "block": {"crop": false}, "id": "mekanism:energized_smelter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "能量板", "id": "mekanism:energy_tablet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集碳", "id": "mekanism:enriched_carbon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集钻石", "id": "mekanism:enriched_diamond"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集金", "id": "mekanism:enriched_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集铁", "id": "mekanism:enriched_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集红石", "id": "mekanism:enriched_redstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集强化黑曜石", "id": "mekanism:enriched_refined_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富集锡", "id": "mekanism:enriched_tin"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "富集仓", "block": {"crop": false}, "id": "mekanism:enrichment_chamber"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态乙烯桶", "id": "mekanism:ethene_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "火焰喷射器", "id": "mekanism:flamethrower"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "流体填充机", "block": {"crop": false}, "id": "mekanism:fluidic_plenisher"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氟石", "id": "mekanism:fluorite_gem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氟石矿石", "block": {"crop": false}, "id": "mekanism:fluorite_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "公式装配器", "block": {"crop": false}, "id": "mekanism:formulaic_assemblicator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "自由助跑器", "id": "mekanism:free_runners", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "装甲自由助跑器", "id": "mekanism:free_runners_armored", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "固体燃料加热器", "block": {"crop": false}, "id": "mekanism:fuelwood_heater"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "滴管", "id": "mekanism:gauge_dropper"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "盖革计数器", "id": "mekanism:geiger_counter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "防辐射靴子", "id": "mekanism:hazmat_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "防辐射外衣", "id": "mekanism:hazmat_gown", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "防辐射面罩", "id": "mekanism:hazmat_mask", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "防辐射裤子", "id": "mekanism:hazmat_pants", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 648, "localized": "高密度聚乙烯加强鞘翅", "id": "mekanism:hdpe_elytra"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高密度聚乙烯丸", "id": "mekanism:hdpe_pellet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高密度聚乙烯棒", "id": "mekanism:hdpe_rod"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高密度聚乙烯片", "id": "mekanism:hdpe_sheet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "塑料棒", "id": "mekanism:hdpe_stick"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "重水桶", "id": "mekanism:heavy_water_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态氢氟酸桶", "id": "mekanism:hydrofluoric_acid_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态氢桶", "id": "mekanism:hydrogen_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态氯化氢桶", "id": "mekanism:hydrogen_chloride_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "输导外壳", "block": {"crop": false}, "id": "mekanism:induction_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "输导端口", "block": {"crop": false}, "id": "mekanism:induction_port"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工业报警器", "block": {"crop": false}, "id": "mekanism:industrial_alarm"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青铜锭", "id": "mekanism:ingot_bronze"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅锭", "id": "mekanism:ingot_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇锭", "id": "mekanism:ingot_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化荧石锭", "id": "mekanism:ingot_refined_glowstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化黑曜石锭", "id": "mekanism:ingot_refined_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢锭", "id": "mekanism:ingot_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡锭", "id": "mekanism:ingot_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀锭", "id": "mekanism:ingot_uranium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "同位素离心机", "block": {"crop": false}, "id": "mekanism:isotopic_centrifuge"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "喷气背包", "id": "mekanism:jetpack", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "装甲喷气背包", "id": "mekanism:jetpack_armored", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "激光", "block": {"crop": false}, "id": "mekanism:laser"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "激光增幅器", "block": {"crop": false}, "id": "mekanism:laser_amplifier"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "激光牵引光束", "block": {"crop": false}, "id": "mekanism:laser_tractor_beam"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅矿石", "block": {"crop": false}, "id": "mekanism:lead_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态锂桶", "id": "mekanism:lithium_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "物流分类机", "block": {"crop": false}, "id": "mekanism:logistical_sorter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "Meka工具", "id": "mekanism:meka_tool"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "MekaSuit护甲", "id": "mekanism:mekas<PERSON>_bodyarmor", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "MekaSuit靴子", "id": "mekanism:mekasuit_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "MekaSuit头盔", "id": "mekanism:mekasuit_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "MekaSuit裤子", "id": "mekanism:mekasuit_pants", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "冶金灌注机", "block": {"crop": false}, "id": "mekanism:metallurgic_infuser"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "改造站", "block": {"crop": false}, "id": "mekanism:modification_station"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "攻击增强单元", "id": "mekanism:module_attack_amplification_unit"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "模块基板", "id": "mekanism:module_base"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "破掘单元", "id": "mekanism:module_blasting_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "充能分配单元", "id": "mekanism:module_charge_distribution_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "颜色调变单元", "id": "mekanism:module_color_modulation_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "放射量测定器单元", "id": "mekanism:module_dosimeter_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "电解呼吸装置", "id": "mekanism:module_electrolytic_breathing_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "鞘翅单元", "id": "mekanism:module_elytra_unit"}, {"maxStackSize": 8, "maxDamage": 0, "localized": "能量单元", "id": "mekanism:module_energy_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "挖掘提升单元", "id": "mekanism:module_excavation_escalation_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "耕种单元", "id": "mekanism:module_farming_unit"}, {"maxStackSize": 3, "maxDamage": 0, "localized": "矿物精炼单元", "id": "mekanism:module_fortune_unit"}, {"maxStackSize": 2, "maxDamage": 0, "localized": "冰霜行者单元", "id": "mekanism:module_frost_walker_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "盖革计数器单元", "id": "mekanism:module_geiger_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "重力调节单元", "id": "mekanism:module_gravitational_modulating_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "陀螺稳定单元", "id": "mekanism:module_gyroscopic_stabilization_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "液压推进单元", "id": "mekanism:module_hydraulic_propulsion_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "静水推进单元", "id": "mekanism:module_hydrostatic_repulsor_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "吸入净化单元", "id": "mekanism:module_inhalation_purification_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "喷气背包单元", "id": "mekanism:module_jetpack_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "激光消散单元", "id": "mekanism:module_laser_dissipation_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "动力助推单元", "id": "mekanism:module_locomotive_boosting_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "磁力吸引装置", "id": "mekanism:module_magnetic_attraction_unit"}, {"maxStackSize": 5, "maxDamage": 0, "localized": "马达伺服单元", "id": "mekanism:module_motorized_servo_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "营养注射单元", "id": "mekanism:module_nutritional_injection_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "辐射屏蔽单元", "id": "mekanism:module_radiation_shielding_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "剪切单元", "id": "mekanism:module_shearing_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精准采集单元", "id": "mekanism:module_silk_touch_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "传送单元", "id": "mekanism:module_teleportation_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "矿脉挖掘单元", "id": "mekanism:module_vein_mining_unit"}, {"maxStackSize": 4, "maxDamage": 0, "localized": "视觉增强单元", "id": "mekanism:module_vision_enhancement_unit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "网络读取器", "id": "mekanism:network_reader"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青铜粒", "id": "mekanism:nugget_bronze"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅粒", "id": "mekanism:nugget_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇粒", "id": "mekanism:nugget_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化荧石粒", "id": "mekanism:nugget_refined_glowstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化黑曜石粒", "id": "mekanism:nugget_refined_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢粒", "id": "mekanism:nugget_steel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡粒", "id": "mekanism:nugget_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀粒", "id": "mekanism:nugget_uranium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "营养液化器", "block": {"crop": false}, "id": "mekanism:nutritional_liquifier"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "营养糊剂桶", "id": "mekanism:nutritional_paste_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "标签转换器", "block": {"crop": false}, "id": "mekanism:oredictionificator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "锇压缩机", "block": {"crop": false}, "id": "mekanism:osmium_compressor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇矿石", "block": {"crop": false}, "id": "mekanism:osmium_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态氧桶", "id": "mekanism:oxygen_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "上色机", "block": {"crop": false}, "id": "mekanism:painting_machine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "反物质靶丸", "id": "mekanism:pellet_antimatter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钚靶丸", "id": "mekanism:pellet_plutonium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钋靶丸", "id": "mekanism:pellet_polonium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "私人桶", "block": {"crop": false}, "id": "mekanism:personal_barrel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "私人箱子", "block": {"crop": false}, "id": "mekanism:personal_chest"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "颜料提取器", "block": {"crop": false}, "id": "mekanism:pigment_extractor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "颜料混合器", "block": {"crop": false}, "id": "mekanism:pigment_mixer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "便携式QIO仪表板", "id": "mekanism:portable_qio_dashboard"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "便携传送器", "id": "mekanism:portable_teleporter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "精密锯木机", "block": {"crop": false}, "id": "mekanism:precision_sawmill"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "分压元件", "block": {"crop": false}, "id": "mekanism:pressure_disperser"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "加压反应室", "block": {"crop": false}, "id": "mekanism:pressurized_reaction_chamber"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "提纯仓", "block": {"crop": false}, "id": "mekanism:purification_chamber"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "QIO仪表板", "block": {"crop": false}, "id": "mekanism:qio_dashboard"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "QIO驱动器阵列", "block": {"crop": false}, "id": "mekanism:qio_drive_array"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "QIO驱动器", "id": "mekanism:qio_drive_base"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "超密集QIO驱动器", "id": "mekanism:qio_drive_hyper_dense"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "超质量QIO驱动器", "id": "mekanism:qio_drive_supermassive"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "时间膨胀QIO驱动器", "id": "mekanism:qio_drive_time_dilating"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "QIO输出端口", "block": {"crop": false}, "id": "mekanism:qio_exporter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "QIO输入端口", "block": {"crop": false}, "id": "mekanism:qio_importer"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "QIO红石适配器", "block": {"crop": false}, "id": "mekanism:qio_redstone_adapter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "量子传送装置", "block": {"crop": false}, "id": "mekanism:quantum_entangloporter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "放射性废料桶", "block": {"crop": false}, "id": "mekanism:radioactive_waste_barrel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铅", "id": "mekanism:raw_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗锇", "id": "mekanism:raw_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗锡", "id": "mekanism:raw_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铀", "id": "mekanism:raw_uranium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "再处理裂变碎片", "id": "mekanism:reprocessed_fissile_fragment"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "电阻型加热器", "block": {"crop": false}, "id": "mekanism:resistive_heater"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "限流管道", "block": {"crop": false}, "id": "mekanism:restrictive_transporter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "机器人", "id": "mekanism:robit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "回旋式气液转换机", "block": {"crop": false}, "id": "mekanism:rotary_condensentrator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "盐", "id": "mekanism:salt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锯末", "id": "mekanism:sawdust"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "潜水面罩", "id": "mekanism:scuba_mask", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "潜水罐", "id": "mekanism:scuba_tank", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "安全设置台", "block": {"crop": false}, "id": "mekanism:security_desk"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "振动读取器", "id": "mekanism:seismic_reader"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "可控振源", "block": {"crop": false}, "id": "mekanism:seismic_vibrator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜碎片", "id": "mekanism:shard_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金碎片", "id": "mekanism:shard_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁碎片", "id": "mekanism:shard_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铅碎片", "id": "mekanism:shard_lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锇碎片", "id": "mekanism:shard_osmium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡碎片", "id": "mekanism:shard_tin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀碎片", "id": "mekanism:shard_uranium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态钠桶", "id": "mekanism:sodium_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "太阳能中子活化器", "block": {"crop": false}, "id": "mekanism:solar_neutron_activator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "超临界移相器外壳", "block": {"crop": false}, "id": "mekanism:sps_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "超临界移相器端口", "block": {"crop": false}, "id": "mekanism:sps_port"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态蒸汽桶", "id": "mekanism:steam_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢质机壳", "block": {"crop": false}, "id": "mekanism:steel_casing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结构玻璃", "block": {"crop": false}, "id": "mekanism:structural_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基片", "id": "mekanism:substrate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态二氧化硫桶", "id": "mekanism:sulfur_dioxide_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态三氧化硫桶", "id": "mekanism:sulfur_trioxide_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "硫酸桶", "id": "mekanism:sulfuric_acid_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "增压线圈", "block": {"crop": false}, "id": "mekanism:supercharged_coil"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态过热钠桶", "id": "mekanism:superheated_sodium_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发热元件", "block": {"crop": false}, "id": "mekanism:superheating_element"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "传送核心", "id": "mekanism:teleportation_core"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "传送机", "block": {"crop": false}, "id": "mekanism:teleporter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "传送框架", "block": {"crop": false}, "id": "mekanism:teleporter_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "热力蒸馏方块", "block": {"crop": false}, "id": "mekanism:thermal_evaporation_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "热力蒸馏控制器", "block": {"crop": false}, "id": "mekanism:thermal_evaporation_controller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "热力蒸馏阀门", "block": {"crop": false}, "id": "mekanism:thermal_evaporation_valve"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锡矿石", "block": {"crop": false}, "id": "mekanism:tin_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极箱柜", "block": {"crop": false}, "id": "mekanism:ultimate_bin"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极化学品储罐", "block": {"crop": false}, "id": "mekanism:ultimate_chemical_tank"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极融合工厂", "block": {"crop": false}, "id": "mekanism:ultimate_combining_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极压缩工厂", "block": {"crop": false}, "id": "mekanism:ultimate_compressing_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极控制电路", "id": "mekanism:ultimate_control_circuit"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极粉碎工厂", "block": {"crop": false}, "id": "mekanism:ultimate_crushing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极能量立方", "block": {"crop": false}, "id": "mekanism:ultimate_energy_cube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极富集工厂", "block": {"crop": false}, "id": "mekanism:ultimate_enriching_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极流体储罐", "block": {"crop": false}, "id": "mekanism:ultimate_fluid_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极输导元件", "block": {"crop": false}, "id": "mekanism:ultimate_induction_cell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极输导供应器", "block": {"crop": false}, "id": "mekanism:ultimate_induction_provider"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极灌注工厂", "block": {"crop": false}, "id": "mekanism:ultimate_infusing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极压射工厂", "block": {"crop": false}, "id": "mekanism:ultimate_injecting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极物流管道", "block": {"crop": false}, "id": "mekanism:ultimate_logistical_transporter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极机械管道", "block": {"crop": false}, "id": "mekanism:ultimate_mechanical_pipe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极加压管道", "block": {"crop": false}, "id": "mekanism:ultimate_pressurized_tube"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极提纯工厂", "block": {"crop": false}, "id": "mekanism:ultimate_purifying_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极锯木工厂", "block": {"crop": false}, "id": "mekanism:ultimate_sawing_factory"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "终极熔炼工厂", "block": {"crop": false}, "id": "mekanism:ultimate_smelting_factory"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极热导线缆", "block": {"crop": false}, "id": "mekanism:ultimate_thermodynamic_conductor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极工厂安装器", "id": "mekanism:ultimate_tier_installer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "终极通用线缆", "block": {"crop": false}, "id": "mekanism:ultimate_universal_cable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锚定升级", "id": "mekanism:upgrade_anchor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "能量升级", "id": "mekanism:upgrade_energy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "过滤升级", "id": "mekanism:upgrade_filter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "气体升级", "id": "mekanism:upgrade_gas"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "降噪升级", "id": "mekanism:upgrade_muffling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "速度升级", "id": "mekanism:upgrade_speed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "造石机升级", "id": "mekanism:upgrade_stone_generator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态六氟化铀桶", "id": "mekanism:uranium_hexafluoride_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀矿石", "block": {"crop": false}, "id": "mekanism:uranium_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态氧化铀桶", "id": "mekanism:uranium_oxide_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铀黄饼", "id": "mekanism:yellow_cake_uranium"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金合欢木船", "id": "minecraft:acacia_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木按钮", "block": {"crop": false}, "id": "minecraft:acacia_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金合欢木运输船", "id": "minecraft:acacia_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木门", "block": {"crop": false}, "id": "minecraft:acacia_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木栅栏", "block": {"crop": false}, "id": "minecraft:acacia_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木栅栏门", "block": {"crop": false}, "id": "minecraft:acacia_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式金合欢木告示牌", "block": {"crop": false}, "id": "minecraft:acacia_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢树叶", "block": {"crop": false}, "id": "minecraft:acacia_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢原木", "block": {"crop": false}, "id": "minecraft:acacia_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木板", "block": {"crop": false}, "id": "minecraft:acacia_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木压力板", "block": {"crop": false}, "id": "minecraft:acacia_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢树苗", "block": {"crop": false}, "id": "minecraft:acacia_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "金合欢木告示牌", "block": {"crop": false}, "id": "minecraft:acacia_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木台阶", "block": {"crop": false}, "id": "minecraft:acacia_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木楼梯", "block": {"crop": false}, "id": "minecraft:acacia_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木活板门", "block": {"crop": false}, "id": "minecraft:acacia_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金合欢木", "block": {"crop": false}, "id": "minecraft:acacia_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "激活铁轨", "block": {"crop": false}, "id": "minecraft:activator_rail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空气", "id": "minecraft:air"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "悦灵刷怪蛋", "id": "minecraft:allay_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绒球葱", "block": {"crop": false}, "id": "minecraft:allium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶块", "block": {"crop": false}, "id": "minecraft:amethyst_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶簇", "block": {"crop": false}, "id": "minecraft:amethyst_cluster"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶碎片", "id": "minecraft:amethyst_shard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "远古残骸", "block": {"crop": false}, "id": "minecraft:ancient_debris"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩", "block": {"crop": false}, "id": "minecraft:andesite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩台阶", "block": {"crop": false}, "id": "minecraft:andesite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩楼梯", "block": {"crop": false}, "id": "minecraft:andesite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山岩墙", "block": {"crop": false}, "id": "minecraft:andesite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "垂钓纹样陶片", "id": "minecraft:angler_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁砧", "block": {"crop": false}, "id": "minecraft:anvil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苹果", "id": "minecraft:apple", "food": {"saturation": 0.3, "nutrition": 4, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓箭纹样陶片", "id": "minecraft:archer_pottery_sherd"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "盔甲架", "id": "minecraft:armor_stand"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "举臂纹样陶片", "id": "minecraft:arms_up_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "箭", "id": "minecraft:arrow"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "美西螈桶", "id": "minecraft:axolotl_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "美西螈刷怪蛋", "id": "minecraft:axolotl_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "杜鹃花丛", "block": {"crop": false}, "id": "minecraft:azalea"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "杜鹃树叶", "block": {"crop": false}, "id": "minecraft:azalea_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝花美耳草", "block": {"crop": false}, "id": "minecraft:azure_bluet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烤马铃薯", "id": "minecraft:baked_potato", "food": {"saturation": 0.6, "nutrition": 5, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹子", "block": {"crop": false}, "id": "minecraft:bamboo"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹块", "block": {"crop": false}, "id": "minecraft:bamboo_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹按钮", "block": {"crop": false}, "id": "minecraft:bamboo_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "运输竹筏", "id": "minecraft:bamboo_chest_raft"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹门", "block": {"crop": false}, "id": "minecraft:bamboo_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹栅栏", "block": {"crop": false}, "id": "minecraft:bamboo_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹栅栏门", "block": {"crop": false}, "id": "minecraft:bamboo_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式竹告示牌", "block": {"crop": false}, "id": "minecraft:bamboo_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹马赛克", "block": {"crop": false}, "id": "minecraft:bamboo_mosaic"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹马赛克台阶", "block": {"crop": false}, "id": "minecraft:bamboo_mosaic_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹马赛克楼梯", "block": {"crop": false}, "id": "minecraft:bamboo_mosaic_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹板", "block": {"crop": false}, "id": "minecraft:bamboo_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹压力板", "block": {"crop": false}, "id": "minecraft:bamboo_pressure_plate"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "竹筏", "id": "minecraft:bamboo_raft"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "竹告示牌", "block": {"crop": false}, "id": "minecraft:bamboo_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹台阶", "block": {"crop": false}, "id": "minecraft:bamboo_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹楼梯", "block": {"crop": false}, "id": "minecraft:bamboo_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹活板门", "block": {"crop": false}, "id": "minecraft:bamboo_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木桶", "block": {"crop": false}, "id": "minecraft:barrel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "屏障", "block": {"crop": false}, "id": "minecraft:barrier"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玄武岩", "block": {"crop": false}, "id": "minecraft:basalt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蝙蝠刷怪蛋", "id": "minecraft:bat_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "信标", "block": {"crop": false}, "id": "minecraft:beacon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "基岩", "block": {"crop": false}, "id": "minecraft:bedrock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜂巢", "block": {"crop": false}, "id": "minecraft:bee_nest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜜蜂刷怪蛋", "id": "minecraft:bee_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生牛肉", "id": "minecraft:beef", "food": {"saturation": 0.3, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜂箱", "block": {"crop": false}, "id": "minecraft:beehive"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "甜菜根", "id": "minecraft:beetroot", "food": {"saturation": 0.6, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "甜菜种子", "block": {"crop": true}, "id": "minecraft:beetroot_seeds"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "甜菜汤", "id": "minecraft:beetroot_soup", "food": {"saturation": 0.6, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钟", "block": {"crop": false}, "id": "minecraft:bell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型垂滴叶", "block": {"crop": false}, "id": "minecraft:big_dripleaf"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "白桦木船", "id": "minecraft:birch_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木按钮", "block": {"crop": false}, "id": "minecraft:birch_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "白桦木运输船", "id": "minecraft:birch_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木门", "block": {"crop": false}, "id": "minecraft:birch_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木栅栏", "block": {"crop": false}, "id": "minecraft:birch_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木栅栏门", "block": {"crop": false}, "id": "minecraft:birch_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式白桦木告示牌", "block": {"crop": false}, "id": "minecraft:birch_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦树叶", "block": {"crop": false}, "id": "minecraft:birch_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦原木", "block": {"crop": false}, "id": "minecraft:birch_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木板", "block": {"crop": false}, "id": "minecraft:birch_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木压力板", "block": {"crop": false}, "id": "minecraft:birch_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦树苗", "block": {"crop": false}, "id": "minecraft:birch_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "白桦木告示牌", "block": {"crop": false}, "id": "minecraft:birch_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木台阶", "block": {"crop": false}, "id": "minecraft:birch_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木楼梯", "block": {"crop": false}, "id": "minecraft:birch_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木活板门", "block": {"crop": false}, "id": "minecraft:birch_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白桦木", "block": {"crop": false}, "id": "minecraft:birch_wood"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "黑色旗帜", "block": {"crop": false}, "id": "minecraft:black_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黑色床", "block": {"crop": false}, "id": "minecraft:black_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色蜡烛", "block": {"crop": false}, "id": "minecraft:black_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色地毯", "block": {"crop": false}, "id": "minecraft:black_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色混凝土", "block": {"crop": false}, "id": "minecraft:black_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色混凝土粉末", "block": {"crop": false}, "id": "minecraft:black_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色染料", "id": "minecraft:black_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:black_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黑色潜影盒", "block": {"crop": false}, "id": "minecraft:black_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色染色玻璃", "block": {"crop": false}, "id": "minecraft:black_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色染色玻璃板", "block": {"crop": false}, "id": "minecraft:black_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色陶瓦", "block": {"crop": false}, "id": "minecraft:black_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色羊毛", "block": {"crop": false}, "id": "minecraft:black_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑石", "block": {"crop": false}, "id": "minecraft:blackstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑石台阶", "block": {"crop": false}, "id": "minecraft:blackstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑石楼梯", "block": {"crop": false}, "id": "minecraft:blackstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑石墙", "block": {"crop": false}, "id": "minecraft:blackstone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "利刃纹样陶片", "id": "minecraft:blade_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高炉", "block": {"crop": false}, "id": "minecraft:blast_furnace"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰粉", "id": "minecraft:blaze_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰棒", "id": "minecraft:blaze_rod"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰人刷怪蛋", "id": "minecraft:blaze_spawn_egg"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "蓝色旗帜", "block": {"crop": false}, "id": "minecraft:blue_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蓝色床", "block": {"crop": false}, "id": "minecraft:blue_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色蜡烛", "block": {"crop": false}, "id": "minecraft:blue_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色地毯", "block": {"crop": false}, "id": "minecraft:blue_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色混凝土", "block": {"crop": false}, "id": "minecraft:blue_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色混凝土粉末", "block": {"crop": false}, "id": "minecraft:blue_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色染料", "id": "minecraft:blue_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:blue_glazed_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝冰", "block": {"crop": false}, "id": "minecraft:blue_ice"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "兰花", "block": {"crop": false}, "id": "minecraft:blue_orchid"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蓝色潜影盒", "block": {"crop": false}, "id": "minecraft:blue_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色染色玻璃", "block": {"crop": false}, "id": "minecraft:blue_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色染色玻璃板", "block": {"crop": false}, "id": "minecraft:blue_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色陶瓦", "block": {"crop": false}, "id": "minecraft:blue_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色羊毛", "block": {"crop": false}, "id": "minecraft:blue_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骨头", "id": "minecraft:bone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骨块", "block": {"crop": false}, "id": "minecraft:bone_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骨粉", "id": "minecraft:bone_meal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "书", "id": "minecraft:book"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木书架", "block": {"crop": false}, "id": "minecraft:bookshelf"}, {"maxStackSize": 1, "maxDamage": 384, "localized": "弓", "id": "minecraft:bow", "toolType": "bow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碗", "id": "minecraft:bowl"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脑纹珊瑚", "block": {"crop": false}, "id": "minecraft:brain_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脑纹珊瑚块", "block": {"crop": false}, "id": "minecraft:brain_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脑纹珊瑚扇", "block": {"crop": false}, "id": "minecraft:brain_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "面包", "id": "minecraft:bread", "food": {"saturation": 0.6, "nutrition": 5, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "佳酿纹样陶片", "id": "minecraft:brewer_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "酿造台", "block": {"crop": false}, "id": "minecraft:brewing_stand"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砖", "id": "minecraft:brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砖台阶", "block": {"crop": false}, "id": "minecraft:brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砖楼梯", "block": {"crop": false}, "id": "minecraft:brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砖墙", "block": {"crop": false}, "id": "minecraft:brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砖块", "block": {"crop": false}, "id": "minecraft:bricks"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "棕色旗帜", "block": {"crop": false}, "id": "minecraft:brown_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "棕色床", "block": {"crop": false}, "id": "minecraft:brown_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色蜡烛", "block": {"crop": false}, "id": "minecraft:brown_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色地毯", "block": {"crop": false}, "id": "minecraft:brown_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色混凝土", "block": {"crop": false}, "id": "minecraft:brown_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色混凝土粉末", "block": {"crop": false}, "id": "minecraft:brown_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色染料", "id": "minecraft:brown_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:brown_glazed_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色蘑菇", "block": {"crop": false}, "id": "minecraft:brown_mushroom"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色蘑菇方块", "block": {"crop": false}, "id": "minecraft:brown_mushroom_block"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "棕色潜影盒", "block": {"crop": false}, "id": "minecraft:brown_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色染色玻璃", "block": {"crop": false}, "id": "minecraft:brown_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色染色玻璃板", "block": {"crop": false}, "id": "minecraft:brown_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色陶瓦", "block": {"crop": false}, "id": "minecraft:brown_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色羊毛", "block": {"crop": false}, "id": "minecraft:brown_wool"}, {"maxStackSize": 1, "maxDamage": 64, "localized": "刷子", "id": "minecraft:brush"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "气泡珊瑚", "block": {"crop": false}, "id": "minecraft:bubble_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "气泡珊瑚块", "block": {"crop": false}, "id": "minecraft:bubble_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "气泡珊瑚扇", "block": {"crop": false}, "id": "minecraft:bubble_coral_fan"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "铁桶", "id": "minecraft:bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶母岩", "block": {"crop": false}, "id": "minecraft:budding_amethyst"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "收纳袋", "id": "minecraft:bundle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰纹样陶片", "id": "minecraft:burn_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "仙人掌", "block": {"crop": false}, "id": "minecraft:cactus"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蛋糕", "block": {"crop": false}, "id": "minecraft:cake"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "方解石", "block": {"crop": false}, "id": "minecraft:calcite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "校频幽匿感测体", "block": {"crop": false}, "id": "minecraft:calibrated_sculk_sensor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骆驼刷怪蛋", "id": "minecraft:camel_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "营火", "block": {"crop": false}, "id": "minecraft:campfire"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜡烛", "block": {"crop": false}, "id": "minecraft:candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "胡萝卜", "block": {"crop": true}, "id": "minecraft:carrot", "food": {"saturation": 0.6, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 25, "localized": "胡萝卜钓竿", "id": "minecraft:carrot_on_a_stick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "制图台", "block": {"crop": false}, "id": "minecraft:cartography_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕刻南瓜", "block": {"crop": false}, "id": "minecraft:carved_pumpkin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "猫刷怪蛋", "id": "minecraft:cat_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "炼药锅", "block": {"crop": false}, "id": "minecraft:cauldron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "洞穴蜘蛛刷怪蛋", "id": "minecraft:cave_spider_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁链", "block": {"crop": false}, "id": "minecraft:chain"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "连锁型命令方块", "block": {"crop": false}, "id": "minecraft:chain_command_block"}, {"maxStackSize": 1, "maxDamage": 195, "localized": "锁链靴子", "id": "minecraft:chainmail_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 240, "localized": "锁链胸甲", "id": "minecraft:chainmail_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 165, "localized": "锁链头盔", "id": "minecraft:chainmail_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 225, "localized": "锁链护腿", "id": "minecraft:chainmail_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木炭", "id": "minecraft:charcoal"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "樱花木船", "id": "minecraft:cherry_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木按钮", "block": {"crop": false}, "id": "minecraft:cherry_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "樱花木运输船", "id": "minecraft:cherry_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木门", "block": {"crop": false}, "id": "minecraft:cherry_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木栅栏", "block": {"crop": false}, "id": "minecraft:cherry_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木栅栏门", "block": {"crop": false}, "id": "minecraft:cherry_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式樱花木告示牌", "block": {"crop": false}, "id": "minecraft:cherry_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花树叶", "block": {"crop": false}, "id": "minecraft:cherry_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花原木", "block": {"crop": false}, "id": "minecraft:cherry_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木板", "block": {"crop": false}, "id": "minecraft:cherry_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木压力板", "block": {"crop": false}, "id": "minecraft:cherry_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花树苗", "block": {"crop": false}, "id": "minecraft:cherry_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "樱花木告示牌", "block": {"crop": false}, "id": "minecraft:cherry_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木台阶", "block": {"crop": false}, "id": "minecraft:cherry_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木楼梯", "block": {"crop": false}, "id": "minecraft:cherry_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木活板门", "block": {"crop": false}, "id": "minecraft:cherry_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木", "block": {"crop": false}, "id": "minecraft:cherry_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "箱子", "block": {"crop": false}, "id": "minecraft:chest"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "运输矿车", "id": "minecraft:chest_minecart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生鸡肉", "id": "minecraft:chicken", "food": {"saturation": 0.3, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鸡刷怪蛋", "id": "minecraft:chicken_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "开裂的铁砧", "block": {"crop": false}, "id": "minecraft:chipped_anvil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹书架", "block": {"crop": false}, "id": "minecraft:chiseled_bookshelf"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹深板岩", "block": {"crop": false}, "id": "minecraft:chiseled_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹下界砖块", "block": {"crop": false}, "id": "minecraft:chiseled_nether_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹磨制黑石", "block": {"crop": false}, "id": "minecraft:chiseled_polished_blackstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹石英块", "block": {"crop": false}, "id": "minecraft:chiseled_quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹红砂岩", "block": {"crop": false}, "id": "minecraft:chiseled_red_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹砂岩", "block": {"crop": false}, "id": "minecraft:chiseled_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹石砖", "block": {"crop": false}, "id": "minecraft:chiseled_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂花", "block": {"crop": false}, "id": "minecraft:chorus_flower"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂果", "id": "minecraft:chorus_fruit", "food": {"saturation": 0.3, "nutrition": 4, "alwaysEdible": true}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫颂植株", "block": {"crop": false}, "id": "minecraft:chorus_plant"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏土", "block": {"crop": false}, "id": "minecraft:clay"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏土球", "id": "minecraft:clay_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "时钟", "id": "minecraft:clock"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "煤炭", "id": "minecraft:coal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "煤炭块", "block": {"crop": false}, "id": "minecraft:coal_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "煤矿石", "block": {"crop": false}, "id": "minecraft:coal_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂土", "block": {"crop": false}, "id": "minecraft:coarse_dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:coast_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩圆石", "block": {"crop": false}, "id": "minecraft:cobbled_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩圆石台阶", "block": {"crop": false}, "id": "minecraft:cobbled_deepslate_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩圆石楼梯", "block": {"crop": false}, "id": "minecraft:cobbled_deepslate_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩圆石墙", "block": {"crop": false}, "id": "minecraft:cobbled_deepslate_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "圆石", "block": {"crop": false}, "id": "minecraft:cobblestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "圆石台阶", "block": {"crop": false}, "id": "minecraft:cobblestone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "圆石楼梯", "block": {"crop": false}, "id": "minecraft:cobblestone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "圆石墙", "block": {"crop": false}, "id": "minecraft:cobblestone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜘蛛网", "block": {"crop": false}, "id": "minecraft:cobweb"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可可豆", "block": {"crop": false}, "id": "minecraft:cocoa_beans"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生鳕鱼", "id": "minecraft:cod", "food": {"saturation": 0.1, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "鳕鱼桶", "id": "minecraft:cod_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鳕鱼刷怪蛋", "id": "minecraft:cod_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "命令方块", "block": {"crop": false}, "id": "minecraft:command_block"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "命令方块矿车", "id": "minecraft:command_block_minecart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石比较器", "block": {"crop": false}, "id": "minecraft:comparator"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "指南针", "id": "minecraft:compass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "堆肥桶", "block": {"crop": false}, "id": "minecraft:composter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "潮涌核心", "block": {"crop": false}, "id": "minecraft:conduit"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "牛排", "id": "minecraft:cooked_beef", "food": {"saturation": 0.8, "nutrition": 8, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熟鸡肉", "id": "minecraft:cooked_chicken", "food": {"saturation": 0.6, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熟鳕鱼", "id": "minecraft:cooked_cod", "food": {"saturation": 0.6, "nutrition": 5, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熟羊肉", "id": "minecraft:cooked_mutton", "food": {"saturation": 0.8, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熟猪排", "id": "minecraft:cooked_porkchop", "food": {"saturation": 0.8, "nutrition": 8, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熟兔肉", "id": "minecraft:cooked_rabbit", "food": {"saturation": 0.6, "nutrition": 5, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熟鲑鱼", "id": "minecraft:cooked_salmon", "food": {"saturation": 0.8, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "曲奇", "id": "minecraft:cookie", "food": {"saturation": 0.1, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜块", "block": {"crop": false}, "id": "minecraft:copper_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜锭", "id": "minecraft:copper_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜矿石", "block": {"crop": false}, "id": "minecraft:copper_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "矢车菊", "block": {"crop": false}, "id": "minecraft:cornflower"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "牛刷怪蛋", "id": "minecraft:cow_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "裂纹深板岩砖", "block": {"crop": false}, "id": "minecraft:cracked_deepslate_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "裂纹深板岩瓦", "block": {"crop": false}, "id": "minecraft:cracked_deepslate_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "裂纹下界砖块", "block": {"crop": false}, "id": "minecraft:cracked_nether_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "裂纹磨制黑石砖", "block": {"crop": false}, "id": "minecraft:cracked_polished_blackstone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "裂纹石砖", "block": {"crop": false}, "id": "minecraft:cracked_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木工作台", "block": {"crop": false}, "id": "minecraft:crafting_table"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "旗帜图案", "id": "minecraft:creeper_banner_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苦力怕的头", "block": {"crop": false}, "id": "minecraft:creeper_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苦力怕刷怪蛋", "id": "minecraft:creeper_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木按钮", "block": {"crop": false}, "id": "minecraft:crimson_button"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木门", "block": {"crop": false}, "id": "minecraft:crimson_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木栅栏", "block": {"crop": false}, "id": "minecraft:crimson_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木栅栏门", "block": {"crop": false}, "id": "minecraft:crimson_fence_gate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红菌", "block": {"crop": false}, "id": "minecraft:crimson_fungus"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式绯红木告示牌", "block": {"crop": false}, "id": "minecraft:crimson_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红菌核", "block": {"crop": false}, "id": "minecraft:crimson_hyphae"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红菌岩", "block": {"crop": false}, "id": "minecraft:crimson_nylium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木板", "block": {"crop": false}, "id": "minecraft:crimson_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木压力板", "block": {"crop": false}, "id": "minecraft:crimson_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红菌索", "block": {"crop": false}, "id": "minecraft:crimson_roots"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "绯红木告示牌", "block": {"crop": false}, "id": "minecraft:crimson_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木台阶", "block": {"crop": false}, "id": "minecraft:crimson_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木楼梯", "block": {"crop": false}, "id": "minecraft:crimson_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红菌柄", "block": {"crop": false}, "id": "minecraft:crimson_stem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绯红木活板门", "block": {"crop": false}, "id": "minecraft:crimson_trapdoor"}, {"maxStackSize": 1, "maxDamage": 465, "localized": "弩", "id": "minecraft:crossbow", "toolType": "crossbow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "哭泣的黑曜石", "block": {"crop": false}, "id": "minecraft:crying_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制铜块", "block": {"crop": false}, "id": "minecraft:cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制铜台阶", "block": {"crop": false}, "id": "minecraft:cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制铜楼梯", "block": {"crop": false}, "id": "minecraft:cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制红砂岩", "block": {"crop": false}, "id": "minecraft:cut_red_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制红砂岩台阶", "block": {"crop": false}, "id": "minecraft:cut_red_sandstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制砂岩", "block": {"crop": false}, "id": "minecraft:cut_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切制砂岩台阶", "block": {"crop": false}, "id": "minecraft:cut_sandstone_slab"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "青色旗帜", "block": {"crop": false}, "id": "minecraft:cyan_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "青色床", "block": {"crop": false}, "id": "minecraft:cyan_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色蜡烛", "block": {"crop": false}, "id": "minecraft:cyan_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色地毯", "block": {"crop": false}, "id": "minecraft:cyan_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色混凝土", "block": {"crop": false}, "id": "minecraft:cyan_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色混凝土粉末", "block": {"crop": false}, "id": "minecraft:cyan_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色染料", "id": "minecraft:cyan_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:cyan_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "青色潜影盒", "block": {"crop": false}, "id": "minecraft:cyan_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色染色玻璃", "block": {"crop": false}, "id": "minecraft:cyan_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色染色玻璃板", "block": {"crop": false}, "id": "minecraft:cyan_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色陶瓦", "block": {"crop": false}, "id": "minecraft:cyan_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色羊毛", "block": {"crop": false}, "id": "minecraft:cyan_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "损坏的铁砧", "block": {"crop": false}, "id": "minecraft:damaged_anvil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蒲公英", "block": {"crop": false}, "id": "minecraft:dandelion"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "危机纹样陶片", "id": "minecraft:danger_pottery_sherd"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "深色橡木船", "id": "minecraft:dark_oak_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木按钮", "block": {"crop": false}, "id": "minecraft:dark_oak_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "深色橡木运输船", "id": "minecraft:dark_oak_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木门", "block": {"crop": false}, "id": "minecraft:dark_oak_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木栅栏", "block": {"crop": false}, "id": "minecraft:dark_oak_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木栅栏门", "block": {"crop": false}, "id": "minecraft:dark_oak_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式深色橡木告示牌", "block": {"crop": false}, "id": "minecraft:dark_oak_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡树树叶", "block": {"crop": false}, "id": "minecraft:dark_oak_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木原木", "block": {"crop": false}, "id": "minecraft:dark_oak_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木木板", "block": {"crop": false}, "id": "minecraft:dark_oak_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木压力板", "block": {"crop": false}, "id": "minecraft:dark_oak_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡树树苗", "block": {"crop": false}, "id": "minecraft:dark_oak_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "深色橡木告示牌", "block": {"crop": false}, "id": "minecraft:dark_oak_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木台阶", "block": {"crop": false}, "id": "minecraft:dark_oak_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木楼梯", "block": {"crop": false}, "id": "minecraft:dark_oak_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木活板门", "block": {"crop": false}, "id": "minecraft:dark_oak_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深色橡木", "block": {"crop": false}, "id": "minecraft:dark_oak_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "暗海晶石", "block": {"crop": false}, "id": "minecraft:dark_prismarine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "暗海晶石台阶", "block": {"crop": false}, "id": "minecraft:dark_prismarine_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "暗海晶石楼梯", "block": {"crop": false}, "id": "minecraft:dark_prismarine_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "阳光探测器", "block": {"crop": false}, "id": "minecraft:daylight_detector"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的脑纹珊瑚", "block": {"crop": false}, "id": "minecraft:dead_brain_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的脑纹珊瑚块", "block": {"crop": false}, "id": "minecraft:dead_brain_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的脑纹珊瑚扇", "block": {"crop": false}, "id": "minecraft:dead_brain_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的气泡珊瑚", "block": {"crop": false}, "id": "minecraft:dead_bubble_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的气泡珊瑚块", "block": {"crop": false}, "id": "minecraft:dead_bubble_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的气泡珊瑚扇", "block": {"crop": false}, "id": "minecraft:dead_bubble_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "枯萎的灌木", "block": {"crop": false}, "id": "minecraft:dead_bush"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的火珊瑚", "block": {"crop": false}, "id": "minecraft:dead_fire_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的火珊瑚块", "block": {"crop": false}, "id": "minecraft:dead_fire_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的火珊瑚扇", "block": {"crop": false}, "id": "minecraft:dead_fire_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的鹿角珊瑚", "block": {"crop": false}, "id": "minecraft:dead_horn_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的鹿角珊瑚块", "block": {"crop": false}, "id": "minecraft:dead_horn_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的鹿角珊瑚扇", "block": {"crop": false}, "id": "minecraft:dead_horn_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的管珊瑚", "block": {"crop": false}, "id": "minecraft:dead_tube_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的管珊瑚块", "block": {"crop": false}, "id": "minecraft:dead_tube_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "失活的管珊瑚扇", "block": {"crop": false}, "id": "minecraft:dead_tube_coral_fan"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "调试棒", "id": "minecraft:debug_stick"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "饰纹陶罐", "block": {"crop": false}, "id": "minecraft:decorated_pot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩", "block": {"crop": false}, "id": "minecraft:deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩砖台阶", "block": {"crop": false}, "id": "minecraft:deepslate_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩砖楼梯", "block": {"crop": false}, "id": "minecraft:deepslate_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩砖墙", "block": {"crop": false}, "id": "minecraft:deepslate_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩砖", "block": {"crop": false}, "id": "minecraft:deepslate_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层煤矿石", "block": {"crop": false}, "id": "minecraft:deepslate_coal_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铜矿石", "block": {"crop": false}, "id": "minecraft:deepslate_copper_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层钻石矿石", "block": {"crop": false}, "id": "minecraft:deepslate_diamond_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层绿宝石矿石", "block": {"crop": false}, "id": "minecraft:deepslate_emerald_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层金矿石", "block": {"crop": false}, "id": "minecraft:deepslate_gold_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层铁矿石", "block": {"crop": false}, "id": "minecraft:deepslate_iron_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层青金石矿石", "block": {"crop": false}, "id": "minecraft:deepslate_lapis_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深层红石矿石", "block": {"crop": false}, "id": "minecraft:deepslate_redstone_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩瓦台阶", "block": {"crop": false}, "id": "minecraft:deepslate_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩瓦楼梯", "block": {"crop": false}, "id": "minecraft:deepslate_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩瓦墙", "block": {"crop": false}, "id": "minecraft:deepslate_tile_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "深板岩瓦", "block": {"crop": false}, "id": "minecraft:deepslate_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "探测铁轨", "block": {"crop": false}, "id": "minecraft:detector_rail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钻石", "id": "minecraft:diamond"}, {"maxStackSize": 1, "maxDamage": 1561, "localized": "钻石斧", "id": "minecraft:diamond_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钻石块", "block": {"crop": false}, "id": "minecraft:diamond_block"}, {"maxStackSize": 1, "maxDamage": 429, "localized": "钻石靴子", "id": "minecraft:diamond_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 528, "localized": "钻石胸甲", "id": "minecraft:diamond_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 363, "localized": "钻石头盔", "id": "minecraft:diamond_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1561, "localized": "钻石锄", "id": "minecraft:diamond_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "钻石马铠", "id": "minecraft:diamond_horse_armor"}, {"maxStackSize": 1, "maxDamage": 495, "localized": "钻石护腿", "id": "minecraft:diamond_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钻石矿石", "block": {"crop": false}, "id": "minecraft:diamond_ore"}, {"maxStackSize": 1, "maxDamage": 1561, "localized": "钻石镐", "id": "minecraft:diamond_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 1561, "localized": "钻石锹", "id": "minecraft:diamond_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 1561, "localized": "钻石剑", "id": "minecraft:diamond_sword", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩", "block": {"crop": false}, "id": "minecraft:diorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩台阶", "block": {"crop": false}, "id": "minecraft:diorite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩楼梯", "block": {"crop": false}, "id": "minecraft:diorite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪长岩墙", "block": {"crop": false}, "id": "minecraft:diorite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥土", "block": {"crop": false}, "id": "minecraft:dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "土径", "block": {"crop": false}, "id": "minecraft:dirt_path"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "唱片残片", "id": "minecraft:disc_fragment_5"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发射器", "block": {"crop": false}, "id": "minecraft:dispenser"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海豚刷怪蛋", "id": "minecraft:dolphin_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "驴刷怪蛋", "id": "minecraft:donkey_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "龙息", "id": "minecraft:dragon_breath"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "龙蛋", "block": {"crop": false}, "id": "minecraft:dragon_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "龙首", "block": {"crop": false}, "id": "minecraft:dragon_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "干海带", "id": "minecraft:dried_kelp", "food": {"saturation": 0.3, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "干海带块", "block": {"crop": false}, "id": "minecraft:dried_kelp_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石块", "block": {"crop": false}, "id": "minecraft:dripstone_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "投掷器", "block": {"crop": false}, "id": "minecraft:dropper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "溺尸刷怪蛋", "id": "minecraft:drowned_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:dune_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "回响碎片", "id": "minecraft:echo_shard"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "鸡蛋", "id": "minecraft:egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "远古守卫者刷怪蛋", "id": "minecraft:elder_guardian_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 432, "localized": "鞘翅", "id": "minecraft:elytra"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿宝石", "id": "minecraft:emerald"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿宝石块", "block": {"crop": false}, "id": "minecraft:emerald_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿宝石矿石", "block": {"crop": false}, "id": "minecraft:emerald_ore"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "附魔书", "id": "minecraft:enchanted_book"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "附魔金苹果", "id": "minecraft:enchanted_golden_apple", "food": {"saturation": 1.2, "nutrition": 4, "alwaysEdible": true}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "附魔台", "block": {"crop": false}, "id": "minecraft:enchanting_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地水晶", "id": "minecraft:end_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地传送门框架", "block": {"crop": false}, "id": "minecraft:end_portal_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地烛", "block": {"crop": false}, "id": "minecraft:end_rod"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石", "block": {"crop": false}, "id": "minecraft:end_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石砖台阶", "block": {"crop": false}, "id": "minecraft:end_stone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石砖楼梯", "block": {"crop": false}, "id": "minecraft:end_stone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石砖墙", "block": {"crop": false}, "id": "minecraft:end_stone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末地石砖", "block": {"crop": false}, "id": "minecraft:end_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影箱", "block": {"crop": false}, "id": "minecraft:ender_chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影龙刷怪蛋", "id": "minecraft:ender_dragon_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影之眼", "id": "minecraft:ender_eye"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "末影珍珠", "id": "minecraft:ender_pearl"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影人刷怪蛋", "id": "minecraft:enderman_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影螨刷怪蛋", "id": "minecraft:endermite_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "唤魔者刷怪蛋", "id": "minecraft:evoker_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "附魔之瓶", "id": "minecraft:experience_bottle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "探险纹样陶片", "id": "minecraft:explorer_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜块", "block": {"crop": false}, "id": "minecraft:exposed_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的切制铜块", "block": {"crop": false}, "id": "minecraft:exposed_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的切制铜台阶", "block": {"crop": false}, "id": "minecraft:exposed_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的切制铜楼梯", "block": {"crop": false}, "id": "minecraft:exposed_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:eye_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "耕地", "block": {"crop": false}, "id": "minecraft:farmland"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "羽毛", "id": "minecraft:feather"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发酵蛛眼", "id": "minecraft:fermented_spider_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蕨", "block": {"crop": false}, "id": "minecraft:fern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "地图", "id": "minecraft:filled_map"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火焰弹", "id": "minecraft:fire_charge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火珊瑚", "block": {"crop": false}, "id": "minecraft:fire_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火珊瑚块", "block": {"crop": false}, "id": "minecraft:fire_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火珊瑚扇", "block": {"crop": false}, "id": "minecraft:fire_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烟花火箭", "id": "minecraft:firework_rocket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烟火之星", "id": "minecraft:firework_star"}, {"maxStackSize": 1, "maxDamage": 64, "localized": "钓鱼竿", "id": "minecraft:fishing_rod"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "制箭台", "block": {"crop": false}, "id": "minecraft:fletching_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "燧石", "id": "minecraft:flint"}, {"maxStackSize": 1, "maxDamage": 64, "localized": "打火石", "id": "minecraft:flint_and_steel"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "旗帜图案", "id": "minecraft:flower_banner_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花盆", "block": {"crop": false}, "id": "minecraft:flower_pot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "盛开的杜鹃花丛", "block": {"crop": false}, "id": "minecraft:flowering_azalea"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "盛开的杜鹃树叶", "block": {"crop": false}, "id": "minecraft:flowering_azalea_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "狐狸刷怪蛋", "id": "minecraft:fox_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "挚友纹样陶片", "id": "minecraft:friend_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青蛙刷怪蛋", "id": "minecraft:frog_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青蛙卵", "block": {"crop": false}, "id": "minecraft:frogspawn"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔炉", "block": {"crop": false}, "id": "minecraft:furnace"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "动力矿车", "id": "minecraft:furnace_minecart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "恶魂刷怪蛋", "id": "minecraft:ghast_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "恶魂之泪", "id": "minecraft:ghast_tear"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镶金黑石", "block": {"crop": false}, "id": "minecraft:gilded_blackstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玻璃", "block": {"crop": false}, "id": "minecraft:glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玻璃瓶", "id": "minecraft:glass_bottle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玻璃板", "block": {"crop": false}, "id": "minecraft:glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "闪烁的西瓜片", "id": "minecraft:glistering_melon_slice"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "旗帜图案", "id": "minecraft:globe_banner_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发光浆果", "block": {"crop": false}, "id": "minecraft:glow_berries", "food": {"saturation": 0.1, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "荧光墨囊", "id": "minecraft:glow_ink_sac"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "荧光物品展示框", "id": "minecraft:glow_item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发光地衣", "block": {"crop": false}, "id": "minecraft:glow_lichen"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发光鱿鱼刷怪蛋", "id": "minecraft:glow_squid_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "荧石", "block": {"crop": false}, "id": "minecraft:glowstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "荧石粉", "id": "minecraft:glowstone_dust"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "山羊角", "id": "minecraft:goat_horn"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "山羊刷怪蛋", "id": "minecraft:goat_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金块", "block": {"crop": false}, "id": "minecraft:gold_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金锭", "id": "minecraft:gold_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金粒", "id": "minecraft:gold_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金矿石", "block": {"crop": false}, "id": "minecraft:gold_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金苹果", "id": "minecraft:golden_apple", "food": {"saturation": 1.2, "nutrition": 4, "alwaysEdible": true}}, {"maxStackSize": 1, "maxDamage": 32, "localized": "金斧", "id": "minecraft:golden_axe", "toolType": "axe"}, {"maxStackSize": 1, "maxDamage": 91, "localized": "金靴子", "id": "minecraft:golden_boots", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金胡萝卜", "id": "minecraft:golden_carrot", "food": {"saturation": 1.2, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 112, "localized": "金胸甲", "id": "minecraft:golden_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 77, "localized": "金头盔", "id": "minecraft:golden_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 32, "localized": "金锄", "id": "minecraft:golden_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "金马铠", "id": "minecraft:golden_horse_armor"}, {"maxStackSize": 1, "maxDamage": 105, "localized": "金护腿", "id": "minecraft:golden_leggings", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 32, "localized": "金镐", "id": "minecraft:golden_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 32, "localized": "金锹", "id": "minecraft:golden_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 32, "localized": "金剑", "id": "minecraft:golden_sword", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩", "block": {"crop": false}, "id": "minecraft:granite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩台阶", "block": {"crop": false}, "id": "minecraft:granite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩楼梯", "block": {"crop": false}, "id": "minecraft:granite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "花岗岩墙", "block": {"crop": false}, "id": "minecraft:granite_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "草", "block": {"crop": false}, "id": "minecraft:grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "草方块", "block": {"crop": false}, "id": "minecraft:grass_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "沙砾", "block": {"crop": false}, "id": "minecraft:gravel"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "灰色旗帜", "block": {"crop": false}, "id": "minecraft:gray_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "灰色床", "block": {"crop": false}, "id": "minecraft:gray_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色蜡烛", "block": {"crop": false}, "id": "minecraft:gray_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色地毯", "block": {"crop": false}, "id": "minecraft:gray_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色混凝土", "block": {"crop": false}, "id": "minecraft:gray_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色混凝土粉末", "block": {"crop": false}, "id": "minecraft:gray_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色染料", "id": "minecraft:gray_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:gray_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "灰色潜影盒", "block": {"crop": false}, "id": "minecraft:gray_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色染色玻璃", "block": {"crop": false}, "id": "minecraft:gray_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色染色玻璃板", "block": {"crop": false}, "id": "minecraft:gray_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色陶瓦", "block": {"crop": false}, "id": "minecraft:gray_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色羊毛", "block": {"crop": false}, "id": "minecraft:gray_wool"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "绿色旗帜", "block": {"crop": false}, "id": "minecraft:green_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "绿色床", "block": {"crop": false}, "id": "minecraft:green_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色蜡烛", "block": {"crop": false}, "id": "minecraft:green_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色地毯", "block": {"crop": false}, "id": "minecraft:green_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色混凝土", "block": {"crop": false}, "id": "minecraft:green_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色混凝土粉末", "block": {"crop": false}, "id": "minecraft:green_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色染料", "id": "minecraft:green_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:green_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "绿色潜影盒", "block": {"crop": false}, "id": "minecraft:green_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色染色玻璃", "block": {"crop": false}, "id": "minecraft:green_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色染色玻璃板", "block": {"crop": false}, "id": "minecraft:green_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色陶瓦", "block": {"crop": false}, "id": "minecraft:green_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色羊毛", "block": {"crop": false}, "id": "minecraft:green_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂轮", "block": {"crop": false}, "id": "minecraft:grindstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "守卫者刷怪蛋", "id": "minecraft:guardian_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火药", "id": "minecraft:gunpowder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "垂根", "block": {"crop": false}, "id": "minecraft:hanging_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "干草捆", "block": {"crop": false}, "id": "minecraft:hay_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海洋之心", "id": "minecraft:heart_of_the_sea"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "爱心纹样陶片", "id": "minecraft:heart_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "心碎纹样陶片", "id": "minecraft:heartbreak_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "重质测重压力板", "block": {"crop": false}, "id": "minecraft:heavy_weighted_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "疣猪兽刷怪蛋", "id": "minecraft:hoglin_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜂蜜块", "block": {"crop": false}, "id": "minecraft:honey_block"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "蜂蜜瓶", "id": "minecraft:honey_bottle", "food": {"saturation": 0.1, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜜脾", "id": "minecraft:honeycomb"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜜脾块", "block": {"crop": false}, "id": "minecraft:honeycomb_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "漏斗", "block": {"crop": false}, "id": "minecraft:hopper"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "漏斗矿车", "id": "minecraft:hopper_minecart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鹿角珊瑚", "block": {"crop": false}, "id": "minecraft:horn_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鹿角珊瑚块", "block": {"crop": false}, "id": "minecraft:horn_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鹿角珊瑚扇", "block": {"crop": false}, "id": "minecraft:horn_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "马刷怪蛋", "id": "minecraft:horse_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:host_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "狼嚎纹样陶片", "id": "minecraft:howl_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "尸壳刷怪蛋", "id": "minecraft:husk_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "冰", "block": {"crop": false}, "id": "minecraft:ice"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀雕纹石砖", "block": {"crop": false}, "id": "minecraft:infested_chiseled_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀圆石", "block": {"crop": false}, "id": "minecraft:infested_cobblestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀裂纹石砖", "block": {"crop": false}, "id": "minecraft:infested_cracked_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀深板岩", "block": {"crop": false}, "id": "minecraft:infested_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀苔石砖", "block": {"crop": false}, "id": "minecraft:infested_mossy_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀石头", "block": {"crop": false}, "id": "minecraft:infested_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虫蚀石砖", "block": {"crop": false}, "id": "minecraft:infested_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "墨囊", "id": "minecraft:ink_sac"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "铁斧", "id": "minecraft:iron_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁栏杆", "block": {"crop": false}, "id": "minecraft:iron_bars"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁块", "block": {"crop": false}, "id": "minecraft:iron_block"}, {"maxStackSize": 1, "maxDamage": 195, "localized": "铁靴子", "id": "minecraft:iron_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 240, "localized": "铁胸甲", "id": "minecraft:iron_chestplate", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁门", "block": {"crop": false}, "id": "minecraft:iron_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁傀儡刷怪蛋", "id": "minecraft:iron_golem_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 165, "localized": "铁头盔", "id": "minecraft:iron_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "铁锄", "id": "minecraft:iron_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "铁马铠", "id": "minecraft:iron_horse_armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁锭", "id": "minecraft:iron_ingot"}, {"maxStackSize": 1, "maxDamage": 225, "localized": "铁护腿", "id": "minecraft:iron_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁粒", "id": "minecraft:iron_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁矿石", "block": {"crop": false}, "id": "minecraft:iron_ore"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "铁镐", "id": "minecraft:iron_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "铁锹", "id": "minecraft:iron_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "铁剑", "id": "minecraft:iron_sword", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁活板门", "block": {"crop": false}, "id": "minecraft:iron_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物品展示框", "id": "minecraft:item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "南瓜灯", "block": {"crop": false}, "id": "minecraft:jack_o_lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "拼图方块", "block": {"crop": false}, "id": "minecraft:jigsaw"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "唱片机", "block": {"crop": false}, "id": "minecraft:jukebox"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "丛林木船", "id": "minecraft:jungle_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木按钮", "block": {"crop": false}, "id": "minecraft:jungle_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "丛林木运输船", "id": "minecraft:jungle_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木门", "block": {"crop": false}, "id": "minecraft:jungle_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木栅栏", "block": {"crop": false}, "id": "minecraft:jungle_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木栅栏门", "block": {"crop": false}, "id": "minecraft:jungle_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式丛林木告示牌", "block": {"crop": false}, "id": "minecraft:jungle_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林树叶", "block": {"crop": false}, "id": "minecraft:jungle_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林原木", "block": {"crop": false}, "id": "minecraft:jungle_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木板", "block": {"crop": false}, "id": "minecraft:jungle_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木压力板", "block": {"crop": false}, "id": "minecraft:jungle_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林树苗", "block": {"crop": false}, "id": "minecraft:jungle_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "丛林木告示牌", "block": {"crop": false}, "id": "minecraft:jungle_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木台阶", "block": {"crop": false}, "id": "minecraft:jungle_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木楼梯", "block": {"crop": false}, "id": "minecraft:jungle_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木活板门", "block": {"crop": false}, "id": "minecraft:jungle_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丛林木", "block": {"crop": false}, "id": "minecraft:jungle_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海带", "block": {"crop": false}, "id": "minecraft:kelp"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "知识之书", "id": "minecraft:knowledge_book"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木梯子", "block": {"crop": false}, "id": "minecraft:ladder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灯笼", "block": {"crop": false}, "id": "minecraft:lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青金石块", "block": {"crop": false}, "id": "minecraft:lapis_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青金石", "id": "minecraft:lapis_lazuli"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青金石矿石", "block": {"crop": false}, "id": "minecraft:lapis_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型紫晶芽", "block": {"crop": false}, "id": "minecraft:large_amethyst_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型蕨", "block": {"crop": false}, "id": "minecraft:large_fern"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔岩桶", "id": "minecraft:lava_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "拴绳", "id": "minecraft:lead"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皮革", "id": "minecraft:leather"}, {"maxStackSize": 1, "maxDamage": 65, "localized": "皮革靴子", "id": "minecraft:leather_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 80, "localized": "皮革外套", "id": "minecraft:leather_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 55, "localized": "皮革帽子", "id": "minecraft:leather_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "皮革马铠", "id": "minecraft:leather_horse_armor"}, {"maxStackSize": 1, "maxDamage": 75, "localized": "皮革裤子", "id": "minecraft:leather_leggings", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "讲台", "block": {"crop": false}, "id": "minecraft:lectern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "拉杆", "block": {"crop": false}, "id": "minecraft:lever"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光源方块", "block": {"crop": false}, "id": "minecraft:light"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "淡蓝色旗帜", "block": {"crop": false}, "id": "minecraft:light_blue_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "淡蓝色床", "block": {"crop": false}, "id": "minecraft:light_blue_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色蜡烛", "block": {"crop": false}, "id": "minecraft:light_blue_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色地毯", "block": {"crop": false}, "id": "minecraft:light_blue_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色混凝土", "block": {"crop": false}, "id": "minecraft:light_blue_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色混凝土粉末", "block": {"crop": false}, "id": "minecraft:light_blue_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色染料", "id": "minecraft:light_blue_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:light_blue_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "淡蓝色潜影盒", "block": {"crop": false}, "id": "minecraft:light_blue_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色染色玻璃", "block": {"crop": false}, "id": "minecraft:light_blue_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色染色玻璃板", "block": {"crop": false}, "id": "minecraft:light_blue_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色陶瓦", "block": {"crop": false}, "id": "minecraft:light_blue_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色羊毛", "block": {"crop": false}, "id": "minecraft:light_blue_wool"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "淡灰色旗帜", "block": {"crop": false}, "id": "minecraft:light_gray_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "淡灰色床", "block": {"crop": false}, "id": "minecraft:light_gray_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色蜡烛", "block": {"crop": false}, "id": "minecraft:light_gray_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色地毯", "block": {"crop": false}, "id": "minecraft:light_gray_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色混凝土", "block": {"crop": false}, "id": "minecraft:light_gray_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色混凝土粉末", "block": {"crop": false}, "id": "minecraft:light_gray_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色染料", "id": "minecraft:light_gray_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:light_gray_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "淡灰色潜影盒", "block": {"crop": false}, "id": "minecraft:light_gray_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色染色玻璃", "block": {"crop": false}, "id": "minecraft:light_gray_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色染色玻璃板", "block": {"crop": false}, "id": "minecraft:light_gray_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色陶瓦", "block": {"crop": false}, "id": "minecraft:light_gray_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色羊毛", "block": {"crop": false}, "id": "minecraft:light_gray_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "轻质测重压力板", "block": {"crop": false}, "id": "minecraft:light_weighted_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "避雷针", "block": {"crop": false}, "id": "minecraft:lightning_rod"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丁香", "block": {"crop": false}, "id": "minecraft:lilac"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铃兰", "block": {"crop": false}, "id": "minecraft:lily_of_the_valley"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "睡莲", "block": {"crop": false}, "id": "minecraft:lily_pad"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "黄绿色旗帜", "block": {"crop": false}, "id": "minecraft:lime_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黄绿色床", "block": {"crop": false}, "id": "minecraft:lime_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色蜡烛", "block": {"crop": false}, "id": "minecraft:lime_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色地毯", "block": {"crop": false}, "id": "minecraft:lime_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色混凝土", "block": {"crop": false}, "id": "minecraft:lime_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色混凝土粉末", "block": {"crop": false}, "id": "minecraft:lime_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色染料", "id": "minecraft:lime_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:lime_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黄绿色潜影盒", "block": {"crop": false}, "id": "minecraft:lime_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色染色玻璃", "block": {"crop": false}, "id": "minecraft:lime_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色染色玻璃板", "block": {"crop": false}, "id": "minecraft:lime_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色陶瓦", "block": {"crop": false}, "id": "minecraft:lime_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色羊毛", "block": {"crop": false}, "id": "minecraft:lime_wool"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "滞留型水瓶", "id": "minecraft:lingering_potion"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "羊驼刷怪蛋", "id": "minecraft:llama_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磁石", "block": {"crop": false}, "id": "minecraft:lodestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "织布机", "block": {"crop": false}, "id": "minecraft:loom"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "品红色旗帜", "block": {"crop": false}, "id": "minecraft:magenta_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "品红色床", "block": {"crop": false}, "id": "minecraft:magenta_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色蜡烛", "block": {"crop": false}, "id": "minecraft:magenta_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色地毯", "block": {"crop": false}, "id": "minecraft:magenta_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色混凝土", "block": {"crop": false}, "id": "minecraft:magenta_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色混凝土粉末", "block": {"crop": false}, "id": "minecraft:magenta_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色染料", "id": "minecraft:magenta_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:magenta_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "品红色潜影盒", "block": {"crop": false}, "id": "minecraft:magenta_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色染色玻璃", "block": {"crop": false}, "id": "minecraft:magenta_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色染色玻璃板", "block": {"crop": false}, "id": "minecraft:magenta_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色陶瓦", "block": {"crop": false}, "id": "minecraft:magenta_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色羊毛", "block": {"crop": false}, "id": "minecraft:magenta_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "岩浆块", "block": {"crop": false}, "id": "minecraft:magma_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "岩浆膏", "id": "minecraft:magma_cream"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "岩浆怪刷怪蛋", "id": "minecraft:magma_cube_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "红树木船", "id": "minecraft:mangrove_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木按钮", "block": {"crop": false}, "id": "minecraft:mangrove_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "红树木运输船", "id": "minecraft:mangrove_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木门", "block": {"crop": false}, "id": "minecraft:mangrove_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木栅栏", "block": {"crop": false}, "id": "minecraft:mangrove_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木栅栏门", "block": {"crop": false}, "id": "minecraft:mangrove_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式红树木告示牌", "block": {"crop": false}, "id": "minecraft:mangrove_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树树叶", "block": {"crop": false}, "id": "minecraft:mangrove_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树原木", "block": {"crop": false}, "id": "minecraft:mangrove_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木板", "block": {"crop": false}, "id": "minecraft:mangrove_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木压力板", "block": {"crop": false}, "id": "minecraft:mangrove_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树胎生苗", "block": {"crop": false}, "id": "minecraft:mangrove_propagule"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树根", "block": {"crop": false}, "id": "minecraft:mangrove_roots"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "红树木告示牌", "block": {"crop": false}, "id": "minecraft:mangrove_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木台阶", "block": {"crop": false}, "id": "minecraft:mangrove_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木楼梯", "block": {"crop": false}, "id": "minecraft:mangrove_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木活板门", "block": {"crop": false}, "id": "minecraft:mangrove_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红树木", "block": {"crop": false}, "id": "minecraft:mangrove_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空地图", "id": "minecraft:map"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中型紫晶芽", "block": {"crop": false}, "id": "minecraft:medium_amethyst_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "西瓜", "block": {"crop": false}, "id": "minecraft:melon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "西瓜种子", "block": {"crop": false}, "id": "minecraft:melon_seeds"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "西瓜片", "id": "minecraft:melon_slice", "food": {"saturation": 0.3, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "奶桶", "id": "minecraft:milk_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "矿车", "id": "minecraft:minecart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "采矿纹样陶片", "id": "minecraft:miner_pottery_sherd"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "旗帜图案", "id": "minecraft:mojang_banner_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "哞菇刷怪蛋", "id": "minecraft:mooshroom_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔藓块", "block": {"crop": false}, "id": "minecraft:moss_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "覆地苔藓", "block": {"crop": false}, "id": "minecraft:moss_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石", "block": {"crop": false}, "id": "minecraft:mossy_cobblestone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石台阶", "block": {"crop": false}, "id": "minecraft:mossy_cobblestone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石楼梯", "block": {"crop": false}, "id": "minecraft:mossy_cobblestone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石墙", "block": {"crop": false}, "id": "minecraft:mossy_cobblestone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石砖台阶", "block": {"crop": false}, "id": "minecraft:mossy_stone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石砖楼梯", "block": {"crop": false}, "id": "minecraft:mossy_stone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石砖墙", "block": {"crop": false}, "id": "minecraft:mossy_stone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "苔石砖", "block": {"crop": false}, "id": "minecraft:mossy_stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "悲恸纹样陶片", "id": "minecraft:mourner_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥巴", "block": {"crop": false}, "id": "minecraft:mud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥砖台阶", "block": {"crop": false}, "id": "minecraft:mud_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥砖楼梯", "block": {"crop": false}, "id": "minecraft:mud_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥砖墙", "block": {"crop": false}, "id": "minecraft:mud_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥砖", "block": {"crop": false}, "id": "minecraft:mud_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "沾泥的红树根", "block": {"crop": false}, "id": "minecraft:muddy_mangrove_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骡刷怪蛋", "id": "minecraft:mule_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蘑菇柄", "block": {"crop": false}, "id": "minecraft:mushroom_stem"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蘑菇煲", "id": "minecraft:mushroom_stew", "food": {"saturation": 0.6, "nutrition": 6, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_11"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_13"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_5"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_blocks"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_cat"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_chirp"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_far"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_mall"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_mellohi"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_otherside"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_pigstep"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_relic"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_stal"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_strad"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_wait"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "minecraft:music_disc_ward"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生羊肉", "id": "minecraft:mutton", "food": {"saturation": 0.3, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "菌丝体", "block": {"crop": false}, "id": "minecraft:mycelium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "命名牌", "id": "minecraft:name_tag"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鹦鹉螺壳", "id": "minecraft:nautilus_shell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖", "id": "minecraft:nether_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖栅栏", "block": {"crop": false}, "id": "minecraft:nether_brick_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖台阶", "block": {"crop": false}, "id": "minecraft:nether_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖楼梯", "block": {"crop": false}, "id": "minecraft:nether_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖墙", "block": {"crop": false}, "id": "minecraft:nether_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖块", "block": {"crop": false}, "id": "minecraft:nether_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界金矿石", "block": {"crop": false}, "id": "minecraft:nether_gold_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界石英矿石", "block": {"crop": false}, "id": "minecraft:nether_quartz_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界苗", "block": {"crop": false}, "id": "minecraft:nether_sprouts"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界之星", "id": "minecraft:nether_star"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界疣", "block": {"crop": false}, "id": "minecraft:nether_wart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界疣块", "block": {"crop": false}, "id": "minecraft:nether_wart_block"}, {"maxStackSize": 1, "maxDamage": 2031, "localized": "下界合金斧", "id": "minecraft:netherite_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金块", "block": {"crop": false}, "id": "minecraft:netherite_block"}, {"maxStackSize": 1, "maxDamage": 481, "localized": "下界合金靴子", "id": "minecraft:netherite_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 592, "localized": "下界合金胸甲", "id": "minecraft:netherite_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 407, "localized": "下界合金头盔", "id": "minecraft:netherite_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 2031, "localized": "下界合金锄", "id": "minecraft:netherite_hoe", "toolType": "hoe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金锭", "id": "minecraft:netherite_ingot"}, {"maxStackSize": 1, "maxDamage": 555, "localized": "下界合金护腿", "id": "minecraft:netherite_leggings", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 2031, "localized": "下界合金镐", "id": "minecraft:netherite_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金碎片", "id": "minecraft:netherite_scrap"}, {"maxStackSize": 1, "maxDamage": 2031, "localized": "下界合金锹", "id": "minecraft:netherite_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 2031, "localized": "下界合金剑", "id": "minecraft:netherite_sword", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:netherite_upgrade_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界岩", "block": {"crop": false}, "id": "minecraft:netherrack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "音符盒", "block": {"crop": false}, "id": "minecraft:note_block"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "橡木船", "id": "minecraft:oak_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木按钮", "block": {"crop": false}, "id": "minecraft:oak_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "橡木运输船", "id": "minecraft:oak_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木门", "block": {"crop": false}, "id": "minecraft:oak_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木栅栏", "block": {"crop": false}, "id": "minecraft:oak_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木栅栏门", "block": {"crop": false}, "id": "minecraft:oak_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式橡木告示牌", "block": {"crop": false}, "id": "minecraft:oak_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡树树叶", "block": {"crop": false}, "id": "minecraft:oak_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木原木", "block": {"crop": false}, "id": "minecraft:oak_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木木板", "block": {"crop": false}, "id": "minecraft:oak_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木压力板", "block": {"crop": false}, "id": "minecraft:oak_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡树树苗", "block": {"crop": false}, "id": "minecraft:oak_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "橡木告示牌", "block": {"crop": false}, "id": "minecraft:oak_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木台阶", "block": {"crop": false}, "id": "minecraft:oak_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木楼梯", "block": {"crop": false}, "id": "minecraft:oak_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木活板门", "block": {"crop": false}, "id": "minecraft:oak_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橡木", "block": {"crop": false}, "id": "minecraft:oak_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "侦测器", "block": {"crop": false}, "id": "minecraft:observer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石", "block": {"crop": false}, "id": "minecraft:obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "豹猫刷怪蛋", "id": "minecraft:ocelot_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "赭黄蛙明灯", "block": {"crop": false}, "id": "minecraft:ochre_froglight"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "橙色旗帜", "block": {"crop": false}, "id": "minecraft:orange_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "橙色床", "block": {"crop": false}, "id": "minecraft:orange_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色蜡烛", "block": {"crop": false}, "id": "minecraft:orange_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色地毯", "block": {"crop": false}, "id": "minecraft:orange_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色混凝土", "block": {"crop": false}, "id": "minecraft:orange_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色混凝土粉末", "block": {"crop": false}, "id": "minecraft:orange_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色染料", "id": "minecraft:orange_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:orange_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "橙色潜影盒", "block": {"crop": false}, "id": "minecraft:orange_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色染色玻璃", "block": {"crop": false}, "id": "minecraft:orange_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色染色玻璃板", "block": {"crop": false}, "id": "minecraft:orange_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色陶瓦", "block": {"crop": false}, "id": "minecraft:orange_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色郁金香", "block": {"crop": false}, "id": "minecraft:orange_tulip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色羊毛", "block": {"crop": false}, "id": "minecraft:orange_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滨菊", "block": {"crop": false}, "id": "minecraft:oxeye_daisy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜块", "block": {"crop": false}, "id": "minecraft:oxidized_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的切制铜块", "block": {"crop": false}, "id": "minecraft:oxidized_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的切制铜台阶", "block": {"crop": false}, "id": "minecraft:oxidized_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的切制铜楼梯", "block": {"crop": false}, "id": "minecraft:oxidized_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浮冰", "block": {"crop": false}, "id": "minecraft:packed_ice"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泥坯", "block": {"crop": false}, "id": "minecraft:packed_mud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "画", "id": "minecraft:painting"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熊猫刷怪蛋", "id": "minecraft:panda_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纸", "id": "minecraft:paper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鹦鹉刷怪蛋", "id": "minecraft:parrot_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "珠光蛙明灯", "block": {"crop": false}, "id": "minecraft:pearlescent_froglight"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "牡丹", "block": {"crop": false}, "id": "minecraft:peony"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石化橡木台阶", "block": {"crop": false}, "id": "minecraft:petrified_oak_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幻翼膜", "id": "minecraft:phantom_membrane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幻翼刷怪蛋", "id": "minecraft:phantom_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "猪刷怪蛋", "id": "minecraft:pig_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "旗帜图案", "id": "minecraft:piglin_banner_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "猪灵蛮兵刷怪蛋", "id": "minecraft:piglin_brute_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "猪灵的头", "block": {"crop": false}, "id": "minecraft:piglin_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "猪灵刷怪蛋", "id": "minecraft:piglin_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "掠夺者刷怪蛋", "id": "minecraft:pillager_spawn_egg"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "粉红色旗帜", "block": {"crop": false}, "id": "minecraft:pink_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "粉红色床", "block": {"crop": false}, "id": "minecraft:pink_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色蜡烛", "block": {"crop": false}, "id": "minecraft:pink_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色地毯", "block": {"crop": false}, "id": "minecraft:pink_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色混凝土", "block": {"crop": false}, "id": "minecraft:pink_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色混凝土粉末", "block": {"crop": false}, "id": "minecraft:pink_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色染料", "id": "minecraft:pink_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:pink_glazed_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色花簇", "block": {"crop": false}, "id": "minecraft:pink_petals"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "粉红色潜影盒", "block": {"crop": false}, "id": "minecraft:pink_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色染色玻璃", "block": {"crop": false}, "id": "minecraft:pink_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色染色玻璃板", "block": {"crop": false}, "id": "minecraft:pink_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色陶瓦", "block": {"crop": false}, "id": "minecraft:pink_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色郁金香", "block": {"crop": false}, "id": "minecraft:pink_tulip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色羊毛", "block": {"crop": false}, "id": "minecraft:pink_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "活塞", "block": {"crop": false}, "id": "minecraft:piston"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "瓶子草", "block": {"crop": false}, "id": "minecraft:pitcher_plant"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "瓶子草荚果", "block": {"crop": false}, "id": "minecraft:pitcher_pod"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玩家的头", "block": {"crop": false}, "id": "minecraft:player_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "富饶纹样陶片", "id": "minecraft:plenty_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰化土", "block": {"crop": false}, "id": "minecraft:podzol"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滴水石锥", "block": {"crop": false}, "id": "minecraft:pointed_dripstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "毒马铃薯", "id": "minecraft:poisonous_potato", "food": {"saturation": 0.3, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "北极熊刷怪蛋", "id": "minecraft:polar_bear_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制安山岩", "block": {"crop": false}, "id": "minecraft:polished_andesite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制安山岩台阶", "block": {"crop": false}, "id": "minecraft:polished_andesite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制安山岩楼梯", "block": {"crop": false}, "id": "minecraft:polished_andesite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制玄武岩", "block": {"crop": false}, "id": "minecraft:polished_basalt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石", "block": {"crop": false}, "id": "minecraft:polished_blackstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石砖台阶", "block": {"crop": false}, "id": "minecraft:polished_blackstone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石砖楼梯", "block": {"crop": false}, "id": "minecraft:polished_blackstone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石砖墙", "block": {"crop": false}, "id": "minecraft:polished_blackstone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石砖", "block": {"crop": false}, "id": "minecraft:polished_blackstone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石按钮", "block": {"crop": false}, "id": "minecraft:polished_blackstone_button"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石压力板", "block": {"crop": false}, "id": "minecraft:polished_blackstone_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石台阶", "block": {"crop": false}, "id": "minecraft:polished_blackstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石楼梯", "block": {"crop": false}, "id": "minecraft:polished_blackstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑石墙", "block": {"crop": false}, "id": "minecraft:polished_blackstone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制深板岩", "block": {"crop": false}, "id": "minecraft:polished_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制深板岩台阶", "block": {"crop": false}, "id": "minecraft:polished_deepslate_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制深板岩楼梯", "block": {"crop": false}, "id": "minecraft:polished_deepslate_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制深板岩墙", "block": {"crop": false}, "id": "minecraft:polished_deepslate_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制闪长岩", "block": {"crop": false}, "id": "minecraft:polished_diorite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制闪长岩台阶", "block": {"crop": false}, "id": "minecraft:polished_diorite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制闪长岩楼梯", "block": {"crop": false}, "id": "minecraft:polished_diorite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制花岗岩", "block": {"crop": false}, "id": "minecraft:polished_granite"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制花岗岩台阶", "block": {"crop": false}, "id": "minecraft:polished_granite_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制花岗岩楼梯", "block": {"crop": false}, "id": "minecraft:polished_granite_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "爆裂紫颂果", "id": "minecraft:popped_chorus_fruit"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虞美人", "block": {"crop": false}, "id": "minecraft:poppy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生猪排", "id": "minecraft:porkchop", "food": {"saturation": 0.3, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "马铃薯", "block": {"crop": true}, "id": "minecraft:potato", "food": {"saturation": 0.3, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "水瓶", "id": "minecraft:potion"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "细雪桶", "block": {"crop": false}, "id": "minecraft:powder_snow_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "动力铁轨", "block": {"crop": false}, "id": "minecraft:powered_rail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石", "block": {"crop": false}, "id": "minecraft:prismarine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石砖台阶", "block": {"crop": false}, "id": "minecraft:prismarine_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石砖楼梯", "block": {"crop": false}, "id": "minecraft:prismarine_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石砖", "block": {"crop": false}, "id": "minecraft:prismarine_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶砂粒", "id": "minecraft:prismarine_crystals"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶碎片", "id": "minecraft:prismarine_shard"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石台阶", "block": {"crop": false}, "id": "minecraft:prismarine_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石楼梯", "block": {"crop": false}, "id": "minecraft:prismarine_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石墙", "block": {"crop": false}, "id": "minecraft:prismarine_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "珍宝纹样陶片", "id": "minecraft:prize_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "河豚", "id": "minecraft:pufferfish", "food": {"saturation": 0.1, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "河豚桶", "id": "minecraft:pufferfish_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "河豚刷怪蛋", "id": "minecraft:pufferfish_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "南瓜", "block": {"crop": false}, "id": "minecraft:pumpkin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "南瓜派", "id": "minecraft:pumpkin_pie", "food": {"saturation": 0.3, "nutrition": 8, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "南瓜种子", "block": {"crop": false}, "id": "minecraft:pumpkin_seeds"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "紫色旗帜", "block": {"crop": false}, "id": "minecraft:purple_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "紫色床", "block": {"crop": false}, "id": "minecraft:purple_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色蜡烛", "block": {"crop": false}, "id": "minecraft:purple_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色地毯", "block": {"crop": false}, "id": "minecraft:purple_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色混凝土", "block": {"crop": false}, "id": "minecraft:purple_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色混凝土粉末", "block": {"crop": false}, "id": "minecraft:purple_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色染料", "id": "minecraft:purple_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:purple_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "紫色潜影盒", "block": {"crop": false}, "id": "minecraft:purple_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色染色玻璃", "block": {"crop": false}, "id": "minecraft:purple_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色染色玻璃板", "block": {"crop": false}, "id": "minecraft:purple_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色陶瓦", "block": {"crop": false}, "id": "minecraft:purple_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色羊毛", "block": {"crop": false}, "id": "minecraft:purple_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫珀块", "block": {"crop": false}, "id": "minecraft:purpur_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫珀柱", "block": {"crop": false}, "id": "minecraft:purpur_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫珀台阶", "block": {"crop": false}, "id": "minecraft:purpur_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫珀楼梯", "block": {"crop": false}, "id": "minecraft:purpur_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界石英", "id": "minecraft:quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英块", "block": {"crop": false}, "id": "minecraft:quartz_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英砖", "block": {"crop": false}, "id": "minecraft:quartz_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英柱", "block": {"crop": false}, "id": "minecraft:quartz_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英台阶", "block": {"crop": false}, "id": "minecraft:quartz_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石英楼梯", "block": {"crop": false}, "id": "minecraft:quartz_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生兔肉", "id": "minecraft:rabbit", "food": {"saturation": 0.3, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "兔子脚", "id": "minecraft:rabbit_foot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "兔子皮", "id": "minecraft:rabbit_hide"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "兔子刷怪蛋", "id": "minecraft:rabbit_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "兔肉煲", "id": "minecraft:rabbit_stew", "food": {"saturation": 0.6, "nutrition": 10, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁轨", "block": {"crop": false}, "id": "minecraft:rail"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:raiser_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "劫掠兽刷怪蛋", "id": "minecraft:ravager_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铜", "id": "minecraft:raw_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铜块", "block": {"crop": false}, "id": "minecraft:raw_copper_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗金", "id": "minecraft:raw_gold"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗金块", "block": {"crop": false}, "id": "minecraft:raw_gold_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铁", "id": "minecraft:raw_iron"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗铁块", "block": {"crop": false}, "id": "minecraft:raw_iron_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "追溯指针", "id": "minecraft:recovery_compass"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "红色旗帜", "block": {"crop": false}, "id": "minecraft:red_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "红色床", "block": {"crop": false}, "id": "minecraft:red_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色蜡烛", "block": {"crop": false}, "id": "minecraft:red_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色地毯", "block": {"crop": false}, "id": "minecraft:red_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色混凝土", "block": {"crop": false}, "id": "minecraft:red_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色混凝土粉末", "block": {"crop": false}, "id": "minecraft:red_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色染料", "id": "minecraft:red_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:red_glazed_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色蘑菇", "block": {"crop": false}, "id": "minecraft:red_mushroom"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色蘑菇方块", "block": {"crop": false}, "id": "minecraft:red_mushroom_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色下界砖台阶", "block": {"crop": false}, "id": "minecraft:red_nether_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色下界砖楼梯", "block": {"crop": false}, "id": "minecraft:red_nether_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色下界砖墙", "block": {"crop": false}, "id": "minecraft:red_nether_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色下界砖块", "block": {"crop": false}, "id": "minecraft:red_nether_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红沙", "block": {"crop": false}, "id": "minecraft:red_sand"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砂岩", "block": {"crop": false}, "id": "minecraft:red_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砂岩台阶", "block": {"crop": false}, "id": "minecraft:red_sandstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砂岩楼梯", "block": {"crop": false}, "id": "minecraft:red_sandstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红砂岩墙", "block": {"crop": false}, "id": "minecraft:red_sandstone_wall"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "红色潜影盒", "block": {"crop": false}, "id": "minecraft:red_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色染色玻璃", "block": {"crop": false}, "id": "minecraft:red_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色染色玻璃板", "block": {"crop": false}, "id": "minecraft:red_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色陶瓦", "block": {"crop": false}, "id": "minecraft:red_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色郁金香", "block": {"crop": false}, "id": "minecraft:red_tulip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色羊毛", "block": {"crop": false}, "id": "minecraft:red_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石粉", "block": {"crop": false}, "id": "minecraft:redstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石块", "block": {"crop": false}, "id": "minecraft:redstone_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石灯", "block": {"crop": false}, "id": "minecraft:redstone_lamp"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石矿石", "block": {"crop": false}, "id": "minecraft:redstone_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石火把", "block": {"crop": false}, "id": "minecraft:redstone_torch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化深板岩", "block": {"crop": false}, "id": "minecraft:reinforced_deepslate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石中继器", "block": {"crop": false}, "id": "minecraft:repeater"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "循环型命令方块", "block": {"crop": false}, "id": "minecraft:repeating_command_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "重生锚", "block": {"crop": false}, "id": "minecraft:respawn_anchor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:rib_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "缠根泥土", "block": {"crop": false}, "id": "minecraft:rooted_dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰丛", "block": {"crop": false}, "id": "minecraft:rose_bush"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "腐肉", "id": "minecraft:rotten_flesh", "food": {"saturation": 0.1, "nutrition": 4, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "鞍", "id": "minecraft:saddle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生鲑鱼", "id": "minecraft:salmon", "food": {"saturation": 0.1, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "鲑鱼桶", "id": "minecraft:salmon_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鲑鱼刷怪蛋", "id": "minecraft:salmon_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "沙子", "block": {"crop": false}, "id": "minecraft:sand"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩", "block": {"crop": false}, "id": "minecraft:sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩台阶", "block": {"crop": false}, "id": "minecraft:sandstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩楼梯", "block": {"crop": false}, "id": "minecraft:sandstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砂岩墙", "block": {"crop": false}, "id": "minecraft:sandstone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脚手架", "block": {"crop": false}, "id": "minecraft:scaffolding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幽匿块", "block": {"crop": false}, "id": "minecraft:sculk"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幽匿催发体", "block": {"crop": false}, "id": "minecraft:sculk_catalyst"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幽匿感测体", "block": {"crop": false}, "id": "minecraft:sculk_sensor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幽匿尖啸体", "block": {"crop": false}, "id": "minecraft:sculk_shrieker"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "幽匿脉络", "block": {"crop": false}, "id": "minecraft:sculk_vein"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鳞甲", "id": "minecraft:scute"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶灯", "block": {"crop": false}, "id": "minecraft:sea_lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海泡菜", "block": {"crop": false}, "id": "minecraft:sea_pickle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海草", "block": {"crop": false}, "id": "minecraft:seagrass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:sentry_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:shaper_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "麦捆纹样陶片", "id": "minecraft:sheaf_pottery_sherd"}, {"maxStackSize": 1, "maxDamage": 238, "localized": "剪刀", "id": "minecraft:shears", "toolType": "shears"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绵羊刷怪蛋", "id": "minecraft:sheep_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "树荫纹样陶片", "id": "minecraft:shelter_pottery_sherd"}, {"maxStackSize": 1, "maxDamage": 336, "localized": "盾牌", "id": "minecraft:shield", "toolType": "shield"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "菌光体", "block": {"crop": false}, "id": "minecraft:shroomlight"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "潜影盒", "block": {"crop": false}, "id": "minecraft:shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "潜影壳", "id": "minecraft:shulker_shell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "潜影贝刷怪蛋", "id": "minecraft:shulker_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:silence_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蠹虫刷怪蛋", "id": "minecraft:silverfish_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅马刷怪蛋", "id": "minecraft:skeleton_horse_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅头颅", "block": {"crop": false}, "id": "minecraft:skeleton_skull"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骷髅刷怪蛋", "id": "minecraft:skeleton_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "旗帜图案", "id": "minecraft:skull_banner_pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "头颅纹样陶片", "id": "minecraft:skull_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液球", "id": "minecraft:slime_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液块", "block": {"crop": false}, "id": "minecraft:slime_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "史莱姆刷怪蛋", "id": "minecraft:slime_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型紫晶芽", "block": {"crop": false}, "id": "minecraft:small_amethyst_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型垂滴叶", "block": {"crop": false}, "id": "minecraft:small_dripleaf"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造台", "block": {"crop": false}, "id": "minecraft:smithing_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烟熏炉", "block": {"crop": false}, "id": "minecraft:smoker"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑玄武岩", "block": {"crop": false}, "id": "minecraft:smooth_basalt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑石英块", "block": {"crop": false}, "id": "minecraft:smooth_quartz"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑石英台阶", "block": {"crop": false}, "id": "minecraft:smooth_quartz_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑石英楼梯", "block": {"crop": false}, "id": "minecraft:smooth_quartz_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑红砂岩", "block": {"crop": false}, "id": "minecraft:smooth_red_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑红砂岩台阶", "block": {"crop": false}, "id": "minecraft:smooth_red_sandstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑红砂岩楼梯", "block": {"crop": false}, "id": "minecraft:smooth_red_sandstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑砂岩", "block": {"crop": false}, "id": "minecraft:smooth_sandstone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑砂岩台阶", "block": {"crop": false}, "id": "minecraft:smooth_sandstone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑砂岩楼梯", "block": {"crop": false}, "id": "minecraft:smooth_sandstone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑石头", "block": {"crop": false}, "id": "minecraft:smooth_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "平滑石头台阶", "block": {"crop": false}, "id": "minecraft:smooth_stone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "嗅探兽蛋", "block": {"crop": false}, "id": "minecraft:sniffer_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "嗅探兽刷怪蛋", "id": "minecraft:sniffer_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "嗅探纹样陶片", "id": "minecraft:snort_pottery_sherd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:snout_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雪", "block": {"crop": false}, "id": "minecraft:snow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雪块", "block": {"crop": false}, "id": "minecraft:snow_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雪傀儡刷怪蛋", "id": "minecraft:snow_golem_spawn_egg"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "雪球", "id": "minecraft:snowball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂营火", "block": {"crop": false}, "id": "minecraft:soul_campfire"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂灯笼", "block": {"crop": false}, "id": "minecraft:soul_lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂沙", "block": {"crop": false}, "id": "minecraft:soul_sand"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂土", "block": {"crop": false}, "id": "minecraft:soul_soil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂火把", "block": {"crop": false}, "id": "minecraft:soul_torch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "刷怪笼", "block": {"crop": false}, "id": "minecraft:spawner"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "光灵箭", "id": "minecraft:spectral_arrow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜘蛛眼", "id": "minecraft:spider_eye", "food": {"saturation": 0.8, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜘蛛刷怪蛋", "id": "minecraft:spider_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:spire_armor_trim_smithing_template"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "喷溅型水瓶", "id": "minecraft:splash_potion"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海绵", "block": {"crop": false}, "id": "minecraft:sponge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "孢子花", "block": {"crop": false}, "id": "minecraft:spore_blossom"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "云杉木船", "id": "minecraft:spruce_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木按钮", "block": {"crop": false}, "id": "minecraft:spruce_button"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "云杉木运输船", "id": "minecraft:spruce_chest_boat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木门", "block": {"crop": false}, "id": "minecraft:spruce_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木栅栏", "block": {"crop": false}, "id": "minecraft:spruce_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木栅栏门", "block": {"crop": false}, "id": "minecraft:spruce_fence_gate"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式云杉木告示牌", "block": {"crop": false}, "id": "minecraft:spruce_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉树叶", "block": {"crop": false}, "id": "minecraft:spruce_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉原木", "block": {"crop": false}, "id": "minecraft:spruce_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木板", "block": {"crop": false}, "id": "minecraft:spruce_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木压力板", "block": {"crop": false}, "id": "minecraft:spruce_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉树苗", "block": {"crop": false}, "id": "minecraft:spruce_sapling"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "云杉木告示牌", "block": {"crop": false}, "id": "minecraft:spruce_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木台阶", "block": {"crop": false}, "id": "minecraft:spruce_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木楼梯", "block": {"crop": false}, "id": "minecraft:spruce_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木活板门", "block": {"crop": false}, "id": "minecraft:spruce_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "云杉木", "block": {"crop": false}, "id": "minecraft:spruce_wood"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "望远镜", "id": "minecraft:spyglass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "鱿鱼刷怪蛋", "id": "minecraft:squid_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "木棍", "id": "minecraft:stick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏性活塞", "block": {"crop": false}, "id": "minecraft:sticky_piston"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石头", "block": {"crop": false}, "id": "minecraft:stone"}, {"maxStackSize": 1, "maxDamage": 131, "localized": "石斧", "id": "minecraft:stone_axe", "toolType": "axe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石砖台阶", "block": {"crop": false}, "id": "minecraft:stone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石砖楼梯", "block": {"crop": false}, "id": "minecraft:stone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石砖墙", "block": {"crop": false}, "id": "minecraft:stone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石砖", "block": {"crop": false}, "id": "minecraft:stone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石头按钮", "block": {"crop": false}, "id": "minecraft:stone_button"}, {"maxStackSize": 1, "maxDamage": 131, "localized": "石锄", "id": "minecraft:stone_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 131, "localized": "石镐", "id": "minecraft:stone_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石头压力板", "block": {"crop": false}, "id": "minecraft:stone_pressure_plate"}, {"maxStackSize": 1, "maxDamage": 131, "localized": "石锹", "id": "minecraft:stone_shovel", "toolType": "shovel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石头台阶", "block": {"crop": false}, "id": "minecraft:stone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石头楼梯", "block": {"crop": false}, "id": "minecraft:stone_stairs"}, {"maxStackSize": 1, "maxDamage": 131, "localized": "石剑", "id": "minecraft:stone_sword", "toolType": "sword"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "切石机", "block": {"crop": false}, "id": "minecraft:stonecutter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流浪者刷怪蛋", "id": "minecraft:stray_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "炽足兽刷怪蛋", "id": "minecraft:strider_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "线", "block": {"crop": false}, "id": "minecraft:string"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮金合欢原木", "block": {"crop": false}, "id": "minecraft:stripped_acacia_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮金合欢木", "block": {"crop": false}, "id": "minecraft:stripped_acacia_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮竹块", "block": {"crop": false}, "id": "minecraft:stripped_bamboo_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮白桦原木", "block": {"crop": false}, "id": "minecraft:stripped_birch_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮白桦木", "block": {"crop": false}, "id": "minecraft:stripped_birch_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮樱花原木", "block": {"crop": false}, "id": "minecraft:stripped_cherry_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮樱花木", "block": {"crop": false}, "id": "minecraft:stripped_cherry_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮绯红菌核", "block": {"crop": false}, "id": "minecraft:stripped_crimson_hyphae"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮绯红菌柄", "block": {"crop": false}, "id": "minecraft:stripped_crimson_stem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮深色橡木原木", "block": {"crop": false}, "id": "minecraft:stripped_dark_oak_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮深色橡木", "block": {"crop": false}, "id": "minecraft:stripped_dark_oak_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮丛林原木", "block": {"crop": false}, "id": "minecraft:stripped_jungle_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮丛林木", "block": {"crop": false}, "id": "minecraft:stripped_jungle_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮红树原木", "block": {"crop": false}, "id": "minecraft:stripped_mangrove_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮红树木", "block": {"crop": false}, "id": "minecraft:stripped_mangrove_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮橡木原木", "block": {"crop": false}, "id": "minecraft:stripped_oak_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮橡木", "block": {"crop": false}, "id": "minecraft:stripped_oak_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮云杉原木", "block": {"crop": false}, "id": "minecraft:stripped_spruce_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮云杉木", "block": {"crop": false}, "id": "minecraft:stripped_spruce_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮诡异菌核", "block": {"crop": false}, "id": "minecraft:stripped_warped_hyphae"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮诡异菌柄", "block": {"crop": false}, "id": "minecraft:stripped_warped_stem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结构方块", "block": {"crop": false}, "id": "minecraft:structure_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "结构空位", "block": {"crop": false}, "id": "minecraft:structure_void"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "糖", "id": "minecraft:sugar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "甘蔗", "block": {"crop": false}, "id": "minecraft:sugar_cane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "向日葵", "block": {"crop": false}, "id": "minecraft:sunflower"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可疑的沙砾", "block": {"crop": false}, "id": "minecraft:suspicious_gravel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "可疑的沙子", "block": {"crop": false}, "id": "minecraft:suspicious_sand"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "谜之炖菜", "id": "minecraft:suspicious_stew", "food": {"saturation": 0.6, "nutrition": 6, "alwaysEdible": true}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "甜浆果", "block": {"crop": false}, "id": "minecraft:sweet_berries", "food": {"saturation": 0.1, "nutrition": 2, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蝌蚪桶", "id": "minecraft:tadpole_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蝌蚪刷怪蛋", "id": "minecraft:tadpole_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "高草丛", "block": {"crop": false}, "id": "minecraft:tall_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "标靶", "block": {"crop": false}, "id": "minecraft:target"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陶瓦", "block": {"crop": false}, "id": "minecraft:terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:tide_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "遮光玻璃", "block": {"crop": false}, "id": "minecraft:tinted_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "剧毒之箭", "id": "minecraft:tipped_arrow"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "TNT", "block": {"crop": false}, "id": "minecraft:tnt"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "TNT矿车", "id": "minecraft:tnt_minecart"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火把", "block": {"crop": false}, "id": "minecraft:torch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火把花", "block": {"crop": false}, "id": "minecraft:torchflower"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "火把花种子", "block": {"crop": true}, "id": "minecraft:torchflower_seeds"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "不死图腾", "id": "minecraft:totem_of_undying"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "行商羊驼刷怪蛋", "id": "minecraft:trader_llama_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "陷阱箱", "block": {"crop": false}, "id": "minecraft:trapped_chest"}, {"maxStackSize": 1, "maxDamage": 250, "localized": "三叉戟", "id": "minecraft:trident", "toolType": "trident"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绊线钩", "block": {"crop": false}, "id": "minecraft:tripwire_hook"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "热带鱼", "id": "minecraft:tropical_fish", "food": {"saturation": 0.1, "nutrition": 1, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "热带鱼桶", "id": "minecraft:tropical_fish_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "热带鱼刷怪蛋", "id": "minecraft:tropical_fish_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "管珊瑚", "block": {"crop": false}, "id": "minecraft:tube_coral"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "管珊瑚块", "block": {"crop": false}, "id": "minecraft:tube_coral_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "管珊瑚扇", "block": {"crop": false}, "id": "minecraft:tube_coral_fan"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝灰岩", "block": {"crop": false}, "id": "minecraft:tuff"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海龟蛋", "block": {"crop": false}, "id": "minecraft:turtle_egg"}, {"maxStackSize": 1, "maxDamage": 275, "localized": "海龟壳", "id": "minecraft:turtle_helmet", "toolType": "armor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海龟刷怪蛋", "id": "minecraft:turtle_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "缠怨藤", "block": {"crop": false}, "id": "minecraft:twisting_vines"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青翠蛙明灯", "block": {"crop": false}, "id": "minecraft:verdant_froglight"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:vex_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "恼鬼刷怪蛋", "id": "minecraft:vex_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "村民刷怪蛋", "id": "minecraft:villager_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "卫道士刷怪蛋", "id": "minecraft:vindicator_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "藤蔓", "block": {"crop": false}, "id": "minecraft:vine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流浪商人刷怪蛋", "id": "minecraft:wandering_trader_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:ward_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "监守者刷怪蛋", "id": "minecraft:warden_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木按钮", "block": {"crop": false}, "id": "minecraft:warped_button"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木门", "block": {"crop": false}, "id": "minecraft:warped_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木栅栏", "block": {"crop": false}, "id": "minecraft:warped_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木栅栏门", "block": {"crop": false}, "id": "minecraft:warped_fence_gate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异菌", "block": {"crop": false}, "id": "minecraft:warped_fungus"}, {"maxStackSize": 1, "maxDamage": 100, "localized": "诡异菌钓竿", "id": "minecraft:warped_fungus_on_a_stick"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式诡异木告示牌", "block": {"crop": false}, "id": "minecraft:warped_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异菌核", "block": {"crop": false}, "id": "minecraft:warped_hyphae"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异菌岩", "block": {"crop": false}, "id": "minecraft:warped_nylium"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木板", "block": {"crop": false}, "id": "minecraft:warped_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木压力板", "block": {"crop": false}, "id": "minecraft:warped_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异菌索", "block": {"crop": false}, "id": "minecraft:warped_roots"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "诡异木告示牌", "block": {"crop": false}, "id": "minecraft:warped_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木台阶", "block": {"crop": false}, "id": "minecraft:warped_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木楼梯", "block": {"crop": false}, "id": "minecraft:warped_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异菌柄", "block": {"crop": false}, "id": "minecraft:warped_stem"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异木活板门", "block": {"crop": false}, "id": "minecraft:warped_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "诡异疣块", "block": {"crop": false}, "id": "minecraft:warped_wart_block"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "水桶", "id": "minecraft:water_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的铜块", "block": {"crop": false}, "id": "minecraft:waxed_copper_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的切制铜块", "block": {"crop": false}, "id": "minecraft:waxed_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的切制铜台阶", "block": {"crop": false}, "id": "minecraft:waxed_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的切制铜楼梯", "block": {"crop": false}, "id": "minecraft:waxed_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的斑驳铜块", "block": {"crop": false}, "id": "minecraft:waxed_exposed_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的斑驳切制铜块", "block": {"crop": false}, "id": "minecraft:waxed_exposed_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的斑驳切制铜台阶", "block": {"crop": false}, "id": "minecraft:waxed_exposed_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的斑驳切制铜楼梯", "block": {"crop": false}, "id": "minecraft:waxed_exposed_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的氧化铜块", "block": {"crop": false}, "id": "minecraft:waxed_oxidized_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的氧化切制铜块", "block": {"crop": false}, "id": "minecraft:waxed_oxidized_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的氧化切制铜台阶", "block": {"crop": false}, "id": "minecraft:waxed_oxidized_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的氧化切制铜楼梯", "block": {"crop": false}, "id": "minecraft:waxed_oxidized_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的锈蚀铜块", "block": {"crop": false}, "id": "minecraft:waxed_weathered_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的锈蚀切制铜块", "block": {"crop": false}, "id": "minecraft:waxed_weathered_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的锈蚀切制铜台阶", "block": {"crop": false}, "id": "minecraft:waxed_weathered_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的锈蚀切制铜楼梯", "block": {"crop": false}, "id": "minecraft:waxed_weathered_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:wayfinder_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜块", "block": {"crop": false}, "id": "minecraft:weathered_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的切制铜块", "block": {"crop": false}, "id": "minecraft:weathered_cut_copper"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的切制铜台阶", "block": {"crop": false}, "id": "minecraft:weathered_cut_copper_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的切制铜楼梯", "block": {"crop": false}, "id": "minecraft:weathered_cut_copper_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "垂泪藤", "block": {"crop": false}, "id": "minecraft:weeping_vines"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "湿海绵", "block": {"crop": false}, "id": "minecraft:wet_sponge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小麦", "id": "minecraft:wheat"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小麦种子", "block": {"crop": true}, "id": "minecraft:wheat_seeds"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "白色旗帜", "block": {"crop": false}, "id": "minecraft:white_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "白色床", "block": {"crop": false}, "id": "minecraft:white_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色蜡烛", "block": {"crop": false}, "id": "minecraft:white_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色地毯", "block": {"crop": false}, "id": "minecraft:white_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色混凝土", "block": {"crop": false}, "id": "minecraft:white_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色混凝土粉末", "block": {"crop": false}, "id": "minecraft:white_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色染料", "id": "minecraft:white_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:white_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "白色潜影盒", "block": {"crop": false}, "id": "minecraft:white_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色染色玻璃", "block": {"crop": false}, "id": "minecraft:white_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色染色玻璃板", "block": {"crop": false}, "id": "minecraft:white_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色陶瓦", "block": {"crop": false}, "id": "minecraft:white_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色郁金香", "block": {"crop": false}, "id": "minecraft:white_tulip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色羊毛", "block": {"crop": false}, "id": "minecraft:white_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锻造模板", "id": "minecraft:wild_armor_trim_smithing_template"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "女巫刷怪蛋", "id": "minecraft:witch_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凋灵玫瑰", "block": {"crop": false}, "id": "minecraft:wither_rose"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凋灵骷髅头颅", "block": {"crop": false}, "id": "minecraft:wither_skeleton_skull"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凋灵骷髅刷怪蛋", "id": "minecraft:wither_skeleton_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凋灵刷怪蛋", "id": "minecraft:wither_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "狼刷怪蛋", "id": "minecraft:wolf_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 59, "localized": "木斧", "id": "minecraft:wooden_axe", "toolType": "axe"}, {"maxStackSize": 1, "maxDamage": 59, "localized": "木锄", "id": "minecraft:wooden_hoe", "toolType": "hoe"}, {"maxStackSize": 1, "maxDamage": 59, "localized": "木镐", "id": "minecraft:wooden_pickaxe", "toolType": "pickaxe"}, {"maxStackSize": 1, "maxDamage": 59, "localized": "木锹", "id": "minecraft:wooden_shovel", "toolType": "shovel"}, {"maxStackSize": 1, "maxDamage": 59, "localized": "木剑", "id": "minecraft:wooden_sword", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "书与笔", "id": "minecraft:writable_book"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "成书", "id": "minecraft:written_book"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "黄色旗帜", "block": {"crop": false}, "id": "minecraft:yellow_banner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黄色床", "block": {"crop": false}, "id": "minecraft:yellow_bed"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色蜡烛", "block": {"crop": false}, "id": "minecraft:yellow_candle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色地毯", "block": {"crop": false}, "id": "minecraft:yellow_carpet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色混凝土", "block": {"crop": false}, "id": "minecraft:yellow_concrete"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色混凝土粉末", "block": {"crop": false}, "id": "minecraft:yellow_concrete_powder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色染料", "id": "minecraft:yellow_dye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色带釉陶瓦", "block": {"crop": false}, "id": "minecraft:yellow_glazed_terracotta"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黄色潜影盒", "block": {"crop": false}, "id": "minecraft:yellow_shulker_box"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色染色玻璃", "block": {"crop": false}, "id": "minecraft:yellow_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色染色玻璃板", "block": {"crop": false}, "id": "minecraft:yellow_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色陶瓦", "block": {"crop": false}, "id": "minecraft:yellow_terracotta"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色羊毛", "block": {"crop": false}, "id": "minecraft:yellow_wool"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸疣猪兽刷怪蛋", "id": "minecraft:zoglin_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸的头", "block": {"crop": false}, "id": "minecraft:zombie_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸马刷怪蛋", "id": "minecraft:zombie_horse_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸刷怪蛋", "id": "minecraft:zombie_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸村民刷怪蛋", "id": "minecraft:zombie_villager_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸猪灵刷怪蛋", "id": "minecraft:zombified_piglin_spawn_egg"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "唱片刻录机", "block": {"crop": false}, "id": "netmusic:cd_burner"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "电脑", "block": {"crop": false}, "id": "netmusic:computer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "音乐唱片", "id": "netmusic:music_cd"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "唱片机", "block": {"crop": false}, "id": "netmusic:music_player"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音响背包", "id": "netmusic:music_player_backpack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Purpur Tiles", "block": {"crop": false}, "id": "cataclysm:purpur_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Void Purpur Tiles", "block": {"crop": false}, "id": "cataclysm:void_purpur_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Purpur Tile Pillar", "block": {"crop": false}, "id": "cataclysm:purpur_tile_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Purpur Tile Slab", "block": {"crop": false}, "id": "cataclysm:purpur_tile_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Purpur Tile Stairs", "block": {"crop": false}, "id": "cataclysm:purpur_tile_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Purpur Tile Wall", "block": {"crop": false}, "id": "cataclysm:purpur_tile_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "虚空晶体", "block": {"crop": false}, "id": "cataclysm:void_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制黑曜石砖块", "block": {"crop": false}, "id": "cataclysm:polished_obsidian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Polished Obsidian Slab", "block": {"crop": false}, "id": "cataclysm:polished_obsidian_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Polished Obsidian Stairs", "block": {"crop": false}, "id": "cataclysm:polished_obsidian_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Polished Obsidian Wall", "block": {"crop": false}, "id": "cataclysm:polished_obsidian_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Obsidian Pillar", "block": {"crop": false}, "id": "cataclysm:obsidian_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Chorus Trapdoor", "block": {"crop": false}, "id": "cataclysm:chorus_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石砖栅栏", "block": {"crop": false}, "id": "cataclysm:prismarine_brick_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "海晶石砖墙", "block": {"crop": false}, "id": "cataclysm:prismarine_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石", "block": {"crop": false}, "id": "cataclysm:azure_seastone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石台阶", "block": {"crop": false}, "id": "cataclysm:azure_seastone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石楼梯", "block": {"crop": false}, "id": "cataclysm:azure_seastone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石墙", "block": {"crop": false}, "id": "cataclysm:azure_seastone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石栅栏", "block": {"crop": false}, "id": "cataclysm:azure_seastone_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石瓦", "block": {"crop": false}, "id": "cataclysm:azure_seastone_tiles"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石", "block": {"crop": false}, "id": "cataclysm:chiseled_azure_seastone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石砖", "block": {"crop": false}, "id": "cataclysm:azure_seastone_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石砖台阶", "block": {"crop": false}, "id": "cataclysm:azure_seastone_brick_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石砖楼梯", "block": {"crop": false}, "id": "cataclysm:azure_seastone_brick_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石砖墙", "block": {"crop": false}, "id": "cataclysm:azure_seastone_brick_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_empty"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_urchinkin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_cindaria"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_hippocamtus"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_clawdian"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_thunder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_sea"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_underworld"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_harvest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_smithing"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石壁画", "block": {"crop": false}, "id": "cataclysm:azure_seastone_mural_wisdom"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(小海胆)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_urchinkin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(水母莉亚 1)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_cindaria_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(水母莉亚 2)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_cindaria_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(水母莉亚 3)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_cindaria_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(水母莉亚 4)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_cindaria_4"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(沧溟巡守 1)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_hippocamtus_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(沧溟巡守 2)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_hippocamtus_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(沧溟巡守 3)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_hippocamtus_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(沧溟巡守 4)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_hippocamtus_4"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(巨钳守卫 1)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_clawdian_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(巨钳守卫 2)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_clawdian_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(巨钳守卫 3)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_clawdian_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(巨钳守卫 4)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_clawdian_4"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 1)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_1"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 2)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_2"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 3)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_3"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 4)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_4"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 5)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_5"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 6)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_6"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 7)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_7"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 8)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_8"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石(斯库拉 9)", "block": {"crop": false}, "id": "cataclysm:curved_azure_seastone_scylla_9"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制蔚蓝海石", "block": {"crop": false}, "id": "cataclysm:polished_azure_seastone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制蔚蓝海石台阶", "block": {"crop": false}, "id": "cataclysm:polished_azure_seastone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制蔚蓝海石楼梯", "block": {"crop": false}, "id": "cataclysm:polished_azure_seastone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制蔚蓝海石墙", "block": {"crop": false}, "id": "cataclysm:polished_azure_seastone_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石柱", "block": {"crop": false}, "id": "cataclysm:azure_seastone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蔚蓝海石柱墙", "block": {"crop": false}, "id": "cataclysm:azure_seastone_pillar_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石柱", "block": {"crop": false}, "id": "cataclysm:chiseled_azure_seastone_pillar"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹蔚蓝海石柱墙", "block": {"crop": false}, "id": "cataclysm:chiseled_azure_seastone_pillar_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "泪宝石", "id": "cataclysm:lacrima"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风暴精华", "id": "cataclysm:essence_of_the_storm"}, {"maxStackSize": 1, "maxDamage": 514, "localized": "蔚蓝海石盾", "id": "cataclysm:azure_sea_shield", "toolType": "shield"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "漩涡护手", "id": "cataclysm:gauntlet_of_maelstrom"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "炽热之握", "id": "cataclysm:blazing_grips"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "甲壳巨钳", "id": "cataclysm:chitin_claw"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "沙暴之怒", "id": "cataclysm:wrath_of_the_desert"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "神怒长槊", "id": "cataclysm:astrape"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "霆浪锚戟", "id": "cataclysm:cera<PERSON>us"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "献祭者", "id": "cataclysm:the_immolator"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "下界合金拟像", "id": "cataclysm:netherite_effigy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "怪奇之钥", "id": "cataclysm:strange_key"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔岩动力电池", "id": "cataclysm:lava_power_cell"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_scylla"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "音乐唱片", "id": "cataclysm:music_disc_the_cataclysmfarer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "风暴之眼", "id": "cataclysm:storm_eye"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "item.cataclysm.urchin_spike", "id": "cataclysm:urchin_spike"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "item.cataclysm.blood_clot", "id": "cataclysm:blood_clot"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "下界合金幼兽桶", "id": "cataclysm:netherite_ministrosity_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金幼兽刷怪蛋", "id": "cataclysm:netherite_ministrosity_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斯库拉刷怪蛋", "id": "cataclysm:scylla_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "巨钳守卫刷怪蛋", "id": "cataclysm:clawdian_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "沧溟巡守刷怪蛋", "id": "cataclysm:hippocamtus_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水母莉亚刷怪蛋", "id": "cataclysm:cindaria_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "章鱼宿主刷怪蛋", "id": "cataclysm:octohost_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "章鱼共生体刷怪蛋", "id": "cataclysm:symbiocto_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小海胆刷怪蛋", "id": "cataclysm:urchinkin_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "<PERSON>重生笼", "block": {"crop": false}, "id": "cataclysm:boss_respawner"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "女神雕像", "block": {"crop": false}, "id": "cataclysm:goddess_statue"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Guide", "id": "guideme:guide"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "通透玻璃", "block": {"crop": false}, "id": "tconstruct:clear_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "遮光通透玻璃", "block": {"crop": false}, "id": "tconstruct:clear_tinted_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "通透玻璃板", "block": {"crop": false}, "id": "tconstruct:clear_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:white_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:orange_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:magenta_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:light_blue_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:yellow_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:lime_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:pink_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:gray_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:light_gray_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:cyan_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:purple_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:blue_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:brown_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:green_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:red_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色染色通透玻璃", "block": {"crop": false}, "id": "tconstruct:black_clear_stained_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:white_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:orange_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:magenta_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:light_blue_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:yellow_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:lime_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:pink_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:gray_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:light_gray_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:cyan_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:purple_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:blue_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:brown_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:green_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:red_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色染色通透玻璃板", "block": {"crop": false}, "id": "tconstruct:black_clear_stained_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂玻璃", "block": {"crop": false}, "id": "tconstruct:soul_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵魂玻璃板", "block": {"crop": false}, "id": "tconstruct:soul_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金栏杆", "block": {"crop": false}, "id": "tconstruct:gold_bars"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石板", "block": {"crop": false}, "id": "tconstruct:obsidian_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金平台", "block": {"crop": false}, "id": "tconstruct:gold_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁平台", "block": {"crop": false}, "id": "tconstruct:iron_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钴平台", "block": {"crop": false}, "id": "tconstruct:cobalt_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜平台", "block": {"crop": false}, "id": "tconstruct:copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "斑驳的铜平台", "block": {"crop": false}, "id": "tconstruct:exposed_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的铜平台", "block": {"crop": false}, "id": "tconstruct:weathered_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "氧化的铜平台", "block": {"crop": false}, "id": "tconstruct:oxidized_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡铜平台", "block": {"crop": false}, "id": "tconstruct:waxed_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的斑驳铜平台", "block": {"crop": false}, "id": "tconstruct:waxed_exposed_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的锈蚀铜平台", "block": {"crop": false}, "id": "tconstruct:waxed_weathered_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "涂蜡的氧化铜平台", "block": {"crop": false}, "id": "tconstruct:waxed_oxidized_copper_platform"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "奶酪块", "block": {"crop": false}, "id": "tconstruct:cheese_block", "food": {"saturation": 0.4, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钴块", "block": {"crop": false}, "id": "tconstruct:cobalt_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钴锭", "id": "tconstruct:cobalt_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钴粒", "id": "tconstruct:cobalt_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢块", "block": {"crop": false}, "id": "tconstruct:steel_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢锭", "id": "tconstruct:steel_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钢粒", "id": "tconstruct:steel_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏钢块", "block": {"crop": false}, "id": "tconstruct:slimesteel_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏钢锭", "id": "tconstruct:slimesteel_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏钢粒", "id": "tconstruct:slimesteel_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶青铜块", "block": {"crop": false}, "id": "tconstruct:amethyst_bronze_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶青铜锭", "id": "tconstruct:amethyst_bronze_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫水晶青铜粒", "id": "tconstruct:amethyst_bronze_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰金块", "block": {"crop": false}, "id": "tconstruct:rose_gold_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰金锭", "id": "tconstruct:rose_gold_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玫瑰金粒", "id": "tconstruct:rose_gold_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生铁块", "block": {"crop": false}, "id": "tconstruct:pig_iron_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生铁锭", "id": "tconstruct:pig_iron_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "生铁粒", "id": "tconstruct:pig_iron_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "余烬黏液块", "block": {"crop": false}, "id": "tconstruct:cinderslime_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "余烬黏液锭", "id": "tconstruct:cinderslime_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "余烬黏液粒", "id": "tconstruct:cinderslime_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皇后史莱姆块", "block": {"crop": false}, "id": "tconstruct:queens_slime_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皇后史莱姆锭", "id": "tconstruct:queens_slime_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "皇后史莱姆粒", "id": "tconstruct:queens_slime_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玛玉灵块", "block": {"crop": false}, "id": "tconstruct:manyullyn_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玛玉灵锭", "id": "tconstruct:manyullyn_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玛玉灵粒", "id": "tconstruct:manyullyn_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色科林斯青铜块", "block": {"crop": false}, "id": "tconstruct:hepatizon_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色科林斯青铜锭", "id": "tconstruct:hepati<PERSON>_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色科林斯青铜粒", "id": "tconstruct:hepatizon_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "魂钢块", "block": {"crop": false}, "id": "tconstruct:soulsteel_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "魂钢锭", "id": "tconstruct:soulsteel_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "魂钢粒", "id": "tconstruct:soulsteel_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骑士史莱姆块", "block": {"crop": false}, "id": "tconstruct:knightslime_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骑士史莱姆锭", "id": "tconstruct:<PERSON><PERSON><PERSON>_ingot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "骑士史莱姆粒", "id": "tconstruct:<PERSON><PERSON>e_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纳瓦特尔木板", "block": {"crop": false}, "id": "tconstruct:nahuatl"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纳瓦特尔台阶", "block": {"crop": false}, "id": "tconstruct:nahuatl_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纳瓦特尔楼梯", "block": {"crop": false}, "id": "tconstruct:nahuatl_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纳瓦特尔栅栏", "block": {"crop": false}, "id": "tconstruct:nahuatl_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰木", "block": {"crop": false}, "id": "tconstruct:blazewood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰木台阶", "block": {"crop": false}, "id": "tconstruct:blazewood_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰木楼梯", "block": {"crop": false}, "id": "tconstruct:blazewood_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰木栅栏", "block": {"crop": false}, "id": "tconstruct:blazewood_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "尖竹钉", "block": {"crop": false}, "id": "tconstruct:punji"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "大地黏液蛋糕", "block": {"crop": false}, "id": "tconstruct:earth_cake"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "碧空黏液蛋糕", "block": {"crop": false}, "id": "tconstruct:sky_cake"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "灵浆蛋糕", "block": {"crop": false}, "id": "tconstruct:ichor_cake"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "末影黏液蛋糕", "block": {"crop": false}, "id": "tconstruct:ender_cake"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "黏血蛋糕", "block": {"crop": false}, "id": "tconstruct:blood_cake"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "岩浆蛋糕", "block": {"crop": false}, "id": "tconstruct:magma_cake"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界钴矿石", "block": {"crop": false}, "id": "tconstruct:cobalt_ore"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗钴块", "block": {"crop": false}, "id": "tconstruct:raw_cobalt_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液块", "block": {"crop": false}, "id": "tconstruct:sky_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆块", "block": {"crop": false}, "id": "tconstruct:ichor_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液块", "block": {"crop": false}, "id": "tconstruct:ender_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝固黏液块", "block": {"crop": false}, "id": "tconstruct:earth_congealed_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝固碧空黏液块", "block": {"crop": false}, "id": "tconstruct:sky_congealed_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝固灵浆块", "block": {"crop": false}, "id": "tconstruct:ichor_congealed_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "凝固末影黏液块", "block": {"crop": false}, "id": "tconstruct:ender_congealed_slime"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液泥土", "block": {"crop": false}, "id": "tconstruct:earth_slime_dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液泥土", "block": {"crop": false}, "id": "tconstruct:sky_slime_dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆泥土", "block": {"crop": false}, "id": "tconstruct:ichor_slime_dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液泥土", "block": {"crop": false}, "id": "tconstruct:ender_slime_dirt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液草方块", "block": {"crop": false}, "id": "tconstruct:earth_vanilla_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液草方块", "block": {"crop": false}, "id": "tconstruct:sky_vanilla_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆菌岩", "block": {"crop": false}, "id": "tconstruct:ichor_vanilla_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液草方块", "block": {"crop": false}, "id": "tconstruct:ender_vanilla_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血菌岩", "block": {"crop": false}, "id": "tconstruct:blood_vanilla_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液草方块", "block": {"crop": false}, "id": "tconstruct:earth_earth_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液草方块", "block": {"crop": false}, "id": "tconstruct:sky_earth_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆菌岩", "block": {"crop": false}, "id": "tconstruct:ichor_earth_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液草方块", "block": {"crop": false}, "id": "tconstruct:ender_earth_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血菌岩", "block": {"crop": false}, "id": "tconstruct:blood_earth_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液草方块", "block": {"crop": false}, "id": "tconstruct:earth_sky_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液草方块", "block": {"crop": false}, "id": "tconstruct:sky_sky_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆菌岩", "block": {"crop": false}, "id": "tconstruct:ichor_sky_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液草方块", "block": {"crop": false}, "id": "tconstruct:ender_sky_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血菌岩", "block": {"crop": false}, "id": "tconstruct:blood_sky_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液草方块", "block": {"crop": false}, "id": "tconstruct:earth_ender_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液草方块", "block": {"crop": false}, "id": "tconstruct:sky_ender_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆菌岩", "block": {"crop": false}, "id": "tconstruct:ichor_ender_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液草方块", "block": {"crop": false}, "id": "tconstruct:ender_ender_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血菌岩", "block": {"crop": false}, "id": "tconstruct:blood_ender_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液草方块", "block": {"crop": false}, "id": "tconstruct:earth_ichor_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液草方块", "block": {"crop": false}, "id": "tconstruct:sky_ichor_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆菌岩", "block": {"crop": false}, "id": "tconstruct:ichor_ichor_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液草方块", "block": {"crop": false}, "id": "tconstruct:ender_ichor_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血菌岩", "block": {"crop": false}, "id": "tconstruct:blood_ichor_slime_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木板", "block": {"crop": false}, "id": "tconstruct:greenheart_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木台阶", "block": {"crop": false}, "id": "tconstruct:greenheart_planks_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木楼梯", "block": {"crop": false}, "id": "tconstruct:greenheart_planks_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木栅栏", "block": {"crop": false}, "id": "tconstruct:greenheart_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮绿心原木", "block": {"crop": false}, "id": "tconstruct:stripped_greenheart_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮绿心木", "block": {"crop": false}, "id": "tconstruct:stripped_greenheart_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心原木", "block": {"crop": false}, "id": "tconstruct:greenheart_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木", "block": {"crop": false}, "id": "tconstruct:greenheart_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木门", "block": {"crop": false}, "id": "tconstruct:greenheart_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木活板门", "block": {"crop": false}, "id": "tconstruct:greenheart_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木栅栏门", "block": {"crop": false}, "id": "tconstruct:greenheart_fence_gate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木压力板", "block": {"crop": false}, "id": "tconstruct:greenheart_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心木按钮", "block": {"crop": false}, "id": "tconstruct:greenheart_button"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "绿心木告示牌", "block": {"crop": false}, "id": "tconstruct:greenheart_sign"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式绿心木告示牌", "block": {"crop": false}, "id": "tconstruct:greenheart_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木板", "block": {"crop": false}, "id": "tconstruct:skyroot_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木台阶", "block": {"crop": false}, "id": "tconstruct:skyroot_planks_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木楼梯", "block": {"crop": false}, "id": "tconstruct:skyroot_planks_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木栅栏", "block": {"crop": false}, "id": "tconstruct:skyroot_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮天根原木", "block": {"crop": false}, "id": "tconstruct:stripped_skyroot_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮天根木", "block": {"crop": false}, "id": "tconstruct:stripped_skyroot_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根原木", "block": {"crop": false}, "id": "tconstruct:skyroot_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木", "block": {"crop": false}, "id": "tconstruct:skyroot_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木门", "block": {"crop": false}, "id": "tconstruct:skyroot_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木活板门", "block": {"crop": false}, "id": "tconstruct:skyroot_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木栅栏门", "block": {"crop": false}, "id": "tconstruct:skyroot_fence_gate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木压力板", "block": {"crop": false}, "id": "tconstruct:skyroot_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根木按钮", "block": {"crop": false}, "id": "tconstruct:skyroot_button"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "天根木告示牌", "block": {"crop": false}, "id": "tconstruct:skyroot_sign"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式天根木告示牌", "block": {"crop": false}, "id": "tconstruct:skyroot_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木板", "block": {"crop": false}, "id": "tconstruct:bloodshroom_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木台阶", "block": {"crop": false}, "id": "tconstruct:bloodshroom_planks_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木楼梯", "block": {"crop": false}, "id": "tconstruct:bloodshroom_planks_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木栅栏", "block": {"crop": false}, "id": "tconstruct:bloodshroom_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮血菌柄", "block": {"crop": false}, "id": "tconstruct:stripped_bloodshroom_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮血菌核", "block": {"crop": false}, "id": "tconstruct:stripped_bloodshroom_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌柄", "block": {"crop": false}, "id": "tconstruct:bloodshroom_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌核", "block": {"crop": false}, "id": "tconstruct:bloodshroom_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木门", "block": {"crop": false}, "id": "tconstruct:bloodshroom_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木活板门", "block": {"crop": false}, "id": "tconstruct:bloodshroom_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木栅栏门", "block": {"crop": false}, "id": "tconstruct:bloodshroom_fence_gate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木压力板", "block": {"crop": false}, "id": "tconstruct:bloodshroom_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌木按钮", "block": {"crop": false}, "id": "tconstruct:bloodshroom_button"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "血菌木告示牌", "block": {"crop": false}, "id": "tconstruct:bloodshroom_sign"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式血菌木告示牌", "block": {"crop": false}, "id": "tconstruct:bloodshroom_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木木板", "block": {"crop": false}, "id": "tconstruct:enderbark_planks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木台阶", "block": {"crop": false}, "id": "tconstruct:enderbark_planks_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木楼梯", "block": {"crop": false}, "id": "tconstruct:enderbark_planks_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木栅栏", "block": {"crop": false}, "id": "tconstruct:enderbark_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮末影皮木原木", "block": {"crop": false}, "id": "tconstruct:stripped_enderbark_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "去皮末影皮木", "block": {"crop": false}, "id": "tconstruct:stripped_enderbark_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木原木", "block": {"crop": false}, "id": "tconstruct:enderbark_log"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木", "block": {"crop": false}, "id": "tconstruct:enderbark_wood"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木门", "block": {"crop": false}, "id": "tconstruct:enderbark_door"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木活板门", "block": {"crop": false}, "id": "tconstruct:enderbark_trapdoor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木栅栏门", "block": {"crop": false}, "id": "tconstruct:enderbark_fence_gate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木压力板", "block": {"crop": false}, "id": "tconstruct:enderbark_pressure_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮木按钮", "block": {"crop": false}, "id": "tconstruct:enderbark_button"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "末影皮木告示牌", "block": {"crop": false}, "id": "tconstruct:enderbark_sign"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "悬挂式末影皮木告示牌", "block": {"crop": false}, "id": "tconstruct:enderbark_hanging_sign"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮树根", "block": {"crop": false}, "id": "tconstruct:enderbark_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液末影皮树根", "block": {"crop": false}, "id": "tconstruct:earth_enderbark_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液末影皮树根", "block": {"crop": false}, "id": "tconstruct:sky_enderbark_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液末影皮树根", "block": {"crop": false}, "id": "tconstruct:ichor_enderbark_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液末影皮树根", "block": {"crop": false}, "id": "tconstruct:ender_enderbark_roots"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液蕨", "block": {"crop": false}, "id": "tconstruct:earth_slime_fern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液蕨", "block": {"crop": false}, "id": "tconstruct:sky_slime_fern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液菌索", "block": {"crop": false}, "id": "tconstruct:ichor_slime_fern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液蕨", "block": {"crop": false}, "id": "tconstruct:ender_slime_fern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血菌索", "block": {"crop": false}, "id": "tconstruct:blood_slime_fern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液草丛", "block": {"crop": false}, "id": "tconstruct:earth_slime_tall_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液草丛", "block": {"crop": false}, "id": "tconstruct:sky_slime_tall_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液草丛", "block": {"crop": false}, "id": "tconstruct:ichor_slime_tall_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液草丛", "block": {"crop": false}, "id": "tconstruct:ender_slime_tall_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血草丛", "block": {"crop": false}, "id": "tconstruct:blood_slime_tall_grass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿心树苗", "block": {"crop": false}, "id": "tconstruct:earth_slime_sapling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "天根树苗", "block": {"crop": false}, "id": "tconstruct:sky_slime_sapling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "血菌", "block": {"crop": false}, "id": "tconstruct:blood_slime_sapling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆树苗", "block": {"crop": false}, "id": "tconstruct:ichor_slime_sapling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影皮树胎生苗", "block": {"crop": false}, "id": "tconstruct:ender_slime_sapling"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液树叶", "block": {"crop": false}, "id": "tconstruct:earth_slime_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液树叶", "block": {"crop": false}, "id": "tconstruct:sky_slime_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆疣", "block": {"crop": false}, "id": "tconstruct:ichor_slime_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血疣", "block": {"crop": false}, "id": "tconstruct:blood_slime_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液树叶", "block": {"crop": false}, "id": "tconstruct:ender_slime_leaves"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液藤蔓", "block": {"crop": false}, "id": "tconstruct:sky_slime_vine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏液藤蔓", "block": {"crop": false}, "id": "tconstruct:ender_slime_vine"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液水晶", "id": "tconstruct:earth_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液水晶块", "block": {"crop": false}, "id": "tconstruct:earth_slime_crystal_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液水晶母岩", "block": {"crop": false}, "id": "tconstruct:budding_earth_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液水晶簇", "block": {"crop": false}, "id": "tconstruct:earth_slime_crystal_cluster"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型大地黏液晶芽", "block": {"crop": false}, "id": "tconstruct:small_earth_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中型大地黏液晶芽", "block": {"crop": false}, "id": "tconstruct:medium_earth_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型大地黏液晶芽", "block": {"crop": false}, "id": "tconstruct:large_earth_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液水晶", "id": "tconstruct:sky_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液水晶块", "block": {"crop": false}, "id": "tconstruct:sky_slime_crystal_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液水晶母岩", "block": {"crop": false}, "id": "tconstruct:budding_sky_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液水晶簇", "block": {"crop": false}, "id": "tconstruct:sky_slime_crystal_cluster"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型碧空黏液晶芽", "block": {"crop": false}, "id": "tconstruct:small_sky_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中型碧空黏液晶芽", "block": {"crop": false}, "id": "tconstruct:medium_sky_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型碧空黏液晶芽", "block": {"crop": false}, "id": "tconstruct:large_sky_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆水晶", "id": "tconstruct:ichor_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆水晶块", "block": {"crop": false}, "id": "tconstruct:ichor_slime_crystal_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆水晶母岩", "block": {"crop": false}, "id": "tconstruct:budding_ichor_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆水晶簇", "block": {"crop": false}, "id": "tconstruct:ichor_slime_crystal_cluster"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型灵浆晶芽", "block": {"crop": false}, "id": "tconstruct:small_ichor_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中型灵浆晶芽", "block": {"crop": false}, "id": "tconstruct:medium_ichor_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型灵浆晶芽", "block": {"crop": false}, "id": "tconstruct:large_ichor_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液水晶", "id": "tconstruct:ender_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液水晶块", "block": {"crop": false}, "id": "tconstruct:ender_slime_crystal_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液水晶母岩", "block": {"crop": false}, "id": "tconstruct:budding_ender_slime_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液水晶簇", "block": {"crop": false}, "id": "tconstruct:ender_slime_crystal_cluster"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型末影黏液晶芽", "block": {"crop": false}, "id": "tconstruct:small_ender_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "中型末影黏液晶芽", "block": {"crop": false}, "id": "tconstruct:medium_ender_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大型末影黏液晶芽", "block": {"crop": false}, "id": "tconstruct:large_ender_slime_crystal_bud"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工作站", "block": {"crop": false}, "id": "tconstruct:crafting_station"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工匠站", "block": {"crop": false}, "id": "tconstruct:tinker_station"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "部件制造台", "block": {"crop": false}, "id": "tconstruct:part_builder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工匠箱", "block": {"crop": false}, "id": "tconstruct:tinkers_chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "部件箱", "block": {"crop": false}, "id": "tconstruct:part_chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铸模箱", "block": {"crop": false}, "id": "tconstruct:cast_chest"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "强化调配台", "block": {"crop": false}, "id": "tconstruct:modifier_worktable"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工匠砧", "block": {"crop": false}, "id": "tconstruct:tinkers_anvil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工匠砧", "block": {"crop": false}, "id": "tconstruct:scorched_anvil"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "砖泥", "block": {"crop": false}, "id": "tconstruct:grout"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界砖泥", "block": {"crop": false}, "id": "tconstruct:nether_grout"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑石头", "block": {"crop": false}, "id": "tconstruct:seared_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑石头台阶", "block": {"crop": false}, "id": "tconstruct:seared_stone_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑石头楼梯", "block": {"crop": false}, "id": "tconstruct:seared_stone_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑圆石", "block": {"crop": false}, "id": "tconstruct:seared_cobble"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑圆石台阶", "block": {"crop": false}, "id": "tconstruct:seared_cobble_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑圆石楼梯", "block": {"crop": false}, "id": "tconstruct:seared_cobble_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑圆石墙", "block": {"crop": false}, "id": "tconstruct:seared_cobble_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑地砖", "block": {"crop": false}, "id": "tconstruct:seared_paver"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑地砖台阶", "block": {"crop": false}, "id": "tconstruct:seared_paver_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑地砖楼梯", "block": {"crop": false}, "id": "tconstruct:seared_paver_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑砖块", "block": {"crop": false}, "id": "tconstruct:seared_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑砖台阶", "block": {"crop": false}, "id": "tconstruct:seared_bricks_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑砖楼梯", "block": {"crop": false}, "id": "tconstruct:seared_bricks_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑砖墙", "block": {"crop": false}, "id": "tconstruct:seared_bricks_wall"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "裂纹焦黑砖块", "block": {"crop": false}, "id": "tconstruct:seared_cracked_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "精致焦黑砖块", "block": {"crop": false}, "id": "tconstruct:seared_fancy_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "三角纹焦黑砖块", "block": {"crop": false}, "id": "tconstruct:seared_triangle_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑梯子", "block": {"crop": false}, "id": "tconstruct:seared_ladder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑玻璃", "block": {"crop": false}, "id": "tconstruct:seared_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑灵魂玻璃", "block": {"crop": false}, "id": "tconstruct:seared_soul_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑遮光玻璃", "block": {"crop": false}, "id": "tconstruct:seared_tinted_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑玻璃板", "block": {"crop": false}, "id": "tconstruct:seared_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑灵魂玻璃板", "block": {"crop": false}, "id": "tconstruct:seared_soul_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑排液孔", "block": {"crop": false}, "id": "tconstruct:seared_drain"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑输导孔", "block": {"crop": false}, "id": "tconstruct:seared_duct"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑滑槽", "block": {"crop": false}, "id": "tconstruct:seared_chute"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐石头", "block": {"crop": false}, "id": "tconstruct:scorched_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "磨制焦褐石头", "block": {"crop": false}, "id": "tconstruct:polished_scorched_stone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐砖块", "block": {"crop": false}, "id": "tconstruct:scorched_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐砖台阶", "block": {"crop": false}, "id": "tconstruct:scorched_bricks_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐砖楼梯", "block": {"crop": false}, "id": "tconstruct:scorched_bricks_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐砖栅栏", "block": {"crop": false}, "id": "tconstruct:scorched_bricks_fence"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐石径", "block": {"crop": false}, "id": "tconstruct:scorched_road"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐石径台阶", "block": {"crop": false}, "id": "tconstruct:scorched_road_slab"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐石径楼梯", "block": {"crop": false}, "id": "tconstruct:scorched_road_stairs"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "雕纹焦褐砖块", "block": {"crop": false}, "id": "tconstruct:chiseled_scorched_bricks"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐梯子", "block": {"crop": false}, "id": "tconstruct:scorched_ladder"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐玻璃", "block": {"crop": false}, "id": "tconstruct:scorched_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐灵魂玻璃", "block": {"crop": false}, "id": "tconstruct:scorched_soul_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐遮光玻璃", "block": {"crop": false}, "id": "tconstruct:scorched_tinted_glass"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐玻璃板", "block": {"crop": false}, "id": "tconstruct:scorched_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐灵魂玻璃板", "block": {"crop": false}, "id": "tconstruct:scorched_soul_glass_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐排液孔", "block": {"crop": false}, "id": "tconstruct:scorched_drain"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐输导孔", "block": {"crop": false}, "id": "tconstruct:scorched_duct"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐滑槽", "block": {"crop": false}, "id": "tconstruct:scorched_chute"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑燃料储罐", "block": {"crop": false}, "id": "tconstruct:seared_fuel_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑燃料量器", "block": {"crop": false}, "id": "tconstruct:seared_fuel_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑材料储罐", "block": {"crop": false}, "id": "tconstruct:seared_ingot_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑材料量器", "block": {"crop": false}, "id": "tconstruct:seared_ingot_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑灯笼", "block": {"crop": false}, "id": "tconstruct:seared_lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑浇注口", "block": {"crop": false}, "id": "tconstruct:seared_faucet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑浇注道", "block": {"crop": false}, "id": "tconstruct:seared_channel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑铸造盆", "block": {"crop": false}, "id": "tconstruct:seared_basin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑铸件台", "block": {"crop": false}, "id": "tconstruct:seared_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "浇注储罐", "block": {"crop": false}, "id": "tconstruct:seared_casting_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐燃料储罐", "block": {"crop": false}, "id": "tconstruct:scorched_fuel_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐燃料量器", "block": {"crop": false}, "id": "tconstruct:scorched_fuel_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐材料储罐", "block": {"crop": false}, "id": "tconstruct:scorched_ingot_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐材料量器", "block": {"crop": false}, "id": "tconstruct:scorched_ingot_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐灯笼", "block": {"crop": false}, "id": "tconstruct:scorched_lantern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐浇注口", "block": {"crop": false}, "id": "tconstruct:scorched_faucet"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐浇注道", "block": {"crop": false}, "id": "tconstruct:scorched_channel"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐铸造盆", "block": {"crop": false}, "id": "tconstruct:scorched_basin"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐铸件台", "block": {"crop": false}, "id": "tconstruct:scorched_table"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐工匠储罐", "block": {"crop": false}, "id": "tconstruct:scorched_proxy_tank"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜量计", "block": {"crop": false}, "id": "tconstruct:copper_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石量计", "block": {"crop": false}, "id": "tconstruct:obsidian_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜流体加农炮", "block": {"crop": false}, "id": "tconstruct:seared_fluid_cannon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钴流体加农炮", "block": {"crop": false}, "id": "tconstruct:scorched_fluid_cannon"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "冶炼炉控制器", "block": {"crop": false}, "id": "tconstruct:smeltery_controller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "熔铸炉控制器", "block": {"crop": false}, "id": "tconstruct:foundry_controller"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑熔化炉", "block": {"crop": false}, "id": "tconstruct:seared_melter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑加热器", "block": {"crop": false}, "id": "tconstruct:seared_heater"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐合金炉", "block": {"crop": false}, "id": "tconstruct:scorched_alloyer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "培根", "id": "tconstruct:bacon", "food": {"saturation": 0.6, "nutrition": 4, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宝钻苹果", "id": "tconstruct:jeweled_apple", "food": {"saturation": 1.2, "nutrition": 4, "alwaysEdible": true}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "奶酪", "id": "tconstruct:cheese_ingot", "food": {"saturation": 0.4, "nutrition": 3, "alwaysEdible": false}}, {"maxStackSize": 1, "maxDamage": 0, "localized": "匠魂宝典：材料与你", "id": "tconstruct:materials_and_you"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "入门熔炼术", "id": "tconstruct:puny_smelting"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "大师熔炼术", "id": "tconstruct:mighty_smelting"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工匠装置宝典", "id": "tconstruct:tinkers_gadgetry"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "奇异熔铸术", "id": "tconstruct:fantastic_foundry"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "工匠大百科", "id": "tconstruct:encyclopedia"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液球", "id": "tconstruct:sky_slime_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆球", "id": "tconstruct:ichor_slime_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液球", "id": "tconstruct:ender_slime_ball"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜粒", "id": "tconstruct:copper_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金粒", "id": "tconstruct:netherite_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "残骸碎片", "id": "tconstruct:debris_nugget"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "噬生之骨", "id": "tconstruct:necrotic_bone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "毒之骨", "id": "tconstruct:venombone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰之骨", "id": "tconstruct:blazing_bone"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "辐射之骨", "id": "tconstruct:necronium_bone"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "肩驮架", "id": "tconstruct:piggy_backpack"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "反向金物品展示框", "id": "tconstruct:reversed_gold_item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钻石物品展示框", "id": "tconstruct:diamond_item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "玛玉灵物品展示框", "id": "tconstruct:manyullyn_item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金物品展示框", "id": "tconstruct:gold_item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "通透玻璃物品展示框", "id": "tconstruct:clear_item_frame"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "下界合金物品展示框", "id": "tconstruct:netherite_item_frame"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "荧光球", "id": "tconstruct:glow_ball"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "EFLN", "id": "tconstruct:efln_ball"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "石英手里剑", "id": "tconstruct:quartz_shuriken"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "燧石手里剑", "id": "tconstruct:flint_shuriken"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粗钴", "id": "tconstruct:raw_cobalt"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大地黏液种子", "id": "tconstruct:earth_slime_grass_seeds"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空黏液种子", "id": "tconstruct:sky_slime_grass_seeds"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灵浆种子", "id": "tconstruct:ichor_slime_grass_seeds"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影黏液种子", "id": "tconstruct:ender_slime_grass_seeds"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏血种子", "id": "tconstruct:blood_slime_grass_seeds"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "烈焰人的头", "block": {"crop": false}, "id": "tconstruct:blaze_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影人的头", "block": {"crop": false}, "id": "tconstruct:end<PERSON>_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "流浪者头颅", "block": {"crop": false}, "id": "tconstruct:stray_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "尸壳的头", "block": {"crop": false}, "id": "tconstruct:husk_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "溺尸的头", "block": {"crop": false}, "id": "tconstruct:drowned_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蜘蛛的头", "block": {"crop": false}, "id": "tconstruct:spider_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "洞穴蜘蛛的头", "block": {"crop": false}, "id": "tconstruct:cave_spider_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "猪灵蛮兵的头", "block": {"crop": false}, "id": "tconstruct:piglin_brute_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "僵尸猪灵的头", "block": {"crop": false}, "id": "tconstruct:zombified_piglin_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "模具", "id": "tconstruct:pattern"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "丝绢", "id": "tconstruct:silky_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "龙鳞", "id": "tconstruct:dragon_scale"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿宝石加固板", "id": "tconstruct:emerald_reinforcement"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏钢加固板", "id": "tconstruct:slimesteel_reinforcement"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铁加固板", "id": "tconstruct:iron_reinforcement"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑加固板", "id": "tconstruct:seared_reinforcement"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "金加固板", "id": "tconstruct:gold_reinforcement"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "钴加固板", "id": "tconstruct:cobalt_reinforcement"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑曜石加固板", "id": "tconstruct:obsidian_reinforcement"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "强化水晶", "id": "tconstruct:modifier_crystal"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "创造强化槽", "id": "tconstruct:creative_slot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "修补件", "id": "tconstruct:repair_kit"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镐头", "id": "tconstruct:pick_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锤头", "id": "tconstruct:hammer_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型斧刃", "id": "tconstruct:small_axe_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "板斧刃", "id": "tconstruct:broad_axe_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型剑刃", "id": "tconstruct:small_blade"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宽刃", "id": "tconstruct:broad_blade"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锛头", "id": "tconstruct:adze_head"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大板", "id": "tconstruct:large_plate"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓臂", "id": "tconstruct:bow_limb"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓把", "id": "tconstruct:bow_grip"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓弦", "id": "tconstruct:bowstring"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绑定结", "id": "tconstruct:tool_binding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧套环", "id": "tconstruct:tough_binding"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工具手柄", "id": "tconstruct:tool_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧手柄", "id": "tconstruct:tough_handle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "头盔镶板", "id": "tconstruct:helmet_plating"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "胸甲镶板", "id": "tconstruct:chestplate_plating"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "护腿镶板", "id": "tconstruct:leggings_plating"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "靴子镶板", "id": "tconstruct:boots_plating"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁链基底", "id": "tconstruct:maille"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "盾牌基底", "id": "tconstruct:shield_core"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镐", "id": "tconstruct:pickaxe"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "大锤", "id": "tconstruct:sledge_hammer"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "矿脉锤", "id": "tconstruct:vein_hammer"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "鹤嘴锄", "id": "tconstruct:mattock"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "凿石锹", "id": "tconstruct:pickadze"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "开掘铲", "id": "tconstruct:excavator"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "短柄斧", "id": "tconstruct:hand_axe"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "板斧", "id": "tconstruct:broad_axe"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "短刃镰", "id": "tconstruct:kama"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镰刀", "id": "tconstruct:scythe"}, {"maxStackSize": 2, "maxDamage": 1, "localized": "匕首", "id": "tconstruct:dagger"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "剑", "id": "tconstruct:sword"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "劈刀", "id": "tconstruct:cleaver"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "弩", "id": "tconstruct:crossbow"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "长弓", "id": "tconstruct:longbow"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "焦砖打火石", "id": "tconstruct:flint_and_brick"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "碧空黏液手杖", "id": "tconstruct:sky_staff"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "大地黏液手杖", "id": "tconstruct:earth_staff"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "灵浆手杖", "id": "tconstruct:ichor_staff"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "末影黏液手杖", "id": "tconstruct:ender_staff"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "熔炼锅", "id": "tconstruct:melting_pan"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "战镐", "id": "tconstruct:war_pick"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "战斗牌子", "id": "tconstruct:battlesign"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "喷流刃", "id": "tconstruct:swasher"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "旅行者护目镜", "id": "tconstruct:travelers_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "旅行者背心", "id": "tconstruct:travelers_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "旅行者裤子", "id": "tconstruct:travelers_leggings", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "旅行者靴子", "id": "tconstruct:travelers_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镶板头盔", "id": "tconstruct:plate_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镶板胸甲", "id": "tconstruct:plate_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镶板护腿", "id": "tconstruct:plate_leggings", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镶板靴子", "id": "tconstruct:plate_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "黏液靴子", "id": "tconstruct:slime_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "黏液壳", "id": "tconstruct:slime_leggings", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "黏液鞘翅", "id": "tconstruct:slime_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "黏液头颅", "id": "tconstruct:slime_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "旅行者盾牌", "id": "tconstruct:travelers_shield"}, {"maxStackSize": 1, "maxDamage": 1, "localized": "镶板盾牌", "id": "tconstruct:plate_shield"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "水晶镞", "id": "tconstruct:crystalshot"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦黑砖", "id": "tconstruct:seared_brick"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "焦褐砖", "id": "tconstruct:scorched_brick"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "铜罐", "id": "tconstruct:copper_can"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空白沙子铸模", "id": "tconstruct:blank_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "空白红沙铸模", "id": "tconstruct:blank_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锭金质铸模", "id": "tconstruct:ingot_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锭沙子铸模", "id": "tconstruct:ingot_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锭红沙铸模", "id": "tconstruct:ingot_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粒金质铸模", "id": "tconstruct:nugget_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粒沙子铸模", "id": "tconstruct:nugget_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粒红沙铸模", "id": "tconstruct:nugget_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宝石金质铸模", "id": "tconstruct:gem_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宝石沙子铸模", "id": "tconstruct:gem_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宝石红沙铸模", "id": "tconstruct:gem_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "杆金质铸模", "id": "tconstruct:rod_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "杆沙子铸模", "id": "tconstruct:rod_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "杆红沙铸模", "id": "tconstruct:rod_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "修补件金质铸模", "id": "tconstruct:repair_kit_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "修补件沙子铸模", "id": "tconstruct:repair_kit_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "修补件红沙铸模", "id": "tconstruct:repair_kit_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小板金质铸模", "id": "tconstruct:plate_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小板沙子铸模", "id": "tconstruct:plate_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小板红沙铸模", "id": "tconstruct:plate_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "齿轮金质铸模", "id": "tconstruct:gear_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "齿轮沙子铸模", "id": "tconstruct:gear_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "齿轮红沙铸模", "id": "tconstruct:gear_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "币金质铸模", "id": "tconstruct:coin_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "币沙子铸模", "id": "tconstruct:coin_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "币红沙铸模", "id": "tconstruct:coin_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "线金质铸模", "id": "tconstruct:wire_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "线沙子铸模", "id": "tconstruct:wire_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "线红沙铸模", "id": "tconstruct:wire_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镐头金质铸模", "id": "tconstruct:pick_head_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镐头沙子铸模", "id": "tconstruct:pick_head_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "镐头红沙铸模", "id": "tconstruct:pick_head_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型斧刃金质铸模", "id": "tconstruct:small_axe_head_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型斧刃沙子铸模", "id": "tconstruct:small_axe_head_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型斧刃红沙铸模", "id": "tconstruct:small_axe_head_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型剑刃金质铸模", "id": "tconstruct:small_blade_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型剑刃沙子铸模", "id": "tconstruct:small_blade_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "小型剑刃红沙铸模", "id": "tconstruct:small_blade_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锛头金质铸模", "id": "tconstruct:adze_head_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锛头沙子铸模", "id": "tconstruct:adze_head_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锛头红沙铸模", "id": "tconstruct:adze_head_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锤头金质铸模", "id": "tconstruct:hammer_head_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锤头沙子铸模", "id": "tconstruct:hammer_head_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锤头红沙铸模", "id": "tconstruct:hammer_head_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宽刃金质铸模", "id": "tconstruct:broad_blade_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宽刃沙子铸模", "id": "tconstruct:broad_blade_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "宽刃红沙铸模", "id": "tconstruct:broad_blade_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "板斧刃金质铸模", "id": "tconstruct:broad_axe_head_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "板斧刃沙子铸模", "id": "tconstruct:broad_axe_head_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "板斧刃红沙铸模", "id": "tconstruct:broad_axe_head_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大板金质铸模", "id": "tconstruct:large_plate_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大板沙子铸模", "id": "tconstruct:large_plate_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "大板红沙铸模", "id": "tconstruct:large_plate_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绑定结金质铸模", "id": "tconstruct:tool_binding_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绑定结沙子铸模", "id": "tconstruct:tool_binding_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绑定结红沙铸模", "id": "tconstruct:tool_binding_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧套环金质铸模", "id": "tconstruct:tough_binding_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧套环沙子铸模", "id": "tconstruct:tough_binding_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧套环红沙铸模", "id": "tconstruct:tough_binding_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工具手柄金质铸模", "id": "tconstruct:tool_handle_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工具手柄沙子铸模", "id": "tconstruct:tool_handle_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工具手柄红沙铸模", "id": "tconstruct:tool_handle_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧手柄金质铸模", "id": "tconstruct:tough_handle_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧手柄沙子铸模", "id": "tconstruct:tough_handle_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "坚韧手柄红沙铸模", "id": "tconstruct:tough_handle_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓臂金质铸模", "id": "tconstruct:bow_limb_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓臂沙子铸模", "id": "tconstruct:bow_limb_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓臂红沙铸模", "id": "tconstruct:bow_limb_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓把金质铸模", "id": "tconstruct:bow_grip_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓把沙子铸模", "id": "tconstruct:bow_grip_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "弓把红沙铸模", "id": "tconstruct:bow_grip_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "头盔镶板金质铸模", "id": "tconstruct:helmet_plating_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "头盔镶板沙子铸模", "id": "tconstruct:helmet_plating_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "头盔镶板红沙铸模", "id": "tconstruct:helmet_plating_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "胸甲镶板金质铸模", "id": "tconstruct:chestplate_plating_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "胸甲镶板沙子铸模", "id": "tconstruct:chestplate_plating_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "胸甲镶板红沙铸模", "id": "tconstruct:chestplate_plating_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "护腿镶板金质铸模", "id": "tconstruct:leggings_plating_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "护腿镶板沙子铸模", "id": "tconstruct:leggings_plating_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "护腿镶板红沙铸模", "id": "tconstruct:leggings_plating_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "靴子镶板金质铸模", "id": "tconstruct:boots_plating_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "靴子镶板沙子铸模", "id": "tconstruct:boots_plating_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "靴子镶板红沙铸模", "id": "tconstruct:boots_plating_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁链基底金质铸模", "id": "tconstruct:maille_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁链基底沙子铸模", "id": "tconstruct:maille_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁链基底红沙铸模", "id": "tconstruct:maille_red_sand_cast"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石质头盔镶板", "id": "tconstruct:helmet_plating_dummy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石质胸甲镶板", "id": "tconstruct:chestplate_plating_dummy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石质护腿镶板", "id": "tconstruct:leggings_plating_dummy"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "石质靴子镶板", "id": "tconstruct:boots_plating_dummy"}, {"maxStackSize": 16, "maxDamage": 0, "localized": "毒液瓶", "id": "tconstruct:venom_bottle", "food": {"saturation": 0.0, "nutrition": 0, "alwaysEdible": true}}, {"maxStackSize": 16, "maxDamage": 0, "localized": "大地黏液瓶", "id": "tconstruct:earth_slime_bottle", "food": {"saturation": 0.0, "nutrition": 0, "alwaysEdible": true}}, {"maxStackSize": 16, "maxDamage": 0, "localized": "碧空黏液瓶", "id": "tconstruct:sky_slime_bottle", "food": {"saturation": 0.0, "nutrition": 0, "alwaysEdible": true}}, {"maxStackSize": 16, "maxDamage": 0, "localized": "灵浆瓶", "id": "tconstruct:ichor_slime_bottle", "food": {"saturation": 0.0, "nutrition": 0, "alwaysEdible": true}}, {"maxStackSize": 16, "maxDamage": 0, "localized": "末影黏液瓶", "id": "tconstruct:ender_slime_bottle", "food": {"saturation": 0.0, "nutrition": 0, "alwaysEdible": true}}, {"maxStackSize": 16, "maxDamage": 0, "localized": "岩浆瓶", "id": "tconstruct:magma_bottle"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "肉汤", "id": "tconstruct:meat_soup", "food": {"saturation": 0.6, "nutrition": 8, "alwaysEdible": false}}, {"maxStackSize": 64, "maxDamage": 0, "localized": "喷溅型玻璃瓶", "id": "tconstruct:splash_bottle"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "滞留型玻璃瓶", "id": "tconstruct:lingering_bottle"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "毒液桶", "id": "tconstruct:venom_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "大地黏液桶", "id": "tconstruct:earth_slime_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "碧空黏液桶", "id": "tconstruct:sky_slime_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "末影黏液桶", "id": "tconstruct:ender_slime_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "岩浆桶", "id": "tconstruct:magma_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "灵浆桶", "id": "tconstruct:ichor_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蜂蜜桶", "id": "tconstruct:honey_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "甜菜汤桶", "id": "tconstruct:beetroot_soup_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "蘑菇煲桶", "id": "tconstruct:mushroom_stew_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "兔肉煲桶", "id": "tconstruct:rabbit_stew_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "肉汤桶", "id": "tconstruct:meat_soup_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "粗制的药水桶", "id": "tconstruct:potion_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "焦黑熔石桶", "id": "tconstruct:seared_stone_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "焦褐熔石桶", "id": "tconstruct:scorched_stone_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融黏土桶", "id": "tconstruct:molten_clay_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融玻璃桶", "id": "tconstruct:molten_glass_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "液态灵魂桶", "id": "tconstruct:liquid_soul_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融陶瓷桶", "id": "tconstruct:molten_porcelain_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融黑曜石桶", "id": "tconstruct:molten_obsidian_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融末影珍珠桶", "id": "tconstruct:molten_ender_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "烈焰血桶", "id": "tconstruct:blazing_blood_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融绿宝石桶", "id": "tconstruct:molten_emerald_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融石英桶", "id": "tconstruct:molten_quartz_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融紫水晶桶", "id": "tconstruct:molten_amethyst_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融钻石桶", "id": "tconstruct:molten_diamond_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融残骸桶", "id": "tconstruct:molten_debris_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融铁桶", "id": "tconstruct:molten_iron_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融金桶", "id": "tconstruct:molten_gold_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融铜桶", "id": "tconstruct:molten_copper_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融钴桶", "id": "tconstruct:molten_cobalt_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融钢桶", "id": "tconstruct:molten_steel_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融黏钢桶", "id": "tconstruct:molten_slimesteel_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融紫水晶青铜桶", "id": "tconstruct:molten_amethyst_bronze_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融玫瑰金桶", "id": "tconstruct:molten_rose_gold_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融生铁桶", "id": "tconstruct:molten_pig_iron_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融玛玉灵桶", "id": "tconstruct:molten_manyullyn_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融黑色科林斯青铜桶", "id": "tconstruct:molten_hepatizon_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融余烬黏液桶", "id": "tconstruct:molten_cinderslime_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融皇后史莱姆桶", "id": "tconstruct:molten_queens_slime_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融魂钢桶", "id": "tconstruct:molten_soulsteel_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融下界合金桶", "id": "tconstruct:molten_netherite_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融骑士史莱姆桶", "id": "tconstruct:molten_knightslime_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融锡桶", "id": "tconstruct:molten_tin_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融铝桶", "id": "tconstruct:molten_aluminum_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融铅桶", "id": "tconstruct:molten_lead_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融银桶", "id": "tconstruct:molten_silver_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融镍桶", "id": "tconstruct:molten_nickel_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融锌桶", "id": "tconstruct:molten_zinc_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融铂桶", "id": "tconstruct:molten_platinum_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融钨桶", "id": "tconstruct:molten_tungsten_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融锇桶", "id": "tconstruct:molten_osmium_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融铀桶", "id": "tconstruct:molten_uranium_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融青铜桶", "id": "tconstruct:molten_bronze_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融黄铜桶", "id": "tconstruct:molten_brass_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融琥珀金桶", "id": "tconstruct:molten_electrum_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融殷钢桶", "id": "tconstruct:molten_invar_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融康铜桶", "id": "tconstruct:molten_constantan_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融白镴桶", "id": "tconstruct:molten_pewter_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融末影桶", "id": "tconstruct:molten_enderium_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融流明桶", "id": "tconstruct:molten_lumium_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融信素桶", "id": "tconstruct:molten_signalum_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融强化荧石桶", "id": "tconstruct:molten_refined_glowstone_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融强化黑曜石桶", "id": "tconstruct:molten_refined_obsidian_bucket"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "熔融镍铬合金桶", "id": "tconstruct:molten_nicrosil_bucket"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "碧空史莱姆刷怪蛋", "id": "tconstruct:sky_slime_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "末影史莱姆刷怪蛋", "id": "tconstruct:ender_slime_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黏土怪刷怪蛋", "id": "tconstruct:terracube_spawn_egg"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锁链传动轮", "block": {"crop": false}, "id": "create:chain_conveyor"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "物品舱口", "block": {"crop": false}, "id": "create:item_hatch"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "打包机", "block": {"crop": false}, "id": "create:packager"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "理包机", "block": {"crop": false}, "id": "create:repackager"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "货物蛙港", "block": {"crop": false}, "id": "create:package_frogport"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色邮箱", "block": {"crop": false}, "id": "create:white_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色邮箱", "block": {"crop": false}, "id": "create:orange_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色邮箱", "block": {"crop": false}, "id": "create:magenta_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色邮箱", "block": {"crop": false}, "id": "create:light_blue_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色邮箱", "block": {"crop": false}, "id": "create:yellow_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色邮箱", "block": {"crop": false}, "id": "create:lime_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色邮箱", "block": {"crop": false}, "id": "create:pink_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色邮箱", "block": {"crop": false}, "id": "create:gray_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色邮箱", "block": {"crop": false}, "id": "create:light_gray_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色邮箱", "block": {"crop": false}, "id": "create:cyan_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色邮箱", "block": {"crop": false}, "id": "create:purple_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色邮箱", "block": {"crop": false}, "id": "create:blue_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色邮箱", "block": {"crop": false}, "id": "create:brown_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色邮箱", "block": {"crop": false}, "id": "create:green_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色邮箱", "block": {"crop": false}, "id": "create:red_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色邮箱", "block": {"crop": false}, "id": "create:black_postbox"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "仓储链接站", "block": {"crop": false}, "id": "create:stock_link"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "仓储发报机", "block": {"crop": false}, "id": "create:stock_ticker"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红石请求器", "block": {"crop": false}, "id": "create:redstone_requester"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工厂仪表", "block": {"crop": false}, "id": "create:factory_gauge"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "白色桌布", "block": {"crop": false}, "id": "create:white_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "橙色桌布", "block": {"crop": false}, "id": "create:orange_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "品红色桌布", "block": {"crop": false}, "id": "create:magenta_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡蓝色桌布", "block": {"crop": false}, "id": "create:light_blue_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄色桌布", "block": {"crop": false}, "id": "create:yellow_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄绿色桌布", "block": {"crop": false}, "id": "create:lime_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "粉红色桌布", "block": {"crop": false}, "id": "create:pink_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "灰色桌布", "block": {"crop": false}, "id": "create:gray_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "淡灰色桌布", "block": {"crop": false}, "id": "create:light_gray_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "青色桌布", "block": {"crop": false}, "id": "create:cyan_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "紫色桌布", "block": {"crop": false}, "id": "create:purple_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "蓝色桌布", "block": {"crop": false}, "id": "create:blue_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "棕色桌布", "block": {"crop": false}, "id": "create:brown_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "绿色桌布", "block": {"crop": false}, "id": "create:green_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "红色桌布", "block": {"crop": false}, "id": "create:red_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黑色桌布", "block": {"crop": false}, "id": "create:black_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "安山桌面", "block": {"crop": false}, "id": "create:andesite_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "黄铜桌面", "block": {"crop": false}, "id": "create:brass_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "铜桌面", "block": {"crop": false}, "id": "create:copper_table_cloth"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "脉冲计时器", "block": {"crop": false}, "id": "create:pulse_timer"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "发信线圈", "id": "create:transmitter"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纸浆", "id": "create:pulp"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纸板", "id": "create:cardboard"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "纸棍", "id": "create:cardboard_sword", "toolType": "sword"}, {"maxStackSize": 1, "maxDamage": 44, "localized": "纸板头盔", "id": "create:cardboard_helmet", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 64, "localized": "纸板胸甲", "id": "create:cardboard_chestplate", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 60, "localized": "纸板护腿", "id": "create:cardboard_leggings", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 52, "localized": "纸板靴子", "id": "create:cardboard_boots", "toolType": "armor"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "纸壳包裹", "id": "create:cardboard_package_12x12"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "纸壳包裹", "id": "create:cardboard_package_10x12"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "纸壳包裹", "id": "create:cardboard_package_10x8"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "纸壳包裹", "id": "create:cardboard_package_12x10"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_creeper_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_darcy_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_evan_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_jinx_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_kryppers_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_simi_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_starlotte_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_thunder_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_up_package"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "稀有包裹", "id": "create:rare_vector_package"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "包裹过滤器", "id": "create:package_filter"}, {"maxStackSize": 1, "maxDamage": 0, "localized": "采购清单", "id": "create:shopping_list"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "呼唤铃", "block": {"crop": false}, "id": "create:desk_bell"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的工业铁块", "block": {"crop": false}, "id": "create:weathered_iron_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "纸板块", "block": {"crop": false}, "id": "create:cardboard_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "打包纸板块", "block": {"crop": false}, "id": "create:bound_cardboard_block"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木窗户", "block": {"crop": false}, "id": "create:cherry_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹窗户", "block": {"crop": false}, "id": "create:bamboo_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业铁窗户", "block": {"crop": false}, "id": "create:industrial_iron_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的工业铁窗户", "block": {"crop": false}, "id": "create:weathered_iron_window"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "樱花木窗户板", "block": {"crop": false}, "id": "create:cherry_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "竹窗户板", "block": {"crop": false}, "id": "create:bamboo_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "工业铁窗户板", "block": {"crop": false}, "id": "create:industrial_iron_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "锈蚀的工业铁窗户板", "block": {"crop": false}, "id": "create:weathered_iron_window_pane"}, {"maxStackSize": 64, "maxDamage": 0, "localized": "Bronze Fluid Tank", "block": {"crop": false}, "id": "undefined:bronze_fluid_tank"}]