{"client": {"disableColoredCableRecipesInJEI": true, "enableFacadesInJEI_comment": "Show facades in JEI ingredient list", "enableFacadesInJEI": true, "enableFacadeRecipesInJEI_comment": "Show facade recipes in JEI for supported blocks", "enableFacadeRecipesInJEI": true, "enableEffects": true, "useTerminalUseLargeFont": false, "useColoredCraftingStatus": true, "PowerUnit_comment": "Power unit shown in AE UIs", "PowerUnit": "ae", "showDebugGuiOverlays_comment": "Show debugging GUI overlays", "showDebugGuiOverlays": false, "showPlacementPreview_comment": "Show a preview of part and facade placement", "showPlacementPreview": true, "notifyForFinishedCraftingJobs_comment": "Show toast when long-running crafting jobs finish.", "notifyForFinishedCraftingJobs": true, "clearGridOnClose_comment": "Automatically clear the crafting/encoding grid when closing the terminal", "clearGridOnClose": false, "terminalMargin_comment": "The vertical margin to apply when sizing terminals. Used to make room for centered item mod search bars", "terminalMargin": 25}, "terminals": {"terminalStyle": "small", "pinAutoCraftedItems_comment": "Pin items that the player auto-crafts to the top of the terminal", "pinAutoCraftedItems": true}, "search": {"searchModNameInTooltips_comment": "Should the mod name be included when searching in tooltips.", "searchModNameInTooltips": false, "useExternalSearch_comment": "Replaces AEs own search with the search of REI or JEI", "useExternalSearch": false, "clearExternalSearchOnOpen_comment": "When using useExternalSearch, clears the search when the terminal opens", "clearExternalSearchOnOpen": true, "syncWithExternalSearch_comment": "When REI/JEI is installed, automatically set the AE or REI/JEI search text when either is changed while the terminal is open", "syncWithExternalSearch": true, "rememberLastSearch_comment": "Remembers the last search term and restores it when the terminal opens", "rememberLastSearch": true, "autoFocusSearch_comment": "Automatically focuses the search field when the terminal opens", "autoFocusSearch": false}, "tooltips": {"showCellUpgrades_comment": "Show installed upgrades in the tooltips of storage cells, color applicators and matter cannons", "showCellUpgrades": true, "showCellContent_comment": "Show a preview of the content in the tooltips of storage cells, color applicators and matter cannons", "showCellContent": true, "maxCellContentShown_comment": "The maximum number of content entries to show in the tooltip of storage cells, color applicators and matter cannons", "maxCellContentShown": 5, "enableGuideHotkey_comment": "Enables the 'hold key to show guide' functionality in tooltips", "enableGuideHotkey": true}}