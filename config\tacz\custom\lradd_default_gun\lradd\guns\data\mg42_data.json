{
  
  "ammo": "tacz:308",

  "ammo_amount": 150,
  "bolt": "open_bolt",
  "rpm": 840,
  "bullet": {
    // 寿命，单位秒
    "life": 0.8,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 10,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.4,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 2,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        {"distance": 1.5, "damage": 11},
        {"distance": 9, "damage": 10},
        {"distance": 23, "damage": 9.2},
        {"distance": 45, "damage": 8.6},
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 7.8}
      ]
    },
    // 速度 m/s
    "speed": 220,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.01,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 2
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  "reload": {
    "type": "magazine",
    "feed": {
      "empty": 5,
      "tactical": 5
    },
    "cooldown": {
      "empty": 5.2083,
      "tactical": 5.2083
    }
  },
  "draw_time": 1,
  "put_away_time": 1.125,
  "aim_time": 0.2,
  "sprint_time": 0.3,
  "fire_mode": [
    "auto",
    "semi"
  ],
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.825, 0.825]},
      {"time": 0.38, "value": [0.825, 0.825]},
      {"time": 0.72, "value": [-0.27, -0.27]},
      {"time": 1.14, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [-0.85, -0.85]},
      {"time": 0.36, "value": [-0.2, -0.2]},
      {"time": 0.79, "value": [0, 0]}
    ]
  },
  "inaccuracy": {
    "stand": 3.8,
    "move": 5.2,
    "sneak": 3.12,
    "lie": 2.65,
    "aim": 0.425
  },
  "move_speed": {
    "base": 0.75,
    // 瞄准情况
    "aim": 0.635
  }
}