{"modPriorities": ["minecraft", "kube<PERSON>s", "crafttweaker", "create", "thermal", "immersiveengineering", "mekanism"], "stoneStrata": ["stone", "nether", "deepslate", "granite", "diorite", "andesite"], "tags": ["forge:nuggets/{material}", "forge:dusts/{material}", "forge:gears/{material}", "forge:gems/{material}", "forge:ingots/{material}", "forge:raw_materials/{material}", "forge:ores/{material}", "forge:plates/{material}", "forge:rods/{material}", "forge:wires/{material}", "forge:storage_blocks/{material}", "forge:storage_blocks/raw_{material}"], "materials": ["aeternium", "aluminum", "amber", "apatite", "bitumen", "brass", "bronze", "charcoal", "chrome", "cinnabar", "coal", "coal_coke", "cobalt", "constantan", "copper", "diamond", "electrum", "elementium", "emerald", "enderium", "fluorite", "gold", "graphite", "invar", "iridium", "iron", "lapis", "lead", "lumium", "mithril", "netherite", "nickel", "obsidian", "osmium", "peridot", "platinum", "potassium_nitrate", "ruby", "sapphire", "signalum", "silver", "steel", "sulfur", "tin", "tungsten", "uranium", "zinc"], "priorityOverrides": {}, "customTags": {}, "tagOwnerships": {}, "itemTagInheritanceMode": "ALLOW", "itemTagInheritance": {}, "blockTagInheritanceMode": "ALLOW", "blockTagInheritance": {}, "ignoredTags": [], "ignoredItems": [], "ignoredRecipeTypes": ["cucumber:shaped_tag"], "ignoredRecipes": [], "itemsHidingJeiRei": true}