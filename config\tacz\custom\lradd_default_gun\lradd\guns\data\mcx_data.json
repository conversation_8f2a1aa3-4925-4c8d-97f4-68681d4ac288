{
  
  "ammo": "tacz:68x51fury",

  "ammo_amount": 30,
  "extended_mag_ammo_amount": [
    35,
    40,
    50
  ],
  "bolt": "open_bolt",
  "rpm": 600,
  "bullet": {
    // 寿命，单位秒
    "life": 0.8,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 8.2,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.4,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 1.75,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        {"distance": 1.5, "damage": 9},
        {"distance": 10, "damage": 8.2},
        {"distance": 26, "damage": 7.8},
        {"distance": 40, "damage": 7.2},
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 6.5}
      ]
    },
    // 速度 m/s
    "speed": 220,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.01,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 1
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  "reload": {
    "type": "magazine",
    "feed": {
      "empty": 3,
      "tactical": 3
    },
    "cooldown": {
      "empty": 3.75,
      "tactical": 3.125
    }
  },
  "draw_time": 1.1,
  "put_away_time": 0.4,
  "aim_time": 0.2,
  "sprint_time": 0.3,
  "fire_mode": [
    "auto",
    "semi"
  ],
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.435, 0.435]},
      {"time": 0.16, "value": [0.32, 0.32]},
      {"time": 0.27, "value": [-0.17, -0.17]},
      {"time": 0.52, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [-0.35, -0.25]},
      {"time": 0.25, "value": [-0.125, 0.075]},
      {"time": 0.4, "value": [0, 0]}
    ]
  },
  "inaccuracy": {
    "stand": 1.8,
    "move": 2.75,
    "sneak": 1.15,
    "lie": 0.7,
    "aim": 0.15
  },
  "move_speed": {
    "base": 0.9,
    // 瞄准情况
    "aim": 0.84
  },
  // 开放的配件槽。未指定的槽位默认关闭。全部配件槽类型有:
  // scope, stock, muzzle, grip, laser, extended_mag
  "allow_attachment_types": [
    "scope",
    "stock",
    "muzzle",
    "grip",
    "laser",
    "extended_mag"
  ]
}