{
  "slot": "lradd:attachment/slot/scope_holosun",
  // 配件的默认模型，不可为空
  "model": "lradd:attachment/scope_holosun_geo",
  // 配件默认模型使用的材质，不可为空
  "texture": "lradd:attachment/uv/scope_holosun",
  // 只有 瞄具 配件需要设置这个属性，表示瞄具的放大倍率。
  // 玩家可以在数组设置的几个倍率之间切换
  "lod": {
    "model": "lradd:attachment/lod/scope_holosun_lod",
    "texture": "lradd:attachment/lod/scope_holosun_lod"
  },
  "zoom": [
    1.33,
    2.5
  ],
  // 如果配件是瞄具，且为筒状放大瞄具，此选项应为true
  "scope": true,
  // 如果配件是瞄具，且为红点或全息瞄具，此选项应为true
  "sight": false,
  // 只有 瞄具 配件需要设置这个属性，表示开镜后，枪身和瞄具渲染的fov。
  // 默认情况下，mc 渲染手部模型的 fov 为 70。
  "fov": 40,
  "sounds": {
    "install": "tacz:attachments/scope_general_c",
    "uninstall": "tacz:attachments/scope_general_c"
  }
}