[15:08:17] [INIT] KubeJS 2001.6.5-build.16; MC 2001 forge
[15:08:17] [INIT] Loaded plugins:
[15:08:17] [INIT] - dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgePlugin
[15:08:17] [INIT] - dev.latvian.mods.kubejs.forge.BuiltinKubeJSForgeClientPlugin
[15:08:17] [INIT] - com.prunoideae.powerfuljs.forge.PowerfulJSPluginForge
[15:08:17] [INIT] - com.almostreliable.morejs.Plugin
[15:08:17] [INIT] - dev.latvian.kubejs.mekanism.MekanismKubeJSPlugin
[15:08:17] [INIT] - net.liopyu.menujs.MenuJSPlugin
[15:08:17] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbquests.kubejs.KubeJSIntegration
[15:08:17] [INIT] - dev.ftb.mods.ftbxmodcompat.ftbteams.kubejs.FTBTeamsKubeJSPlugin
[15:08:17] [INIT] - net.prizowo.keyboardjs.kubejs.KeyboardJSPlugin
[15:08:17] [INIT] - com.almostreliable.lootjs.kube.LootJSPlugin
[15:08:17] [INIT] - com.probejs.ProbeJSPlugin
[15:08:17] [INIT] - com.tom.createores.kubejs.KubeJSExcavation
[15:08:17] [INIT] - net.liopyu.entityjs.EntityJSPlugin
[15:08:17] [INIT] - dev.latvian.mods.kubejs.create.KubeJSCreatePlugin
[15:08:17] [INFO] example.js#5: Hello, World! (Loaded client scripts)
[15:08:17] [INFO] Loaded script client_scripts:example.js in 0.001 s
[15:08:17] [INFO] Loaded 1/1 KubeJS client scripts in 0.023 s with 0 errors and 0 warnings
[15:08:43] [INFO] Client resource reload complete!
