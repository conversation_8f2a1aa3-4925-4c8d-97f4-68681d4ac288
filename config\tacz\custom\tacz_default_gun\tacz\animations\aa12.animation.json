{"format_version": "1.8.0", "animations": {"static_idle": {"loop": true, "animation_length": 0.4, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": [90.8048, -31.27324, -131.00752], "position": [8.025, -9.5, -6.2], "scale": [1, 1.5, 1]}, "constraint": {"rotation": [0.1, 0.1, 0.1], "position": [0.1, 0.1, 0.4]}}}, "static_bolt_caught": {"animation_length": 0.45, "bones": {"bullet": {"scale": 0}}}, "idle_VG": {"animation_length": 0.45, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": [103.186, -16.37578, -194.87667], "position": [7.175, -12.2, -9.625], "scale": [1, 1.5, 1]}}}, "idle_AFG": {"animation_length": 0.45, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": [94.02477, -20.82142, -149.6082], "position": [8.55, -9.25, -8.725], "scale": [1, 1.5, 1]}}}, "draw": {"animation_length": 0.95, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": [90.8048, -31.27324, -131.00752], "position": [8.025, -9.5, -6.2], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [42.57929, -37.55577, -0.08401], "lerp_mode": "catmullrom"}, "0.2": {"post": [24.01193, -29.89831, -5.38555], "lerp_mode": "catmullrom"}, "0.3": {"post": [-3.04998, -9.12352, 1.70645], "lerp_mode": "catmullrom"}, "0.5": {"post": [-1.46, 0.47, -1.3], "lerp_mode": "catmullrom"}, "0.6": {"post": [-0.55, -0.01, 0.82], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, -0.3], "lerp_mode": "catmullrom"}, "0.9": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [3.3, -17, 5.7], "lerp_mode": "catmullrom"}, "0.2": {"post": [1.18, -6.07, -2.76], "lerp_mode": "catmullrom"}, "0.45": {"post": [0.41, -0.67, -0.3], "lerp_mode": "catmullrom"}, "0.55": {"post": [0.17, 0, 0.29938], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.1, 0, 0.6], "lerp_mode": "catmullrom"}, "0.7": {"post": [0.03, 0.04, -0.06188], "lerp_mode": "catmullrom"}, "0.8": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.89297, -0.12891, 0.44141], "lerp_mode": "catmullrom"}, "0.35": {"post": [0.94297, -0.19141, 0.44531], "lerp_mode": "catmullrom"}, "0.55": {"post": [-0.1, 0.01953, -0.25391], "lerp_mode": "catmullrom"}, "0.65": {"post": [0.1, 0.01953, 0.27344], "lerp_mode": "catmullrom"}, "0.8": {"post": [-0.0375, 0.01953, -0.01953], "lerp_mode": "catmullrom"}, "0.95": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.1": {"effect": "aa12_raise"}}}, "put_away": {"animation_length": 0.6, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [90.8048, -31.27324, -131.00752], "0.2": [112.81797, -22.00113, -177.77512]}, "position": {"0.0": [8.025, -9.5, -6.2], "0.1": [9.07, -9.22, -5.17], "0.2": [9.005, -7.65, -4.15]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.05": {"post": [-0.51171, -3.6801, -4.19401], "lerp_mode": "catmullrom"}, "0.2": {"post": [4.36882, -11.73179, -10.29382], "lerp_mode": "catmullrom"}, "0.35": {"post": [11.60471, -21.56305, -3.66288], "lerp_mode": "catmullrom"}, "0.45": {"post": [16.97, -33.62, 3.38], "lerp_mode": "catmullrom"}, "0.6": {"post": [34.73, -44.04, -3.09], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.05": {"post": [0, -0.49, -0.02], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.52, -1.47, -0.56], "lerp_mode": "catmullrom"}, "0.35": {"post": [-0.36, -2.92, -0.84], "lerp_mode": "catmullrom"}, "0.45": {"post": [0.13, -5.86, -0.16], "lerp_mode": "catmullrom"}, "0.6": {"post": [1.72, -9.68, 0.16], "lerp_mode": "catmullrom"}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.35, 0.025, -0.225], "lerp_mode": "catmullrom"}, "0.4": {"post": [0.1, 0.025, 0.075], "lerp_mode": "catmullrom"}, "0.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.15": {"effect": "aa12_drop"}}}, "reload_tactical": {"animation_length": 3.25, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [90.8048, -31.27324, -131.00752], "0.1": {"pre": [90.90379, -33.32282, -117.11885], "post": [90.90379, -33.32282, -117.11885], "lerp_mode": "catmullrom"}, "0.2": {"post": [90.8048, -31.27324, -131.00752], "lerp_mode": "catmullrom"}, "0.35": {"post": [118.49093, -23.96373, -176.7233], "lerp_mode": "catmullrom"}, "0.5": [106.56923, -7.85181, -193.84542], "0.6": [106.56923, -7.85181, -193.84542], "0.7": [106.56923, -7.85181, -193.84542], "0.85": [106.56923, -7.85181, -193.84542], "1.7": {"pre": [104.99, -6.91, -195.53], "post": [104.99, -6.91, -195.53], "lerp_mode": "catmullrom"}, "1.85": {"post": [110.28021, -42.51898, -208.67734], "lerp_mode": "catmullrom"}, "2.0": {"post": [110.31599, -34.66734, -218.44448], "lerp_mode": "catmullrom"}, "2.1": {"post": [108.25456, -32.79464, -204.78161], "lerp_mode": "catmullrom"}, "2.25": {"post": [109.50334, -32.83852, -205.34122], "lerp_mode": "catmullrom"}, "2.35": [129.74122, -28.25695, -209.97569], "2.45": [128.18542, -24.36787, -206.46872], "2.6": {"pre": [87.50262, -23.99609, -138.61183], "post": [87.50262, -23.99609, -138.61183], "lerp_mode": "catmullrom"}, "2.7": [90.8048, -31.27324, -131.00752], "2.8": [90.8048, -31.27324, -131.00752]}, "position": {"0.0": [8.025, -9.5, -6.2], "0.1": [8.425, -9.5, -6.2], "0.4": {"pre": [8.83971, -14.39591, -0.7183], "post": [8.83971, -14.39591, -0.7183], "lerp_mode": "catmullrom"}, "0.55": [8.38365, -13.93931, 0.43136], "0.6": [8.38365, -13.93931, 0.43136], "0.7": [8.38365, -13.93931, 0.43136], "0.85": {"pre": [8.38, -22.54, 0.43], "post": [8.38, -22.54, 0.43], "lerp_mode": "catmullrom"}, "1.0": {"post": [10.11, -25.85, 1.03], "lerp_mode": "catmullrom"}, "1.15": {"post": [8.38, -30.84, 5.23], "lerp_mode": "catmullrom"}, "1.45": {"post": [8.38, -35.04, 8.13], "lerp_mode": "catmullrom"}, "1.7": {"post": [7.98, -33.64, 2.73], "lerp_mode": "catmullrom"}, "1.85": [8.68, -24.04, -1.87], "1.9": [8.72, -24.525, -2.1], "2.0": {"pre": [9.07, -23.2, -2.1], "post": [9.07, -23.2, -2.1], "lerp_mode": "catmullrom"}, "2.1": {"post": [8.29, -22.83, -2.08], "lerp_mode": "catmullrom"}, "2.2": [8.1, -21.67187, -2.04], "2.25": [8.08, -20.75562, -1.87], "2.35": [8.28, -14.14, -0.47], "2.45": [8.28, -14.14, -0.47], "2.6": {"pre": [8.64, -11.82, -3.34], "post": [8.64, -11.82, -3.34], "lerp_mode": "catmullrom"}, "2.7": [8.025, -9.5, -6.2], "2.8": [8.025, -9.5, -6.2]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-2.26525, -2.37715, -5.04106], "lerp_mode": "catmullrom"}, "0.3": {"post": [-13.34898, -0.63534, -29.48993], "lerp_mode": "catmullrom"}, "0.4": {"post": [-14.30797, -1.31435, -28.46174], "lerp_mode": "catmullrom"}, "0.5": {"post": [-14.35415, -1.25247, -30.51516], "lerp_mode": "catmullrom"}, "0.6": {"post": [-15.05, -2.07828, -29.78], "lerp_mode": "catmullrom"}, "0.7": {"post": [-16.71249, -4.04284, -31.45875], "lerp_mode": "catmullrom"}, "0.8": {"post": [-17.77, -4.10828, -35.83], "lerp_mode": "catmullrom"}, "0.9": {"post": [-17.72867, -3.92054, -32.45978], "lerp_mode": "catmullrom"}, "1.0": {"post": [-17.43, -3.85828, -33.99], "lerp_mode": "catmullrom"}, "1.1": {"post": [-16.83, -3.82828, -35.24], "lerp_mode": "catmullrom"}, "1.3": {"post": [-16.75747, -3.10868, -35.18341], "lerp_mode": "catmullrom"}, "1.5": {"post": [-18.34, -3.15703, -34.44], "lerp_mode": "catmullrom"}, "1.7": {"post": [-19.01, -5.25703, -35.87], "lerp_mode": "catmullrom"}, "1.85": {"post": [-19.00565, -5.03644, -37.49036], "lerp_mode": "catmullrom"}, "1.95": {"post": [-20.42, -3.94703, -35.02], "lerp_mode": "catmullrom"}, "2.05": {"post": [-20.51272, -3.25081, -36.33603], "lerp_mode": "catmullrom"}, "2.2": {"post": [-19.88, -3.28703, -36.41], "lerp_mode": "catmullrom"}, "2.3": {"post": [-19.42, -3.39703, -38.19], "lerp_mode": "catmullrom"}, "2.35": {"post": [-19.2, -3.65703, -35.42], "lerp_mode": "catmullrom"}, "2.4": {"post": [-20.43386, -2.85383, -35.8955], "lerp_mode": "catmullrom"}, "2.45": {"post": [-18.93009, -4.62747, -37.24338], "lerp_mode": "catmullrom"}, "2.55": {"post": [-14.39944, -3.25081, -27.73603], "lerp_mode": "catmullrom"}, "2.65": {"post": [-3.21178, -0.31007, -3.2743], "lerp_mode": "catmullrom"}, "2.8": {"post": [1.63475, 1.30484, 2.98881], "lerp_mode": "catmullrom"}, "2.95": {"post": [0.60157, -0.76065, 0.92954], "lerp_mode": "catmullrom"}, "3.0": {"post": [-1.09609, -0.51, 0.0475], "lerp_mode": "catmullrom"}, "3.05": {"post": [-1.74609, 0, 1.08516], "lerp_mode": "catmullrom"}, "3.15": {"post": [-0.2, 0, -0.09844], "lerp_mode": "catmullrom"}, "3.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [0.31, -0.16625, -0.98], "lerp_mode": "catmullrom"}, "0.25": {"post": [0.69729, -0.08395, -1.7], "lerp_mode": "catmullrom"}, "0.35": {"post": [0.76538, 0.53752, -0.65], "lerp_mode": "catmullrom"}, "0.45": {"post": [0.76538, 0.56799, 0.075], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.76409, 0.28728, -0.01], "lerp_mode": "catmullrom"}, "0.75": {"post": [0.8, 0.06, -0.01], "lerp_mode": "catmullrom"}, "0.8": {"post": [1.16216, -0.3007, 0], "lerp_mode": "catmullrom"}, "0.95": {"post": [0.32922, 0.56157, 0.01], "lerp_mode": "catmullrom"}, "1.15": {"post": [0.36682, 0.03011, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0.4642, -0.07667, 0.02031], "lerp_mode": "catmullrom"}, "1.45": {"post": [0.87795, 0.15254, -0.04422], "lerp_mode": "catmullrom"}, "1.6": {"post": [0.94795, 0.33832, 0], "lerp_mode": "catmullrom"}, "1.85": {"post": [0.95666, 0.40277, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.14281, 0.49453, 0], "lerp_mode": "catmullrom"}, "2.25": {"post": [1.16, 0.58, 0], "lerp_mode": "catmullrom"}, "2.35": {"post": [1.58, -1.27734, 0.02], "lerp_mode": "catmullrom"}, "2.45": {"post": [1.5253, -0.85543, 0], "lerp_mode": "catmullrom"}, "2.65": {"post": [-0.58906, -1.87328, -0.8475], "lerp_mode": "catmullrom"}, "2.75": {"post": [-0.42, -1.42, -0.92], "lerp_mode": "catmullrom"}, "2.85": {"post": [-0.1, -0.27, -0.50094], "lerp_mode": "catmullrom"}, "2.95": {"post": [0, 0, 0.36719], "lerp_mode": "catmullrom"}, "3.05": {"post": [0, 0, -0.23437], "lerp_mode": "catmullrom"}, "3.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "magazine_and_bullet": {"rotation": {"0.7": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.85": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.05": {"post": [13, 0, -16], "lerp_mode": "catmullrom"}, "1.2": {"post": [0, 0, 11], "lerp_mode": "catmullrom"}, "1.65": {"post": [0, 0, 11], "lerp_mode": "catmullrom"}, "1.85": [-4.97275, 0.52199, 1.97733], "2.0": [-5.75048, -0.69419, -9.56291], "2.1": [-4.67, 0, -1.33], "2.25": [0, 0, 0], "2.35": [0, 0, 0]}, "position": {"0.7": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.85": {"post": [0, -8.2, -0.61719], "lerp_mode": "catmullrom"}, "1.05": {"post": [1.28045, -12.73791, 0.45742], "lerp_mode": "catmullrom"}, "1.2": {"post": [-0.1, -22.6, 6.3], "lerp_mode": "catmullrom"}, "1.65": {"post": [-0.1, -24.3, 3.8], "lerp_mode": "catmullrom"}, "1.85": [0.4, -8.6, -0.7], "1.9": [0.41, -9.16812, -0.83], "2.0": [0.42, -7.93, -0.96], "2.1": [0.2, -7.3, -0.725], "2.25": [0.2, -5.5, -0.6], "2.35": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "camera": {"relative_to": {"rotation": "entity"}, "rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.28, 0.82813, -1.41797], "lerp_mode": "catmullrom"}, "0.45": {"post": [2.7, 1.28516, -1.41797], "lerp_mode": "catmullrom"}, "0.7": {"post": [2.43, 1.44922, -1.41797], "lerp_mode": "catmullrom"}, "0.8": {"post": [3.14063, 1.16016, -2.4625], "lerp_mode": "catmullrom"}, "0.95": {"post": [2.54813, 1.11719, -1.0625], "lerp_mode": "catmullrom"}, "1.1": {"post": [2.89516, 1.19922, -1.74609], "lerp_mode": "catmullrom"}, "1.35": {"post": [3.25016, 1.11719, -1.41797], "lerp_mode": "catmullrom"}, "1.55": {"post": [2.91781, 1.03516, -1.41797], "lerp_mode": "catmullrom"}, "1.85": {"post": [1.89, 1.12109, -1.41797], "lerp_mode": "catmullrom"}, "1.9": {"post": [1.915, 1.11, -1.21094], "lerp_mode": "catmullrom"}, "2.05": {"post": [2.22, 1.03906, -1.41797], "lerp_mode": "catmullrom"}, "2.2": {"post": [2.135, 1.24609, -1.41797], "lerp_mode": "catmullrom"}, "2.3": {"post": [2.77781, 1.41016, -2.38672], "lerp_mode": "catmullrom"}, "2.45": {"post": [2.06312, 1.41016, -0.87109], "lerp_mode": "catmullrom"}, "2.6": {"post": [2.475, 1.28516, -1.41797], "lerp_mode": "catmullrom"}, "2.7": {"post": [2.305, 0.95313, -1.41797], "lerp_mode": "catmullrom"}, "2.9": {"post": [0.16406, 0.12109, 0], "lerp_mode": "catmullrom"}, "2.95": {"post": [0.24109, 0.02, 0.42969], "lerp_mode": "catmullrom"}, "3.05": {"post": [-0.125, -0.00391, -0.17969], "lerp_mode": "catmullrom"}, "3.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "wfoly_sh_jak12_reload_raise"}, "0.65": {"effect": "aa12_reload_fast_magout"}, "1.35": {"effect": "aa12_inspect_mvmnt"}, "1.85": {"effect": "aa12_reload_maghits"}, "2.2": {"effect": "aa12_reload_end"}, "2.25": {"effect": "aa12_reload_magin"}}}, "reload_empty": {"animation_length": 4.05, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [90.8048, -31.27324, -131.00752], "0.15": {"pre": [100.25482, -32.19326, -129.25715], "post": [100.25482, -32.19326, -129.25715], "lerp_mode": "catmullrom"}, "0.35": {"post": [90.8048, -31.27324, -131.00752], "lerp_mode": "catmullrom"}, "0.5": {"post": [97.57293, -18.64279, -165.39321], "lerp_mode": "catmullrom"}, "0.6": [116.4136, -22.44744, -206.89084], "0.75": [116.4136, -22.44744, -206.89084], "0.8": {"pre": [99.16224, -31.98438, -203.31958], "post": [99.16224, -31.98438, -203.31958], "lerp_mode": "catmullrom"}, "0.9": {"post": [98.63923, 0.60232, -225.61212], "lerp_mode": "catmullrom"}, "0.95": {"post": [98.587, 1.12665, -229.07303], "lerp_mode": "catmullrom"}, "1.5": [94.05549, -23.29996, -197.24305], "1.7": [102.26769, -40.586, -198.90312], "1.85": [102.26769, -40.586, -198.90312], "2.0": [92.16888, -46.1307, -190.21327], "2.05": [92.39061, -43.54599, -192.69253], "2.15": [117.47967, -40.72318, -192.47317], "2.2": [117.47967, -40.72318, -192.47317], "2.35": {"pre": [130.57577, -16.61419, -185.9138], "post": [130.57577, -16.61419, -185.9138], "lerp_mode": "catmullrom"}, "2.5": [126.0773, 31.43308, -265.02005], "2.6": [120.10752, 21.7553, -271.9564], "2.7": [124.55644, 24.69941, -269.87717], "2.8": [87.77938, 30.00511, -272.20886], "2.85": {"pre": [105.95478, 31.45751, -276.95181], "post": [105.95478, 31.45751, -276.95181], "lerp_mode": "catmullrom"}, "2.9": {"post": [117.51258, 28.78207, -266.6437], "lerp_mode": "catmullrom"}, "2.95": {"post": [115.51258, 28.78207, -266.6437], "lerp_mode": "catmullrom"}, "3.05": [147.60786, 32.07459, -271.38781], "3.1": [145.01258, 28.78207, -266.6437], "3.2": {"pre": [129.00265, -15.26395, -205.31163], "post": [129.00265, -15.26395, -205.31163], "lerp_mode": "catmullrom"}, "3.25": {"post": [90.8048, -31.27324, -131.00752], "lerp_mode": "catmullrom"}, "3.35": [90.8048, -31.27324, -131.00752]}, "position": {"0.0": [8.025, -9.5, -6.2], "0.35": {"pre": [8.425, -9.5, -6.2], "post": [8.425, -9.5, -6.2], "lerp_mode": "catmullrom"}, "0.45": {"post": [8.545, -11.14, -4.94], "lerp_mode": "catmullrom"}, "0.55": {"post": [8.325, -12.8, -1.905], "lerp_mode": "catmullrom"}, "0.7": [8.04453, -12.9, -1.5], "0.75": [8.04453, -12.9, -1.5], "0.8": {"pre": [8.74453, -17.9, 0], "post": [8.74453, -17.9, 0], "lerp_mode": "catmullrom"}, "0.9": {"post": [11.34453, -19.1, 3.8], "lerp_mode": "catmullrom"}, "0.95": {"post": [12.14453, -27.2, 2.6], "lerp_mode": "catmullrom"}, "1.15": {"post": [13.84453, -24.9, 4.8], "lerp_mode": "catmullrom"}, "1.5": [13.16982, -23.89335, 2.20178], "1.6": [11.18041, -22.78014, 0.21293], "1.7": [8.79344, -22.715, -0.98], "1.85": [9.07144, -22.02749, -1.25304], "2.0": [9.09344, -21.96, -0.68], "2.05": [8.79344, -21.99, -0.48], "2.15": [7.87344, -12.8, -0.6], "2.2": [7.87344, -12.8, -0.6], "2.3": {"pre": [8.59, -12.45, -0.82], "post": [8.59, -12.45, -0.82], "lerp_mode": "catmullrom"}, "2.4": {"post": [9.49, -9.76, -1.13], "lerp_mode": "catmullrom"}, "2.5": [9.15, -5.4, -3.1], "2.6": [9.17094, -5.4, -2.95313], "2.7": [9.57969, -5.4, -1.53282], "2.8": [9.41719, -5.4, 4.35625], "2.95": [9.41719, -5.4, 4.35625], "3.05": [9.23125, -5.4, -1.9], "3.1": [9.23125, -5.4, -1.9], "3.2": {"pre": [10, -8.92, -4.05], "post": [10, -8.92, -4.05], "lerp_mode": "catmullrom"}, "3.25": {"post": [8.7, -10.775, -6.2], "lerp_mode": "catmullrom"}, "3.35": [8.025, -9.5, -6.2]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.15": {"post": [-3.20127, -3.07695, -1.78944], "lerp_mode": "catmullrom"}, "0.3": {"post": [-13.97, -1.75125, -11.33], "lerp_mode": "catmullrom"}, "0.35": {"post": [-16.41043, -2.87458, -15.099], "lerp_mode": "catmullrom"}, "0.4": {"post": [-18.13432, -1.44164, -12.19443], "lerp_mode": "catmullrom"}, "0.5": {"post": [-18.21036, -1.43855, -14.00979], "lerp_mode": "catmullrom"}, "0.6": {"post": [-17.8, -2.04125, -12.49], "lerp_mode": "catmullrom"}, "0.7": {"post": [-17.43666, -3.86167, -12.43741], "lerp_mode": "catmullrom"}, "0.85": {"post": [-17.90681, -4.46077, -13.12473], "lerp_mode": "catmullrom"}, "0.95": {"post": [-16.54, -5.07125, -18.03], "lerp_mode": "catmullrom"}, "1.05": {"post": [-15.62692, -4.62862, -14.41546], "lerp_mode": "catmullrom"}, "1.15": {"post": [-15.98028, -4.83815, -16.24945], "lerp_mode": "catmullrom"}, "1.25": {"post": [-16.49, -4.10125, -14.39], "lerp_mode": "catmullrom"}, "1.35": {"post": [-16.53832, -3.9979, -14.51685], "lerp_mode": "catmullrom"}, "1.45": {"post": [-16.17556, -4.56457, -17.97802], "lerp_mode": "catmullrom"}, "1.6": {"post": [-16.58255, -7.98343, -25.61325], "lerp_mode": "catmullrom"}, "1.7": {"post": [-19.2722, -8.07671, -28.13582], "lerp_mode": "catmullrom"}, "1.75": {"post": [-20.05845, -7.73942, -29.55901], "lerp_mode": "catmullrom"}, "1.8": {"post": [-20.05812, -7.86125, -29.96375], "lerp_mode": "catmullrom"}, "1.9": {"post": [-19.61203, -8.92125, -30.25375], "lerp_mode": "catmullrom"}, "2.0": {"post": [-19.15594, -9.21125, -29.81375], "lerp_mode": "catmullrom"}, "2.05": {"post": [-19.39933, -9.11526, -29.83487], "lerp_mode": "catmullrom"}, "2.15": {"post": [-18.57817, -5.91257, -28.89092], "lerp_mode": "catmullrom"}, "2.2": {"post": [-16.63017, -6.90509, -32.91995], "lerp_mode": "catmullrom"}, "2.25": {"post": [-16.83766, -7.44125, -29.72375], "lerp_mode": "catmullrom"}, "2.3": {"post": [-17.33766, -7.53125, -27.72375], "lerp_mode": "catmullrom"}, "2.35": {"post": [-16.91609, -6.90481, -22.60174], "lerp_mode": "catmullrom"}, "2.5": {"post": [-6.19875, -3.82658, -0.5644], "lerp_mode": "catmullrom"}, "2.65": {"post": [-7.05067, -2.00156, 10.65747], "lerp_mode": "catmullrom"}, "2.7": {"post": [-7.52844, -1.90953, 11], "lerp_mode": "catmullrom"}, "2.8": {"post": [-8.53268, -3.27654, 12.62551], "lerp_mode": "catmullrom"}, "2.85": {"post": [-8.72844, -2.73953, 13.74], "lerp_mode": "catmullrom"}, "3.0": {"post": [-8.44775, -2.7528, 11.89159], "lerp_mode": "catmullrom"}, "3.1": {"post": [-5.70228, -0.35019, 17.2522], "lerp_mode": "catmullrom"}, "3.2": {"post": [-3.84844, -1.02953, 15.56], "lerp_mode": "catmullrom"}, "3.3": {"post": [0.85156, -0.84953, 13.73], "lerp_mode": "catmullrom"}, "3.5": {"post": [-6.17059, -1.3766, 4.49142], "lerp_mode": "catmullrom"}, "3.65": {"post": [-0.25, -0.8, -1.3], "lerp_mode": "catmullrom"}, "3.75": {"post": [0, 0.1, 0.9], "lerp_mode": "catmullrom"}, "3.9": {"post": [0, 0, -0.2], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-0.07047, -1.25859, -0.14609], "lerp_mode": "catmullrom"}, "0.25": {"post": [-0.29125, -1.09547, -1.6225], "lerp_mode": "catmullrom"}, "0.35": {"post": [-0.30781, -0.04766, -1.825], "lerp_mode": "catmullrom"}, "0.45": {"post": [-0.35781, 0.25234, -0.06719], "lerp_mode": "catmullrom"}, "0.55": {"post": [-0.38281, 0.06234, 0.55281], "lerp_mode": "catmullrom"}, "0.65": {"post": [-0.30781, 0.05234, 0.30781], "lerp_mode": "catmullrom"}, "0.75": {"post": [-0.34, 0.08, 0.31], "lerp_mode": "catmullrom"}, "0.8": {"post": [-0.18281, 0.08593, 0.30781], "lerp_mode": "catmullrom"}, "0.95": {"post": [0.19219, -0.66953, 0.30781], "lerp_mode": "catmullrom"}, "1.1": {"post": [0.04219, -0.94922, 0.30781], "lerp_mode": "catmullrom"}, "1.3": {"post": [-0.30781, -0.33203, 0.30781], "lerp_mode": "catmullrom"}, "1.45": {"post": [-0.30781, 0.10234, 0.30781], "lerp_mode": "catmullrom"}, "1.55": {"post": [-0.60781, 0.30234, 0.30781], "lerp_mode": "catmullrom"}, "1.7": {"post": [-0.58281, 0.75234, 0.30781], "lerp_mode": "catmullrom"}, "1.85": {"post": [-0.57781, 0.59234, 0.28781], "lerp_mode": "catmullrom"}, "2.1": {"post": [-0.48281, 0.67734, 0.30781], "lerp_mode": "catmullrom"}, "2.15": {"post": [-0.055, -0.31766, 0.60781], "lerp_mode": "catmullrom"}, "2.25": {"post": [-0.19172, -0.79813, 0.1], "lerp_mode": "catmullrom"}, "2.35": {"post": [-0.19172, -0.34188, 0.43594], "lerp_mode": "catmullrom"}, "2.5": {"post": [-0.40172, -1.90984, 0.79438], "lerp_mode": "catmullrom"}, "2.65": {"post": [-0.80281, -1.51594, 0.83438], "lerp_mode": "catmullrom"}, "2.75": {"post": [-0.73, -1.37, 0.82], "lerp_mode": "catmullrom"}, "2.8": {"post": [-0.68781, -1.89969, 0.35938], "lerp_mode": "catmullrom"}, "2.9": {"post": [-0.71281, -1.29969, -0.00562], "lerp_mode": "catmullrom"}, "3.0": {"post": [-0.78781, -1.32469, -0.06562], "lerp_mode": "catmullrom"}, "3.1": {"post": [-0.78781, -1.35516, 0.69406], "lerp_mode": "catmullrom"}, "3.2": {"post": [-0.98781, -1.87078, 0.49406], "lerp_mode": "catmullrom"}, "3.35": {"post": [-0.70109, -4.88781, -1.05453], "lerp_mode": "catmullrom"}, "3.5": {"post": [-0.50703, -1.30984, -2.51891], "lerp_mode": "catmullrom"}, "3.6": {"post": [-0.28, -0.70906, -1.46719], "lerp_mode": "catmullrom"}, "3.65": {"post": [-0.16, -0.55, 0.60407], "lerp_mode": "catmullrom"}, "3.7": {"post": [-0.06641, -0.37187, 0.50312], "lerp_mode": "catmullrom"}, "3.8": {"post": [-0.02, -0.1, -0.21594], "lerp_mode": "catmullrom"}, "3.9": {"post": [0, 0, -0.13672], "lerp_mode": "catmullrom"}, "4.05": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "magazine_and_bullet": {"rotation": {"0.75": [0, 0, 0], "0.85": [0, 0, 0], "1.0": [20, 0, 0], "1.4": [20, 0, 0], "1.5": [7.17591, 5.98372, -39.62446], "1.7": [-12, 0, 0], "1.85": [-9.84783, -0.35802, -2.97858], "1.95": [-3.92392, -0.17901, -1.48929], "2.05": [0, 0, 0], "2.15": [0, 0, 0]}, "position": {"0.75": [0, 0, 0], "0.8": [0, -4.77, -0.37422], "0.85": [0, -9.11, -0.665], "0.95": [0, -17.8, -0.10781], "1.1": [-2.75869, -30.94359, 3.05816], "1.25": [1.69506, -22.27835, 9.04581], "1.4": [4.70351, -15.86903, 5.48137], "1.5": [4.2, -12.67344, 1.86375], "1.6": [2.1, -9.74, -1.35531], "1.7": [0, -8.08203, -1.09844], "1.85": [0.3, -7.43172, -0.92344], "1.95": [0.25, -7.09, -0.83984], "2.05": [0, -7.4, -0.74609], "2.15": [0, 0, 0]}}, "bolt": {"position": {"0.0": [0, 0, -3.995], "2.7": [0, 0, -3.995], "2.75": [0, 0, 0]}}, "charge_handle": {"position": {"2.7": [0, 0, 0], "2.8": [0, 0, 7], "2.95": [0, 0, 7], "3.05": [0, 0, 0]}}, "charge_l": {"rotation": {"2.55": [0, 0, 0], "2.7": [0, -90, 0], "3.1": [0, -90, 0], "3.15": [0, 0, 0]}}, "bullet_in_mag": {"position": {"0.0": [0, -1, 0], "1.15": [0, -1, 0], "1.2": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.15": {"post": [0.21984, 0.11719, 0], "lerp_mode": "catmullrom"}, "0.4": {"post": [2.63672, 0.65625, -1.04297], "lerp_mode": "catmullrom"}, "0.75": {"post": [2.45641, 0.92578, -1.04297], "lerp_mode": "catmullrom"}, "0.85": {"post": [3.08688, 0.69531, -0.42578], "lerp_mode": "catmullrom"}, "1.0": {"post": [2.71969, 0.57813, -1.54687], "lerp_mode": "catmullrom"}, "1.2": {"post": [2.97563, 0.69531, -1.04297], "lerp_mode": "catmullrom"}, "1.55": {"post": [2.88906, 0.65625, -1.04297], "lerp_mode": "catmullrom"}, "1.7": {"post": [3.37, 0.87, -1.04297], "lerp_mode": "catmullrom"}, "1.75": {"post": [3.55, 0.95, -0.88672], "lerp_mode": "catmullrom"}, "1.85": {"post": [3.85969, 1.08203, -1.08203], "lerp_mode": "catmullrom"}, "2.1": {"post": [3.89, 1.08203, -1.04297], "lerp_mode": "catmullrom"}, "2.2": {"post": [4.33266, 1.08203, -0.38672], "lerp_mode": "catmullrom"}, "2.3": {"post": [3.57875, 1.08203, -1.46875], "lerp_mode": "catmullrom"}, "2.45": {"post": [3.21875, 0.76953, -0.88672], "lerp_mode": "catmullrom"}, "2.6": {"post": [1.45141, 0.50391, -1.04297], "lerp_mode": "catmullrom"}, "2.7": {"post": [1.13, 0.49, -1.05297], "lerp_mode": "catmullrom"}, "2.8": {"post": [1.10234, 0.50391, -0.61719], "lerp_mode": "catmullrom"}, "2.85": {"post": [1.62, 0.50391, -1.35156], "lerp_mode": "catmullrom"}, "3.0": {"post": [1.43, 0.50391, -1.04297], "lerp_mode": "catmullrom"}, "3.1": {"post": [2.20437, 0.50391, -0.46484], "lerp_mode": "catmullrom"}, "3.2": {"post": [1.90984, 0.50391, -1.08203], "lerp_mode": "catmullrom"}, "3.35": {"post": [2.1, 0.24, -0.91531], "lerp_mode": "catmullrom"}, "3.45": {"post": [2.195, 0.07813, -0.73437], "lerp_mode": "catmullrom"}, "3.65": {"post": [-0.21094, 0, -0.30859], "lerp_mode": "catmullrom"}, "3.75": {"post": [0.17188, 0, 0.38672], "lerp_mode": "catmullrom"}, "3.85": {"post": [-0.14062, 0, -0.15625], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bullet": {"scale": {"0.0": [0, 0, 0], "1.1": [0, 0, 0], "1.15": [1, 1, 1]}}}, "sound_effects": {"0.0": {"effect": "aa12_reload_raise"}, "0.75": {"effect": "aa12_reload_empty_magout"}, "1.35": {"effect": "iw8_phys_mag_drop_br_poly_dirt_03"}, "1.6": {"effect": "aa12_reload_drum_mvmnt"}, "1.9": {"effect": "aa12_reload_empty_magin"}, "2.5": {"effect": "wfoly_sh_jak12_reload_empty_boltpull"}, "2.9": {"effect": "aa12_reload_empty_fast_drum_boltforward"}, "3.15": {"effect": "aa12_reload_empty_end"}}}, "inspect": {"animation_length": 5.55, "bones": {"righthand": {"rotation": [94.51712, 4.98455, -179.60671], "position": [-6.70625, -12.4, 7.4], "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [90.8048, -31.27324, -131.00752], "0.35": {"pre": [103.56087, -0.17697, -197.45951], "post": [103.56087, -0.17697, -197.45951], "lerp_mode": "catmullrom"}, "0.5": {"post": [103.57028, -2.12119, -197.9287], "lerp_mode": "catmullrom"}, "0.65": {"post": [102.49, -2.99, -198.19], "lerp_mode": "catmullrom"}, "0.75": {"post": [100.88901, -5.69006, -187.53021], "lerp_mode": "catmullrom"}, "0.95": {"post": [95.76158, 0.71665, -165.63248], "lerp_mode": "catmullrom"}, "1.05": {"post": [95.36522, 2.47431, -163.44807], "lerp_mode": "catmullrom"}, "1.25": {"post": [95.70663, 3.40038, -164.01367], "lerp_mode": "catmullrom"}, "1.6": {"post": [95.76543, 2.20907, -165.4818], "lerp_mode": "catmullrom"}, "1.85": {"post": [90.72123, -12.34396, -200.76519], "lerp_mode": "catmullrom"}, "2.0": {"post": [117.50401, -5.64948, -194.89904], "lerp_mode": "catmullrom"}, "2.1": {"post": [120.94, -2.39, -201.1], "lerp_mode": "catmullrom"}, "2.25": {"post": [120.94, -2.39, -201.1], "lerp_mode": "catmullrom"}, "2.35": {"post": [103.92931, -2.38152, -201.07384], "lerp_mode": "catmullrom"}, "2.55": {"post": [111.82559, -23.5517, -161.37814], "lerp_mode": "catmullrom"}, "2.8": {"post": [66.72292, -34.10853, -141.85682], "lerp_mode": "catmullrom"}, "4.45": {"post": [65.58, -34.51, -141.6], "lerp_mode": "catmullrom"}, "4.6": {"post": [70.41882, -38.16472, -153.24657], "lerp_mode": "catmullrom"}, "4.8": {"post": [69.85875, -38.57267, -154.52391], "lerp_mode": "catmullrom"}, "4.9": {"post": [75.2475, -29.9974, -141.61939], "lerp_mode": "catmullrom"}, "5.0": [90.8048, -31.27324, -131.00752], "5.3": [90.8048, -31.27324, -131.00752]}, "position": {"0.0": [8.025, -9.5, -6.2], "0.2": {"pre": [9.29, -16.95, -3.41], "post": [9.29, -16.95, -3.41], "lerp_mode": "catmullrom"}, "0.35": [8.55859, -16.1875, -0.83203], "0.45": [8.55859, -16.1875, -0.83203], "0.6": {"pre": [8.55859, -19.6875, -1.16406], "post": [8.55859, -19.6875, -1.16406], "lerp_mode": "catmullrom"}, "0.7": {"post": [8.82422, -24.3125, -1.70547], "lerp_mode": "catmullrom"}, "0.8": {"post": [10.28953, -24.9675, -0.59203], "lerp_mode": "catmullrom"}, "0.95": {"post": [10.97656, -21.62172, 0.52366], "lerp_mode": "catmullrom"}, "1.05": {"post": [10.85677, -21.32759, 0.61537], "lerp_mode": "catmullrom"}, "1.25": {"post": [10.81355, -21.37514, 0.56207], "lerp_mode": "catmullrom"}, "1.6": {"post": [10.35586, -22.16848, 0.34949], "lerp_mode": "catmullrom"}, "1.75": {"post": [9.78953, -25.15891, -0.59203], "lerp_mode": "catmullrom"}, "1.85": [8.55859, -25.09375, -1.70547], "1.95": [8.55859, -24.98766, -1.95984], "2.0": [8.55859, -24.3125, -1.70547], "2.1": [8.55859, -16.1875, -0.83203], "2.25": {"pre": [8.37857, -16.23243, -0.59931], "post": [8.37857, -16.23243, -0.59931], "lerp_mode": "catmullrom"}, "2.35": {"post": [8.37857, -16.23243, -0.99931], "lerp_mode": "catmullrom"}, "2.45": {"post": [8.2133, -13.60993, -2.80635], "lerp_mode": "catmullrom"}, "2.55": {"post": [7.71922, -10.03246, -5.00545], "lerp_mode": "catmullrom"}, "2.8": {"post": [8.79976, -7.78496, -4.94733], "lerp_mode": "catmullrom"}, "4.45": {"post": [8.75, -7.56, -4.94], "lerp_mode": "catmullrom"}, "4.6": {"post": [9.65725, -8.04552, -4.5483], "lerp_mode": "catmullrom"}, "4.8": {"post": [8.52214, -8.03051, -4.53978], "lerp_mode": "catmullrom"}, "4.9": {"post": [9.78415, -10.88528, -5.11946], "lerp_mode": "catmullrom"}, "5.0": [8.025, -9.5, -6.2], "5.3": [8.025, -9.5, -6.2]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-5.46934, -6.59483, -3.87442], "lerp_mode": "catmullrom"}, "0.3": {"post": [-24.36045, -18.3372, -24.5736], "lerp_mode": "catmullrom"}, "0.45": {"post": [-26.6372, -23.44434, -30.03394], "lerp_mode": "catmullrom"}, "0.6": {"post": [-27.1372, -23.44434, -30.53394], "lerp_mode": "catmullrom"}, "0.9": {"post": [-27.57, -24.03, -31.84547], "lerp_mode": "catmullrom"}, "1.1": {"post": [-26.6372, -24.44434, -33.28003], "lerp_mode": "catmullrom"}, "1.45": {"post": [-25.24484, -24.87172, -34.76282], "lerp_mode": "catmullrom"}, "1.65": {"post": [-25.02484, -25.14172, -35.06281], "lerp_mode": "catmullrom"}, "1.8": {"post": [-25.68531, -24.51055, -34.39042], "lerp_mode": "catmullrom"}, "2.05": {"post": [-25.89, -25.17, -35.55], "lerp_mode": "catmullrom"}, "2.1": {"post": [-26.36769, -24.44268, -34.05701], "lerp_mode": "catmullrom"}, "2.2": {"post": [-24.26656, -25.85154, -37.78486], "lerp_mode": "catmullrom"}, "2.3": {"post": [-25.85782, -24.16621, -33.81841], "lerp_mode": "catmullrom"}, "2.45": {"post": [-23.12164, -22.41959, -23.06423], "lerp_mode": "catmullrom"}, "2.75": {"post": [-33.43597, 2.67804, 38.4926], "lerp_mode": "catmullrom"}, "2.9": {"post": [-34.83597, 5.27804, 55.3926], "lerp_mode": "catmullrom"}, "3.25": {"post": [-34.91, 4.72656, 56.81], "lerp_mode": "catmullrom"}, "3.4": {"post": [-34.14, 5.47656, 58.61], "lerp_mode": "catmullrom"}, "3.6": {"post": [-34.1349, 5.84963, 58.7146], "lerp_mode": "catmullrom"}, "3.8": {"post": [-34.74262, 8.83811, 65.4196], "lerp_mode": "catmullrom"}, "4.0": {"post": [-35.47, 8.61656, 66.12], "lerp_mode": "catmullrom"}, "4.2": {"post": [-36.25, 9.09656, 66.41], "lerp_mode": "catmullrom"}, "4.5": {"post": [-35.44, 9.53656, 64.48], "lerp_mode": "catmullrom"}, "4.6": {"post": [-35.57282, 9.29734, 66.33695], "lerp_mode": "catmullrom"}, "4.75": {"post": [-31.59262, 7.93811, 58.1196], "lerp_mode": "catmullrom"}, "4.95": {"post": [-2.49762, -11.47315, 23.75119], "lerp_mode": "catmullrom"}, "5.1": {"post": [4.76155, -3.98624, 3.16823], "lerp_mode": "catmullrom"}, "5.2": {"post": [-0.75, 0, -2.25], "lerp_mode": "catmullrom"}, "5.35": {"post": [0.25, 0, 1.25], "lerp_mode": "catmullrom"}, "5.55": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.15": {"post": [0.30875, -0.91641, -0.54], "lerp_mode": "catmullrom"}, "0.35": {"post": [1.36297, 1.47656, -1.70312], "lerp_mode": "catmullrom"}, "0.45": {"post": [1.64593, 2.33502, -1.8], "lerp_mode": "catmullrom"}, "0.6": {"post": [1.6539, 2.52735, -1.7], "lerp_mode": "catmullrom"}, "0.7": {"post": [1.44969, 2.93984, -1.69], "lerp_mode": "catmullrom"}, "0.85": {"post": [1.31498, 2.58845, -1.69], "lerp_mode": "catmullrom"}, "1.1": {"post": [1.29844, 2.09375, -1.7], "lerp_mode": "catmullrom"}, "1.3": {"post": [1.30703, 2.09375, -1.7], "lerp_mode": "catmullrom"}, "1.65": {"post": [1.15691, 2.40446, -1.66], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.75985, 2.72422, -1.63], "lerp_mode": "catmullrom"}, "2.05": {"post": [2.23828, 2.61828, -1.62], "lerp_mode": "catmullrom"}, "2.1": {"post": [2.68141, 2.02219, -1.61], "lerp_mode": "catmullrom"}, "2.2": {"post": [2.54688, 1.76375, -1.61], "lerp_mode": "catmullrom"}, "2.35": {"post": [2.34641, 1.8417, -1.7], "lerp_mode": "catmullrom"}, "2.6": {"post": [1.19487, 0.80056, -2.65], "lerp_mode": "catmullrom"}, "2.8": {"post": [-0.28906, -0.91797, -3.5], "lerp_mode": "catmullrom"}, "2.95": {"post": [-0.35291, 0.55958, -3.67], "lerp_mode": "catmullrom"}, "3.15": {"post": [-0.46358, 1.41911, -3.65], "lerp_mode": "catmullrom"}, "3.35": {"post": [-0.43906, 1.45, -3.62], "lerp_mode": "catmullrom"}, "3.55": {"post": [-0.37996, 1.39166, -3.6], "lerp_mode": "catmullrom"}, "3.8": {"post": [-0.18816, 1.72318, -3.25234], "lerp_mode": "catmullrom"}, "4.05": {"post": [-0.15599, 2.61198, -2.96328], "lerp_mode": "catmullrom"}, "4.35": {"post": [-0.15342, 2.80122, -2.96328], "lerp_mode": "catmullrom"}, "4.5": {"post": [-0.14906, 2.89141, -2.96328], "lerp_mode": "catmullrom"}, "4.65": {"post": [-0.27982, 2.42806, -2.96328], "lerp_mode": "catmullrom"}, "4.8": {"post": [-0.15111, 2.27636, -2.96328], "lerp_mode": "catmullrom"}, "4.95": {"post": [0.44921, -0.66467, -2.95328], "lerp_mode": "catmullrom"}, "5.1": {"post": [0.28719, -0.43, -2.83594], "lerp_mode": "catmullrom"}, "5.2": {"post": [0.47, -0.14, 0.39], "lerp_mode": "catmullrom"}, "5.3": {"post": [0.19924, 0.01743, -0.13359], "lerp_mode": "catmullrom"}, "5.4": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "magazine_and_bullet": {"rotation": {"0.0": [0, 0, 0], "0.65": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-4, 0, 0], "lerp_mode": "catmullrom"}, "0.95": {"post": [-4.87, -7.98, 17.04], "lerp_mode": "catmullrom"}, "1.05": {"post": [-4.97, -10.29, 22.16], "lerp_mode": "catmullrom"}, "1.25": {"post": [-4.7339, -10.80007, 22.74683], "lerp_mode": "catmullrom"}, "1.6": {"post": [-4.97, -10.2, 22.40953], "lerp_mode": "catmullrom"}, "1.7": {"post": [-4.75, -7.5, 16.25953], "lerp_mode": "catmullrom"}, "1.85": [-4, 0, 0], "1.9": [-6, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.45": [0, 0, 0], "0.6": {"pre": [0, -3.5, -0.33203], "post": [0, -3.5, -0.33203], "lerp_mode": "catmullrom"}, "0.7": {"post": [0, -8.125, -0.87344], "lerp_mode": "catmullrom"}, "0.8": {"post": [2.02, -8.78, 0.24], "lerp_mode": "catmullrom"}, "0.95": {"post": [4.03906, -5.43422, 1.35569], "lerp_mode": "catmullrom"}, "1.25": {"post": [4.13777, -5.18764, 1.3941], "lerp_mode": "catmullrom"}, "1.6": {"post": [3.62177, -5.96011, 1.18378], "lerp_mode": "catmullrom"}, "1.75": {"post": [2.02, -8.97141, 0.24], "lerp_mode": "catmullrom"}, "1.85": [0, -8.90625, -0.87344], "1.95": [0, -8.80016, -1.12781], "2.0": [0, -8.125, -0.87344], "2.1": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.5, 0, -0.56641], "lerp_mode": "catmullrom"}, "0.45": {"post": [-0.00016, 0.33594, -1.07031], "lerp_mode": "catmullrom"}, "0.7": {"post": [0.48781, -0.29687, -1.44922], "lerp_mode": "catmullrom"}, "0.85": {"post": [2.11484, -2.32031, -0.44531], "lerp_mode": "catmullrom"}, "1.05": {"post": [3.09594, -3.33594, -0.82031], "lerp_mode": "catmullrom"}, "1.3": {"post": [3.46, -3.53, 0.07641], "lerp_mode": "catmullrom"}, "1.5": {"post": [3.61922, -3.33594, 0.125], "lerp_mode": "catmullrom"}, "1.75": {"post": [3.48625, -2.73437, -0.82031], "lerp_mode": "catmullrom"}, "1.85": {"post": [2.89, -1.69, -0.75469], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.93672, 0, -1.64062], "lerp_mode": "catmullrom"}, "2.1": {"post": [1.82, 0.28, -1.31469], "lerp_mode": "catmullrom"}, "2.2": {"post": [1.77, 0.45, -2.16141], "lerp_mode": "catmullrom"}, "2.3": {"post": [1.72, 0.63, -1.85], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.96438, 0.5625, -1.64062], "lerp_mode": "catmullrom"}, "2.75": {"post": [4.33984, -1.23437, 0.62891], "lerp_mode": "catmullrom"}, "2.85": {"post": [4.62, -1.45, 1.65391], "lerp_mode": "catmullrom"}, "3.0": {"post": [5.68453, -1.57, 0.98609], "lerp_mode": "catmullrom"}, "3.15": {"post": [5.98109, -1.62891, 1.19531], "lerp_mode": "catmullrom"}, "3.6": {"post": [6.53234, -1.40234, 1.19531], "lerp_mode": "catmullrom"}, "4.1": {"post": [3.99328, -0.89844, 0.81641], "lerp_mode": "catmullrom"}, "4.5": {"post": [3.25109, -0.95312, 1.07031], "lerp_mode": "catmullrom"}, "4.8": {"post": [3.77187, -0.50391, 0.62891], "lerp_mode": "catmullrom"}, "5.05": {"post": [0, 0, -0.25], "lerp_mode": "catmullrom"}, "5.15": {"post": [-0.55859, 0, 0.31641], "lerp_mode": "catmullrom"}, "5.3": {"post": [0.29688, 0, -0.125], "lerp_mode": "catmullrom"}, "5.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bone5": {"rotation": {"0.6": [0, 0, 0], "0.7": [0, 0, -1.46094], "0.8": [0, 0, 1.15391], "0.95": [0, 0, -0.20375], "1.15": [0, 0, 0], "1.5": [0, 0, 0], "1.85": [0, 0, 0], "1.95": [0, 0, -0.22656], "2.1": [0, 0, 0]}, "position": {"0.6": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9": {"post": [-1.29, 0.69, 0.03], "lerp_mode": "catmullrom"}, "1.1": {"post": [-2.27785, 0.80759, 0.31248], "lerp_mode": "catmullrom"}, "1.65": {"post": [-2.37775, 0.6785, 0.42804], "lerp_mode": "catmullrom"}, "1.95": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "wfoly_sh_jak12_raise"}, "0.55": {"effect": "aa12_inspect_magout"}, "1.15": {"effect": "aa12_inspect_mvmnt"}, "1.85": {"effect": "aa12_inspect_magin"}, "2.45": {"effect": "aa12_reload_mvmnt"}, "4.1": {"effect": "aa12_inspect_end"}}}, "inspect_empty": {"animation_length": 6.2, "bones": {"righthand": {"rotation": {"0.0": [94.51712, 4.98455, -179.60671], "4.6": [94.52, 4.98, -179.61], "4.65": [94.92, 6.76, -176.93], "4.7": [95.29182, 5.54927, -174.53067], "4.8": [94.99635, 4.86719, -178.12092], "4.9": [94.52, 4.98, -179.61]}, "position": {"0.0": [-6.70625, -12.4, 7.4], "4.6": [-6.71, -12.4, 7.4], "4.65": [-6.76, -12.39, 7.38], "4.7": [-6.80083, -12.37629, 7.36552], "4.8": [-6.80083, -12.37629, 7.36552], "4.9": [-6.71, -12.4, 7.4]}, "scale": [1, 1.5, 1]}, "lefthand": {"rotation": {"0.0": [90.8048, -31.27324, -131.00752], "0.35": {"pre": [103.56087, -0.17697, -197.45951], "post": [103.56087, -0.17697, -197.45951], "lerp_mode": "catmullrom"}, "0.5": {"post": [103.57028, -2.12119, -197.9287], "lerp_mode": "catmullrom"}, "0.65": {"post": [102.49, -2.99, -198.19], "lerp_mode": "catmullrom"}, "0.75": {"post": [100.88901, -5.69006, -187.53021], "lerp_mode": "catmullrom"}, "0.95": {"post": [95.76158, 0.71665, -165.63248], "lerp_mode": "catmullrom"}, "1.05": {"post": [95.36522, 2.47431, -163.44807], "lerp_mode": "catmullrom"}, "1.25": {"post": [95.70663, 3.40038, -164.01367], "lerp_mode": "catmullrom"}, "1.6": {"post": [95.76543, 2.20907, -165.4818], "lerp_mode": "catmullrom"}, "1.85": {"post": [90.72123, -12.34396, -200.76519], "lerp_mode": "catmullrom"}, "2.0": {"post": [117.50401, -5.64948, -194.89904], "lerp_mode": "catmullrom"}, "2.1": {"post": [120.94, -2.39, -201.1], "lerp_mode": "catmullrom"}, "2.25": {"post": [120.94, -2.39, -201.1], "lerp_mode": "catmullrom"}, "2.35": {"post": [103.92931, -2.38152, -201.07384], "lerp_mode": "catmullrom"}, "2.55": {"post": [111.82559, -23.5517, -161.37814], "lerp_mode": "catmullrom"}, "2.8": [66.72292, -34.10853, -141.85682], "2.95": [66.72292, -34.10853, -141.85682], "3.15": [106.05513, -33.12176, -208.75713], "3.3": [123.75327, 15.97891, -276.13728], "3.4": [123.75327, 15.97891, -276.13728], "3.5": [130.54053, -4.75358, -280.25339], "3.6": [101.06508, 3.56402, -277.98351], "3.75": [101.0748, -4.2876, -279.5184], "3.85": [122.06649, 13.09219, -285.55833], "3.9": [113.95701, 4.36426, -278.78526], "4.05": [113.95701, 4.36426, -278.78526], "4.15": [100.11234, -26.70115, -212.48563], "4.3": [66.72292, -34.10853, -141.85682], "5.1": [66.72292, -34.10853, -141.85682], "5.25": {"pre": [70.41882, -38.16472, -153.24657], "post": [70.41882, -38.16472, -153.24657], "lerp_mode": "catmullrom"}, "5.45": {"post": [69.85875, -38.57267, -154.52391], "lerp_mode": "catmullrom"}, "5.55": {"post": [75.2475, -29.9974, -141.61939], "lerp_mode": "catmullrom"}, "5.65": [90.8048, -31.27324, -131.00752], "5.95": [90.8048, -31.27324, -131.00752]}, "position": {"0.0": [8.025, -9.5, -6.2], "0.2": {"pre": [9.29, -16.95, -3.41], "post": [9.29, -16.95, -3.41], "lerp_mode": "catmullrom"}, "0.35": [8.55859, -16.1875, -0.83203], "0.45": [8.55859, -16.1875, -0.83203], "0.6": {"pre": [8.55859, -19.6875, -1.16406], "post": [8.55859, -19.6875, -1.16406], "lerp_mode": "catmullrom"}, "0.7": {"post": [8.82422, -24.3125, -1.70547], "lerp_mode": "catmullrom"}, "0.8": {"post": [10.28953, -24.9675, -0.59203], "lerp_mode": "catmullrom"}, "0.95": {"post": [10.97656, -21.62172, 0.52366], "lerp_mode": "catmullrom"}, "1.05": {"post": [10.85677, -21.32759, 0.61537], "lerp_mode": "catmullrom"}, "1.25": {"post": [10.81355, -21.37514, 0.56207], "lerp_mode": "catmullrom"}, "1.6": {"post": [10.35586, -22.16848, 0.34949], "lerp_mode": "catmullrom"}, "1.75": {"post": [9.78953, -25.15891, -0.59203], "lerp_mode": "catmullrom"}, "1.85": [8.55859, -25.09375, -1.70547], "1.95": [8.55859, -24.98766, -1.95984], "2.0": [8.55859, -24.3125, -1.70547], "2.1": [8.55859, -16.1875, -0.83203], "2.25": {"pre": [8.37857, -16.23243, -0.59931], "post": [8.37857, -16.23243, -0.59931], "lerp_mode": "catmullrom"}, "2.35": {"post": [8.37857, -16.23243, -0.99931], "lerp_mode": "catmullrom"}, "2.45": {"post": [8.2133, -13.60993, -2.80635], "lerp_mode": "catmullrom"}, "2.55": {"post": [7.71922, -10.03246, -5.00545], "lerp_mode": "catmullrom"}, "2.8": [8.79976, -7.78496, -4.94733], "2.95": [8.79976, -7.78496, -4.94733], "3.05": [10.0815, -7.16106, -5.17552], "3.15": [10.365, -6.065, -5.85], "3.3": [9.17094, -5.4, -2.95313], "3.4": [9.17094, -5.4, -2.95313], "3.5": [9.57969, -5.4, -1.53282], "3.6": [9.41719, -5.4, 4.35625], "3.75": [9.41719, -5.4, 4.35625], "3.85": [9.23125, -5.4, -1.9], "3.9": [9.23125, -5.4, -1.9], "4.0": [9.23125, -5.4, -1.9], "4.1": [10.55794, -6.16532, -3.05739], "4.2": [10.17453, -7.25459, -4.29045], "4.3": [8.79976, -7.78496, -4.94733], "5.1": [8.79976, -7.78496, -4.94733], "5.25": {"pre": [9.65725, -8.04552, -4.5483], "post": [9.65725, -8.04552, -4.5483], "lerp_mode": "catmullrom"}, "5.45": {"post": [8.52214, -8.03051, -4.53978], "lerp_mode": "catmullrom"}, "5.55": {"post": [9.78415, -10.88528, -5.11946], "lerp_mode": "catmullrom"}, "5.65": [8.025, -9.5, -6.2], "5.95": [8.025, -9.5, -6.2]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-5.46934, -6.59483, -3.87442], "lerp_mode": "catmullrom"}, "0.3": {"post": [-24.36045, -18.3372, -24.5736], "lerp_mode": "catmullrom"}, "0.45": {"post": [-26.6372, -23.44434, -30.03394], "lerp_mode": "catmullrom"}, "0.6": {"post": [-27.1372, -23.44434, -30.53394], "lerp_mode": "catmullrom"}, "0.9": {"post": [-27.57, -24.03, -31.84547], "lerp_mode": "catmullrom"}, "1.1": {"post": [-26.6372, -24.44434, -33.28003], "lerp_mode": "catmullrom"}, "1.45": {"post": [-25.24484, -24.87172, -34.76282], "lerp_mode": "catmullrom"}, "1.65": {"post": [-25.02484, -25.14172, -35.06281], "lerp_mode": "catmullrom"}, "1.8": {"post": [-25.68531, -24.51055, -34.39042], "lerp_mode": "catmullrom"}, "2.05": {"post": [-25.89, -25.17, -35.55], "lerp_mode": "catmullrom"}, "2.1": {"post": [-26.36769, -24.44268, -34.05701], "lerp_mode": "catmullrom"}, "2.2": {"post": [-24.26656, -25.85154, -37.78486], "lerp_mode": "catmullrom"}, "2.3": {"post": [-25.85782, -24.16621, -33.81841], "lerp_mode": "catmullrom"}, "2.45": {"post": [-23.12164, -22.41959, -23.06423], "lerp_mode": "catmullrom"}, "2.75": {"post": [-33.43597, 2.67804, 38.4926], "lerp_mode": "catmullrom"}, "2.9": {"post": [-34.83597, 5.27804, 55.3926], "lerp_mode": "catmullrom"}, "3.05": {"post": [-35, 5.19, 57.06], "lerp_mode": "catmullrom"}, "3.25": {"post": [-32.93095, 4.24284, 60.50558], "lerp_mode": "catmullrom"}, "3.4": {"post": [-32.4525, 3.45312, 60.68422], "lerp_mode": "catmullrom"}, "3.55": {"post": [-32.38, 3.4, 60.33], "lerp_mode": "catmullrom"}, "3.6": {"post": [-33.0575, 5.13484, 61.83484], "lerp_mode": "catmullrom"}, "3.7": {"post": [-34.84974, 7.25979, 63.53491], "lerp_mode": "catmullrom"}, "3.8": {"post": [-35.45746, 10.24827, 67.49382], "lerp_mode": "catmullrom"}, "4.0": {"post": [-35.1575, 8.30797, 66.81922], "lerp_mode": "catmullrom"}, "4.35": {"post": [-34.94, 7.57, 67.3475], "lerp_mode": "catmullrom"}, "4.7": {"post": [-35.99, 8.74, 66.77], "lerp_mode": "catmullrom"}, "4.75": {"post": [-35.92016, 10.00953, 68.3175], "lerp_mode": "catmullrom"}, "4.85": {"post": [-36.25, 9.09656, 68.44906], "lerp_mode": "catmullrom"}, "5.15": {"post": [-35.44, 9.53656, 67.53859], "lerp_mode": "catmullrom"}, "5.25": {"post": [-35.57282, 9.29734, 69.39554], "lerp_mode": "catmullrom"}, "5.4": {"post": [-31.59262, 7.93811, 58.1196], "lerp_mode": "catmullrom"}, "5.6": {"post": [-2.49762, -11.47315, 23.75119], "lerp_mode": "catmullrom"}, "5.75": {"post": [4.76155, -3.98624, 3.16823], "lerp_mode": "catmullrom"}, "5.85": {"post": [-0.75, 0, -2.25], "lerp_mode": "catmullrom"}, "6.0": {"post": [0.25, 0, 1.25], "lerp_mode": "catmullrom"}, "6.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.15": {"post": [0.30875, -0.91641, -0.54], "lerp_mode": "catmullrom"}, "0.35": {"post": [1.36297, 1.47656, -1.70312], "lerp_mode": "catmullrom"}, "0.45": {"post": [1.64593, 2.33502, -1.8], "lerp_mode": "catmullrom"}, "0.6": {"post": [1.6539, 2.52735, -1.7], "lerp_mode": "catmullrom"}, "0.7": {"post": [1.44969, 2.93984, -1.69], "lerp_mode": "catmullrom"}, "0.85": {"post": [1.31498, 2.58845, -1.69], "lerp_mode": "catmullrom"}, "1.1": {"post": [1.29844, 2.09375, -1.7], "lerp_mode": "catmullrom"}, "1.3": {"post": [1.30703, 2.09375, -1.7], "lerp_mode": "catmullrom"}, "1.65": {"post": [1.15691, 2.40446, -1.66], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.75985, 2.72422, -1.63], "lerp_mode": "catmullrom"}, "2.05": {"post": [2.23828, 2.61828, -1.62], "lerp_mode": "catmullrom"}, "2.1": {"post": [2.68141, 2.02219, -1.61], "lerp_mode": "catmullrom"}, "2.2": {"post": [2.54688, 1.76375, -1.61], "lerp_mode": "catmullrom"}, "2.35": {"post": [2.34641, 1.8417, -1.7], "lerp_mode": "catmullrom"}, "2.6": {"post": [1.19487, 0.80056, -2.65], "lerp_mode": "catmullrom"}, "2.8": {"post": [-0.28906, -0.91797, -3.5], "lerp_mode": "catmullrom"}, "2.95": {"post": [-0.35291, 0.55958, -3.67], "lerp_mode": "catmullrom"}, "3.15": {"post": [-0.46358, 0.7277, -3.65], "lerp_mode": "catmullrom"}, "3.35": {"post": [-0.43906, 0.55156, -3.62], "lerp_mode": "catmullrom"}, "3.5": {"post": [-0.37996, 0.68072, -3.6], "lerp_mode": "catmullrom"}, "3.6": {"post": [-0.31, 0.48656, -3.77297], "lerp_mode": "catmullrom"}, "3.65": {"post": [-0.28, 0.71813, -3.64891], "lerp_mode": "catmullrom"}, "3.8": {"post": [-0.18816, 1.37162, -3.6039], "lerp_mode": "catmullrom"}, "4.05": {"post": [-0.15599, 1.98698, -2.96328], "lerp_mode": "catmullrom"}, "4.3": {"post": [-0.15, 2.02625, -2.94], "lerp_mode": "catmullrom"}, "4.7": {"post": [-0.15, 2.14516, -2.95], "lerp_mode": "catmullrom"}, "4.75": {"post": [-0.04453, 2.22688, -3.10625], "lerp_mode": "catmullrom"}, "4.85": {"post": [-0.15, 2.13594, -2.95], "lerp_mode": "catmullrom"}, "5.0": {"post": [-0.15342, 2.19966, -2.96328], "lerp_mode": "catmullrom"}, "5.15": {"post": [-0.14906, 2.37188, -2.96328], "lerp_mode": "catmullrom"}, "5.3": {"post": [-0.27982, 2.139, -2.96328], "lerp_mode": "catmullrom"}, "5.45": {"post": [-0.15111, 1.9873, -2.96328], "lerp_mode": "catmullrom"}, "5.6": {"post": [0.44921, -0.66467, -2.95328], "lerp_mode": "catmullrom"}, "5.75": {"post": [0.28719, -0.43, -2.83594], "lerp_mode": "catmullrom"}, "5.85": {"post": [0.47, -0.14, 0.39], "lerp_mode": "catmullrom"}, "5.95": {"post": [0.19924, 0.01743, -0.13359], "lerp_mode": "catmullrom"}, "6.05": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "magazine_and_bullet": {"rotation": {"0.0": [0, 0, 0], "0.65": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-4, 0, 0], "lerp_mode": "catmullrom"}, "0.95": {"post": [-4.87, -7.98, 17.04], "lerp_mode": "catmullrom"}, "1.05": {"post": [-4.97, -10.29, 22.16], "lerp_mode": "catmullrom"}, "1.25": {"post": [-4.7339, -10.80007, 22.74683], "lerp_mode": "catmullrom"}, "1.6": {"post": [-4.97, -10.2, 22.40953], "lerp_mode": "catmullrom"}, "1.7": {"post": [-4.75, -7.5, 16.25953], "lerp_mode": "catmullrom"}, "1.85": [-4, 0, 0], "1.9": [-6, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.45": [0, 0, 0], "0.6": {"pre": [0, -3.5, -0.33203], "post": [0, -3.5, -0.33203], "lerp_mode": "catmullrom"}, "0.7": {"post": [0, -8.125, -0.87344], "lerp_mode": "catmullrom"}, "0.8": {"post": [2.02, -8.78, 0.24], "lerp_mode": "catmullrom"}, "0.95": {"post": [4.03906, -5.43422, 1.35569], "lerp_mode": "catmullrom"}, "1.25": {"post": [4.13777, -5.18764, 1.3941], "lerp_mode": "catmullrom"}, "1.6": {"post": [3.62177, -5.96011, 1.18378], "lerp_mode": "catmullrom"}, "1.75": {"post": [2.02, -8.97141, 0.24], "lerp_mode": "catmullrom"}, "1.85": [0, -8.90625, -0.87344], "1.95": [0, -8.80016, -1.12781], "2.0": [0, -8.125, -0.87344], "2.1": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.5, 0, -0.56641], "lerp_mode": "catmullrom"}, "0.45": {"post": [-0.00016, 0.33594, -1.07031], "lerp_mode": "catmullrom"}, "0.7": {"post": [0.48781, -0.29687, -1.44922], "lerp_mode": "catmullrom"}, "0.85": {"post": [2.11484, -2.32031, -0.44531], "lerp_mode": "catmullrom"}, "1.05": {"post": [3.09594, -3.33594, -0.82031], "lerp_mode": "catmullrom"}, "1.3": {"post": [3.46, -3.53, 0.07641], "lerp_mode": "catmullrom"}, "1.5": {"post": [3.61922, -3.33594, 0.125], "lerp_mode": "catmullrom"}, "1.75": {"post": [3.48625, -2.73437, -0.82031], "lerp_mode": "catmullrom"}, "1.85": {"post": [2.89, -1.69, -0.75469], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.93672, 0, -1.64062], "lerp_mode": "catmullrom"}, "2.1": {"post": [1.82, 0.28, -1.31469], "lerp_mode": "catmullrom"}, "2.2": {"post": [1.77, 0.45, -2.16141], "lerp_mode": "catmullrom"}, "2.3": {"post": [1.72, 0.63, -1.85], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.96438, 0.5625, -1.64062], "lerp_mode": "catmullrom"}, "2.75": {"post": [4.33984, -1.23437, 0.62891], "lerp_mode": "catmullrom"}, "2.85": {"post": [4.62, -1.45, 1.65391], "lerp_mode": "catmullrom"}, "3.0": {"post": [4.79, -1.57, 0.98609], "lerp_mode": "catmullrom"}, "3.15": {"post": [4.91859, -1.62891, 1.19531], "lerp_mode": "catmullrom"}, "3.5": {"post": [4.99672, -1.51, 1.23], "lerp_mode": "catmullrom"}, "3.55": {"post": [5.25984, -1.47, 1.50344], "lerp_mode": "catmullrom"}, "3.6": {"post": [4.64562, -1.40234, 0.92187], "lerp_mode": "catmullrom"}, "3.7": {"post": [4.40625, -1.32, 1.21984], "lerp_mode": "catmullrom"}, "3.8": {"post": [3.93656, -1.23, 1.06], "lerp_mode": "catmullrom"}, "3.85": {"post": [3.72391, -1.18, 1.31688], "lerp_mode": "catmullrom"}, "3.95": {"post": [3.53563, -1.07, 0.74641], "lerp_mode": "catmullrom"}, "4.1": {"post": [2.39953, -0.89844, 0.81641], "lerp_mode": "catmullrom"}, "4.25": {"post": [1.56, -0.9, 0.84], "lerp_mode": "catmullrom"}, "4.5": {"post": [1.68625, -0.92, 0.91], "lerp_mode": "catmullrom"}, "4.7": {"post": [1.62, -0.95, 0.97], "lerp_mode": "catmullrom"}, "4.75": {"post": [1.78016, -0.95, 1.25734], "lerp_mode": "catmullrom"}, "4.85": {"post": [1.42969, -0.96, 0.82641], "lerp_mode": "catmullrom"}, "5.0": {"post": [1.61672, -0.99, 1.07], "lerp_mode": "catmullrom"}, "5.15": {"post": [1.63781, -0.95312, 1.07031], "lerp_mode": "catmullrom"}, "5.45": {"post": [2.15859, -0.50391, 0.62891], "lerp_mode": "catmullrom"}, "5.7": {"post": [0, 0, -0.25], "lerp_mode": "catmullrom"}, "5.8": {"post": [-0.55859, 0, 0.31641], "lerp_mode": "catmullrom"}, "5.95": {"post": [0.29688, 0, -0.125], "lerp_mode": "catmullrom"}, "6.15": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bone5": {"rotation": {"0.6": [0, 0, 0], "0.7": [0, 0, -1.46094], "0.8": [0, 0, 1.15391], "0.95": [0, 0, -0.20375], "1.15": [0, 0, 0], "1.5": [0, 0, 0], "1.85": [0, 0, 0], "1.95": [0, 0, -0.22656], "2.1": [0, 0, 0]}, "position": {"0.6": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9": {"post": [-1.29, 0.69, 0.03], "lerp_mode": "catmullrom"}, "1.1": {"post": [-2.27785, 0.80759, 0.31248], "lerp_mode": "catmullrom"}, "1.65": {"post": [-2.37775, 0.6785, 0.42804], "lerp_mode": "catmullrom"}, "1.95": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bullet_in_mag": {"position": [0, -1, 0]}, "bolt": {"position": {"0.0": [0, 0, -3.995], "3.5": [0, 0, -3.995], "3.6": [0, 0, 0], "4.7": [0, 0, 0], "4.75": [0, 0, -3.995], "4.8": [0, 0, -3.67359], "4.85": [0, 0, -3.995]}}, "charge_handle": {"position": {"3.5": [0, 0, 0], "3.6": [0, 0, 7], "3.75": [0, 0, 7], "3.85": [0, 0, 0]}}, "charge_l": {"rotation": {"3.35": [0, 0, 0], "3.5": [0, -90, 0], "3.9": [0, -90, 0], "3.95": [0, 0, 0]}}, "bullet": {"scale": 0}}, "sound_effects": {"0.0": {"effect": "wfoly_sh_jak12_raise"}, "0.55": {"effect": "aa12_inspect_magout"}, "1.15": {"effect": "aa12_inspect_mvmnt"}, "1.85": {"effect": "aa12_inspect_magin"}, "2.45": {"effect": "aa12_reload_mvmnt"}, "3.25": {"effect": "aa12_reload_empty_boltpull"}, "3.75": {"effect": "aa12_reload_empty_fast_drum_boltforward"}, "4.7": {"effect": "weap_mike4_deadtrigger_plr_ads_05"}, "4.75": {"effect": "aa12_inspect_end"}, "4.8": {"effect": "weap_mike4_fire_dryfire_plr_ads_01"}}}, "shoot": {"loop": true, "animation_length": 0.75, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0167": {"post": [1.43203, 0, -1.63828], "lerp_mode": "catmullrom"}, "0.0333": {"post": [-1.99297, 0, 1.55703], "lerp_mode": "catmullrom"}, "0.1": {"post": [-2.28203, 0, -0.76719], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-2.17265, 0, 0.825], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.09766, 0, 0.50938], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0.2275, 0.05859, 0.10891], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-0.11328, 0.11328, 0], "lerp_mode": "catmullrom"}, "0.55": {"post": [-0.0625, -0.05859, 0], "lerp_mode": "catmullrom"}, "0.6833": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0167": {"post": [0, -0.22656, 1.95156], "lerp_mode": "catmullrom"}, "0.0833": {"post": [0, -0.28516, 1.58844], "lerp_mode": "catmullrom"}, "0.15": {"post": [0, -0.28516, 0.08828], "lerp_mode": "catmullrom"}, "0.2": {"post": [0, 0, -0.40391], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -0.36719], "lerp_mode": "catmullrom"}, "0.3167": {"post": [0, 0, 0.05469], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0, 0, 0.19922], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0, -0.10547, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.10547, -0.08031], "lerp_mode": "catmullrom"}, "0.6": {"post": [0, -0.03516, -0.07031], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"0.0": [0, 0, 0], "0.0333": [0, 0, -3.425], "0.1": [0, 0, 0]}}, "charge_handle": {"position": {"0.0": [0, 0, 0], "0.0333": [0, 0, 0.5], "0.0833": [0, 0, 0]}}, "charge_l": {"rotation": {"0.0167": [0, 0, 0], "0.0667": [0, -17, 0], "0.1167": [0, 0, 0], "0.1833": [0, 0, 0], "0.25": [0, -8, 0], "0.3167": [0, 0, 0]}}, "charge_r": {"rotation": {"0.0": [0, 0, 0], "0.05": [0, 10.65, 0], "0.1": [0, 0, 0], "0.1667": [0, 0, 0], "0.2333": [0, 7.05, 0], "0.3": [0, 0, 0]}}, "camera": {"rotation": {"0.0": [0, 0, 0], "0.0167": [0, 0, 1.55], "0.05": [0, 0, -1.275], "0.0833": [0, 0, 0.995], "0.1167": [0, 0, -0.58], "0.15": [0, 0, 0.47], "0.1833": [0, 0, 0]}}}}}}