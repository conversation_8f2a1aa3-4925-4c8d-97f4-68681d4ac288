{"format_version": "1.8.0", "animations": {"static_idle": {"animation_length": 0.23333, "bones": {"lefthand": {"rotation": [89.52779, -393.457, -128.79941], "position": [7.925, -11.05, -2.5], "scale": [1, 1.5, 1]}, "righthand": {"rotation": [101.2139, -323.71163, -174.99863], "position": [-6.4, -14.425, 2], "scale": [1, 1.5, 1]}, "constraint": {"rotation": [0.05, 0.05, 0.05], "position": [0.2, 0.2, 0.2]}}}, "draw": {"animation_length": 1.3, "bones": {"root": {"rotation": {"0.0": [-106.51189, -22.48392, 9.41779], "0.2333": [-52.59389, -13.40941, -2.66565], "0.3333": [-35.13359, -5.12707, 0.31771], "0.4333": [-12.90997, -6.50028, -7.19254], "0.5": [-4.36769, -5.14555, 0.4584], "0.5333": [-3.78864, -5.08234, -2.86114], "0.6333": {"pre": [-0.94724, -3.20987, 0.9386], "post": [-0.94724, -3.20987, 0.9386], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-1.95, -0.44, -1.31], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-2.0016, 0.01741, -1.49817], "lerp_mode": "catmullrom"}, "1.0333": {"post": [0.3296, -0.02576, 2.32998], "lerp_mode": "catmullrom"}, "1.1333": {"post": [0, 0, 2], "lerp_mode": "catmullrom"}, "1.3": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": [-17, -31, 15], "0.2": [-6.05, -11.1, 4.67], "0.3333": {"pre": [-0.5, -2.7, 1.9], "post": [-0.5, -2.7, 1.9], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.01, -3.42, -0.41], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-0.04, -3.95, 0.36], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-0.02, -3.15, 1.76], "lerp_mode": "catmullrom"}, "0.8667": {"post": [0, -1.01, -0.25], "lerp_mode": "catmullrom"}, "0.9333": {"post": [0, -0.6, -0.5], "lerp_mode": "catmullrom"}, "1.0333": {"post": [0, -0.08, 1.26], "lerp_mode": "catmullrom"}, "1.1": {"post": [0, -0.19, 0.51], "lerp_mode": "catmullrom"}, "1.2333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": {"0.0": [45.22326, -385.45422, -103.14054], "0.3": {"pre": [89.52779, -393.457, -128.79941], "post": [89.52779, -393.457, -128.79941], "lerp_mode": "catmullrom"}}, "position": {"0.0": [13.22, -21.925, 7.625], "0.3": {"pre": [7.925, -11.05, -2.5], "post": [7.925, -11.05, -2.5], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "gun_and_righthand": {"rotation": {"0.0": [0, 0, 0], "0.3": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.3": [0, 0, 0]}}, "righthand": {"rotation": [101.2139, -323.71163, -174.99863], "position": [-6.4, -14.425, 2], "scale": [1, 1.5, 1]}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3": {"post": [-1.175, 0.325, -0.7], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0.88, -0.025, -0.925], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-0.245, 0.025, 0.175], "lerp_mode": "catmullrom"}, "0.9333": {"post": [0.15, 0, -0.2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0.02, 0, -0.08], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-0.15, 0, 0.3], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-0.06, 0, -0.205], "lerp_mode": "catmullrom"}, "1.2667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "tacz:m95/m95_raise_arm"}, "0.3": {"effect": "tacz:m95/m95_raise_01"}, "0.3333": {"effect": "tacz:m95/m95_raise_end"}}}, "put_away": {"animation_length": 0.86667, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-4.53, -1.25, -1.42], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-27.15, -7.52, -8.53], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-40.73045, -11.28181, -12.80042], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-51.25268, -7.98916, -16.98839], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-59.1046, 20.10068, 0.608], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-67.12, 33.68, 8.77], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-74.38, 35.33, 10.02], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.38, -0.89, 0.09], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-1.325, -1.5, 0.28], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-1.95, -2.9, 1.95], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-2.2, -4.555, 4.37], "lerp_mode": "catmullrom"}, "0.7": {"post": [-4.7, -8.48, 7.94], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-7.59, -13.82, 14.07], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": [89.52779, -393.457, -128.79941], "position": [7.925, -11.05, -2.5], "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [101.2139, -323.71163, -174.99863], "0.1": [101.2139, -323.71163, -174.99863], "0.3": [99.92205, -324.96551, -168.50279], "0.5667": [101.48941, -375.72158, -1.01416], "0.6667": [134.2185, -345.09581, -33.36169], "0.8667": [131.93942, -347.49859, -38.54941]}, "position": {"0.0": [-6.4, -14.425, 2], "0.1": [-6.4, -14.425, 2], "0.3": [-9.38656, -8.07425, 7.05538], "0.3667": [-4.83735, -3.87436, 7.75939], "0.4667": [-2.14866, -7.06411, 1.38744], "0.5667": [-2.36662, -9.54845, -4.43353], "0.6667": [-3.50353, -7.0548, -5.75069], "0.8667": [-3.34854, -8.15579, -8.65617]}, "scale": [1, 1.5, 1]}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-0.225, 0, -0.125], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0.725, 0, 0.175], "lerp_mode": "catmullrom"}, "0.5": {"post": [1.125, 0, 1.575], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0.275, 0, 1.5], "lerp_mode": "catmullrom"}, "0.8667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "tacz:m95/m95_put_away"}}}, "reload_tactical": {"animation_length": 4.5, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [-22.58669, -3.07441, 15.33405], "lerp_mode": "catmullrom"}, "0.4": {"post": [-30.43858, 1.86212, 31.81402], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-33.05971, 5.16183, 39.67005], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-33.53098, 3.86184, 41.8581], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-34.29177, 2.97517, 41.93889], "lerp_mode": "catmullrom"}, "1.0": {"post": [-33.99449, 1.80964, 38.54477], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-34.52821, 3.35571, 40.90752], "lerp_mode": "catmullrom"}, "1.2667": [-33.70746, 2.98188, 39.9892], "1.3667": [-35.56928, -0.02315, 35.34261], "1.4667": [-34.28199, 2.99703, 40.07586], "1.5": [-32.97341, 5.29328, 44.51485], "1.6667": {"pre": [-33.10974, 1.50976, 37.92265], "post": [-33.10974, 1.50976, 37.92265], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-33.82274, 3.77808, 41.84919], "lerp_mode": "catmullrom"}, "2.0333": [-36.61201, 6.92709, 45.45735], "2.3": {"pre": [-39.27183, 6.82856, 43.68822], "post": [-39.27183, 6.82856, 43.68822], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-39.53149, 8.31942, 47.48088], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-39.53025, 8.3138, 47.46774], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-40.19436, 9.98962, 48.96567], "lerp_mode": "catmullrom"}, "2.8": {"post": [-39.16916, 7.3395, 44.59081], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-40.49827, 9.36274, 47.47311], "lerp_mode": "catmullrom"}, "2.9667": {"post": [-39.82513, 6.2649, 44.60883], "lerp_mode": "catmullrom"}, "3.0667": [-40.76065, 5.80425, 43.73373], "3.2667": [-37.66518, 5.67474, 36.22251], "3.4": [-29.32038, 3.42008, 13.4317], "3.5667": {"pre": [-10.59028, -1.92168, 0.36644], "post": [-10.59028, -1.92168, 0.36644], "lerp_mode": "catmullrom"}, "3.7": [-4.44534, -0.51817, 4.83543], "3.8667": {"pre": [-0.94585, 0.78959, 0.87252], "post": [-0.94585, 0.78959, 0.87252], "lerp_mode": "catmullrom"}, "4.0667": {"post": [2, 0.00105, -0.5], "lerp_mode": "catmullrom"}, "4.1667": {"post": [0.20183, 0.07896, 1.68632], "lerp_mode": "catmullrom"}, "4.3": {"post": [0.23051, 0.06375, 0.75603], "lerp_mode": "catmullrom"}, "4.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.54, 0.25, 0.135], "lerp_mode": "catmullrom"}, "0.3667": {"post": [6.075, 3.675, 0.7075], "lerp_mode": "catmullrom"}, "0.5": {"post": [7.805, 4.925, 1.3675], "lerp_mode": "catmullrom"}, "0.6667": {"post": [7.775, 4.25, 1.6575], "lerp_mode": "catmullrom"}, "0.8667": {"post": [7.775, 4, 1.3075], "lerp_mode": "catmullrom"}, "0.9667": {"post": [7.785, 3.68, 1.245], "lerp_mode": "catmullrom"}, "1.0667": {"post": [7.775, 3.625, 1.1075], "lerp_mode": "catmullrom"}, "1.3667": [7.575, 4.15, 1.1075], "1.4667": [7.575, 3.7725, 1.1075], "1.5667": [7.475, 3.5225, 1.1075], "1.8": {"pre": [7.675, 3.7225, 1.1075], "post": [7.675, 3.7225, 1.1075], "lerp_mode": "catmullrom"}, "1.9333": {"post": [8.14, 3.75, 1.12], "lerp_mode": "catmullrom"}, "2.1667": {"post": [8.47, 4.5, 1.14], "lerp_mode": "catmullrom"}, "2.3": {"post": [8.475, 4.8225, 1.1075], "lerp_mode": "catmullrom"}, "2.5": {"post": [8.825, 4.9325, 0.7675], "lerp_mode": "catmullrom"}, "2.6": {"post": [8.905, 4.9625, 0.7975], "lerp_mode": "catmullrom"}, "2.7": {"post": [8.875, 4.8725, 1.1075], "lerp_mode": "catmullrom"}, "2.8": {"post": [8.775, 4.8725, 1.1075], "lerp_mode": "catmullrom"}, "2.8667": {"post": [8.775, 4.4725, 1.1075], "lerp_mode": "catmullrom"}, "3.0": {"post": [8.855, 4.3725, 1.0675], "lerp_mode": "catmullrom"}, "3.2": [7.275, 1.8225, 1.1075], "3.3667": [3.45, 0.205, 1.625], "3.5667": {"pre": [0.7, -0.8, 0.525], "post": [0.7, -0.8, 0.525], "lerp_mode": "catmullrom"}, "3.7": {"post": [0.46, -1.55, -0.79], "lerp_mode": "catmullrom"}, "3.8667": {"post": [0.28, -1.35, -2.04], "lerp_mode": "catmullrom"}, "3.9667": {"post": [0.13, -1.14, -1.475], "lerp_mode": "catmullrom"}, "4.0667": {"post": [0, -0.85, 0.225], "lerp_mode": "catmullrom"}, "4.1667": {"post": [0, 0.04, 0.51], "lerp_mode": "catmullrom"}, "4.2667": {"post": [0, -0.02, -0.18], "lerp_mode": "catmullrom"}, "4.4": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": {"0.0": {"post": [89.52779, -393.457, -128.79941], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [7.925, -11.05, -2.5], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "mag_and_bullet": {"rotation": {"0.9667": [0, 0, 0], "1.0": [-7.5, 0, 0], "1.3667": [-7.5, 0, 0], "1.4": [-4.5, 0, 0], "1.4667": {"pre": [-12.5, 0, 0], "post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5667": {"post": [-23.64279, 2.01727, 32.33354], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-11.82538, 12.81448, 68.2243], "lerp_mode": "catmullrom"}, "1.8": {"post": [-31.83, 12.81, 68.22], "lerp_mode": "catmullrom"}, "2.2667": [-31.83, 12.81, 68.22], "2.4": {"pre": [-19.69755, 0.77037, 8.10029], "post": [-19.69755, 0.77037, 8.10029], "lerp_mode": "catmullrom"}, "2.5": [-12.5, 0, 0], "2.5667": [-14.5, 0, 0], "2.6": [-17.5, 0, 0], "2.6667": {"pre": [-31.5, 0, 0], "post": [-31.5, 0, 0], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-31.5, 0, 0], "lerp_mode": "catmullrom"}, "2.8": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.9": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.9667": [0, 0, 0], "1.0": [0, -1, 0], "1.3667": {"pre": [0, -1, 0], "post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.4": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.4667": {"post": [0, -4, -0.8], "lerp_mode": "catmullrom"}, "1.5667": {"post": [0.7, -9.4, -1.5], "lerp_mode": "catmullrom"}, "1.6667": {"post": [1.34677, -16.15901, 6.92345], "lerp_mode": "catmullrom"}, "1.8": {"post": [4.85794, -20.25763, 20.40391], "lerp_mode": "catmullrom"}, "2.2667": [2.30454, -17.54701, 19.15628], "2.4": {"pre": [-0.5, -9.5, -0.9], "post": [-0.5, -9.5, -0.9], "lerp_mode": "catmullrom"}, "2.5": [0, -4, -0.8], "2.5667": [0, -2.8, -0.85], "2.6": [0, -2.4, -1.025], "2.6667": {"pre": [0, -2.925, -1.85], "post": [0, -2.925, -1.85], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0, -2.5, -1.975], "lerp_mode": "catmullrom"}, "2.8": {"post": [0, -0.2, 0], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0, -0.2, 0], "lerp_mode": "catmullrom"}, "2.9": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "righthand": {"rotation": {"0.0": [101.21, -323.71, -175], "0.4": [100.08061, -333.53922, -177.16105], "0.5667": [100.08, -333.54, -177.16], "0.9": [149.08, -341.53, -166.74], "0.9333": [150.33, -341.53, -166.74], "0.9667": [151.58, -341.53, -166.74], "1.0": [143.58, -341.53, -166.74], "1.0333": [149.06, -342.31, -161.14], "1.0667": [146.58, -341.53, -166.74], "1.3667": [74.88, -329.02, -256.27], "1.4": [75.32, -331.92, -255.38], "1.4333": [77.64, -331.93, -257.99], "1.4667": [81.32, -331.92, -255.38], "1.5": [84.67, -332.57, -243.81], "1.5333": [88.6, -333.52, -227.49], "1.5667": [92.58, -334.62, -212.67], "1.6": [96.82, -336.58, -201.05], "1.6333": [101.11, -338.69, -190.94], "1.6667": [104.35, -338.69, -183.54], "1.7": [105.9, -334.56, -179.98], "1.7333": [106.39, -328.32, -179.14], "1.7667": [106.64, -323.74, -178.83], "2.3": [106.64, -323.74, -178.83], "2.3667": [95.91, -329.99, -222.28], "2.4": [90.44, -332.56, -241.99], "2.4333": [86.47, -333.1, -250.62], "2.5": [81.32, -331.92, -255.38], "2.5667": [83.18573, -329.93362, -255.64935], "2.6": [85.05271, -326.94113, -255.90746], "2.6667": [87.64261, -322.46811, -256.59675], "2.7": [87.67086, -323.46916, -256.54876], "2.7667": [87.64261, -322.46811, -256.59675], "2.8": [88.06665, -345.45444, -255.64757], "2.8667": [88.07, -345.45, -255.65], "2.9": [88.09, -347.45, -255.58], "2.9333": [87.57, -347.44, -258.37], "2.9667": [87.06, -347.1, -261.18], "3.0": [88.08, -346.45, -255.61], "3.0333": [92.25, -345.39, -233.4], "3.0667": [97.96, -344.02, -202.81], "3.1": [101.89, -342.62, -180.34], "3.1333": [102.44, -341.66, -175.17], "3.1667": [102.07, -340.54, -174.04], "3.2": [101.2, -339.49, -175.27], "3.2333": [100.27, -338.72, -177.18], "3.2667": [99.7, -338.47, -178.1], "3.3667": [99.41439, -343.3972, -178.97857], "3.5": [101.2139, -323.71163, -174.99863]}, "position": {"0.0": [-6.4, -14.425, 2], "0.4": [-6.4, -14.425, 2], "0.5667": [-6.4, -14.425, 2], "0.6667": [-10.4, -14.92, 8.5], "0.7": [-10.21, -15, 9.49], "0.7333": [-9.72, -15.08, 10.55], "0.7667": [-9.04, -15.17, 11.63], "0.8": [-8.25, -15.25, 12.67], "0.8333": [-7.48, -15.32, 13.62], "0.8667": [-6.83, -15.38, 14.41], "0.9": [-6.4, -15.42, 15], "0.9333": [-6.4, -15.42, 15], "0.9667": [-6.4, -15.42, 15], "1.0": [-6.4, -15.92, 14.8], "1.0333": [-6.4, -15.63, 15.04], "1.0667": [-6.4, -15.92, 14.9], "1.1": [-6.4, -17.11, 14.32], "1.1333": [-6.39, -18.84, 13.5], "1.1667": [-6.39, -20.41, 12.58], "1.2": [-6.4, -21.12, 11.7], "1.3667": [-6.5, -16.32, 8.4], "1.4": [-6.5, -16.42, 8.6], "1.4333": [-5.92, -18.21, 7.65], "1.4667": [-6, -19.2, 7.35], "1.5": [-6.83, -20.65, 6.91], "1.5333": [-7.95, -22.59, 6.46], "1.5667": [-8.7, -24.52, 7.03], "1.6": [-8.81, -26.4, 9.24], "1.6333": [-8.55, -28.28, 12.46], "1.6667": [-8.05, -29.82, 15.75], "1.7": [-7.09, -31, 19.23], "1.7333": [-5.9, -31.85, 22.77], "1.7667": [-5.27, -32.19, 25.09], "2.3": [-7.5, -30.02, 23.45], "2.3667": [-8.49, -26.12, 12.53], "2.4": [-8.59, -24.09, 7.92], "2.4333": [-8.12, -22.3, 6.48], "2.5": [-6.75, -19.35, 6.98], "2.5667": [-6.95, -17.85, 7.275], "2.6": [-6.65, -16.85, 6.675], "2.6667": [-6.65, -16.55, 5.175], "2.7": [-6.65, -16.55, 5.275], "2.7667": [-6.65, -16.55, 5.175], "2.8": [-6.65, -16.05, 8.475], "2.8667": [-6.65, -16.05, 8.475], "2.9": [-6.65, -15.85, 8.48], "2.9333": [-6.31, -16.41, 8.57], "2.9667": [-5.88, -17.22, 8.62], "3.0": [-5.9, -17.82, 8.1], "3.0333": [-6.93, -18.16, 6.46], "3.0667": [-8.4, -18.31, 4.26], "3.1": [-9.2, -18.02, 2.6], "3.1333": [-8.99, -17.49, 2.16], "3.1667": [-8.38, -16.69, 1.98], "3.2": [-7.61, -15.79, 1.96], "3.2333": [-6.88, -14.98, 2], "3.2667": [-6.4, -14.42, 2], "3.3667": [-6.4, -14.425, 2], "3.5": [-6.4, -14.425, 2]}, "scale": [1, 1.5, 1]}, "l2": {"rotation": {"0.4333": [0, 0, 0], "0.5667": [-3, 0, 0], "0.7": [0, 0, 0], "0.8333": [-2, 0, 0], "1.0": [-2, 0, 0], "1.1333": [0, 0, 0], "1.2667": [-2, 0, 0], "1.4": [-0.75, 0, 0], "1.5": [4.375, 0, 0], "1.6667": [-1.025, 0, 0], "1.8": [2.25, 0, 0], "1.9333": [1.25, 0, 0], "2.1333": [2.25, 0, 0], "2.3333": [0.25, 0, 0], "2.5": [1.25, 0, 0], "2.6667": [2.25, 0, 0], "2.7667": [2.25, 0, 0], "2.8": [6.75, 0, 0], "2.9333": [-2.2, 0, 0], "3.0667": [3.95, 0, 0], "3.2": [0, 0, 0], "3.3667": [1.75, 0, 0], "3.5": [0, 0, 0], "3.7333": [0, 0, 0], "3.8667": [-3, 0, 0], "4.0": [-2, 0, 0], "4.1333": [0, 0, 0], "4.3": [-1, 0, 0], "4.4333": [0, 0, 0]}}, "l3": {"rotation": {"0.4333": [0, 0, 0], "0.5667": [-3, 0, 0], "0.7": [0, 0, 0], "0.8333": [-2, 0, 0], "1.0": [-2, 0, 0], "1.1333": [0, 0, 0], "1.2667": [-2, 0, 0], "1.4": [-0.75, 0, 0], "1.5": [4.375, 0, 0], "1.6667": [-1.025, 0, 0], "1.8": [2.25, 0, 0], "1.9333": [1.25, 0, 0], "2.1333": [2.25, 0, 0], "2.3333": [0.25, 0, 0], "2.5": [1.25, 0, 0], "2.6667": [2.25, 0, 0], "2.7667": [2.25, 0, 0], "2.8": [5.175, 0, 0], "2.9333": [-0.425, 0, 0], "3.0667": [3.95, 0, 0], "3.2": [0.6, 0, 0], "3.3667": [1.75, 0, 0], "3.5": [0, 0, 0], "3.7333": [0, 0, 0], "3.8667": [-3, 0, 0], "4.0": [-2, 0, 0], "4.1333": [0, 0, 0], "4.3": [-1, 0, 0], "4.4333": [0, 0, 0]}}, "constraint": {"rotation": {"3.2667": [0.05, 0.05, 0.05], "4.0667": [0.1, 0.1, 0.1]}, "position": {"3.2667": [0.05, 0.05, 0.05], "4.0667": [0.2, 0.2, 0.35]}}, "camera": {"relative_to": {"rotation": "entity"}, "rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.545, 0, 0], "lerp_mode": "catmullrom"}, "0.4": {"post": [3.645, -0.28, 1.06], "lerp_mode": "catmullrom"}, "0.5333": {"post": [4.235, -0.46, 1.73], "lerp_mode": "catmullrom"}, "0.6333": {"post": [5.375, -0.525, 2.2], "lerp_mode": "catmullrom"}, "0.8": {"post": [4.225, -0.57, 1.75], "lerp_mode": "catmullrom"}, "0.9667": {"post": [4.62, -0.59, 1.9], "lerp_mode": "catmullrom"}, "1.0667": {"post": [5.305, -0.61, 2.4], "lerp_mode": "catmullrom"}, "1.2": {"post": [4.49, -0.63, 1.9], "lerp_mode": "catmullrom"}, "1.4333": {"post": [4.855, -0.65, 1.9], "lerp_mode": "catmullrom"}, "1.5": {"post": [6.265, -0.35, 2.9], "lerp_mode": "catmullrom"}, "1.6": {"post": [4.715, 0.11, 1], "lerp_mode": "catmullrom"}, "1.7333": {"post": [5.65, 0.295, 2.075], "lerp_mode": "catmullrom"}, "1.9333": {"post": [5.305, 0.19, 1.375], "lerp_mode": "catmullrom"}, "2.2667": {"post": [4.915, 0.26, 1.3], "lerp_mode": "catmullrom"}, "2.6333": {"post": [6.93, -0.65, 1.525], "lerp_mode": "catmullrom"}, "2.7333": {"post": [6.87, -0.74, 1.56], "lerp_mode": "catmullrom"}, "2.8333": {"post": [8, -0.76, 3.045], "lerp_mode": "catmullrom"}, "2.9333": {"post": [6.27, -0.81, 0.625], "lerp_mode": "catmullrom"}, "3.0667": {"post": [6.97, -0.89, 1.985], "lerp_mode": "catmullrom"}, "3.2": {"post": [5.445, -0.85, 1.525], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-0.195, 0.125, 0], "lerp_mode": "catmullrom"}, "3.7667": {"post": [0.335, 0.16, 0.365], "lerp_mode": "catmullrom"}, "3.8667": {"post": [0.58, 0.08, -0.235], "lerp_mode": "catmullrom"}, "4.0": {"post": [0.17, 0, 0], "lerp_mode": "catmullrom"}, "4.1": {"post": [0.56, -0.01, 1.225], "lerp_mode": "catmullrom"}, "4.2": {"post": [-0.4, 0, -0.825], "lerp_mode": "catmullrom"}, "4.3333": {"post": [0.075, 0, 0.325], "lerp_mode": "catmullrom"}, "4.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "tacz:m95/m95_reload_raise"}, "0.8": {"effect": "tacz:m95/m95_reload_magrelease"}, "1.0667": {"effect": "tacz:m95/m95_reload_magout"}, "1.8": {"effect": "tacz:m95/m95_reload_arm"}, "2.6": {"effect": "tacz:m95/m95_reload_magin"}, "3.0": {"effect": "tacz:m95/m95_reload_rotate"}, "3.4667": {"effect": "tacz:m95/m95_reload_end"}}}, "reload_empty": {"animation_length": 5.6, "bones": {"m95_bolt": {"rotation": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.2333": [0, 0, 0], "0.3333": [0, 0, 0], "0.3667": [0, 0, 0], "4.5333": [0, 0, 0], "4.5667": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.2333": [0, 0, 0], "0.3333": [0, 0, 0], "0.3667": [0, 0, 8.1], "0.4": [0, 0, 7.98], "0.4333": [0, 0, 8.09], "4.5333": [0, 0, 8], "4.5667": [0, 0, 0]}}, "rotate": {"rotation": {"0.0": [0, 0, 0], "0.1667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2333": [0, 0, 50], "0.3333": [0, 0, 49], "4.5333": [0, 0, 49], "4.7333": [0, 0, 50.88], "4.7667": [0, 0, 50], "4.8": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2333": [0, 0, 0], "4.7667": [0, 0, 0], "4.8": [0, 0, 0]}}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-0.33, -0.04, -1.07], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0.00061, -0.0349, -0.7253], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-0.00052, -0.03, 3.85], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.99949, -1.04729, 2.86759], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0.00068, -0.04745, 0.80015], "lerp_mode": "catmullrom"}, "0.6": [0.99841, -0.08234, 5.34985], "0.8": {"pre": [2.67718, -0.04505, 12.1253], "post": [2.67718, -0.04505, 12.1253], "lerp_mode": "catmullrom"}, "0.8667": {"post": [2.65638, -0.03788, 13.88775], "lerp_mode": "catmullrom"}, "0.9667": {"post": [2.67439, -0.4211, 11.40467], "lerp_mode": "catmullrom"}, "1.1": {"post": [3, -0.03, 12.925], "lerp_mode": "catmullrom"}, "1.2": {"post": [2.49449, -0.18936, 13.16984], "lerp_mode": "catmullrom"}, "1.2333": {"post": [1.42408, 0.23646, 16.44736], "lerp_mode": "catmullrom"}, "1.3333": {"post": [1.64378, -0.63689, 14.16576], "lerp_mode": "catmullrom"}, "1.4": {"post": [1.10374, -0.11575, 14.04204], "lerp_mode": "catmullrom"}, "1.5": [1.775, -0.02, 11.83], "1.6": [1.755, 0.02, 9.93], "1.7333": [0.425, 0.05, 6.2], "1.8333": {"pre": [-7.15218, -2.25361, 1.91612], "post": [-7.15218, -2.25361, 1.91612], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-43.66, -16.8, -11.7], "lerp_mode": "catmullrom"}, "2.2": {"post": [-61.03546, -23.24793, -16.91364], "lerp_mode": "catmullrom"}, "2.3": {"post": [-61.82902, -24.13032, -17.42744], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-62.0423, -23.78154, -18.44278], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-62.87905, -21.91768, -17.3508], "lerp_mode": "catmullrom"}, "2.4333": {"post": [-62.49843, -22.39725, -17.62244], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-62.41404, -21.47205, -15.9167], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-63.06685, -21.0218, -16.87472], "lerp_mode": "catmullrom"}, "2.8": {"post": [-63.07, -21.02, -16.87], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-62.29818, -24.57434, -18.86082], "lerp_mode": "catmullrom"}, "3.0667": [-62.09765, -24.92462, -18.97239], "3.1667": [-62.50572, -23.68812, -18.3532], "3.3": [-61.39681, -24.36283, -19.88417], "3.3333": [-64.32753, -24.586, -20.05043], "3.4": [-63.62365, -20.76692, -17.98624], "3.5": [-58.3891, -23.34264, -19.47727], "3.5667": [-56.01, -21.86, -18.42], "3.7": {"pre": [-51.38, -18.48, -15.56], "post": [-51.38, -18.48, -15.56], "lerp_mode": "catmullrom"}, "3.9333": {"post": [-14.82, -5.36, -4.49], "lerp_mode": "catmullrom"}, "4.0333": {"post": [0.00061, -0.0349, -0.0003], "lerp_mode": "catmullrom"}, "4.1": [2.55466, -0.51817, 4.83543], "4.2": {"pre": [4.05415, 0.78959, 0.87252], "post": [4.05415, 0.78959, 0.87252], "lerp_mode": "catmullrom"}, "4.2667": {"post": [4.06729, 0.71877, 1.8701], "lerp_mode": "catmullrom"}, "4.3667": {"post": [3.67707, 0.77425, 0.94198], "lerp_mode": "catmullrom"}, "4.6333": {"post": [2.06729, 0.71877, 1.8701], "lerp_mode": "catmullrom"}, "4.6667": {"post": [2.08213, -0.31557, 2.83309], "lerp_mode": "catmullrom"}, "4.7333": {"post": [2.08224, 0.68377, 2.86942], "lerp_mode": "catmullrom"}, "4.8": {"post": [1.08218, -0.31605, 2.85053], "lerp_mode": "catmullrom"}, "4.9": {"post": [1.05772, 0.72112, 0.86962], "lerp_mode": "catmullrom"}, "4.9333": {"post": [0.04513, 0.75692, -0.13013], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-0.31722, -1.42334, 3.72395], "lerp_mode": "catmullrom"}, "5.2": {"post": [-0.75046, -1.79335, 3.9691], "lerp_mode": "catmullrom"}, "5.2667": {"post": [0.25022, -0.5359, 0.59986], "lerp_mode": "catmullrom"}, "5.3333": {"post": [0.19046, -0.0875, -0.56009], "lerp_mode": "catmullrom"}, "5.3667": {"post": [0.2, -0.01, 0.865], "lerp_mode": "catmullrom"}, "5.4333": {"post": [0.2, 0.05, -0.54], "lerp_mode": "catmullrom"}, "5.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [0, -0.5, -0.8], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0, -1, -2.425], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, -1, -2.825], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0, -0.8, -2.025], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0, -0.9, -2.575], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.05, -0.96, -2.585], "lerp_mode": "catmullrom"}, "0.6": [-0.325, -1, -2.85], "0.8": [-0.4, -1.275, -3.075], "0.8667": [-0.4, -1.375, -3.075], "0.9667": [-0.4, -1.475, -3.075], "1.2333": [-0.5, -1.35, -3.075], "1.3333": [-0.5, -1.575, -2.975], "1.4667": [-0.5, -1.675, -3.075], "1.6667": [-0.5, -1.175, -3.075], "1.9": {"pre": [-0.72, -1.37, -2.215], "post": [-0.72, -1.37, -2.215], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-1.58, 3.95, 0.75], "lerp_mode": "catmullrom"}, "2.2": {"post": [-2.2, 5.6, 2.85], "lerp_mode": "catmullrom"}, "2.3": {"post": [-2.4, 5.5, 3.15], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-2.4, 4.825, 3.15], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-2.4, 4.65, 3.15], "lerp_mode": "catmullrom"}, "2.7": {"post": [-2.4, 5.2, 3.15], "lerp_mode": "catmullrom"}, "2.8": {"post": [-2.4, 5.4, 3.15], "lerp_mode": "catmullrom"}, "2.9": {"post": [-2.4, 5.4, 3.15], "lerp_mode": "catmullrom"}, "3.0667": {"post": [-2.4, 5.225, 3.15], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-2.4, 5.075, 3.15], "lerp_mode": "catmullrom"}, "3.3": {"post": [-2.5, 5.275, 3.15], "lerp_mode": "catmullrom"}, "3.3333": {"post": [-2.52, 4.97, 3.2], "lerp_mode": "catmullrom"}, "3.4": {"post": [-2.52, 4.575, 3.2], "lerp_mode": "catmullrom"}, "3.5333": [-2.5, 4.575, 2.85], "3.7": [-2.06, 4, 2.22], "3.9333": {"pre": [-0.59, 0.395, 0.33], "post": [-0.59, 0.395, 0.33], "lerp_mode": "catmullrom"}, "4.0333": {"post": [0, -1.175, -0.95], "lerp_mode": "catmullrom"}, "4.1": {"post": [0.36, -2.025, -0.165], "lerp_mode": "catmullrom"}, "4.2": {"post": [0.28, -2.225, 0.235], "lerp_mode": "catmullrom"}, "4.2667": {"post": [0.28, -1.725, 0.235], "lerp_mode": "catmullrom"}, "4.4667": {"post": [0.29, -1.525, 0.265], "lerp_mode": "catmullrom"}, "4.6333": {"post": [0.28, -1.75, 0.235], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0.18, -1.65, -0.165], "lerp_mode": "catmullrom"}, "4.7333": {"post": [0.18, -1.65, -0.365], "lerp_mode": "catmullrom"}, "4.8333": {"post": [0.18, -1.65, -0.265], "lerp_mode": "catmullrom"}, "4.9333": {"post": [0.18, -1.55, -0.065], "lerp_mode": "catmullrom"}, "5.1667": {"post": [0, 0.1, -0.9], "lerp_mode": "catmullrom"}, "5.2667": {"post": [0, 0, -0.725], "lerp_mode": "catmullrom"}, "5.3667": {"post": [0, 0, 0.325], "lerp_mode": "catmullrom"}, "5.4333": {"post": [0, 0, 0.275], "lerp_mode": "catmullrom"}, "5.5": {"post": [0, 0, -0.05], "lerp_mode": "catmullrom"}, "5.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": {"0.0": [89.52779, -393.457, -128.79941], "1.6667": [89.52779, -393.457, -128.79941], "1.8": {"pre": [100.01578, -399.50431, -128.81441], "post": [100.01578, -399.50431, -128.81441], "lerp_mode": "catmullrom"}, "1.9333": {"post": [92.25285, -387.68838, -128.47925], "lerp_mode": "catmullrom"}, "2.0333": {"post": [89.58, -393.36, -128.8], "lerp_mode": "catmullrom"}, "2.1": {"post": [84.46474, -403.45971, -128.68369], "lerp_mode": "catmullrom"}, "2.2": {"post": [67.71002, -409.63402, -104.00225], "lerp_mode": "catmullrom"}, "2.3333": [80.43029, -378.86478, -105.8445], "2.7333": [80.43029, -378.86478, -105.8445], "3.5333": [80.43029, -378.86478, -105.8445], "3.8667": [89.52779, -393.457, -128.79941]}, "position": {"0.0": [7.925, -11.05, -2.5], "1.6667": [7.925, -11.05, -2.5], "2.0333": {"pre": [8.145, -11.375, -2.08], "post": [8.145, -11.375, -2.08], "lerp_mode": "catmullrom"}, "2.1": {"post": [11.175, -16.425, 6], "lerp_mode": "catmullrom"}, "2.2": {"post": [12.175, -20.425, 12], "lerp_mode": "catmullrom"}, "2.3333": [7.175, -16.925, 12.7], "2.7333": [7.175, -16.925, 12.7], "3.5333": [7.175, -16.925, 12.7], "3.7": {"pre": [7.535, -17.845, 3.89], "post": [7.535, -17.845, 3.89], "lerp_mode": "catmullrom"}, "3.8": {"post": [7.805, -13.175, -0.97], "lerp_mode": "catmullrom"}, "3.8667": [7.925, -11.05, -2.5]}, "scale": [1, 1.5, 1]}, "mag_and_bullet": {"rotation": {"0.8": [0, 0, 0], "0.8667": [-7.5, 0, 0], "1.2333": [-7.5, 0, 0], "1.4": [-34.5, 0, 0], "1.4667": [-34.5, 0, 0], "2.2333": [-34.5, 0, 0], "2.5333": [0, 0, 0]}, "position": {"0.8": [0, 0, 0], "0.8667": [0, -1, 0], "1.2333": [0, -1, 0], "1.4": [-0.5, -23.2, -0.1], "1.4667": [4.5, -23.2, 28.9], "2.2333": [4.5, -23.2, 28.9], "2.5333": [0, 0, 0]}, "scale": {"1.4": [1, 1, 1], "1.4333": [0, 0, 0], "2.5333": [0, 0, 0], "2.5667": [1, 1, 1]}}, "righthand": {"rotation": {"0.0": [101.2139, -323.71163, -174.99863], "0.1667": {"pre": [88.38612, -319.74651, -245.90328], "post": [88.38612, -319.74651, -245.90328], "lerp_mode": "catmullrom"}, "0.2333": [85.02123, -319.98006, -251.12409], "0.3333": [88.39005, -319.74919, -245.8941], "0.3667": [87.27066, -296.76498, -247.28687], "0.4333": {"pre": [87.27, -296.76, -247.29], "post": [87.27, -296.76, -247.29], "lerp_mode": "catmullrom"}, "0.5": {"post": [87.27, -296.76, -247.29], "lerp_mode": "catmullrom"}, "0.6": {"post": [77.41646, -326.46588, -261.69175], "lerp_mode": "catmullrom"}, "0.7": {"post": [77.42, -326.47, -261.69], "lerp_mode": "catmullrom"}, "0.8": {"post": [77.75982, -328.91161, -261.05436], "lerp_mode": "catmullrom"}, "0.8667": {"post": [76.6949, -322.0825, -262.92683], "lerp_mode": "catmullrom"}, "0.9333": {"post": [77.03195, -324.02766, -262.36117], "lerp_mode": "catmullrom"}, "1.2": [79.02806, -342.62687, -257.96893], "1.2333": [78.90302, -340.66696, -258.37326], "1.4": {"pre": [76.50368, -321.1158, -263.22246], "post": [76.50368, -321.1158, -263.22246], "lerp_mode": "catmullrom"}, "1.5": {"post": [112.48051, -326.17602, -173.43479], "lerp_mode": "catmullrom"}, "1.6667": {"post": [96.79395, -336.0991, -174.85172], "lerp_mode": "catmullrom"}, "1.7667": [92.61599, -335.13452, -178.47545], "4.1333": [92.61599, -335.13452, -178.47545], "4.2333": [101.21, -323.71, -175], "4.4": [87.27066, -296.76498, -247.28687], "4.4667": [87.78845, -303.75856, -246.68852], "4.5333": {"pre": [131.34889, -356.2811, -154.70557], "post": [131.34889, -356.2811, -154.70557], "lerp_mode": "catmullrom"}, "4.6333": {"post": [142.9864, -395.30317, -131.70404], "lerp_mode": "catmullrom"}, "4.7333": {"post": [142.99, -395.3, -131.7], "lerp_mode": "catmullrom"}, "4.7667": {"post": [142.99, -395.3, -131.7], "lerp_mode": "catmullrom"}, "4.8": {"post": [120.79211, -411.56093, -109.95108], "lerp_mode": "catmullrom"}, "4.8333": {"post": [120.79211, -411.56093, -109.95108], "lerp_mode": "catmullrom"}, "4.9333": {"post": [127.842, -350.86674, -111.83878], "lerp_mode": "catmullrom"}, "5.0667": [107.60421, -324.29344, -172.6334], "5.1": [107.21, -323.71, -175], "5.1667": [101.2139, -323.71163, -174.99863]}, "position": {"0.0": [-6.4, -14.425, 2], "0.1": {"pre": [-8.5, -14.905, 7.13], "post": [-8.5, -14.905, 7.13], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-10, -12.175, 11.15], "lerp_mode": "catmullrom"}, "0.2333": [-11, -8.875, 10.95], "0.3333": [-11, -8.875, 10.85], "0.3667": [-11, -8.575, 19.1], "0.4333": {"pre": [-11, -8.575, 19.1], "post": [-11, -8.575, 19.1], "lerp_mode": "catmullrom"}, "0.5": {"post": [-11, -8.575, 19.1], "lerp_mode": "catmullrom"}, "0.6": {"post": [-11, -14.575, 17.575], "lerp_mode": "catmullrom"}, "0.7": {"post": [-7, -15.375, 17.3], "lerp_mode": "catmullrom"}, "0.8": {"post": [-7, -15.475, 17.3], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-7, -15.875, 17.2], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-7, -15.975, 17.2], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-6.75, -19.885, 12.75], "lerp_mode": "catmullrom"}, "1.2": [-6.5, -16, 10.575], "1.2333": [-6.5, -16.7, 9.975], "1.4": {"pre": [-6.5, -35.7, 7.475], "post": [-6.5, -35.7, 7.475], "lerp_mode": "catmullrom"}, "1.5": {"post": [-10.5, -24.7, 7.475], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-7.2, -16.775, 4.9], "lerp_mode": "catmullrom"}, "1.7667": [-6.39339, -14.32296, 2.39833], "4.2": [-6.39339, -14.32296, 2.39833], "4.3": {"pre": [-11.4, -15.025, 12.97], "post": [-11.4, -15.025, 12.97], "lerp_mode": "catmullrom"}, "4.4": {"post": [-11, -8.575, 16.95], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-11, -8.575, 16.95], "lerp_mode": "catmullrom"}, "4.5333": [-11, -8.775, 16.85], "4.5667": [-10.25, -8.025, 9.35], "4.6333": [-10.25, -8.025, 9.35], "4.7333": [-10.25, -8.025, 9.35], "4.7667": [-10.25, -8.025, 9.35], "4.8": [-10.45, -10.825, 8.75], "4.8333": [-10.45, -10.825, 8.75], "4.9333": [-8.8, -15.025, 3.33], "5.0667": {"pre": [-6.4, -14.475, 2], "post": [-6.4, -14.475, 2], "lerp_mode": "catmullrom"}, "5.1667": [-6.4, -14.425, 2]}, "scale": [1, 1.5, 1]}, "mag_and_lefthand": {"rotation": {"2.1667": [0, 0, 0], "2.5333": [-27, 0, 0], "2.7333": [-27, 0, 0], "2.8": [-9.38226, 30.73448, -17.63481], "2.9": [-16.91592, 9.21748, -13.00298], "3.0": [-19.5, 0, 0], "3.0667": [-24, 0, 0], "3.1667": {"pre": [-17.5, 0, 0], "post": [-17.5, 0, 0], "lerp_mode": "catmullrom"}, "3.3": {"post": [-20.88, 0, 0], "lerp_mode": "catmullrom"}, "3.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.4": [0, 0, 0]}, "position": {"2.1667": [0, 0, 0], "2.5333": [-0.5, -12.2, 21.9], "2.7333": [-0.5, -12.2, 21.9], "2.8": [1.8, -10, 4.8], "2.9": [0.85441, -4.59734, 0.31123], "3.0": [0.3, -3.5, -0.4], "3.0667": [0.1, -2.5, -0.9], "3.1667": {"pre": [0, -2.1, -0.4], "post": [0, -2.1, -0.4], "lerp_mode": "catmullrom"}, "3.3": {"post": [0, -2.13, -0.56], "lerp_mode": "catmullrom"}, "3.3333": {"post": [0, -0.2, 0], "lerp_mode": "catmullrom"}, "3.4": [0, 0, 0]}}, "l2": {"rotation": {"2.0667": [0, 0, 0], "2.1667": [-4.25, 0, 0], "2.3333": [-0.5, 0, 0], "2.4333": [-2, 0, 0], "2.5667": [0.25, 0, 0], "2.7": [-1.5, 0, 0], "2.8333": [-0.5, 0, 0], "3.0667": [-1.5, 0, 0], "3.1667": [-0.25, 0, 0], "3.3": [-1.25, 0, 0], "3.3667": [2.575, 0, 0], "3.4667": [-1.575, 0, 0], "3.5667": [1.275, 0, 0], "3.7333": [-1, 0, 0], "4.1": [0, 0, 0]}}, "l3": {"rotation": {"2.0667": [0, 0, 0], "2.1667": [-4.25, 0, 0], "2.3333": [-0.5, 0, 0], "2.4333": [-2, 0, 0], "2.5667": [0.25, 0, 0], "2.7": [-1.5, 0, 0], "2.8333": [-0.5, 0, 0], "3.0667": [-1.5, 0, 0], "3.1667": [-0.25, 0, 0], "3.3": [-1.25, 0, 0], "3.3667": [2.575, 0, 0], "3.4667": [-1.575, 0, 0], "3.5667": [1.275, 0, 0], "3.7333": [-1, 0, 0], "4.1": [0, 0, 0]}}, "constraint": {"rotation": {"4.9": [0.01, 0.05, 0.05], "5.3": [0.1, 0.1, 0.1]}, "position": {"4.9": [0.05, 0.05, 0.05], "5.3": [0.2, 0.2, 0.35]}}, "shell_ejection": {"rotation": {"0.3333": [0, 0, 0], "0.3667": [0, 0, 0], "0.4333": [0, -135.42, 0], "0.5": [0, -270.83, 0], "0.5667": [0, -406.25, 0], "0.6333": [0, -541.67, 0], "0.7": [0, -677.08, 0], "0.7667": [0, -812.5, 0], "0.8": [0, 0, 0]}, "position": {"0.3333": [0, 0, 0], "0.3667": [0, 0.0875, 6.01875], "0.4333": {"pre": [-9.1, 1.0875, -1.98125], "post": [-9.1, 1.0875, -1.98125], "lerp_mode": "catmullrom"}, "0.5": {"post": [-16.3, 1.0875, -7.98125], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-24.4, -2.9125, -14.98125], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-32.0957, -10.98295, -22.02632], "lerp_mode": "catmullrom"}, "0.7": {"post": [-37.33299, -22.63106, -27.47164], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-40.31093, -33.78793, -30.65098], "lerp_mode": "catmullrom"}, "0.8": [-46.1, -35.0125, -33.48125], "0.8667": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.7667": {"pre": [1, 1, 1], "post": [0, 0, 0]}, "1.2": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.45, 0, 0.2], "lerp_mode": "catmullrom"}, "0.3": {"post": [0.36, -0.01, 0.25], "lerp_mode": "catmullrom"}, "0.3667": {"post": [1.14, -0.02, 1.05], "lerp_mode": "catmullrom"}, "0.4667": {"post": [0.335, -0.03, -0.325], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.875, 0, 0.475], "lerp_mode": "catmullrom"}, "0.8667": {"post": [1.02, 0.4, 0.3], "lerp_mode": "catmullrom"}, "0.9333": {"post": [1.35, 0.47, 0.575], "lerp_mode": "catmullrom"}, "1.1667": {"post": [1.195, 0.55, 0.3], "lerp_mode": "catmullrom"}, "1.2667": {"post": [2.475, 0.54, 1.25], "lerp_mode": "catmullrom"}, "1.3667": {"post": [0.675, 0.52, -0.225], "lerp_mode": "catmullrom"}, "1.5333": {"post": [1.19, 0.48, 0.65], "lerp_mode": "catmullrom"}, "1.6667": {"post": [0.765, 0.4, 0.3], "lerp_mode": "catmullrom"}, "2.0": {"post": [1.31, 0, -0.4], "lerp_mode": "catmullrom"}, "2.3333": {"post": [4.115, -0.3, -1.325], "lerp_mode": "catmullrom"}, "2.4667": {"post": [3.67, -0.36, -1.175], "lerp_mode": "catmullrom"}, "2.6": {"post": [4.395, -0.3, -1.375], "lerp_mode": "catmullrom"}, "2.8": {"post": [3.635, -0.06, -1.375], "lerp_mode": "catmullrom"}, "3.0333": {"post": [3.72, 0.3, -1.075], "lerp_mode": "catmullrom"}, "3.3": {"post": [3.59, 0.5, -0.875], "lerp_mode": "catmullrom"}, "3.3667": {"post": [5.22, 0.5, 0.125], "lerp_mode": "catmullrom"}, "3.4667": {"post": [3.83, 0.47, -1.65], "lerp_mode": "catmullrom"}, "3.5667": {"post": [4.365, 0.42, -0.55], "lerp_mode": "catmullrom"}, "3.7": {"post": [3.94, 0.38, -1.075], "lerp_mode": "catmullrom"}, "3.8333": {"post": [3.935, 0.3, -0.875], "lerp_mode": "catmullrom"}, "4.1": {"post": [1.24, 0, 0.3], "lerp_mode": "catmullrom"}, "4.2333": {"post": [2.075, -0.02, 1.125], "lerp_mode": "catmullrom"}, "4.3333": {"post": [1.59, 0, 0.525], "lerp_mode": "catmullrom"}, "4.5333": {"post": [1.845, 0, 0.775], "lerp_mode": "catmullrom"}, "4.6": {"post": [2.23, 0, 1.625], "lerp_mode": "catmullrom"}, "4.7333": {"post": [1.445, 0, 0.375], "lerp_mode": "catmullrom"}, "4.8": {"post": [1.51, 0, 0.775], "lerp_mode": "catmullrom"}, "4.8667": {"post": [1.8, 0, 0.725], "lerp_mode": "catmullrom"}, "4.9667": {"post": [1.215, 0, 0.175], "lerp_mode": "catmullrom"}, "5.0667": {"post": [0.535, 0, 0.2], "lerp_mode": "catmullrom"}, "5.2": {"post": [-0.125, 0, 0], "lerp_mode": "catmullrom"}, "5.2667": {"post": [0.65, 0, -0.275], "lerp_mode": "catmullrom"}, "5.3667": {"post": [-0.225, 0, 0.9], "lerp_mode": "catmullrom"}, "5.5": {"post": [0, 0, -0.2], "lerp_mode": "catmullrom"}, "5.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0667": {"effect": "tacz:m95/m95_boltback_shell"}, "0.8667": {"effect": "tacz:m95/m95_reload_magrelease"}, "1.1333": {"effect": "tacz:m95/m95_reload_empty_magout"}, "1.7333": {"effect": "tacz:m95/mag_drup_large_drum_dirt"}, "1.9": {"effect": "tacz:m95/m95_reload_empty_lift"}, "3.0": {"effect": "tacz:m95/m95_reload_empty_maghit"}, "3.2333": {"effect": "tacz:m95/m95_reload_empty_magin"}, "3.7": {"effect": "tacz:m95/m95_reload_empty_drop"}, "3.7667": {"effect": "tacz:m95/m95_reload_empty_drop_end"}, "4.5": {"effect": "tacz:m95/m95_boltclose"}, "5.0": {"effect": "tacz:m95/m95_reload_empty_end"}}}, "bolt": {"animation_length": 1.7, "bones": {"m95_bolt": {"rotation": {"0.0": [0, 0, 0], "0.2333": [0, 0, 0], "0.2667": [0, 0, 0], "0.4667": [0, 0, 0], "0.5333": [0, 0, 0], "0.9333": [0, 0, 0], "1.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.2333": [0, 0, 0], "0.2667": [0, 0, 0], "0.4667": [0, 0, 0], "0.5333": [0, 0, 8.1], "0.6": [0, 0, 7.98], "0.9333": [0, 0, 8], "1.0": [0, 0, 0]}}, "rotate": {"rotation": {"0.0": [0, 0, 0], "0.2333": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": [0, 0, 50], "0.4667": [0, 0, 49], "0.9333": [0, 0, 49], "1.1333": [0, 0, 50.88], "1.1667": [0, 0, 50], "1.2333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.2333": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": [0, 0, 0], "1.1667": [0, 0, 0], "1.2333": [0, 0, 0]}}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0.24846, -1.05218, 3.01702], "lerp_mode": "catmullrom"}, "0.2": {"post": [1.749, -1.59527, 1.95299], "lerp_mode": "catmullrom"}, "0.2667": {"post": [1.9779, -1.77101, 3.26694], "lerp_mode": "catmullrom"}, "0.3": {"post": [1.93005, -3.36114, 5.17062], "lerp_mode": "catmullrom"}, "0.4667": {"post": [1.75108, -3.02942, 2.9618], "lerp_mode": "catmullrom"}, "0.5": {"post": [1.07699, -2.08108, -0.14793], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0.35846, -1.07129, -3.73301], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.00275, -0.04738, -0.49985], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0.02843, -0.41751, 1.8871], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-0.03786, -0.99678, 0.74848], "lerp_mode": "catmullrom"}, "0.9333": {"post": [0.02428, -1.08463, 0.74946], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0.58, -1.23, 1.92], "lerp_mode": "catmullrom"}, "1.0": {"post": [1.03969, -1.30357, 1.2277], "lerp_mode": "catmullrom"}, "1.0667": {"post": [0.00076, -1.10235, 1.99983], "lerp_mode": "catmullrom"}, "1.1667": {"post": [0.00076, -0.10235, 1.99985], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-1.98199, -0.3024, -2.75858], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-1.24292, 0.04982, -0.70861], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-1.2097, 0.42993, -1.29814], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5667": {"post": [0, 0, -0.5], "lerp_mode": "catmullrom"}, "1.6333": {"post": [0, 0, 0.86], "lerp_mode": "catmullrom"}, "1.7": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0.125, -0.6, -0.8], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.35, -0.975, -0.9], "lerp_mode": "catmullrom"}, "0.3": {"post": [0.33, -0.99, -1.16], "lerp_mode": "catmullrom"}, "0.4667": {"post": [0.25, -1, -1.3], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0, -0.9, -0.5], "lerp_mode": "catmullrom"}, "0.6": {"post": [-0.15, -1, -0.7], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-0.145, -0.965, -0.7], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-0.2, -0.775, -0.8], "lerp_mode": "catmullrom"}, "1.0": {"post": [-0.375, -0.75, -1.8], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-0.175, -0.75, -1.6], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-0.2, -0.55, -1.7], "lerp_mode": "catmullrom"}, "1.3": {"post": [0, -0.65, -1.7], "lerp_mode": "catmullrom"}, "1.4": {"post": [0, -0.66, -1.16], "lerp_mode": "catmullrom"}, "1.4333": {"post": [0, -0.725, -0.9], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.43, -0.32], "lerp_mode": "catmullrom"}, "1.5333": {"post": [0, -0.26, -0.05], "lerp_mode": "catmullrom"}, "1.5667": {"post": [0, -0.1, 0.3], "lerp_mode": "catmullrom"}, "1.6333": {"post": [0, 0, 0.05], "lerp_mode": "catmullrom"}, "1.7": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": [89.52779, -393.457, -128.79941], "position": [7.925, -11.05, -2.5], "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [101.2139, -323.71163, -174.99863], "0.2333": [88.38612, -319.74651, -245.90328], "0.2667": [85.02123, -319.98006, -251.12409], "0.3": [88.33869, -317.75067, -245.97187], "0.4667": [88.39005, -319.74919, -245.8941], "0.5333": [86.53376, -290.77361, -248.09097], "0.6667": [87.27066, -296.76498, -247.28687], "0.8333": [86.27566, -289.27656, -248.36565], "0.9333": [131.34889, -356.2811, -154.70557], "1.0667": {"pre": [142.9864, -395.30317, -131.70404], "post": [142.9864, -395.30317, -131.70404], "lerp_mode": "catmullrom"}, "1.1333": {"post": [142.99, -395.3, -131.7], "lerp_mode": "catmullrom"}, "1.1667": {"post": [142.99, -395.3, -131.7], "lerp_mode": "catmullrom"}, "1.2333": {"post": [112.60249, -363.96698, -112.97298], "lerp_mode": "catmullrom"}, "1.3": {"post": [105.94, -351.93, -134.35], "lerp_mode": "catmullrom"}, "1.4333": [92.04639, -323.45507, -183.73366], "1.5": [101.2139, -323.71163, -174.99863]}, "position": {"0.0": [-6.4, -14.425, 2], "0.0667": {"pre": [-9.025, -14.93, 8.505], "post": [-9.025, -14.93, 8.505], "lerp_mode": "catmullrom"}, "0.2333": [-9.2, -12.2, 10.375], "0.2667": [-10.2, -8.5, 10.175], "0.3": [-10.2, -8.5, 10.175], "0.4667": [-10.2, -8.5, 10.175], "0.5333": [-10.2, -8.6, 18.325], "0.9333": [-10.2, -8.8, 18.225], "1.0": [-9.45, -8.05, 10.725], "1.0667": [-9.45, -8.05, 10.725], "1.1333": [-9.45, -8.05, 10.725], "1.1667": [-9.45, -8.05, 10.725], "1.2333": [-10.55, -15.75, 10.125], "1.3": [-9.68, -15.28, 7.625], "1.4333": {"pre": [-7.4, -14.2, 1.875], "post": [-7.4, -14.2, 1.875], "lerp_mode": "catmullrom"}, "1.5": [-6.4, -14.425, 2]}, "scale": [1, 1.5, 1]}, "shell_ejection": {"rotation": {"0.5": [0, 0, 0], "0.5667": [0, 0, 0], "0.6333": [0, -135.42, 0], "0.7": [0, -270.83, 0], "0.7667": [0, -406.25, 0], "0.8333": [0, -541.67, 0], "0.9": [0, -677.08, 0], "0.9667": [0, -812.5, 0], "1.0": [0, 0, 0]}, "position": {"0.4667": [0, 0, 0], "0.5667": [0, 0.0875, 6.01875], "0.6333": {"pre": [-9.1, 1.0875, -1.98125], "post": [-9.1, 1.0875, -1.98125], "lerp_mode": "catmullrom"}, "0.7": {"post": [-16.3, 1.0875, -7.98125], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-24.4, -2.9125, -14.98125], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-31.8, -8.9125, -21.98125], "lerp_mode": "catmullrom"}, "0.9": {"post": [-37.8, -16.9125, -27.28125], "lerp_mode": "catmullrom"}, "0.9667": {"post": [-42, -25.3125, -30.28125], "lerp_mode": "catmullrom"}, "1.0": [-46.1, -35.0125, -33.48125], "1.0667": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.9667": {"pre": [1, 1, 1], "post": [0, 0, 0]}, "1.4": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0.45, -0.1, 0.275], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0.4, -0.15, 0.275], "lerp_mode": "catmullrom"}, "0.3": {"post": [0.825, -0.15, 0.775], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0.215, -0.15, 0.1], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0.525, -0.15, 0.275], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.615, -0.1, 0.8], "lerp_mode": "catmullrom"}, "0.5333": {"post": [1.53, 0, -0.125], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.77, 0.075, 0.425], "lerp_mode": "catmullrom"}, "0.7333": {"post": [1.085, 0.075, 0.2], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0.78, 0, 0.275], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0.55, 0, 0.275], "lerp_mode": "catmullrom"}, "1.0333": {"post": [1.29, -0.1, 0.75], "lerp_mode": "catmullrom"}, "1.1": {"post": [0.645, -0.175, 0], "lerp_mode": "catmullrom"}, "1.1667": {"post": [0.89, -0.175, 0.6], "lerp_mode": "catmullrom"}, "1.2667": {"post": [0.465, -0.1, 0.05], "lerp_mode": "catmullrom"}, "1.3333": {"post": [0.295, 0.05, 0.275], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-0.1, 0, -0.025], "lerp_mode": "catmullrom"}, "1.5": {"post": [0.25, 0, 0.275], "lerp_mode": "catmullrom"}, "1.6": {"post": [-0.1, 0, -0.125], "lerp_mode": "catmullrom"}, "1.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.2667": {"effect": "tacz:m95/m95_boltback_shell"}, "0.8333": {"effect": "tacz:m95/m95_boltclose"}, "1.2667": {"effect": "tacz:m95/m95_bolt_end"}}}, "shoot": {"animation_length": 1.23333, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [1.24069, 0.15232, -6.99835], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-5.82024, 1.33042, 5.79388], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-6.38564, -0.61289, -1.91761], "lerp_mode": "catmullrom"}, "0.2": {"post": [-6.0138, -0.64862, -3.1926], "lerp_mode": "catmullrom"}, "0.3": {"post": [-3.22698, -0.95049, 1.89646], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-0.42425, 0.27786, 0.43549], "lerp_mode": "catmullrom"}, "0.7": {"post": [0.23068, -0.21405, -0.46178], "lerp_mode": "catmullrom"}, "0.9": {"post": [-0.09988, 0.076, 0.12379], "lerp_mode": "catmullrom"}, "1.0667": {"post": [0.04509, 0.01634, -0.04605], "lerp_mode": "catmullrom"}, "1.2333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, -0.75, 4.7], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, 0.675, 5.205], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.125, 1.18, 2.775], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.11, 0.94, 0.66], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.075, 0.51, -0.005], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0, -0.47, -0.485], "lerp_mode": "catmullrom"}, "0.5667": {"post": [0, -0.78, -0.095], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0, -0.415, 0.06], "lerp_mode": "catmullrom"}, "0.9": {"post": [0, -0.15, 0.1], "lerp_mode": "catmullrom"}, "1.0667": {"post": [0.03125, -0.17, -0.03], "lerp_mode": "catmullrom"}, "1.2333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "m95_barrel": {"position": {"0.0": [0, 0, 0], "0.0667": [0, 0, 6], "0.1333": [0, 0, 0], "0.2": [0, 0, 0.55], "0.2667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, 0, 3.4], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, 0, -2.875], "lerp_mode": "catmullrom"}, "0.1": {"post": [0, 0, 2.445], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -2.055], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0, 0, 1.37], "lerp_mode": "catmullrom"}, "0.3": {"post": [0, 0, -0.85], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0, 0, 0.525], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0, 0, -0.125], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "constraint": {"rotation": {"0.0": [0.4, 0.1, 0.1], "0.5667": [0, 0, 0]}, "position": {"0.0": [0.2, 0.3, 0.2], "0.5667": [0, 0, 0]}}}}, "inspect": {"animation_length": 7.2, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-1.38509, -1.24434, 4.29497], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-1.39, -1.24, 4.29], "lerp_mode": "catmullrom"}, "0.4": {"post": [-3.91, -4.41, -2.02], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-10.80252, -12.68944, -19.80615], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-11.67934, -12.73253, -20.88586], "lerp_mode": "catmullrom"}, "1.0": {"post": [-10.42561, -13.0448, -21.82949], "lerp_mode": "catmullrom"}, "1.2": {"post": [-10.56428, -12.85979, -20.82188], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-10.39, -12.99, -21.57], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-10.9146, -13.35759, -23.13092], "lerp_mode": "catmullrom"}, "2.3": {"post": [-10.25634, -13.50926, -24.0715], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-10.31, -13.4275, -24.0075], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-10.01823, -12.01652, -22.39677], "lerp_mode": "catmullrom"}, "3.0": {"post": [-6.0733, -2.55567, -8.68449], "lerp_mode": "catmullrom"}, "3.1667": {"post": [-3.30816, -6.55161, -3.15758], "lerp_mode": "catmullrom"}, "3.3": {"post": [-11.99539, -6.2465, 14.78107], "lerp_mode": "catmullrom"}, "3.4": {"post": [-24.07317, -0.97391, 28.22222], "lerp_mode": "catmullrom"}, "3.6": {"post": [-32.28365, 5.59754, 44.62545], "lerp_mode": "catmullrom"}, "3.7": {"post": [-32.44664, 5.36815, 46.1821], "lerp_mode": "catmullrom"}, "3.7667": {"post": [-31.38661, 6.16105, 47.33802], "lerp_mode": "catmullrom"}, "3.9": {"post": [-31.60382, 7.93459, 52.29084], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-33.84674, 6.64885, 47.50441], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-31.80789, 9.10999, 51.06287], "lerp_mode": "catmullrom"}, "4.5": {"post": [-33.34556, 7.01589, 47.88054], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-32.04707, 7.50249, 48.73097], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-32.4996, 5.69075, 45.69734], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-32.66, 7.68, 48.17], "lerp_mode": "catmullrom"}, "5.2667": {"post": [-33.25196, 5.19352, 46.24503], "lerp_mode": "catmullrom"}, "5.4333": {"post": [-33.10702, 8.53837, 48.81576], "lerp_mode": "catmullrom"}, "5.5333": {"post": [-33.8104, 8.07258, 46.85565], "lerp_mode": "catmullrom"}, "5.6667": {"post": [-33.19256, 12.07899, 53.27034], "lerp_mode": "catmullrom"}, "5.8": {"post": [-32.7588, 8.96748, 49.78305], "lerp_mode": "catmullrom"}, "6.0": {"post": [-21.80021, -1.92924, 39.7835], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-11.32385, -8.47574, 19.6574], "lerp_mode": "catmullrom"}, "6.2333": {"post": [-3.97708, -7.46886, 2.34726], "lerp_mode": "catmullrom"}, "6.3": {"post": [-2.77467, -6.47288, 5.91111], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-1.20016, -5.6579, 3.74312], "lerp_mode": "catmullrom"}, "6.5": {"post": [1.76605, -3.2563, 4.9369], "lerp_mode": "catmullrom"}, "6.5667": {"post": [1.78811, -2.65564, 4.66491], "lerp_mode": "catmullrom"}, "6.6667": {"post": [0.8, -0.92, 3.05], "lerp_mode": "catmullrom"}, "6.7333": {"post": [-0.30298, 0.34027, -1.56498], "lerp_mode": "catmullrom"}, "6.8667": {"post": [0.2321, 0.0577, 2.25602], "lerp_mode": "catmullrom"}, "7.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.2, -0.31, 0], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.2, -0.81, 0], "lerp_mode": "catmullrom"}, "0.4": {"post": [-0.18, -0.4, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.14, 0.42, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 2.8, 0], "lerp_mode": "catmullrom"}, "0.7667": {"post": [0, 3.3, 0], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "1.2667": {"post": [0, 3.1, 0], "lerp_mode": "catmullrom"}, "1.8667": {"post": [0, 3.17, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [-0.1, 3.37, 0], "lerp_mode": "catmullrom"}, "2.6": {"post": [-0.14, 3.53, -0.02], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-0.1, 3.345, 0], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0.4, 2.38, 0.23], "lerp_mode": "catmullrom"}, "3.0": {"post": [1.875, -0.58, -0.855], "lerp_mode": "catmullrom"}, "3.1667": {"post": [3.25, -1.13, -1.83], "lerp_mode": "catmullrom"}, "3.4": {"post": [5.905, 3.385, -3.895], "lerp_mode": "catmullrom"}, "3.5": {"post": [7.58, 4.975, -5.215], "lerp_mode": "catmullrom"}, "3.6": {"post": [8.74, 4.71, -5.795], "lerp_mode": "catmullrom"}, "3.7": {"post": [8.8, 3.5, -6.125], "lerp_mode": "catmullrom"}, "3.8333": {"post": [8.8, 3.4, -6.4], "lerp_mode": "catmullrom"}, "3.9667": {"post": [8.925, 3.975, -6.65], "lerp_mode": "catmullrom"}, "4.1333": {"post": [8.775, 4.03, -6.145], "lerp_mode": "catmullrom"}, "4.2667": {"post": [8.78, 4.07, -6.345], "lerp_mode": "catmullrom"}, "4.4": {"post": [8.85625, 4.0125, -6.875], "lerp_mode": "catmullrom"}, "4.5333": {"post": [8.7175, 3.995, -6.56375], "lerp_mode": "catmullrom"}, "4.8333": {"post": [8.585, 3.88125, -6.685], "lerp_mode": "catmullrom"}, "5.0667": {"post": [8.6, 3.875, -6.65], "lerp_mode": "catmullrom"}, "5.3333": {"post": [8.8, 4.275, -6.65], "lerp_mode": "catmullrom"}, "5.4": {"post": [8.35, 3.77, -6.645], "lerp_mode": "catmullrom"}, "5.5333": {"post": [8.55, 3.52, -6.645], "lerp_mode": "catmullrom"}, "5.7333": {"post": [8.55, 3.55, -6.65], "lerp_mode": "catmullrom"}, "5.8667": {"post": [7.93, 2.62, -6.015], "lerp_mode": "catmullrom"}, "6.0333": {"post": [5.525, -1.04, -4.065], "lerp_mode": "catmullrom"}, "6.1667": {"post": [3.235, -5.1, -4.215], "lerp_mode": "catmullrom"}, "6.3333": {"post": [2.36, -4.48, -4.4], "lerp_mode": "catmullrom"}, "6.5": {"post": [1.305, -1.125, -4.24], "lerp_mode": "catmullrom"}, "6.6333": {"post": [0.79, 0.215, -2.5], "lerp_mode": "catmullrom"}, "6.8": {"post": [0.125, -0.575, 0.275], "lerp_mode": "catmullrom"}, "6.9": {"post": [0, -0.32875, -0.44], "lerp_mode": "catmullrom"}, "7.0333": {"post": [0, -0.02, 0.17], "lerp_mode": "catmullrom"}, "7.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": {"0.0": [89.52779, -393.457, -128.79941], "0.0667": [89.52779, -393.457, -128.79941], "0.1667": [106.58492, -389.48487, -160.83402], "2.7333": [106.58492, -389.48487, -160.83402], "2.9333": [99.00419, -397.258, -136.91417], "5.9667": [99.00419, -397.258, -136.91417], "6.2667": [89.52779, -393.457, -128.79941]}, "position": {"0.0": [7.925, -11.05, -2.5], "0.0667": [7.925, -11.05, -2.5], "0.1667": [8.82, -11.05, -4.6], "2.7333": [8.82, -11.05, -4.6], "2.9333": [7.895, -12.2, -0.275], "5.9667": [7.895, -12.2, -0.275], "6.1333": [7.91, -12.535, -1.51], "6.2667": [7.925, -11.05, -2.5]}, "scale": [1, 1.5, 1]}, "m95_bolt": {"position": {"4.3": [0, 0, 0], "4.3667": [0, 0, 4.15], "4.4333": [0, 0, 4.5], "4.5667": [0, 0, 4.3], "4.7667": [0, 0, 4.15], "5.1": [0, 0, 4.175], "5.2": [0, 0, 0]}}, "rotate": {"rotation": {"4.1667": [0, 0, 0], "4.2667": [0, 0, 50], "5.3": [0, 0, 50], "5.3667": [0, 0, 0]}}, "righthand": {"rotation": {"0.0": [101.2139, -323.71163, -174.99863], "0.1333": [99.91869, -335.51035, -177.54513], "3.3": [99.91869, -335.51035, -177.54513], "3.5667": [102.19451, -350.15083, -164.62137], "3.9333": [102.19451, -350.15083, -164.62137], "4.1333": [101.91854, -354.99682, -256.92111], "4.1667": [101.91854, -354.99682, -256.92111], "4.2667": [101.73685, -357.9796, -208.9652], "4.3": [101.73685, -357.9796, -208.9652], "5.3": [101.73685, -357.9796, -208.9652], "5.3667": [101.91854, -354.99682, -256.92111], "5.4333": [101.91854, -354.99682, -256.92111], "5.5667": [89.80601, -354.88523, -246.88253], "5.7333": [100.13633, -355.13055, -165.07513], "6.0667": [100.13633, -355.13055, -165.07513], "6.3": [101.2139, -323.71163, -174.99863]}, "position": {"0.0": [-6.4, -14.425, 2], "0.1333": [-6.4, -14.145, 2.55], "3.3": [-6.4, -14.145, 2.55], "3.9333": [-6.4, -14.145, 2.55], "4.0": [-7.95, -12.97, 5.71], "4.1": [-8.19, -13.08, 9.49], "4.1333": [-7, -11.79, 11.375], "4.1667": [-7, -11.79, 11.375], "4.2667": [-8.245, -9.915, 10.895], "4.3": [-8.35, -9.75, 11.025], "4.3667": [-8.35, -9.75, 15.175], "4.4333": [-8.35, -9.75, 15.525], "4.5667": [-8.35, -9.75, 15.325], "4.7667": [-8.35, -9.75, 15.175], "5.1": [-8.35, -9.75, 15.2], "5.2": [-8.35, -9.75, 11.025], "5.3": [-8.35, -9.75, 11.025], "5.3667": [-7, -11.79, 11.375], "5.4333": [-7, -11.79, 11.375], "5.5333": [-7.835, -13.21, 9.445], "5.6667": [-8.02, -15.14, 3.565], "5.7333": [-6.4, -14.145, 2.55], "6.0667": [-6.4, -14.145, 2.55], "6.3": [-6.4, -14.425, 2]}, "scale": [1, 1.5, 1]}, "l3": {"rotation": {"0.6": [0, 0, 0], "0.7333": [-3, 0, 0], "0.8667": [0, 0, 0], "1.0": [-2, 0, 0], "3.4333": [-2, 0, 0], "3.5667": [0, 0, 0], "3.7": [-2, 0, 0], "3.8333": [-0.75, 0, 0], "3.9333": [4.375, 0, 0], "4.1": [-1.025, 0, 0], "4.2333": [2.25, 0, 0], "4.3667": [1.25, 0, 0], "4.5667": [2.25, 0, 0], "4.7667": [0.25, 0, 0], "4.9333": [1.25, 0, 0], "5.1": [2.25, 0, 0], "5.2": [2.25, 0, 0], "5.3667": [-0.425, 0, 0], "5.5": [3.95, 0, 0], "5.6333": [0.6, 0, 0], "5.8": [1.75, 0, 0], "5.9333": [0, 0, 0], "6.1667": [0, 0, 0], "6.3": [-3, 0, 0], "6.4333": [-2, 0, 0], "6.5667": [0, 0, 0], "6.7333": [-1, 0, 0], "6.8667": [0, 0, 0]}}, "bullet": {"scale": 0}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0.55, 0, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-2.425, -0.25, -0.85], "lerp_mode": "catmullrom"}, "0.8": {"post": [-2.21, -0.31, -0.65], "lerp_mode": "catmullrom"}, "1.0": {"post": [-2.895, -0.38, -0.86], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-2.455, -0.25, -0.85], "lerp_mode": "catmullrom"}, "1.7333": {"post": [4.925, 3.15, -1.425], "lerp_mode": "catmullrom"}, "2.1": {"post": [6.075, 3.65, -1.425], "lerp_mode": "catmullrom"}, "2.7333": {"post": [6.075, 3.85, -1.35], "lerp_mode": "catmullrom"}, "3.1333": {"post": [1.525, 0, 0], "lerp_mode": "catmullrom"}, "3.4": {"post": [0.955, -1.9, 1.35], "lerp_mode": "catmullrom"}, "3.6333": {"post": [1.64, -1.97, 1.74], "lerp_mode": "catmullrom"}, "3.7333": {"post": [2.11, -1.9, 2.025], "lerp_mode": "catmullrom"}, "3.8667": {"post": [1.665, -1.83, 1.515], "lerp_mode": "catmullrom"}, "4.0667": {"post": [2.14, -1.975, 1.85], "lerp_mode": "catmullrom"}, "4.2333": {"post": [3.62, -2.97, 2.02], "lerp_mode": "catmullrom"}, "4.3333": {"post": [4.83, -3.62, 2.94], "lerp_mode": "catmullrom"}, "4.4333": {"post": [4.55, -3.96, 1.625], "lerp_mode": "catmullrom"}, "4.5667": {"post": [5.36, -3.99, 2.385], "lerp_mode": "catmullrom"}, "4.7333": {"post": [5.21, -4.01, 2.22], "lerp_mode": "catmullrom"}, "4.9333": {"post": [5.24, -4.05, 1.775], "lerp_mode": "catmullrom"}, "5.1667": {"post": [5.35, -4.09, 2.26], "lerp_mode": "catmullrom"}, "5.2667": {"post": [5.81, -4.11, 3.08], "lerp_mode": "catmullrom"}, "5.3667": {"post": [4.475, -4.13, 1.715], "lerp_mode": "catmullrom"}, "5.4667": {"post": [4.405, -4.01, 2.065], "lerp_mode": "catmullrom"}, "5.6": {"post": [2.415, -3.88, 1.75], "lerp_mode": "catmullrom"}, "5.7667": {"post": [1.46, -3.3, 1.675], "lerp_mode": "catmullrom"}, "6.1": {"post": [1.11, 0, 0], "lerp_mode": "catmullrom"}, "6.4": {"post": [-1.3, 0, -0.775], "lerp_mode": "catmullrom"}, "6.6333": {"post": [-1.3, 0, 0], "lerp_mode": "catmullrom"}, "6.7667": {"post": [-0.15, 0, 0.15], "lerp_mode": "catmullrom"}, "6.8667": {"post": [0.48, 0, 0.6], "lerp_mode": "catmullrom"}, "7.0": {"post": [-0.22, 0, -0.525], "lerp_mode": "catmullrom"}, "7.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0667": {"effect": "tacz:m95/m95_inspect_raise"}, "1.8333": {"effect": "tacz:m95/m95_inspect_rattle"}, "2.8": {"effect": "tacz:m95/m95_inspect_rotate"}, "3.2667": {"effect": "tacz:m95/m95_inspect_shoulder"}, "4.0333": {"effect": "tacz:m95/m95_boltback"}, "5.0333": {"effect": "tacz:m95/m95_inspect_boltclose"}, "5.4667": {"effect": "tacz:m95/m95_inspect_lower"}, "6.3": {"effect": "tacz:m95/m95_inspect_end"}}}, "inspect_empty": {"animation_length": 7.2, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-1.38509, -1.24434, 4.29497], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-1.39, -1.24, 4.29], "lerp_mode": "catmullrom"}, "0.4": {"post": [-3.91, -4.41, -2.02], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-10.80252, -12.68944, -19.80615], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-11.67934, -12.73253, -20.88586], "lerp_mode": "catmullrom"}, "1.0": {"post": [-10.42561, -13.0448, -21.82949], "lerp_mode": "catmullrom"}, "1.2": {"post": [-10.56428, -12.85979, -20.82188], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-10.39, -12.99, -21.57], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-10.9146, -13.35759, -23.13092], "lerp_mode": "catmullrom"}, "2.3": {"post": [-10.25634, -13.50926, -24.0715], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-10.31, -13.4275, -24.0075], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-10.01823, -12.01652, -22.39677], "lerp_mode": "catmullrom"}, "3.0": {"post": [-6.0733, -2.55567, -8.68449], "lerp_mode": "catmullrom"}, "3.1667": {"post": [-3.30816, -6.55161, -3.15758], "lerp_mode": "catmullrom"}, "3.3": {"post": [-11.99539, -6.2465, 14.78107], "lerp_mode": "catmullrom"}, "3.4": {"post": [-24.07317, -0.97391, 28.22222], "lerp_mode": "catmullrom"}, "3.6": {"post": [-32.28365, 5.59754, 44.62545], "lerp_mode": "catmullrom"}, "3.7": {"post": [-32.44664, 5.36815, 46.1821], "lerp_mode": "catmullrom"}, "3.7667": {"post": [-31.38661, 6.16105, 47.33802], "lerp_mode": "catmullrom"}, "3.9": {"post": [-31.60382, 7.93459, 52.29084], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-33.84674, 6.64885, 47.50441], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-31.80789, 9.10999, 51.06287], "lerp_mode": "catmullrom"}, "4.5": {"post": [-33.34556, 7.01589, 47.88054], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-32.04707, 7.50249, 48.73097], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-32.4996, 5.69075, 45.69734], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-32.66, 7.68, 48.17], "lerp_mode": "catmullrom"}, "5.2667": {"post": [-33.25196, 5.19352, 46.24503], "lerp_mode": "catmullrom"}, "5.4333": {"post": [-33.10702, 8.53837, 48.81576], "lerp_mode": "catmullrom"}, "5.5333": {"post": [-33.8104, 8.07258, 46.85565], "lerp_mode": "catmullrom"}, "5.6667": {"post": [-33.19256, 12.07899, 53.27034], "lerp_mode": "catmullrom"}, "5.8": {"post": [-32.7588, 8.96748, 49.78305], "lerp_mode": "catmullrom"}, "6.0": {"post": [-21.80021, -1.92924, 39.7835], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-11.32385, -8.47574, 19.6574], "lerp_mode": "catmullrom"}, "6.2333": {"post": [-3.97708, -7.46886, 2.34726], "lerp_mode": "catmullrom"}, "6.3": {"post": [-2.77467, -6.47288, 5.91111], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-1.20016, -5.6579, 3.74312], "lerp_mode": "catmullrom"}, "6.5": {"post": [1.76605, -3.2563, 4.9369], "lerp_mode": "catmullrom"}, "6.5667": {"post": [1.78811, -2.65564, 4.66491], "lerp_mode": "catmullrom"}, "6.6667": {"post": [0.8, -0.92, 3.05], "lerp_mode": "catmullrom"}, "6.7333": {"post": [-0.30298, 0.34027, -1.56498], "lerp_mode": "catmullrom"}, "6.8667": {"post": [0.2321, 0.0577, 2.25602], "lerp_mode": "catmullrom"}, "7.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.2, -0.31, 0], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.2, -0.81, 0], "lerp_mode": "catmullrom"}, "0.4": {"post": [-0.18, -0.4, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.14, 0.42, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 2.8, 0], "lerp_mode": "catmullrom"}, "0.7667": {"post": [0, 3.3, 0], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "1.2667": {"post": [0, 3.1, 0], "lerp_mode": "catmullrom"}, "1.8667": {"post": [0, 3.17, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [-0.1, 3.37, 0], "lerp_mode": "catmullrom"}, "2.6": {"post": [-0.14, 3.53, -0.02], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-0.1, 3.345, 0], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0.4, 2.38, 0.23], "lerp_mode": "catmullrom"}, "3.0": {"post": [1.875, -0.58, -0.855], "lerp_mode": "catmullrom"}, "3.1667": {"post": [3.25, -1.13, -1.83], "lerp_mode": "catmullrom"}, "3.4": {"post": [5.905, 3.385, -3.895], "lerp_mode": "catmullrom"}, "3.5": {"post": [7.58, 4.975, -5.215], "lerp_mode": "catmullrom"}, "3.6": {"post": [8.74, 4.71, -5.795], "lerp_mode": "catmullrom"}, "3.7": {"post": [8.8, 3.5, -6.125], "lerp_mode": "catmullrom"}, "3.8333": {"post": [8.8, 3.4, -6.4], "lerp_mode": "catmullrom"}, "3.9667": {"post": [8.925, 3.975, -6.65], "lerp_mode": "catmullrom"}, "4.1333": {"post": [8.775, 4.03, -6.145], "lerp_mode": "catmullrom"}, "4.2667": {"post": [8.78, 4.07, -6.345], "lerp_mode": "catmullrom"}, "4.4": {"post": [8.85625, 4.0125, -6.875], "lerp_mode": "catmullrom"}, "4.5333": {"post": [8.7175, 3.995, -6.56375], "lerp_mode": "catmullrom"}, "4.8333": {"post": [8.585, 3.88125, -6.685], "lerp_mode": "catmullrom"}, "5.0667": {"post": [8.6, 3.875, -6.65], "lerp_mode": "catmullrom"}, "5.3333": {"post": [8.8, 4.275, -6.65], "lerp_mode": "catmullrom"}, "5.4": {"post": [8.35, 3.77, -6.645], "lerp_mode": "catmullrom"}, "5.5333": {"post": [8.55, 3.52, -6.645], "lerp_mode": "catmullrom"}, "5.7333": {"post": [8.55, 3.55, -6.65], "lerp_mode": "catmullrom"}, "5.8667": {"post": [7.93, 2.62, -6.015], "lerp_mode": "catmullrom"}, "6.0333": {"post": [5.525, -1.04, -4.065], "lerp_mode": "catmullrom"}, "6.1667": {"post": [3.235, -5.1, -4.215], "lerp_mode": "catmullrom"}, "6.3333": {"post": [2.36, -4.48, -4.4], "lerp_mode": "catmullrom"}, "6.5": {"post": [1.305, -1.125, -4.24], "lerp_mode": "catmullrom"}, "6.6333": {"post": [0.79, 0.215, -2.5], "lerp_mode": "catmullrom"}, "6.8": {"post": [0.125, -0.575, 0.275], "lerp_mode": "catmullrom"}, "6.9": {"post": [0, -0.32875, -0.44], "lerp_mode": "catmullrom"}, "7.0333": {"post": [0, -0.02, 0.17], "lerp_mode": "catmullrom"}, "7.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": {"0.0": [89.52779, -393.457, -128.79941], "0.0667": [89.52779, -393.457, -128.79941], "0.1667": [106.58492, -389.48487, -160.83402], "2.7333": [106.58492, -389.48487, -160.83402], "2.9333": [99.00419, -397.258, -136.91417], "5.9667": [99.00419, -397.258, -136.91417], "6.2667": [89.52779, -393.457, -128.79941]}, "position": {"0.0": [7.925, -11.05, -2.5], "0.0667": [7.925, -11.05, -2.5], "0.1667": [8.82, -11.05, -4.6], "2.7333": [8.82, -11.05, -4.6], "2.9333": [7.895, -12.2, -0.275], "5.9667": [7.895, -12.2, -0.275], "6.1333": [7.91, -12.535, -1.51], "6.2667": [7.925, -11.05, -2.5]}, "scale": [1, 1.5, 1]}, "m95_bolt": {"position": {"4.3": [0, 0, 0], "4.3667": [0, 0, 4.15], "4.4333": [0, 0, 4.5], "4.5667": [0, 0, 4.3], "4.7667": [0, 0, 4.15], "5.1": [0, 0, 4.175], "5.2": [0, 0, 0]}}, "rotate": {"rotation": {"4.1667": [0, 0, 0], "4.2667": [0, 0, 50], "5.3": [0, 0, 50], "5.3667": [0, 0, 0]}}, "righthand": {"rotation": {"0.0": [101.2139, -323.71163, -174.99863], "0.1333": [99.91869, -335.51035, -177.54513], "3.3": [99.91869, -335.51035, -177.54513], "3.5667": [102.19451, -350.15083, -164.62137], "3.9333": [102.19451, -350.15083, -164.62137], "4.1333": [101.91854, -354.99682, -256.92111], "4.1667": [101.91854, -354.99682, -256.92111], "4.2667": [101.73685, -357.9796, -208.9652], "4.3": [101.73685, -357.9796, -208.9652], "5.3": [101.73685, -357.9796, -208.9652], "5.3667": [101.91854, -354.99682, -256.92111], "5.4333": [101.91854, -354.99682, -256.92111], "5.5667": [89.80601, -354.88523, -246.88253], "5.7333": [100.13633, -355.13055, -165.07513], "6.0667": [100.13633, -355.13055, -165.07513], "6.3": [101.2139, -323.71163, -174.99863]}, "position": {"0.0": [-6.4, -14.425, 2], "0.1333": [-6.4, -14.145, 2.55], "3.3": [-6.4, -14.145, 2.55], "3.9333": [-6.4, -14.145, 2.55], "4.0": [-7.95, -12.97, 5.71], "4.1": [-8.19, -13.08, 9.49], "4.1333": [-7, -11.79, 11.375], "4.1667": [-7, -11.79, 11.375], "4.2667": [-8.245, -9.915, 10.895], "4.3": [-8.35, -9.75, 11.025], "4.3667": [-8.35, -9.75, 15.175], "4.4333": [-8.35, -9.75, 15.525], "4.5667": [-8.35, -9.75, 15.325], "4.7667": [-8.35, -9.75, 15.175], "5.1": [-8.35, -9.75, 15.2], "5.2": [-8.35, -9.75, 11.025], "5.3": [-8.35, -9.75, 11.025], "5.3667": [-7, -11.79, 11.375], "5.4333": [-7, -11.79, 11.375], "5.5333": [-7.835, -13.21, 9.445], "5.6667": [-8.02, -15.14, 3.565], "5.7333": [-6.4, -14.145, 2.55], "6.0667": [-6.4, -14.145, 2.55], "6.3": [-6.4, -14.425, 2]}, "scale": [1, 1.5, 1]}, "l3": {"rotation": {"0.6": [0, 0, 0], "0.7333": [-3, 0, 0], "0.8667": [0, 0, 0], "1.0": [-2, 0, 0], "3.4333": [-2, 0, 0], "3.5667": [0, 0, 0], "3.7": [-2, 0, 0], "3.8333": [-0.75, 0, 0], "3.9333": [4.375, 0, 0], "4.1": [-1.025, 0, 0], "4.2333": [2.25, 0, 0], "4.3667": [1.25, 0, 0], "4.5667": [2.25, 0, 0], "4.7667": [0.25, 0, 0], "4.9333": [1.25, 0, 0], "5.1": [2.25, 0, 0], "5.2": [2.25, 0, 0], "5.3667": [-0.425, 0, 0], "5.5": [3.95, 0, 0], "5.6333": [0.6, 0, 0], "5.8": [1.75, 0, 0], "5.9333": [0, 0, 0], "6.1667": [0, 0, 0], "6.3": [-3, 0, 0], "6.4333": [-2, 0, 0], "6.5667": [0, 0, 0], "6.7333": [-1, 0, 0], "6.8667": [0, 0, 0]}}, "bullet": {"scale": 0}, "shell_ejection": {"position": {"4.3": [0, 0, 0], "4.3667": [0, 0, 4.15], "4.4333": [0, 0, 4.5], "4.5667": [0, 0, 4.3], "4.7667": [0, 0, 4.15], "5.1": [0, 0, 4.175], "5.2": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0.55, 0, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-2.425, -0.25, -0.85], "lerp_mode": "catmullrom"}, "0.8": {"post": [-2.21, -0.31, -0.65], "lerp_mode": "catmullrom"}, "1.0": {"post": [-2.895, -0.38, -0.86], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-2.455, -0.25, -0.85], "lerp_mode": "catmullrom"}, "1.7333": {"post": [4.925, 3.15, -1.425], "lerp_mode": "catmullrom"}, "2.1": {"post": [6.075, 3.65, -1.425], "lerp_mode": "catmullrom"}, "2.7333": {"post": [6.075, 3.85, -1.35], "lerp_mode": "catmullrom"}, "3.1333": {"post": [1.525, 0, 0], "lerp_mode": "catmullrom"}, "3.4": {"post": [0.955, -1.9, 1.35], "lerp_mode": "catmullrom"}, "3.6333": {"post": [1.64, -1.97, 1.74], "lerp_mode": "catmullrom"}, "3.7333": {"post": [2.11, -1.9, 2.025], "lerp_mode": "catmullrom"}, "3.8667": {"post": [1.665, -1.83, 1.515], "lerp_mode": "catmullrom"}, "4.0667": {"post": [2.14, -1.975, 1.85], "lerp_mode": "catmullrom"}, "4.2333": {"post": [3.62, -2.97, 2.02], "lerp_mode": "catmullrom"}, "4.3333": {"post": [4.83, -3.62, 2.94], "lerp_mode": "catmullrom"}, "4.4333": {"post": [4.55, -3.96, 1.625], "lerp_mode": "catmullrom"}, "4.5667": {"post": [5.36, -3.99, 2.385], "lerp_mode": "catmullrom"}, "4.7333": {"post": [5.21, -4.01, 2.22], "lerp_mode": "catmullrom"}, "4.9333": {"post": [5.24, -4.05, 1.775], "lerp_mode": "catmullrom"}, "5.1667": {"post": [5.35, -4.09, 2.26], "lerp_mode": "catmullrom"}, "5.2667": {"post": [5.81, -4.11, 3.08], "lerp_mode": "catmullrom"}, "5.3667": {"post": [4.475, -4.13, 1.715], "lerp_mode": "catmullrom"}, "5.4667": {"post": [4.405, -4.01, 2.065], "lerp_mode": "catmullrom"}, "5.6": {"post": [2.415, -3.88, 1.75], "lerp_mode": "catmullrom"}, "5.7667": {"post": [1.46, -3.3, 1.675], "lerp_mode": "catmullrom"}, "6.1": {"post": [1.11, 0, 0], "lerp_mode": "catmullrom"}, "6.4": {"post": [-1.3, 0, -0.775], "lerp_mode": "catmullrom"}, "6.6333": {"post": [-1.3, 0, 0], "lerp_mode": "catmullrom"}, "6.7667": {"post": [-0.15, 0, 0.15], "lerp_mode": "catmullrom"}, "6.8667": {"post": [0.48, 0, 0.6], "lerp_mode": "catmullrom"}, "7.0": {"post": [-0.22, 0, -0.525], "lerp_mode": "catmullrom"}, "7.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0667": {"effect": "tacz:m95/m95_inspect_raise"}, "1.8333": {"effect": "tacz:m95/m95_inspect_rattle"}, "2.8": {"effect": "tacz:m95/m95_inspect_rotate"}, "3.2667": {"effect": "tacz:m95/m95_inspect_shoulder"}, "4.0333": {"effect": "tacz:m95/m95_boltback"}, "5.0333": {"effect": "tacz:m95/m95_inspect_boltclose"}, "5.4667": {"effect": "tacz:m95/m95_inspect_lower"}, "6.3": {"effect": "tacz:m95/m95_inspect_end"}}}}}