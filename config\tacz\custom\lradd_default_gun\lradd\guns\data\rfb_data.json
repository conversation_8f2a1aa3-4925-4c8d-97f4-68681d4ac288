{
  
  "ammo": "tacz:762x39",

  "ammo_amount": 20,
  "extended_mag_ammo_amount": [
    25,
    30,
    40
  ],
  "bolt": "open_bolt",
  "rpm": 600,
  "bullet": {
    // 寿命，单位秒
    "life": 0.9,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 8.7,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.35,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 1.75,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        {"distance": 1.35, "damage": 9.4},
        {"distance": 10.7, "damage": 8.7},
        {"distance": 19, "damage": 8.2},
        {"distance": 34, "damage": 7.8},
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 7.1}
      ]
    },
    // 速度 m/s
    "speed": 250,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.01,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 1
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  "reload": {
    "type": "magazine",
    "feed": {
      "empty": 3,
      "tactical": 3
    },
    "cooldown": {
      "empty": 3.4583,
      "tactical": 3.2917
    }
  },
  "draw_time": 0.7083,
  "put_away_time": 0.5,
  "aim_time": 0.2,
  "sprint_time": 0.3,
  "fire_mode": [
    "auto",
    "semi"
  ],
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.85, 0.85]},
      {"time": 0.34, "value": [0.85, 0.85]},
      {"time": 0.56, "value": [-0.225, -0.225]},
      {"time": 0.82, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [-0.4, 0.55]},
      {"time": 0.38, "value": [0, 0]},
      {"time": 0.54, "value": [0, 0]}
    ]
  },
  "inaccuracy": {
    "stand": 1.6,
    "move": 1.85,
    "sneak": 1.2,
    "lie": 0.8,
    "aim": 0.175
  },
  "move_speed": {
    "base": 0.88,
    // 瞄准情况
    "aim": 0.75
  },
  // 开放的配件槽。未指定的槽位默认关闭。全部配件槽类型有:
  // scope, stock, muzzle, grip, laser, extended_mag
  "allow_attachment_types": [
    "scope",
    "muzzle",
    "laser",
    "extended_mag"
  ]
}