[{"localized": "fluid_type.aerlunerpg.acidfluid", "id": "aerlunerpg:acidfluid", "hasBlock": true, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "fluid_type.aerlunerpg.acidfluid_2", "id": "aerlunerpg:acidfluid_2", "hasBlock": true, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "fluid_type.aerlunerpg.acidfluid", "id": "aerlunerpg:flowing_acidfluid", "hasBlock": true, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "fluid_type.aerlunerpg.acidfluid_2", "id": "aerlunerpg:flowing_acidfluid_2", "hasBlock": true, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "巧克力", "id": "create:chocolate", "hasBlock": true, "hasBucket": true, "bucketItem": "create:chocolate_bucket"}, {"localized": "巧克力", "id": "create:flowing_chocolate", "hasBlock": true, "hasBucket": true, "bucketItem": "create:chocolate_bucket"}, {"localized": "蜂蜜", "id": "create:flowing_honey", "hasBlock": true, "hasBucket": true, "bucketItem": "create:honey_bucket"}, {"localized": "不可合成的药水", "id": "create:flowing_potion", "hasBlock": false, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "建筑工茶饮", "id": "create:flowing_tea", "hasBlock": false, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "蜂蜜", "id": "create:honey", "hasBlock": true, "hasBucket": true, "bucketItem": "create:honey_bucket"}, {"localized": "不可合成的药水", "id": "create:potion", "hasBlock": false, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "建筑工茶饮", "id": "create:tea", "hasBlock": false, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "乙醛", "id": "immersiveengineering:acetaldehyde", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:acetaldehyde_bucket"}, {"localized": "乙醛", "id": "immersiveengineering:acetaldehyde_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:acetaldehyde_bucket"}, {"localized": "生物柴油", "id": "immersiveengineering:biodiesel", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:biodiesel_bucket"}, {"localized": "生物柴油", "id": "immersiveengineering:biodiesel_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:biodiesel_bucket"}, {"localized": "液态混凝土", "id": "immersiveengineering:concrete", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:concrete_bucket"}, {"localized": "液态混凝土", "id": "immersiveengineering:concrete_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:concrete_bucket"}, {"localized": "杂酚油", "id": "immersiveengineering:creosote", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:creosote_bucket"}, {"localized": "杂酚油", "id": "immersiveengineering:creosote_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:creosote_bucket"}, {"localized": "乙醇", "id": "immersiveengineering:ethanol", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:ethanol_bucket"}, {"localized": "乙醇", "id": "immersiveengineering:ethanol_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:ethanol_bucket"}, {"localized": "除草剂", "id": "immersiveengineering:herbicide", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:herbicide_bucket"}, {"localized": "除草剂", "id": "immersiveengineering:herbicide_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:herbicide_bucket"}, {"localized": "酚醛树脂", "id": "immersiveengineering:phenolic_resin", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:phenolic_resin_bucket"}, {"localized": "酚醛树脂", "id": "immersiveengineering:phenolic_resin_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:phenolic_resin_bucket"}, {"localized": "植物油", "id": "immersiveengineering:plantoil", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:plantoil_bucket"}, {"localized": "植物油", "id": "immersiveengineering:plantoil_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:plantoil_bucket"}, {"localized": "药水", "id": "immersiveengineering:potion", "hasBlock": false, "hasBucket": true, "bucketItem": "immersiveengineering:potion_bucket"}, {"localized": "红石酸", "id": "immersiveengineering:redstone_acid", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:redstone_acid_bucket"}, {"localized": "红石酸", "id": "immersiveengineering:redstone_acid_flowing", "hasBlock": true, "hasBucket": true, "bucketItem": "immersiveengineering:redstone_acid_bucket"}, {"localized": "盐水", "id": "mekanism:brine", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:brine_bucket"}, {"localized": "液态氯", "id": "mekanism:chlorine", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:chlorine_bucket"}, {"localized": "液态乙烯", "id": "mekanism:ethene", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:ethene_bucket"}, {"localized": "盐水", "id": "mekanism:flowing_brine", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:brine_bucket"}, {"localized": "液态氯", "id": "mekanism:flowing_chlorine", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:chlorine_bucket"}, {"localized": "液态乙烯", "id": "mekanism:flowing_ethene", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:ethene_bucket"}, {"localized": "重水", "id": "mekanism:flowing_heavy_water", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:heavy_water_bucket"}, {"localized": "液态氢氟酸", "id": "mekanism:flowing_hydrofluoric_acid", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:hydrofluoric_acid_bucket"}, {"localized": "液态氢", "id": "mekanism:flowing_hydrogen", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:hydrogen_bucket"}, {"localized": "液态氯化氢", "id": "mekanism:flowing_hydrogen_chloride", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:hydrogen_chloride_bucket"}, {"localized": "液态锂", "id": "mekanism:flowing_lithium", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:lithium_bucket"}, {"localized": "营养糊剂", "id": "mekanism:flowing_nutritional_paste", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:nutritional_paste_bucket"}, {"localized": "液态氧", "id": "mekanism:flowing_oxygen", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:oxygen_bucket"}, {"localized": "液态钠", "id": "mekanism:flowing_sodium", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sodium_bucket"}, {"localized": "液态蒸汽", "id": "mekanism:flowing_steam", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:steam_bucket"}, {"localized": "液态二氧化硫", "id": "mekanism:flowing_sulfur_dioxide", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sulfur_dioxide_bucket"}, {"localized": "液态三氧化硫", "id": "mekanism:flowing_sulfur_trioxide", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sulfur_trioxide_bucket"}, {"localized": "硫酸", "id": "mekanism:flowing_sulfuric_acid", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sulfuric_acid_bucket"}, {"localized": "液态过热钠", "id": "mekanism:flowing_superheated_sodium", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:superheated_sodium_bucket"}, {"localized": "液态六氟化铀", "id": "mekanism:flowing_uranium_hexafluoride", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:uranium_hexafluoride_bucket"}, {"localized": "液态氧化铀", "id": "mekanism:flowing_uranium_oxide", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:uranium_oxide_bucket"}, {"localized": "重水", "id": "mekanism:heavy_water", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:heavy_water_bucket"}, {"localized": "液态氢氟酸", "id": "mekanism:hydrofluoric_acid", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:hydrofluoric_acid_bucket"}, {"localized": "液态氢", "id": "mekanism:hydrogen", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:hydrogen_bucket"}, {"localized": "液态氯化氢", "id": "mekanism:hydrogen_chloride", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:hydrogen_chloride_bucket"}, {"localized": "液态锂", "id": "mekanism:lithium", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:lithium_bucket"}, {"localized": "营养糊剂", "id": "mekanism:nutritional_paste", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:nutritional_paste_bucket"}, {"localized": "液态氧", "id": "mekanism:oxygen", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:oxygen_bucket"}, {"localized": "液态钠", "id": "mekanism:sodium", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sodium_bucket"}, {"localized": "液态蒸汽", "id": "mekanism:steam", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:steam_bucket"}, {"localized": "液态二氧化硫", "id": "mekanism:sulfur_dioxide", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sulfur_dioxide_bucket"}, {"localized": "液态三氧化硫", "id": "mekanism:sulfur_trioxide", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sulfur_trioxide_bucket"}, {"localized": "硫酸", "id": "mekanism:sulfuric_acid", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:sulfuric_acid_bucket"}, {"localized": "液态过热钠", "id": "mekanism:superheated_sodium", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:superheated_sodium_bucket"}, {"localized": "液态六氟化铀", "id": "mekanism:uranium_hexafluoride", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:uranium_hexafluoride_bucket"}, {"localized": "液态氧化铀", "id": "mekanism:uranium_oxide", "hasBlock": true, "hasBucket": true, "bucketItem": "mekanism:uranium_oxide_bucket"}, {"localized": "空气", "id": "minecraft:empty", "hasBlock": false, "hasBucket": false, "bucketItem": "minecraft:air"}, {"localized": "熔岩", "id": "minecraft:flowing_lava", "hasBlock": true, "hasBucket": true, "bucketItem": "minecraft:lava_bucket"}, {"localized": "牛奶", "id": "minecraft:flowing_milk", "hasBlock": false, "hasBucket": true, "bucketItem": "minecraft:milk_bucket"}, {"localized": "水", "id": "minecraft:flowing_water", "hasBlock": true, "hasBucket": true, "bucketItem": "minecraft:water_bucket"}, {"localized": "熔岩", "id": "minecraft:lava", "hasBlock": true, "hasBucket": true, "bucketItem": "minecraft:lava_bucket"}, {"localized": "牛奶", "id": "minecraft:milk", "hasBlock": false, "hasBucket": true, "bucketItem": "minecraft:milk_bucket"}, {"localized": "水", "id": "minecraft:water", "hasBlock": true, "hasBucket": true, "bucketItem": "minecraft:water_bucket"}, {"localized": "毒液", "id": "tconstruct:venom", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:venom_bucket"}, {"localized": "毒液", "id": "tconstruct:flowing_venom", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:venom_bucket"}, {"localized": "细雪", "id": "tconstruct:powdered_snow", "hasBlock": false, "hasBucket": true, "bucketItem": "minecraft:powder_snow_bucket"}, {"localized": "大地黏液", "id": "tconstruct:earth_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:earth_slime_bucket"}, {"localized": "大地黏液", "id": "tconstruct:flowing_earth_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:earth_slime_bucket"}, {"localized": "碧空黏液", "id": "tconstruct:sky_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:sky_slime_bucket"}, {"localized": "碧空黏液", "id": "tconstruct:flowing_sky_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:sky_slime_bucket"}, {"localized": "末影黏液", "id": "tconstruct:ender_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:ender_slime_bucket"}, {"localized": "末影黏液", "id": "tconstruct:flowing_ender_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:ender_slime_bucket"}, {"localized": "岩浆", "id": "tconstruct:magma", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:magma_bucket"}, {"localized": "岩浆", "id": "tconstruct:flowing_magma", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:magma_bucket"}, {"localized": "灵浆", "id": "tconstruct:ichor", "hasBlock": false, "hasBucket": true, "bucketItem": "tconstruct:ichor_bucket"}, {"localized": "蜂蜜", "id": "tconstruct:honey", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:honey_bucket"}, {"localized": "蜂蜜", "id": "tconstruct:flowing_honey", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:honey_bucket"}, {"localized": "甜菜汤", "id": "tconstruct:beetroot_soup", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:beetroot_soup_bucket"}, {"localized": "甜菜汤", "id": "tconstruct:flowing_beetroot_soup", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:beetroot_soup_bucket"}, {"localized": "蘑菇煲", "id": "tconstruct:mushroom_stew", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:mushroom_stew_bucket"}, {"localized": "蘑菇煲", "id": "tconstruct:flowing_mushroom_stew", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:mushroom_stew_bucket"}, {"localized": "兔肉煲", "id": "tconstruct:rabbit_stew", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:rabbit_stew_bucket"}, {"localized": "兔肉煲", "id": "tconstruct:flowing_rabbit_stew", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:rabbit_stew_bucket"}, {"localized": "肉汤", "id": "tconstruct:meat_soup", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:meat_soup_bucket"}, {"localized": "肉汤", "id": "tconstruct:flowing_meat_soup", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:meat_soup_bucket"}, {"localized": "不可合成的药水", "id": "tconstruct:potion", "hasBlock": false, "hasBucket": true, "bucketItem": "tconstruct:potion_bucket"}, {"localized": "焦黑熔石", "id": "tconstruct:seared_stone", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:seared_stone_bucket"}, {"localized": "焦黑熔石", "id": "tconstruct:flowing_seared_stone", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:seared_stone_bucket"}, {"localized": "焦褐熔石", "id": "tconstruct:scorched_stone", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:scorched_stone_bucket"}, {"localized": "焦褐熔石", "id": "tconstruct:flowing_scorched_stone", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:scorched_stone_bucket"}, {"localized": "熔融黏土", "id": "tconstruct:molten_clay", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_clay_bucket"}, {"localized": "熔融黏土", "id": "tconstruct:flowing_molten_clay", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_clay_bucket"}, {"localized": "熔融玻璃", "id": "tconstruct:molten_glass", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_glass_bucket"}, {"localized": "熔融玻璃", "id": "tconstruct:flowing_molten_glass", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_glass_bucket"}, {"localized": "液态灵魂", "id": "tconstruct:liquid_soul", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:liquid_soul_bucket"}, {"localized": "液态灵魂", "id": "tconstruct:flowing_liquid_soul", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:liquid_soul_bucket"}, {"localized": "熔融陶瓷", "id": "tconstruct:molten_porcelain", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_porcelain_bucket"}, {"localized": "熔融陶瓷", "id": "tconstruct:flowing_molten_porcelain", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_porcelain_bucket"}, {"localized": "熔融黑曜石", "id": "tconstruct:molten_obsidian", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_obsidian_bucket"}, {"localized": "熔融黑曜石", "id": "tconstruct:flowing_molten_obsidian", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_obsidian_bucket"}, {"localized": "熔融末影珍珠", "id": "tconstruct:molten_ender", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_ender_bucket"}, {"localized": "熔融末影珍珠", "id": "tconstruct:flowing_molten_ender", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_ender_bucket"}, {"localized": "烈焰血", "id": "tconstruct:blazing_blood", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:blazing_blood_bucket"}, {"localized": "烈焰血", "id": "tconstruct:flowing_blazing_blood", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:blazing_blood_bucket"}, {"localized": "熔融绿宝石", "id": "tconstruct:molten_emerald", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_emerald_bucket"}, {"localized": "熔融绿宝石", "id": "tconstruct:flowing_molten_emerald", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_emerald_bucket"}, {"localized": "熔融石英", "id": "tconstruct:molten_quartz", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_quartz_bucket"}, {"localized": "熔融石英", "id": "tconstruct:flowing_molten_quartz", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_quartz_bucket"}, {"localized": "熔融紫水晶", "id": "tconstruct:molten_amethyst", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_amethyst_bucket"}, {"localized": "熔融紫水晶", "id": "tconstruct:flowing_molten_amethyst", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_amethyst_bucket"}, {"localized": "熔融钻石", "id": "tconstruct:molten_diamond", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_diamond_bucket"}, {"localized": "熔融钻石", "id": "tconstruct:flowing_molten_diamond", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_diamond_bucket"}, {"localized": "熔融残骸", "id": "tconstruct:molten_debris", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_debris_bucket"}, {"localized": "熔融残骸", "id": "tconstruct:flowing_molten_debris", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_debris_bucket"}, {"localized": "熔融铁", "id": "tconstruct:molten_iron", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_iron_bucket"}, {"localized": "熔融铁", "id": "tconstruct:flowing_molten_iron", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_iron_bucket"}, {"localized": "熔融金", "id": "tconstruct:molten_gold", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_gold_bucket"}, {"localized": "熔融金", "id": "tconstruct:flowing_molten_gold", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_gold_bucket"}, {"localized": "熔融铜", "id": "tconstruct:molten_copper", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_copper_bucket"}, {"localized": "熔融铜", "id": "tconstruct:flowing_molten_copper", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_copper_bucket"}, {"localized": "熔融钴", "id": "tconstruct:molten_cobalt", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_cobalt_bucket"}, {"localized": "熔融钴", "id": "tconstruct:flowing_molten_cobalt", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_cobalt_bucket"}, {"localized": "熔融钢", "id": "tconstruct:molten_steel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_steel_bucket"}, {"localized": "熔融钢", "id": "tconstruct:flowing_molten_steel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_steel_bucket"}, {"localized": "熔融黏钢", "id": "tconstruct:molten_slimesteel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_slimesteel_bucket"}, {"localized": "熔融黏钢", "id": "tconstruct:flowing_molten_slimesteel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_slimesteel_bucket"}, {"localized": "熔融紫水晶青铜", "id": "tconstruct:molten_amethyst_bronze", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_amethyst_bronze_bucket"}, {"localized": "熔融紫水晶青铜", "id": "tconstruct:flowing_molten_amethyst_bronze", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_amethyst_bronze_bucket"}, {"localized": "熔融玫瑰金", "id": "tconstruct:molten_rose_gold", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_rose_gold_bucket"}, {"localized": "熔融玫瑰金", "id": "tconstruct:flowing_molten_rose_gold", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_rose_gold_bucket"}, {"localized": "熔融生铁", "id": "tconstruct:molten_pig_iron", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_pig_iron_bucket"}, {"localized": "熔融生铁", "id": "tconstruct:flowing_molten_pig_iron", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_pig_iron_bucket"}, {"localized": "熔融玛玉灵", "id": "tconstruct:molten_manyullyn", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_manyullyn_bucket"}, {"localized": "熔融玛玉灵", "id": "tconstruct:flowing_molten_manyullyn", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_manyullyn_bucket"}, {"localized": "熔融黑色科林斯青铜", "id": "tconstruct:molten_hepatizon", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_hepatizon_bucket"}, {"localized": "熔融黑色科林斯青铜", "id": "tconstruct:flowing_molten_hepatizon", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_hepatizon_bucket"}, {"localized": "熔融余烬黏液", "id": "tconstruct:molten_cinderslime", "hasBlock": false, "hasBucket": true, "bucketItem": "tconstruct:molten_cinderslime_bucket"}, {"localized": "熔融皇后史莱姆", "id": "tconstruct:molten_queens_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_queens_slime_bucket"}, {"localized": "熔融皇后史莱姆", "id": "tconstruct:flowing_molten_queens_slime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_queens_slime_bucket"}, {"localized": "熔融魂钢", "id": "tconstruct:molten_soulsteel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_soulsteel_bucket"}, {"localized": "熔融魂钢", "id": "tconstruct:flowing_molten_soulsteel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_soulsteel_bucket"}, {"localized": "熔融下界合金", "id": "tconstruct:molten_netherite", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_netherite_bucket"}, {"localized": "熔融下界合金", "id": "tconstruct:flowing_molten_netherite", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_netherite_bucket"}, {"localized": "熔融骑士史莱姆", "id": "tconstruct:molten_knightslime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_knightslime_bucket"}, {"localized": "熔融骑士史莱姆", "id": "tconstruct:flowing_molten_knightslime", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_knightslime_bucket"}, {"localized": "熔融锡", "id": "tconstruct:molten_tin", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_tin_bucket"}, {"localized": "熔融锡", "id": "tconstruct:flowing_molten_tin", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_tin_bucket"}, {"localized": "熔融铝", "id": "tconstruct:molten_aluminum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_aluminum_bucket"}, {"localized": "熔融铝", "id": "tconstruct:flowing_molten_aluminum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_aluminum_bucket"}, {"localized": "熔融铅", "id": "tconstruct:molten_lead", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_lead_bucket"}, {"localized": "熔融铅", "id": "tconstruct:flowing_molten_lead", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_lead_bucket"}, {"localized": "熔融银", "id": "tconstruct:molten_silver", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_silver_bucket"}, {"localized": "熔融银", "id": "tconstruct:flowing_molten_silver", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_silver_bucket"}, {"localized": "熔融镍", "id": "tconstruct:molten_nickel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_nickel_bucket"}, {"localized": "熔融镍", "id": "tconstruct:flowing_molten_nickel", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_nickel_bucket"}, {"localized": "熔融锌", "id": "tconstruct:molten_zinc", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_zinc_bucket"}, {"localized": "熔融锌", "id": "tconstruct:flowing_molten_zinc", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_zinc_bucket"}, {"localized": "熔融铂", "id": "tconstruct:molten_platinum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_platinum_bucket"}, {"localized": "熔融铂", "id": "tconstruct:flowing_molten_platinum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_platinum_bucket"}, {"localized": "熔融钨", "id": "tconstruct:molten_tungsten", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_tungsten_bucket"}, {"localized": "熔融钨", "id": "tconstruct:flowing_molten_tungsten", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_tungsten_bucket"}, {"localized": "熔融锇", "id": "tconstruct:molten_osmium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_osmium_bucket"}, {"localized": "熔融锇", "id": "tconstruct:flowing_molten_osmium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_osmium_bucket"}, {"localized": "熔融铀", "id": "tconstruct:molten_uranium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_uranium_bucket"}, {"localized": "熔融铀", "id": "tconstruct:flowing_molten_uranium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_uranium_bucket"}, {"localized": "熔融青铜", "id": "tconstruct:molten_bronze", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_bronze_bucket"}, {"localized": "熔融青铜", "id": "tconstruct:flowing_molten_bronze", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_bronze_bucket"}, {"localized": "熔融黄铜", "id": "tconstruct:molten_brass", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_brass_bucket"}, {"localized": "熔融黄铜", "id": "tconstruct:flowing_molten_brass", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_brass_bucket"}, {"localized": "熔融琥珀金", "id": "tconstruct:molten_electrum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_electrum_bucket"}, {"localized": "熔融琥珀金", "id": "tconstruct:flowing_molten_electrum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_electrum_bucket"}, {"localized": "熔融殷钢", "id": "tconstruct:molten_invar", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_invar_bucket"}, {"localized": "熔融殷钢", "id": "tconstruct:flowing_molten_invar", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_invar_bucket"}, {"localized": "熔融康铜", "id": "tconstruct:molten_constantan", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_constantan_bucket"}, {"localized": "熔融康铜", "id": "tconstruct:flowing_molten_constantan", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_constantan_bucket"}, {"localized": "熔融白镴", "id": "tconstruct:molten_pewter", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_pewter_bucket"}, {"localized": "熔融白镴", "id": "tconstruct:flowing_molten_pewter", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_pewter_bucket"}, {"localized": "熔融末影", "id": "tconstruct:molten_enderium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_enderium_bucket"}, {"localized": "熔融末影", "id": "tconstruct:flowing_molten_enderium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_enderium_bucket"}, {"localized": "熔融流明", "id": "tconstruct:molten_lumium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_lumium_bucket"}, {"localized": "熔融流明", "id": "tconstruct:flowing_molten_lumium", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_lumium_bucket"}, {"localized": "熔融信素", "id": "tconstruct:molten_signalum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_signalum_bucket"}, {"localized": "熔融信素", "id": "tconstruct:flowing_molten_signalum", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_signalum_bucket"}, {"localized": "熔融强化荧石", "id": "tconstruct:molten_refined_glowstone", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_refined_glowstone_bucket"}, {"localized": "熔融强化荧石", "id": "tconstruct:flowing_molten_refined_glowstone", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_refined_glowstone_bucket"}, {"localized": "熔融强化黑曜石", "id": "tconstruct:molten_refined_obsidian", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_refined_obsidian_bucket"}, {"localized": "熔融强化黑曜石", "id": "tconstruct:flowing_molten_refined_obsidian", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_refined_obsidian_bucket"}, {"localized": "熔融镍铬合金", "id": "tconstruct:molten_nicrosil", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_nicrosil_bucket"}, {"localized": "熔融镍铬合金", "id": "tconstruct:flowing_molten_nicrosil", "hasBlock": true, "hasBucket": true, "bucketItem": "tconstruct:molten_nicrosil_bucket"}]