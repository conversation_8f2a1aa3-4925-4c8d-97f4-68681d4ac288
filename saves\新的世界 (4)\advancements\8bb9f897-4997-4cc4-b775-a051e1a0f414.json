{"create:root": {"criteria": {"0": "2025-07-18 09:58:11 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-18 09:58:11 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-18 09:58:11 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-18 09:58:11 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:beach": "2025-07-20 15:46:48 +0800", "minecraft:forest": "2025-07-18 09:58:14 +0800", "minecraft:ocean": "2025-07-20 15:53:57 +0800"}, "done": false}, "tconstruct:tools/materials_and_you": {"criteria": {"crafted_book": "2025-07-20 11:41:25 +0800"}, "done": true}, "tconstruct:internal/starting_book": {"criteria": {"tick": "2025-07-20 11:41:25 +0800"}, "done": true}, "minecraft:recipes/brewing/cauldron": {"criteria": {"has_water_bucket": "2025-07-20 11:42:02 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/radiator": {"criteria": {"has_water_bucket": "2025-07-20 11:42:02 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/jerrycan": {"criteria": {"has_bucket": "2025-07-20 11:42:03 +0800"}, "done": true}, "tconstruct:smeltery/tinkers_anvil": {"criteria": {"crafted_overworld": "2025-07-20 15:40:26 +0800"}, "done": true}, "tconstruct:tools/make_part": {"criteria": {"crafted_part": "2025-07-20 15:40:36 +0800"}, "done": true}, "tconstruct:tools/tool_smith": {"criteria": {"sword": "2025-07-20 15:41:12 +0800", "hand_axe": "2025-07-20 15:52:15 +0800"}, "done": false}, "tconstruct:tools/tinker_tool": {"criteria": {"crafted_tool": "2025-07-20 15:41:12 +0800"}, "done": true}, "tconstruct:tools/material_master": {"criteria": {"manyullyn": "2025-07-20 15:41:12 +0800"}, "done": false}, "tconstruct:tools/netherite_tier": {"criteria": {"harvest_level": "2025-07-20 15:41:12 +0800"}, "done": true}, "minecraft:recipes/decorations/end_crystal": {"criteria": {"has_ender_eye": "2025-07-20 15:41:56 +0800"}, "done": true}, "tconstruct:recipes/decorations/common/glass/vanilla/end_crystal": {"criteria": {"has_ender_eye": "2025-07-20 15:41:56 +0800"}, "done": true}, "minecraft:recipes/decorations/ender_chest": {"criteria": {"has_ender_eye": "2025-07-20 15:41:56 +0800"}, "done": true}, "createoreexcavation:recipes/misc/vein_finder": {"criteria": {"diamond": "2025-07-20 15:41:56 +0800"}, "done": true}, "minecraft:recipes/decorations/soul_torch": {"criteria": {"has_soul_sand": "2025-07-20 15:42:03 +0800"}, "done": true}, "create:recipes/misc/smelting/scoria": {"criteria": {"has_item": "2025-07-20 15:42:03 +0800"}, "done": true}, "minecraft:recipes/decorations/soul_campfire": {"criteria": {"has_soul_sand": "2025-07-20 15:42:03 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_sword": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/decorations/jukebox": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_chestplate": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_pickaxe": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_hoe": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_shovel": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_helmet": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "tconstruct:recipes/decorations/gadgets/frame/diamond": {"criteria": {"has_item": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/building_blocks/diamond_block": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:story/mine_diamond": {"criteria": {"diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_leggings": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "createoreexcavation:recipes/misc/diamond_drill": {"criteria": {"diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_boots": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_axe": {"criteria": {"has_diamond": "2025-07-20 15:42:08 +0800"}, "done": true}, "create:recipes/misc/crafting/appliances/crafting_blueprint": {"criteria": {"has_item": "2025-07-20 15:42:15 +0800"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2025-07-20 15:42:15 +0800"}, "done": true}, "tconstruct:tools/modified": {"criteria": {"crafted_tool": "2025-07-20 15:46:34 +0800"}, "done": true}, "minecraft:adventure/kill_all_mobs": {"criteria": {"minecraft:skeleton": "2025-07-20 15:50:41 +0800", "minecraft:enderman": "2025-07-20 15:50:23 +0800", "minecraft:zombie": "2025-07-20 15:47:00 +0800"}, "done": false}, "minecraft:adventure/root": {"criteria": {"killed_something": "2025-07-20 15:47:00 +0800"}, "done": true}, "minecraft:adventure/kill_a_mob": {"criteria": {"minecraft:zombie": "2025-07-20 15:47:00 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-20 15:48:20 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-20 15:50:24 +0800"}, "done": true}, "minecraft:recipes/misc/bone_meal": {"criteria": {"has_bone": "2025-07-20 15:50:41 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-20 15:52:33 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_planks": {"criteria": {"has_logs": "2025-07-20 15:52:33 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_wood": {"criteria": {"has_log": "2025-07-20 15:52:33 +0800"}, "done": true}, "minecraft:recipes/building_blocks/birch_wood": {"criteria": {"has_log": "2025-07-20 15:52:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/birch_planks": {"criteria": {"has_logs": "2025-07-20 15:52:38 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_hanging_sign": {"criteria": {"has_stripped_logs": "2025-07-20 15:53:17 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stripped_oak_wood": {"criteria": {"has_log": "2025-07-20 15:53:17 +0800"}, "done": true}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-07-20 15:55:03 +0800"}, "done": true}, "minecraft:adventure/shoot_arrow": {"criteria": {"shot_arrow": "2025-07-20 15:56:56 +0800"}, "done": true}, "minecraft:husbandry/tame_an_animal": {"criteria": {"tamed_animal": "2025-07-20 15:59:25 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "tconstruct:recipes/misc/tables/pattern": {"criteria": {"has_item": "2025-07-29 15:13:16 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/torch": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crank": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-07-29 15:13:16 +0800"}, "done": true}, "DataVersion": 3465}