{
  "ammo": "tacz:556x45",
  "ammo_amount": 75,
  "extended_mag_ammo_amount": [
    100,
    150,
    200
  ],
  "bolt": "open_bolt",
  "rpm": 850,
  "bullet": {
    "life": 1.0,
    "bullet_amount": 1,
    "damage": 7.5,
    "tracer_count_interval": 0,
    "extra_damage": {
      "armor_ignore": 0.3,
      "head_shot_multiplier": 1.5,
      "damage_adjust": [
        {"distance": 1, "damage": 8.5},
        {"distance": 35, "damage": 7},
        {"distance": 80, "damage": 5.5},
        {"distance": "infinite", "damage": 4.5}
      ]
    },
    "speed": 260,
    "gravity": 0.098,
    "knockback": 0,
    "friction": 0.015,
    "ignite": false,
    "pierce": 1
  },
  "reload": {
    "type": "magazine",
    "feed": {
      "empty": 6.4,
      "tactical": 4.7
    },
    "cooldown": {
      "empty": 7.57,
      "tactical": 5.9
    }
  },
  "draw_time": 1,
  "put_away_time": 0.67,
  "aim_time": 0.3,
  "sprint_time": 0.2,
  "fire_mode": [
    "auto"
  ],
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.4, 0.4]},
      {"time": 0.3, "value": [0.4, 0.4]},
      {"time": 0.5, "value": [-0.15, -0.15]},
      {"time": 0.65, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [-0.1, 0.2]},
      {"time": 0.3, "value": [0.2, 0.3]},
      {"time": 0.5, "value": [0, 0]}
    ]
  },
  "inaccuracy": {
    "stand": 7,
    "move": 8.5,
    "sneak": 6,
    "lie": 5,
    "aim": 0.175
  },
  // 近战相关
  "melee": {
    // 枪械距离参数，用来延升近战攻击范围
    // 会与刺刀等配件的距离做加和
    "distance": 1,
    // 使用时的冷却时间
    "cooldown": 0.8,
    // 默认近战数据，会被配件的数据替换掉
    "default": {
      // 动画类型：没有枪托的为 melee_push，有枪托的为 melee_stock
      "animation_type": "melee_stock",
      // 刺刀距离参数，枪械里还有个刺刀攻击距离参数，两者做加和
      "distance": 0,
      // 刺刀范围角度
      "range_angle": 30,
      // 伤害值
      "damage": 2,
      // 击退效果
      "knockback": 0.75,
      // 前摇时长，单位：秒
      "prep": 0.1
    }
  },
  "allow_attachment_types": [
    "scope",
    "grip",
    "muzzle",
    "extended_mag"
  ],
  "exclusive_attachments": {
  }
}