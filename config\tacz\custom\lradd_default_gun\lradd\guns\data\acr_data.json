{
  // 指定枪械弹药
  "ammo": "tacz:556x45",
  // 弹容
  "ammo_amount": 30,
  // 扩容弹夹弹药总数
  "extended_mag_ammo_amount": [
    35,
    42,
    50
  ],
  // 枪栓类型：开膛待机（open_bolt）、闭膛待击（closed_bolt）、手动上膛（manual_action)
  "bolt": "closed_bolt",
  // 每分钟射出弹药量，最大不应超过 1200
  "rpm": 720,
  // 子弹实体属性
  "bullet": {
    // 寿命，单位秒
    "life": 0.6,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 6.5,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.3,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 1.75,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        {"distance": 1, "damage": 7.25},
        {"distance": 6, "damage": 6.5},
        {"distance": 12, "damage": 6.2},
        {"distance": 25, "damage": 5.9},
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 5.4}
      ]
    },
    // 速度 m/s
    "speed": 320,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.01,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 1
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  // 换弹相关
  "reload": {
    // magazine 是弹匣供弹，manual 是手动供弹
    "type": "magazine",
    // 弹匣供弹换弹时长（秒），到达此时间点，服务端就更新枪内子弹数量
    "feed": {
      // 空仓换弹
      "empty": 3,
      // 战术换弹
      "tactical": 2
    },
    // 弹匣供弹总时长（秒），到达此时间点，枪械才可以进行开火、检视等行为。
    "cooldown": {
      // 空仓换弹
      "empty": 3.5833,
      // 战术换弹
      "tactical": 2.5417
    }
  },
  // 枪械抬起的动作时长，单位秒。
  // 抬起动作完成后，枪械可以进行开火、检视等行为
  "draw_time": 0.9167,
  // 收枪的动作时长，单位秒。
  "put_away_time": 0.5417,
  // 瞄准时长，单位秒。
  "aim_time": 0.2,
  "sprint_time": 0.3,
  // 拉栓上膛的时间，仅当枪栓类型为 manual_action 时有效
  // "bolt_action_time": 0.9,
  // 开火模式
  "fire_mode": [
    // 全自动
    "auto",
    // 半自动
    "semi",
    "burst"
  ],
  // 多连续发数据，仅多连发会调用
  "burst_data": {
    // 是否连续射击
    "continuous_shoot": true,
    // 连发数
    "count": 3,
    // 组内连发的射速
    "bpm": 850,
    // 每组连发之间的时间间隔（上一组结束时间到下一组开始的时间间隔），单位秒
    "min_interval": 0.45
  },
  // 后坐力
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.45, 0.45]},
      {"time": 0.28, "value": [0.45, 0.45]},
      {"time": 0.37, "value": [0.15, 0.15]},
      {"time": 0.52, "value": [-0.13, -0.13]},
      {"time": 0.76, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [-0.325, -0.275]},
      {"time": 0.45, "value": [-0.125, 0]},
      {"time": 0.62, "value": [0, 0]}
    ]
  },
  // 不准确度
  "inaccuracy": {
    // 站立射击散布
    "stand": 1.45,
    // 移动射击散布
    "move": 2.3,
    // 潜行射击散布
    "sneak": 1.025,
    // 趴下射击散布
    "lie": 0.75,
    // 瞄准射击时散布
    "aim": 0.1
  },
  "move_speed": {
    "base": 0.9,
    "aim": 0.72
  },
  // 开放的配件槽。未指定的槽位默认关闭。全部配件槽类型有:
  // scope, stock, muzzle, grip, laser, extended_mag
  "allow_attachment_types": [
    "scope",
    "stock",
    "muzzle",
    "grip",
    "laser",
    "extended_mag"
  ],
  // 专属的配件属性
  "exclusive_attachments": {
    // 配件 ID，后面的数据和配件的 data 部分结构完全一致
    "tac:8x": {
      "weight": 2.0,
      "ads_addend": 0.04,
      "inaccuracy_addend": -0.4,
      "recoil_modifier": {
        "pitch": -0.2,
        "yaw": -0.1
      }
    }
  }
}