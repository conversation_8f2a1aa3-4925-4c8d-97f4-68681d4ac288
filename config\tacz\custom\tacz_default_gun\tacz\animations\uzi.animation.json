{"format_version": "1.8.0", "animations": {"static_idle": {"animation_length": 0.1, "bones": {"lefthand": {"rotation": [91.47237, -398.26212, -126.75784], "position": [7.75, -13, -3.5], "scale": [1, 1.5, 1]}, "righthand": {"rotation": [100.21875, 11.81504, -177.88617], "position": [-6.6, -14.2, 2.2], "scale": [1, 1.5, 1]}, "constraint": {"rotation": [0.05, 0.2, 0.1], "position": [0.15, 0.2, 0.35]}}}, "static_bolt_caught": {"animation_length": 0.1, "bones": {"lefthand": {"rotation": [91.47237, -398.26212, -126.75784], "position": [7.75, -13, -3.5], "scale": [1, 1.5, 1]}, "bullet": {"scale": 0}, "righthand": {"rotation": [100.21875, 11.81504, -177.88617], "position": [-6.6, -14.2, 2.2], "scale": [1, 1.5, 1]}, "bolt": {"position": [0, 0, -3]}}}, "draw": {"animation_length": 0.6, "bones": {"root": {"rotation": {"0.0": [0.81, -32.64, 33.95], "0.0333": [0.27, -29.86, 31.4], "0.0667": [-0.53, -25.86, 27.73], "0.1": [-1.28, -21.36, 23.7], "0.1333": [-1.65, -17.06, 20.1], "0.1667": [-1.47, -12.69, 17.07], "0.2": [-0.93, -8.04, 14.24], "0.2333": [-0.31, -3.86, 11.52], "0.2667": [0.1, -0.92, 8.88], "0.3": [0.23, 0.25, 6.05], "0.3333": [0.22, 0.18, 3.17], "0.3667": [0.16, -0.37, 0.67], "0.4": [0.11, -0.62, -0.98], "0.4333": [0.05, -0.14, -0.96], "0.4667": [-0.01, 0.27, 0.27], "0.5": [-0.02, 0.27, 0.3], "0.5333": [-0.01, 0.19, 0.21], "0.5667": [0, 0.07, 0.08], "0.6": [0, 0, 0]}, "position": {"0.0": [-3.18, -5.58, 24], "0.0333": [-2.73, -4.99, 20.05], "0.0667": [-2.08, -4.13, 14.3], "0.1": [-1.41, -3.22, 8.44], "0.1333": [-0.9, -2.45, 4.15], "0.1667": [-0.45, -1.32, 1.17], "0.2": [-0.25, -0.53, 0.54], "0.2333": [-0.14, -0.24, -0.06], "0.2667": [-0.07, -0.1, -0.38], "0.3": [-0.03, -0.04, -0.5], "0.3333": [-0.01, -0.05, -0.36], "0.3667": [-0.02, -0.14, -0.04], "0.4": [-0.03, -0.19, 0.17], "0.4333": [-0.02, -0.13, 0.02], "0.4667": [-0.01, -0.05, -0.18], "0.5": [-0.01, -0.02, -0.16], "0.5333": [0, -0.01, -0.07], "0.5667": [0, 0, 0]}}, "lefthand": {"rotation": [91.47237, -398.26212, -126.75784], "position": [7.75, -13, -3.5], "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0333": [125.83218, -9.9242, -160.40804], "0.1667": [100.21875, 11.81504, -177.88617], "0.3": [100.21875, 11.81504, -177.88617]}, "position": {"0.0333": [-14, -15.4, 3.5], "0.1667": [-7.9, -14.2, 2.4], "0.3": [-6.6, -14.2, 2.2]}, "scale": [1, 1.5, 1]}, "camera": {"rotation": {"0.0": [0, 0, 0], "0.0333": [0.01, 0.01, 0.02], "0.0667": [0.03, 0.03, 0.06], "0.1": [0.11, 0.06, 0.13], "0.1333": [0.43, 0.14, 0.35], "0.1667": [0.76, 0.22, 0.53], "0.2": [0.91, 0.23, 0.52], "0.2333": [1.01, 0.22, 0.45], "0.2667": [1.02, 0.19, 0.35], "0.3": [0.79, 0.09, 0.05], "0.3333": [0.48, 0, -0.17], "0.3667": [0.31, -0.02, -0.02], "0.4": [0.17, -0.01, 0.19], "0.4333": [-0.17, -0.01, 0.09], "0.4667": [-0.11, 0, 0.01], "0.5": [0, 0, -0.05], "0.5333": [0.01, 0, -0.05], "0.5667": [0.01, 0, -0.02], "0.6": [0, 0, 0]}}}, "sound_effects": {"0.0": {"effect": "wfoly_plr_sm_uzulu_raise_up"}}}, "put_away": {"animation_length": 0.43333, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0333": [-0.73, -0.37, 2.96], "0.0667": [-1.62, -0.81, 7.37], "0.1": [-3.72, -1.98, 11.25], "0.1333": [-8.1, -4.56, 12.61], "0.1667": [-16.37, -9.07, 10.1], "0.2": [-27.52, -15.04, 5.05], "0.2333": [-38.69, -21.66, -0.49], "0.2667": [-46.98, -28.1, -4.48], "0.3": [-51.39, -34.85, -6.32], "0.3333": [-53.55, -42.12, -7.11], "0.3667": [-54.49, -48.64, -7.37], "0.4": [-55.27, -53.19, -7.61], "0.4333": [-55.27, -53.19, -7.61]}, "position": {"0.0": [0, 0, 0], "0.0333": [0.17, -0.22, 0], "0.0667": [0.4, -0.52, 0], "0.1": [0.69, -0.93, 0], "0.1333": [1.05, -1.48, 0], "0.1667": [1.62, -2.08, 0], "0.2": [2.3, -2.9, 0], "0.2333": [3.05, -5, 0], "0.2667": [3.49, -7.03, 0], "0.3": [3.99, -9.74, 0], "0.3333": [4.51, -12.77, 0], "0.3667": [5.01, -15.76, 0], "0.4": [5.43, -18.32, 0], "0.4333": [5.73, -20.1, 0]}}, "lefthand": {"rotation": [91.47237, -398.26212, -126.75784], "position": [7.75, -13, -3.5], "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [100.21875, 11.81504, -177.88617], "0.1": [105.94152, 22.99469, -163.42299], "0.2": [71.66157, 23.27575, -132.82994]}, "position": {"0.0": [-6.6, -14.2, 2.2], "0.1": [-7.95823, -15.99396, 1.84695], "0.1333": [-8.89145, -16.58212, 3.16495], "0.2": [-12.4662, -13.51428, 7.08287], "0.3333": [-16.28427, -3.9932, 5.33164], "0.4333": [-16.28, -3.99, 5.33]}, "scale": [1, 1.5, 1]}, "camera": {"rotation": {"0.0": [0, 0, 0], "0.0333": [0.05, 0, -0.02], "0.0667": [0.12, 0, -0.04], "0.1": [0.18, 0, -0.07], "0.1333": [0.18, 0, -0.1], "0.1667": [0.07, 0, -0.14], "0.2": [-0.1, 0, -0.2], "0.2333": [-0.26, 0, -0.24], "0.2667": [-0.35, 0, -0.25], "0.3": [-0.32, 0, -0.21], "0.3333": [-0.21, 0, -0.13], "0.3667": [-0.08, 0, -0.05], "0.4": [0, 0, 0]}}}, "sound_effects": {"0.0": {"effect": "wfoly_plr_sm_uzulu_drop_quick_down"}}}, "reload_tactical": {"animation_length": 2.13333, "bones": {"9mm2": {"position": {"0.0": [0, 1, 0], "1.9": [0, 1, 0]}}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-6.0138, -4.32632, -1.32223], "lerp_mode": "catmullrom"}, "0.2": {"post": [-12.12464, -6.97892, -12.11251], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-15.76216, -11.2285, -22.20705], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-19.26364, -12.64972, -25.48627], "lerp_mode": "catmullrom"}, "0.5": {"post": [-20.14, -12.66, -25.87], "lerp_mode": "catmullrom"}, "0.6": {"post": [-19.40168, -11.75024, -18.39142], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-22.27673, -13.93474, -29.1944], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-24.41942, -11.65156, -23.96836], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-19.42993, -16.93624, -30.78823], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-18.49657, -19.78822, -32.86998], "lerp_mode": "catmullrom"}, "1.0": {"post": [-14.47847, -20.52419, -34.82557], "lerp_mode": "catmullrom"}, "1.1": [-14.96726, -19.85815, -33.26856], "1.1667": [-17.23187, -19.07921, -32.38322], "1.2": [-14.09241, -19.3756, -25.95664], "1.2333": [-12.50012, -19.21131, -30.14353], "1.3": {"pre": [-7.31013, -16.97711, -23.25199], "post": [-7.31013, -16.97711, -23.25199], "lerp_mode": "catmullrom"}, "1.4": {"post": [-3.18201, -9.46254, -25.94723], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-1.72689, -3.60464, -2.98213], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.63203, -0.3656, 2.89976], "lerp_mode": "catmullrom"}, "1.7": {"post": [-0.62874, -0.45928, -0.97511], "lerp_mode": "catmullrom"}, "1.8333": {"post": [0, 0, 0.05], "lerp_mode": "catmullrom"}, "1.9667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-0.32, -0.795, 0.1], "lerp_mode": "catmullrom"}, "0.2": [-1.315, -0.145, 0.3], "0.2667": {"pre": [-1.445, 0.95, 0.3], "post": [-1.445, 0.95, 0.3], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-1.365, 1.26, 0.35], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-1.24, 0.79, 0.38], "lerp_mode": "catmullrom"}, "0.6": {"post": [-1.17, 0.5, 0.4], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-1.865, 1.6, 0.35], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-2.02, 1.8, 0.4], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-1.9, 0.98, 0.61], "lerp_mode": "catmullrom"}, "1.0333": {"post": [-1.43, 1.335, 1.03], "lerp_mode": "catmullrom"}, "1.1": {"post": [-0.98, 1.195, 1.35], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-0.52, -0.215, 1.35], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-0.705, 0.16, 1.35], "lerp_mode": "catmullrom"}, "1.3": {"post": [-0.72, 0.265, 0.5], "lerp_mode": "catmullrom"}, "1.4": {"post": [-0.445, -0.3, 0.075], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-0.31, -0.9, -1.705], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.06, -0.65, -0.425], "lerp_mode": "catmullrom"}, "1.7": {"post": [-0.02, -0.315, 0.37], "lerp_mode": "catmullrom"}, "1.7667": {"post": [0, -0.05, 0.25], "lerp_mode": "catmullrom"}, "1.8333": {"post": [0, -0.11, -0.09], "lerp_mode": "catmullrom"}, "1.9333": {"post": [0, 0, 0.025], "lerp_mode": "catmullrom"}, "2.0333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"0.0": [0, 0, 0], "0.0333": [-0.08, -0.4, -2.51], "0.0667": [-0.15, -0.8, -5.03], "0.1": [-0.23, -1.2, -7.54], "0.1333": [-0.3, -1.6, -10.05], "0.1667": [-0.38, -2, -12.56], "0.2": [-0.46, -2.4, -15.08], "0.2333": [-0.53, -2.8, -17.59], "0.2667": [-0.61, -3.2, -20.1], "0.3": [-0.68, -3.6, -22.62], "0.3333": [-0.76, -4, -25.13], "0.3667": [-0.84, -4.4, -27.64], "0.4": [-0.91, -4.8, -30.16], "0.4333": [-0.99, -5.2, -32.67], "0.4667": [-1.06, -5.6, -35.18], "0.5": [-1.06, -5.6, -35.18], "0.5333": [-1.06, -5.6, -35.18], "0.5667": [-1.06, -5.6, -35.18], "0.6": [-1.06, -5.6, -35.18], "0.6333": [2.93, -7.12, -31.32], "0.6667": [6.92, -8.65, -27.47], "0.7": [10.92, -10.18, -23.61], "0.7333": [14.91, -11.71, -19.75], "0.7667": [13.85, -10.65, -16.48], "0.8": [11.18, -8.69, -12.76], "0.8333": [7.62, -6.24, -8.91], "0.8667": [3.87, -3.69, -5.28], "0.9": [0.63, -1.48, -2.2], "0.9333": [-1.37, 0, 0], "0.9667": [-1.14, 0.73, 1.23], "1.0": [1, 0, 0], "1.0333": [0.65, 0, 0], "1.0667": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0333": [0.36, -1.43, 1.52], "0.0667": [0.71, -2.86, 3.04], "0.1": [1.07, -4.29, 4.56], "0.1333": [1.43, -5.73, 6.08], "0.1667": [1.78, -7.16, 7.6], "0.2": [2.14, -8.59, 9.11], "0.2333": [2.5, -10.02, 10.63], "0.2667": [2.85, -11.45, 12.15], "0.3": [3.21, -12.88, 13.67], "0.3333": [2.78, -10.88, 9.65], "0.3667": [2.35, -8.88, 5.62], "0.4": [0.61, -6.26, 3.57], "0.4333": [-1.13, -3.63, 1.52], "0.4667": [-2.87, -1, -0.52], "0.5": [-2.87, -1, -0.52], "0.5333": [-2.87, -1, -0.52], "0.5667": [-2.87, -1, -0.52], "0.6": [-2.87, -1, -0.52], "0.6333": [-2.92, -2.22, -0.75], "0.6667": [-2.97, -3.45, -0.97], "0.7": [-3.02, -4.67, -1.19], "0.7333": [-3.07, -5.89, -1.4], "0.7667": [-2.57, -6.31, -1.11], "0.8": [-2.08, -6.72, -0.81], "0.8333": [-1.58, -7.12, -0.52], "0.8667": [-1.02, -6.78, -0.28], "0.9": [-0.45, -6.43, -0.05], "0.9333": [0.11, -6.08, 0.19], "0.9667": [0.05, -6.09, 0.09], "1.0": [0, -6.1, 0], "1.0333": [0, -6.06, 0], "1.0667": [0, -6.02, 0], "1.1": [0, -5.97, 0], "1.1333": [0, -2.99, 0], "1.1667": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [91.47237, -398.26212, -126.75784], "0.1333": [93.37169, -418.30627, -140.58333], "0.3333": [109.48767, -416.18218, -86.28955], "0.6": [88.50165, -416.2867, -79.09184], "0.7333": [66.00165, -416.2867, -79.09184], "0.8667": [88.5, -416.29, -79.09], "1.3": [88.50165, -416.2867, -79.09184], "1.4333": [114.26, -415.57, -164.59], "1.5333": [114.26, -415.57, -164.59], "1.6333": [105.98075, -404.16166, -134.24557], "1.7": [91.47237, -398.26212, -126.75784]}, "position": {"0.0": [7.75, -13, -3.5], "0.0667": [11.085, -12.51, -2.52], "0.2": [15.105, -15.78, -0.57], "0.3333": [8.775, -19.05, 1.375], "0.6": [9.26139, -19.51628, 1.49649], "0.7333": [9.26139, -19.51628, 1.49649], "0.8667": [9.26, -19.52, 1.5], "1.1": [8.68601, -19.30714, 1.65882], "1.3": [8.68601, -19.30714, 1.65882], "1.4333": [11.91, -26.01, 6.77], "1.5333": [11.91, -26.01, 6.77], "1.5667": [13.16, -18.805, 0.89], "1.6333": [9.1, -13.875, -4.7], "1.7": [7.75, -13, -3.5]}, "scale": [1, 1.5, 1]}, "mag_and_bullet": {"scale": {"0.0": [0, 0, 0], "0.3": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bullet": {"scale": {"0.0": [0, 0, 0], "0.3": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "righthand": {"rotation": [100.21875, 11.81504, -177.88617], "position": {"0.0": [-6.6, -14.2, 2.2], "0.1": [-8.05, -14.2, 2.2], "1.4333": [-8.05, -14.2, 2.2], "1.5333": [-6.6, -14.2, 2.2]}, "scale": [1, 1.5, 1]}, "constraint": {"rotation": {"1.7": [0.05, 0.05, 0.08], "1.8667": [0.05, 0.2, 0.1]}, "position": {"1.7": [0.06, 0.06, 0.06], "1.8667": [0.15, 0.2, 0.35]}}, "additional_magazine": {"rotation": {"0.6": [0, 0, 0], "0.6333": [0.19, 0.25, 0.02], "0.6667": [0.52, 0.67, 0.05], "0.7": [0.58, 0.75, 0.05], "0.7333": [0, 0, 0], "0.7667": [-0.99, -1.36, -0.15], "0.8": [-2.45, -3.41, -0.42], "0.8333": [-4.13, -5.75, -0.71], "0.8667": [-5.81, -8, -0.93], "0.9": [-7.26, -9.79, -0.98], "0.9333": [-8.25, -10.71, -0.76], "1.1": [-8.20196, -4.47988, 4.51548], "1.1667": [13.91882, -6.63254, 11.33053], "1.2333": [12.00046, -14.65229, 8.58624], "1.3": [12.24504, -15.44694, 9.9186], "1.6": [-95.60794, -26.88896, 2.54272]}, "position": {"0.3667": [0, 0, 0], "0.4": [0, 0, 0], "0.4333": [0, 0, 0], "0.4667": [0, 0, 0], "0.5": [0, 0, 0], "0.5333": [0, 0, 0], "0.5667": [0, 0, 0], "0.6": [0, 0, 0], "0.6333": [0, -1.53, 0], "0.6667": [0, -3.05, 0], "0.7": [0, -4.58, 0], "0.7333": [0, -6.1, 0], "0.7667": [0.55, -6.5, 0], "0.8": [1.09, -6.9, 0], "0.8333": [1.64, -7.3, 0], "0.8667": [2.19, -7.17, 0.13], "0.9": [2.73, -7.05, 0.27], "0.9333": [3.28, -6.92, 0.4], "1.1": [2.575, -6.925, 0.325], "1.1667": [2.35, -0.5, 0], "1.3": [2.35, -0.5, 0], "1.4333": [5.26609, -6.16381, 1.35757], "1.5667": [6.54181, -11.93369, 7.00118]}, "scale": {"0.0": [1, 1, 1], "1.5": {"pre": [1, 1, 1], "post": [0, 0, 0]}}}, "camera": {"rotation": {"0.0": [0, 0, 0], "0.0333": [-0.01, 0, -0.02], "0.0667": [-0.01, 0.01, -0.06], "0.1": [0.19, 0.04, -0.2], "0.1333": [0.58, 0.07, -0.43], "0.1667": [1.13, 0.12, -0.75], "0.2": [1.68, 0.17, -1.06], "0.2333": [2.09, 0.21, -1.27], "0.2667": [2.31, 0.23, -1.35], "0.3": [2.43, 0.24, -1.35], "0.3333": [2.47, 0.25, -1.31], "0.3667": [2.46, 0.25, -1.28], "0.4": [2.4, 0.23, -1.27], "0.4333": [2.3, 0.21, -1.24], "0.4667": [2.17, 0.19, -1.21], "0.5": [2.03, 0.16, -1.18], "0.5333": [1.92, 0.14, -1.17], "0.5667": [1.84, 0.12, -1.17], "0.6": [1.83, 0.1, -1.2], "0.6333": [2.31, 0.08, -1.6], "0.6667": [2.8, 0.08, -1.91], "0.7": [2.73, 0.05, -1.71], "0.7333": [2.49, 0.01, -1.34], "0.7667": [2.21, -0.02, -1.13], "0.8": [1.89, -0.07, -1.29], "0.8333": [1.52, -0.11, -1.61], "0.8667": [1.26, -0.15, -1.8], "0.9": [1.16, -0.17, -1.75], "0.9333": [1.12, -0.19, -1.61], "0.9667": [1.14, -0.2, -1.45], "1.0": [1.2, -0.2, -1.33], "1.0333": [1.28, -0.19, -1.29], "1.0667": [1.4, -0.18, -1.3], "1.1": [1.55, -0.16, -1.31], "1.1333": [1.72, -0.14, -1.28], "1.1667": [1.93, -0.12, -1.2], "1.2": [2.74, -0.1, -0.52], "1.2333": [3.33, -0.07, -0.07], "1.2667": [2.87, -0.04, -0.9], "1.3": [2.23, 0, -1.8], "1.3333": [2.15, 0.01, -1.76], "1.3667": [2.18, 0.01, -1.48], "1.4": [2.15, 0.02, -1.25], "1.4333": [2, 0.03, -1.21], "1.4667": [1.79, 0.05, -1.23], "1.5": [1.53, 0.05, -1.2], "1.5333": [0.96, 0.03, -0.99], "1.5667": [0.41, 0.01, -0.72], "1.6": [0.23, 0, -0.59], "1.6333": [0.09, 0, -0.45], "1.6667": [-0.02, 0, -0.31], "1.7": [-0.07, 0, -0.17], "1.7333": [0.01, 0.01, 0.18], "1.7667": [0.12, 0.03, 0.35], "1.8": [0.02, 0.03, 0.11], "1.8333": [-0.12, 0.03, -0.29], "1.8667": [-0.2, 0.03, -0.52], "1.9": [-0.16, 0.01, -0.47], "1.9333": [-0.06, 0, -0.3], "1.9667": [0.04, -0.02, -0.11], "2.0": [0.1, -0.02, 0.03], "2.0333": [0.1, -0.02, 0.06], "2.0667": [0.07, -0.02, 0.05], "2.1": [0.03, -0.01, 0.02], "2.1333": [0, 0, 0]}}}, "sound_effects": {"0.0333": {"effect": "wfoly_plr_sm_uzulu_raise_quick_up"}, "0.2333": {"effect": "wfoly_plr_sm_uzulu_reload_empty_fast_tilt"}, "0.5": {"effect": "wfoly_plr_sm_uzulu_reload_magout_01"}, "0.9333": {"effect": "wfoly_plr_sm_uzulu_reload_magin_01"}, "1.3333": {"effect": "wfoly_plr_sm_uzulu_reload_end"}}}, "reload_empty": {"animation_length": 3.06667, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0333": [-2.96, -3.93, -5.41], "0.0667": [-7.29, -9.66, -13.24], "0.1": [-11.5, -15.43, -21.52], "0.1333": [-14.08, -19.5, -28.29], "0.1667": [-14.15, -21.15, -33.16], "0.2": [-12.68, -21.4, -37.14], "0.2333": [-10.89, -21.08, -40.32], "0.2667": [-9.98, -20.99, -42.81], "0.3": [-11.04, -21.21, -43.63], "0.3333": [-12.97, -21.38, -43.16], "0.3667": [-13.6, -22.02, -45.73], "0.4": [-11.05, -23.79, -55.8], "0.4333": [-7.21, -26.04, -68.91], "0.4667": [-5.65, -27.28, -75.93], "0.5": [-8.77, -26.42, -70.92], "0.5333": [-14.18, -24.55, -59.83], "0.5667": [-18.22, -23.52, -51.38], "0.6": [-19.32, -23.9, -49.44], "0.6333": [-19.57, -24.86, -49.31], "0.6667": [-19.23, -26.09, -50.03], "0.7": [-18.56, -27.31, -50.63], "0.7333": [-17.94, -28, -50.9], "0.7667": [-17.03, -28.78, -51.37], "0.8": [-15.95, -29.61, -51.94], "0.8333": [-14.8, -30.43, -52.51], "0.8667": [-13.72, -31.2, -52.98], "0.9": [-12.83, -31.87, -53.24], "0.9333": [-12.24, -32.39, -53.19], "0.9667": [-11.95, -32.87, -52.47], "1.0": [-12.07, -33.13, -51.19], "1.0333": [-12.41, -33.26, -49.69], "1.0667": [-12.78, -33.33, -48.28], "1.1": [-12.99, -33.41, -47.31], "1.1333": [-13.05, -33.52, -46.43], "1.1667": [-12.85, -33.53, -46.71], "1.2": [-11.93, -33.9, -48.59], "1.2333": [-11.41, -33.8, -50.07], "1.2667": [-12.22, -32.75, -49.21], "1.3": [-13.53, -31.28, -47.57], "1.3333": [-14.43, -30.2, -46.34], "1.3667": [-14.23, -30.17, -46.42], "1.4": [-13.72, -30.17, -46.35], "1.4333": [-13.76, -28.4, -43.68], "1.4667": [-14.24, -26.78, -41.42], "1.5": [-15.33, -27.73, -43.82], "1.5333": [-16.99, -28.03, -44.78], "1.5667": [-19.83, -23.98, -37.11], "1.6": [-21.96, -19.52, -28.24], "1.6333": [-20.65, -18.31, -25.1], "1.6667": [-19, -17.45, -21.97], "1.7": [-21.13, -11.68, -5.48], "1.7333": [-22.58, -5.87, 4.91], "1.7667": [-24.45, 1.93, 18.25], "1.8": [-26.41, 9.84, 31.26], "1.8333": [-28.17, 15.98, 40.68], "1.8667": [-31.26, 22.32, 46.58], "1.9": [-33.49, 25.31, 46.79], "1.9333": [-33.87, 26.38, 47.54], "1.9667": [-33.57, 26.25, 47.38], "2.0": [-33.49, 26.26, 47.07], "2.0333": [-33.39, 26.13, 46.58], "2.0667": [-33.4, 26, 46.27], "2.1": [-33.78, 25.86, 46.51], "2.1333": [-34.21, 25.74, 46.86], "2.1667": [-34.88, 26.14, 47.44], "2.2": [-34.47, 25.74, 46.67], "2.2333": [-31.4, 22.68, 40.63], "2.2667": [-26.59, 19.11, 36.44], "2.3": [-19.33, 16.76, 43.59], "2.3333": [-12.88, 14.97, 51.02], "2.3667": [-11.64, 14.38, 50.6], "2.4": [-11.76, 14.02, 47.68], "2.4333": [-11.44, 12.77, 43.03], "2.4667": [-9.29, 7.98, 31.94], "2.5": [-6.06, 2.7, 19.48], "2.5333": [-4.06, 1.07, 15.13], "2.5667": [-1.53, -0.47, 10.78], "2.6": [1.02, -1.82, 6.7], "2.6333": [3.07, -2.84, 3.18], "2.6667": [4.1, -3.4, 0.48], "2.7": [2.79, -2.95, -1.93], "2.7333": [-0.39, -1.48, -2.55], "2.7667": [-2.62, -0.25, -2.33], "2.8": [-2.15, 0.46, 0.08], "2.8333": [-0.75, 0.69, 2.42], "2.8667": [-0.26, 0.41, 0.91], "2.9": [0, 0, -0.98], "2.9333": [0.05, -0.05, 0.73], "2.9667": [0.04, -0.04, 0.64], "3.0": [0.02, -0.02, 0.28], "3.0333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0333": [-0.16, 0.25, 0.01], "0.0667": [-0.39, 0.62, 0.02], "0.1": [-0.62, 1, 0.03], "0.1333": [-0.81, 1.28, 0.02], "0.1667": [-0.95, 1.45, -0.02], "0.2": [-1.06, 1.56, -0.08], "0.2333": [-1.12, 1.59, -0.14], "0.2667": [-1.11, 1.55, -0.2], "0.3": [-0.85, 1.24, -0.26], "0.3333": [-0.47, 0.78, -0.3], "0.3667": [-0.37, 0.53, -0.35], "0.4": [-0.79, 0.65, -0.41], "0.4333": [-1.48, 0.97, -0.46], "0.4667": [-2.12, 1.25, -0.5], "0.5": [-2.63, 1.42, -0.5], "0.5333": [-3.1, 1.55, -0.49], "0.5667": [-3.42, 1.61, -0.47], "0.6": [-3.55, 1.54, -0.46], "0.6333": [-3.55, 1.39, -0.45], "0.6667": [-3.48, 1.29, -0.44], "0.7": [-3.31, 1.26, -0.41], "0.7333": [-3.08, 1.27, -0.38], "0.7667": [-2.92, 1.27, -0.35], "0.8": [-2.91, 1.27, -0.35], "0.8333": [-2.96, 1.26, -0.35], "0.8667": [-3.02, 1.25, -0.36], "0.9": [-3.06, 1.22, -0.36], "0.9333": [-3.04, 1.18, -0.35], "0.9667": [-2.88, 0.99, -0.3], "1.0": [-2.63, 0.76, -0.23], "1.0333": [-2.32, 0.66, -0.14], "1.0667": [-1.9, 0.78, -0.03], "1.1": [-1.43, 1.02, 0.11], "1.1333": [-1.1, 1.25, 0.24], "1.1667": [-1.01, 1.42, 0.37], "1.2": [-1.07, 1.58, 0.49], "1.2333": [-1.12, 1.69, 0.6], "1.2667": [-1.12, 1.72, 0.68], "1.3": [-1.12, 1.72, 0.75], "1.3333": [-1.13, 1.71, 0.8], "1.3667": [-1.17, 1.69, 0.83], "1.4": [-1.34, 1.72, 0.78], "1.4333": [-1.56, 1.74, 0.68], "1.4667": [-1.54, 1.62, 0.6], "1.5": [-0.73, 0.95, 0.6], "1.5333": [0.08, 0.29, 0.6], "1.5667": [-0.02, 0.25, 0.64], "1.6": [-0.35, 0.34, 0.7], "1.6333": [-0.69, 0.41, 0.81], "1.6667": [-0.84, 0.29, 1], "1.7": [-0.6, -0.37, 1.5], "1.7333": [-0.14, -1.26, 2.13], "1.7667": [0.23, -1.85, 2.6], "1.8": [0.29, -1.7, 2.62], "1.8333": [0.34, -1.55, 2.64], "1.8667": [0.4, -1.4, 2.66], "1.9": [0.46, -1.25, 2.68], "1.9333": [0.42, -1.45, 2.98], "1.9667": [0.38, -1.65, 3.28], "2.0": [0.4, -1.65, 3.27], "2.0333": [0.43, -1.66, 3.26], "2.0667": [0.45, -1.66, 3.25], "2.1": [0.45, -1.68, 3.21], "2.1333": [0.44, -1.72, 3.17], "2.1667": [0.42, -1.73, 3.07], "2.2": [0.39, -1.67, 2.85], "2.2333": [0.28, -1.22, 1.83], "2.2667": [0.19, -0.84, 0.9], "2.3": [0.18, -0.95, 0.9], "2.3333": [0.19, -1.23, 1.14], "2.3667": [0.21, -1.41, 1.16], "2.4": [0.21, -1.34, 0.85], "2.4333": [0.22, -1.16, 0.39], "2.4667": [0.23, -1.06, -0.14], "2.5": [0.26, -1.2, -0.65], "2.5333": [0.34, -2.04, -1.38], "2.5667": [0.45, -3.21, -2.14], "2.6": [0.53, -4.03, -2.62], "2.6333": [0.53, -4.2, -2.62], "2.6667": [0.48, -3.6, -2.23], "2.7": [0.43, -2.58, -1.98], "2.7333": [0.35, -1.23, -1.65], "2.7667": [0.27, -0.23, -1.22], "2.8": [0.11, 0.1, -0.16], "2.8333": [-0.02, -0.02, 0.66], "2.8667": [-0.04, -0.08, 0.5], "2.9": [-0.02, -0.19, 0.03], "2.9333": [0, -0.25, -0.31], "2.9667": [0, -0.22, -0.31], "3.0": [0, -0.14, -0.21], "3.0333": [0, -0.06, -0.08], "3.0667": [0, 0, 0]}}, "mag_and_lefthand": {"rotation": {"0.3667": [0, 0, 0], "0.4667": [29.89144, -32.81886, -97.59565], "0.5667": [25.07607, -50.47556, -102.01126], "0.7": [-16.29666, -12.01626, -43.30025], "0.8": [-8.82285, 20.3426, -42.76308], "0.9667": [0, 0, -38], "1.0667": [3.99908, -0.08689, -5.54804], "1.2333": [4.51527, 0.49834, -9.40841], "1.3333": [5.08356, 0.38599, -3.042], "1.4333": [0, 0, 0], "1.5333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.3": [0, 0, 0], "0.3667": [0, -4.13, 0], "0.4667": [3.21045, -7.32749, 0.35318], "0.5333": [3.74266, -11.1802, 1.66198], "0.7": [0.82236, -9.88378, 10.17692], "0.7667": [1.02616, -13.2634, 10.08992], "0.8667": [1.09378, -13.49864, 9.94847], "0.9667": [3.11625, -11.44778, 5.9778], "1.0667": [2.31884, -6.72923, 1.06715], "1.1333": [0.9, -6, 0], "1.2333": [0.61, -6.3, 0], "1.3333": [0.47, -6.09, 0], "1.4333": [0, -5.8, 0], "1.5333": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [91.47, -398.26, -126.76], "0.0333": [87.1, -402.46, -127.77], "0.0667": [95.35, -400.66, -145.23], "0.1": [107.17, -397.25, -169.35], "0.1333": [114.91, -392.23, -188.83], "0.1667": [114.32, -384.12, -199.07], "0.2": [109.66, -374.4, -204.66], "0.2333": [105.99, -367.43, -208.11], "0.2667": [105.99, -367.43, -208.11], "0.3": [105.99, -367.43, -208.11], "0.3333": [105.99, -367.43, -208.11], "0.3667": [105.99, -367.43, -208.11], "0.4": [105.99, -367.43, -208.11], "0.4333": [105.99, -367.43, -208.11], "0.4667": [105.99, -367.43, -208.11], "0.5": [105.99, -367.43, -208.11], "0.5333": [105.99, -367.43, -208.11], "0.5667": [105.99, -367.43, -208.11], "0.6": [105.99, -367.43, -208.11], "0.6333": [105.99, -367.43, -208.11], "0.6667": [105.99, -367.43, -208.11], "0.7": [105.99, -367.43, -208.11], "0.7333": [105.99, -367.43, -208.11], "0.7667": [105.99, -367.43, -208.11], "0.8": [88.38, -394.03, -99.63], "0.8333": [88.38, -394.03, -99.63], "0.8667": [88.38, -394.03, -99.63], "0.9": [88.38, -394.03, -99.63], "0.9333": [88.38, -394.03, -99.63], "0.9667": [88.38, -394.03, -99.63], "1.0": [88.38, -394.03, -99.63], "1.0333": [88.38, -394.03, -99.63], "1.0667": [88.38, -394.03, -99.63], "1.1": [88.38, -394.03, -99.63], "1.1333": [88.38, -394.03, -99.63], "1.1667": [88.38, -394.03, -99.63], "1.2": [88.38, -394.03, -99.63], "1.2333": [88.38, -394.03, -99.63], "1.2667": [88.38, -394.03, -99.63], "1.3": [88.38, -394.03, -99.63], "1.3333": [88.38, -394.03, -99.63], "1.3667": [88.38, -394.03, -99.63], "1.4": [88.38, -394.03, -99.63], "1.4333": [88.38, -394.03, -99.63], "1.4667": [88.38, -394.03, -99.63], "1.5": [88.38, -394.03, -99.63], "1.5333": [88.38, -394.03, -99.63], "1.5667": [88.38, -394.03, -99.63], "1.6": [88.38, -394.03, -99.63], "1.6333": [88.38, -394.03, -99.63], "1.6667": [94.59, -381.04, -141.95], "1.7": [100.8, -368.07, -184.26], "1.7333": [107.01, -355.09, -226.58], "1.7667": [113.22, -342.11, -268.89], "1.8": [119.44, -329.13, -311.2], "1.8333": [125.64, -316.15, -353.51], "1.8667": [128.66, -318.63, -355.76], "1.9": [131.67, -321.12, -358], "1.9333": [143.44, -319.75, -354.5], "1.9667": [155.2, -318.38, -351], "2.0": [155.2, -318.38, -351], "2.0333": [155.2, -318.38, -351], "2.0667": [155.2, -318.38, -351], "2.1": [155.2, -318.38, -351], "2.1333": [155.2, -318.38, -351], "2.1667": [155.2, -318.38, -351], "2.2": [155.2, -318.38, -351], "2.2333": [158.18, -327.29, -339.96], "2.2667": [161.16, -336.2, -328.92], "2.3": [159.51, -338.13, -323.26], "2.3333": [157.86, -340.05, -317.59], "2.3667": [153.1, -355.48, -301.49], "2.4": [148.34, -370.9, -285.38], "2.4333": [143.58, -386.33, -269.28], "2.4667": [138.82, -401.75, -253.18], "2.5": [126.99, -400.88, -221.57], "2.5333": [115.15, -400.01, -189.97], "2.5667": [103.31, -399.13, -158.36], "2.6": [91.47, -398.26, -126.76]}, "position": {"0.0": [7.75, -13, -3.5], "0.0333": [8.73, -14.25, -1.91], "0.0667": [9.08, -15.29, -1.37], "0.1": [9.43, -16.32, -0.83], "0.1333": [9.12, -16.95, -0.28], "0.1667": [8.81, -17.58, 0.27], "0.2": [7.61, -17.84, 0.84], "0.2333": [6.4, -18.1, 1.4], "0.2667": [6.4, -18.1, 1.4], "0.3": [6.4, -18.1, 1.4], "0.3333": [6.4, -18.1, 1.4], "0.3667": [6.4, -18.1, 1.4], "0.4": [6.4, -18.1, 1.4], "0.4333": [6.4, -18.1, 1.4], "0.4667": [6.4, -18.1, 1.4], "0.5": [6.4, -18.1, 1.4], "0.5333": [6.4, -18.1, 1.4], "0.5667": [6.4, -18.1, 1.4], "0.6": [6.4, -18.1, 1.4], "0.6333": [6.4, -18.1, 1.4], "0.6667": [6.4, -18.1, 1.4], "0.7": [6.4, -18.1, 1.4], "0.7333": [6.4, -18.1, 1.4], "0.7667": [6.4, -18.1, 1.4], "0.8": [6.83, -19.07, 1.4], "0.8333": [6.83, -19.07, 1.4], "0.8667": [6.83, -19.07, 1.4], "0.9": [6.83, -19.07, 1.4], "0.9333": [6.83, -19.07, 1.4], "0.9667": [6.83, -19.07, 1.4], "1.0": [6.83, -19.07, 1.4], "1.0333": [6.83, -19.07, 1.4], "1.0667": [6.83, -19.07, 1.4], "1.1": [6.83, -19.07, 1.4], "1.1333": [6.83, -19.07, 1.4], "1.1667": [6.83, -19.07, 1.4], "1.2": [6.83, -19.07, 1.4], "1.2333": [6.83, -19.07, 1.4], "1.2667": [6.83, -19.07, 1.4], "1.3": [6.83, -19.07, 1.4], "1.3333": [6.83, -19.07, 1.4], "1.3667": [6.83, -19.07, 1.4], "1.4": [6.83, -19.07, 1.4], "1.4333": [6.83, -19.07, 1.4], "1.4667": [6.83, -19.07, 1.4], "1.5": [6.83, -19.07, 1.4], "1.5333": [6.83, -19.07, 1.4], "1.5667": [6.83, -19.07, 1.4], "1.6": [6.83, -19.07, 1.4], "1.6333": [6.83, -19.07, 1.4], "1.6667": [8.63, -17.9, 0.48], "1.7": [11.04, -16.19, -0.8], "1.7333": [12.23, -14.25, -1.87], "1.7667": [10.98, -11.85, -2.48], "1.8": [8.52, -9.23, -2.87], "1.8333": [6.68, -7.37, -3.12], "1.8667": [6.68, -7.37, -3.12], "1.9": [6.68, -7.37, -3.12], "1.9333": [6.68, -7.14, -1.56], "1.9667": [6.68, -6.9, 0], "2.0": [6.68, -6.9, 0], "2.0333": [6.68, -6.9, 0], "2.0667": [6.68, -6.9, 0], "2.1": [6.68, -6.9, 0], "2.1333": [6.68, -6.9, 0], "2.1667": [6.68, -6.9, 0], "2.2": [6.68, -6.9, 0], "2.2333": [8.15, -7.13, 2.48], "2.2667": [9.63, -7.36, 4.95], "2.3": [10.44, -8, 4.67], "2.3333": [11.25, -8.63, 4.39], "2.3667": [11.38, -9.95, 3.69], "2.4": [11.51, -11.27, 3], "2.4333": [11.64, -12.59, 2.31], "2.4667": [11.78, -13.9, 1.61], "2.5": [10.77, -13.68, 0.33], "2.5333": [9.76, -13.45, -0.94], "2.5667": [8.76, -13.23, -2.22], "2.6": [7.75, -13, -3.5]}, "scale": [1, 1.5, 1]}, "mag_and_bullet": {"scale": {"0.0": [0, 0, 0], "0.7": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bullet": {"scale": {"0.0": [0, 0, 0], "0.7667": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "righthand": {"rotation": [100.21875, 11.81504, -177.88617], "position": {"0.0333": [-6.6, -14.2, 2.2], "0.1333": [-8.075, -14.2, 2.2], "1.6667": [-8.075, -14.2, 2.2], "1.7333": [-6.6, -14.2, 2.2]}, "scale": [1, 1.5, 1]}, "constraint": {"rotation": {"0.7": [0.05, 0.05, 0.08], "1.7": [0.05, 0.05, 0.08], "1.9": [0.1, 0.9, 0.8], "2.4": [0.1, 0.9, 0.8], "2.6667": [0.05, 0.05, 0.1], "2.8667": [0.05, 0.2, 0.1]}, "position": {"0.7": [0.06, 0.06, 0.06], "1.7": [0.06, 0.06, 0.06], "1.9": [0.4, 0.6, 0.05], "2.4": [0.4, 0.6, 0.05], "2.6667": [0.15, 0.2, 0.2], "2.8667": [0.15, 0.2, 0.35]}}, "pull": {"position": {"1.9": [0, 0, 0], "1.9667": [0, 0, 3.4], "2.2": [0, 0, 3.4], "2.2667": [0, 0, 0]}}, "bolt": {"position": {"1.9": [0, 0, -2.8], "1.9667": [0, 0, 0]}}, "additional_magazine": {"rotation": {"0.3667": [0, 0, 0], "0.4": [2.5, 0.09, -6.98], "0.4333": [6, 0.6, -18.09], "0.4667": [9.03, 3.75, -37.08], "0.5": [11.28, 13.54, -65.52], "0.5333": [13.06, 25.96, -101.85], "0.5667": [13.81, 31.21, -145.19], "0.6": [13.55, 28.33, -172.06], "0.6333": [12.83, 22.25, -203.93], "0.6667": [11.86, 14.38, -237.56], "0.7": [10.82, 6.12, -269.68], "0.7333": [9.91, -1.1, -297.05], "0.7667": [9.31, -5.88, -316.41]}, "position": {"0.3": [0, 0, 0], "0.3333": [0, -2.06, 0], "0.3667": [0, -4.13, 0], "0.4": [0.5, -5.06, 0.13], "0.4333": [1.27, -5.88, 0.31], "0.4667": [2.51, -7.33, 0.35], "0.5": [4.55, -9.59, 0.04], "0.5333": [7.06, -12.48, -0.41], "0.5667": [9.19, -16.13, -0.46], "0.6": [10.86, -20.58, -0.03], "0.6333": [12.15, -25.79, 0.8], "0.6667": [12.47, -31.74, 2.32], "0.7": [11.07, -39.5, 5.17], "0.7333": [8.68, -48, 8.71], "0.7667": [6.92, -54.03, 11.26]}, "scale": {"0.0": [1, 1, 1], "0.7333": {"pre": [1, 1, 1], "post": [0, 0, 0]}}}, "camera": {"rotation": {"0.0": [0, 0, 0], "0.0333": [0.01, -0.05, -0.04], "0.0667": [0.04, -0.11, -0.11], "0.1": [0.08, -0.07, -0.22], "0.1333": [0.16, 0.12, -0.39], "0.1667": [0.25, 0.41, -0.6], "0.2": [0.33, 0.65, -0.8], "0.2333": [0.31, 0.78, -1.01], "0.2667": [0.27, 0.86, -1.21], "0.3": [0.4, 0.9, -1.3], "0.3333": [1.29, 0.93, -1.02], "0.3667": [2.02, 0.84, -0.84], "0.4": [1.78, 0.67, -1.22], "0.4333": [1.23, 0.43, -1.79], "0.4667": [0.88, 0.2, -2.15], "0.5": [0.94, -0.03, -2.06], "0.5333": [1.19, -0.26, -1.75], "0.5667": [1.42, -0.44, -1.53], "0.6": [1.53, -0.54, -1.53], "0.6333": [1.62, -0.62, -1.59], "0.6667": [1.7, -0.66, -1.67], "0.7": [1.77, -0.65, -1.72], "0.7333": [1.81, -0.6, -1.74], "0.7667": [1.84, -0.5, -1.76], "0.8": [1.86, -0.38, -1.76], "0.8333": [1.88, -0.27, -1.76], "0.8667": [1.91, -0.18, -1.75], "0.9": [1.94, -0.13, -1.73], "0.9333": [1.97, -0.09, -1.71], "0.9667": [2, -0.06, -1.68], "1.0": [2.04, -0.04, -1.64], "1.0333": [2.1, -0.02, -1.6], "1.0667": [2.22, -0.03, -1.51], "1.1": [2.37, -0.06, -1.4], "1.1333": [2.51, -0.08, -1.3], "1.1667": [2.6, -0.1, -1.27], "1.2": [2.51, -0.08, -1.48], "1.2333": [2.4, -0.05, -1.75], "1.2667": [2.48, -0.04, -1.87], "1.3": [2.6, -0.04, -1.95], "1.3333": [2.63, -0.02, -1.97], "1.3667": [2.31, 0.02, -1.84], "1.4": [1.94, 0.07, -1.6], "1.4333": [1.73, 0.12, -1.19], "1.4667": [1.72, 0.18, -0.95], "1.5": [2.24, 0.25, -1.42], "1.5333": [2.68, 0.33, -1.92], "1.5667": [2.45, 0.36, -1.89], "1.6": [2.01, 0.38, -1.7], "1.6333": [1.72, 0.38, -1.54], "1.6667": [1.75, 0.33, -1.46], "1.7": [1.94, 0.25, -1.4], "1.7333": [2.12, 0.16, -1.38], "1.7667": [2.22, 0.04, -1.4], "1.8": [2.31, -0.1, -1.45], "1.8333": [2.35, -0.2, -1.5], "1.8667": [2.21, -0.27, -1.55], "1.9": [2.18, -0.29, -1.58], "1.9333": [2.81, -0.33, -1.62], "1.9667": [3.33, -0.35, -1.6], "2.0": [2.82, -0.37, -1.33], "2.0333": [2.21, -0.38, -1.15], "2.0667": [2.25, -0.39, -1.49], "2.1": [2.4, -0.37, -1.85], "2.1333": [2.3, -0.34, -1.85], "2.1667": [2.16, -0.29, -1.75], "2.2": [2.15, -0.25, -1.6], "2.2333": [2.72, -0.19, -1.24], "2.2667": [3.17, -0.14, -0.95], "2.3": [2.89, -0.12, -1.04], "2.3333": [2.37, -0.1, -1.23], "2.3667": [2, -0.1, -1.3], "2.4": [1.97, -0.11, -1.17], "2.4333": [2.07, -0.14, -0.93], "2.4667": [2.17, -0.16, -0.68], "2.5": [2.13, -0.18, -0.48], "2.5333": [1.81, -0.2, -0.33], "2.5667": [1.33, -0.21, -0.23], "2.6": [0.9, -0.2, -0.17], "2.6333": [0.65, -0.16, -0.18], "2.6667": [0.42, -0.1, -0.22], "2.7": [0.24, -0.04, -0.25], "2.7333": [0.13, 0, -0.22], "2.7667": [0.24, 0.02, 0.13], "2.8": [0.38, 0.01, 0.41], "2.8333": [0.12, 0.01, 0.12], "2.8667": [-0.15, 0, -0.22], "2.9": [-0.16, 0, -0.22], "2.9333": [-0.11, 0, -0.15], "2.9667": [-0.04, 0, -0.06], "3.0": [0, 0, 0]}}}, "sound_effects": {"0.0": {"effect": "wfoly_plr_sm_uzulu_raise_up"}, "0.2333": {"effect": "wfoly_plr_sm_uzulu_reload_empty_magout_01"}, "0.9333": {"effect": "wfoly_plr_sm_uzulu_reload_empty_rattle"}, "0.9667": {"effect": "iw8_phys_mag_drop_smg_metal_dirt_04"}, "1.3": {"effect": "wfoly_plr_sm_uzulu_reload_empty_tilt"}, "1.8333": {"effect": "wfoly_plr_sm_uzulu_reload_empty_fast_charge_01"}, "2.3333": {"effect": "wfoly_plr_sm_uzulu_reload_empty_end"}}}, "inspect": {"animation_length": 5.1, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-11.4, -39.17, -45.465], "lerp_mode": "catmullrom"}, "0.4": {"post": [-12.09, -47.67, -53.42], "lerp_mode": "catmullrom"}, "0.6": {"post": [-10.94, -48.79, -55.465], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-8.36907, -49.45083, -58.20011], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-8.975, -48.64, -58.35], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-16.26359, -34.63452, -39.31901], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-15.32021, -10.31204, -36.61094], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-15.32021, -10.31204, -36.61094], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-13.38322, -12.02676, -31.60918], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-14.66959, -13.28761, -38.49377], "lerp_mode": "catmullrom"}, "2.2": {"post": [-13.61949, -11.00059, -35.08987], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-14.06914, -12.93791, -37.68337], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-18.25, -33.61, -42.14], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-19.31498, -37.24029, -41.82042], "lerp_mode": "catmullrom"}, "2.8333": {"post": [-16.26, -37.08, -45.52], "lerp_mode": "catmullrom"}, "2.8667": {"post": [-18.21179, -35.32332, -40.4917], "lerp_mode": "catmullrom"}, "2.9": {"post": [-23.66338, -33.51494, -36.367], "lerp_mode": "catmullrom"}, "3.0": {"post": [-17.3516, -34.24712, -46.35448], "lerp_mode": "catmullrom"}, "3.1": {"post": [-18.69, -30.92, -37.02], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-18.27603, -30.1356, -34.42539], "lerp_mode": "catmullrom"}, "3.3667": {"post": [-21.18, -29.07, -30.26], "lerp_mode": "catmullrom"}, "3.5333": {"post": [-24.33938, -28.494, -28.39956], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-20.2, -29.55, -30.96], "lerp_mode": "catmullrom"}, "4.0333": {"post": [-21.64066, -30.62855, -33.43114], "lerp_mode": "catmullrom"}, "4.2333": {"post": [-20.10535, -28.88692, -32.59058], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-14.08, -23.34, -22.49], "lerp_mode": "catmullrom"}, "4.5667": {"post": [2.51996, -7.02572, -0.01833], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0.25183, -0.82226, -1.85471], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-0.00274, -0.34214, 0.80421], "lerp_mode": "catmullrom"}, "4.8333": {"post": [0, 0.08125, -0.775], "lerp_mode": "catmullrom"}, "4.9667": {"post": [0, 0, 0.25], "lerp_mode": "catmullrom"}, "5.1": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.345, -0.28, -0.12], "lerp_mode": "catmullrom"}, "0.3": {"post": [1.6, 3.75, 0.6], "lerp_mode": "catmullrom"}, "0.4333": {"post": [2.325, 4.25, 1.18], "lerp_mode": "catmullrom"}, "0.6": {"post": [2.545, 3.825, 1.53], "lerp_mode": "catmullrom"}, "0.9": {"post": [2.54, 3.695, 1.68], "lerp_mode": "catmullrom"}, "1.0333": {"post": [2.52, 3.925, 1.74], "lerp_mode": "catmullrom"}, "1.1333": {"post": [2.395, 3.6, 1.68], "lerp_mode": "catmullrom"}, "1.2667": {"post": [1.435, 1.145, 0.955], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-0.03, 0.85, 1.23], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-0.27, 0.98, 1.105], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-0.345, 0.48, 1.11], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-1.42, 1.93, 1.435], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-1.795, 1.76, 1.44], "lerp_mode": "catmullrom"}, "2.0": {"post": [-1.62, 0.9, 1.44], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-1.47, 0.405, 1.44], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-1.475, 0.55, 1.44], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-1.295, 1.075, 0.885], "lerp_mode": "catmullrom"}, "2.7": {"post": [-0.42, 2.775, 1.14], "lerp_mode": "catmullrom"}, "2.8333": {"post": [0.105, 3.03, 1.315], "lerp_mode": "catmullrom"}, "2.9": {"post": [1.105, 1.355, 1.32], "lerp_mode": "catmullrom"}, "3.0333": {"post": [0.805, 2.355, 1.32], "lerp_mode": "catmullrom"}, "3.1667": {"post": [0.135, 2.055, 0.995], "lerp_mode": "catmullrom"}, "3.3": {"post": [0.14, 0.36, -0.425], "lerp_mode": "catmullrom"}, "3.4333": {"post": [0.415, 3.785, 0.82], "lerp_mode": "catmullrom"}, "3.5333": {"post": [0.49, 5.51, 1.605], "lerp_mode": "catmullrom"}, "3.6333": {"post": [0.565, 5.485, 1.695], "lerp_mode": "catmullrom"}, "3.8": {"post": [0.52, 5.075, 1.7], "lerp_mode": "catmullrom"}, "4.1333": {"post": [0.47, 5.08, 1.3], "lerp_mode": "catmullrom"}, "4.2333": {"post": [0.47, 4.82, 1.025], "lerp_mode": "catmullrom"}, "4.3333": {"post": [0.53, 2.93, -0.6], "lerp_mode": "catmullrom"}, "4.4667": {"post": [0.6, -2.65, -3.9], "lerp_mode": "catmullrom"}, "4.5333": {"post": [0.51, -2.965, -2.34], "lerp_mode": "catmullrom"}, "4.6": {"post": [0.31, -0.86, -0.41], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0, -0.05, 0.6], "lerp_mode": "catmullrom"}, "4.7667": {"post": [0, -0.125, -0.15], "lerp_mode": "catmullrom"}, "4.8667": {"post": [0, 0, 0.15], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"1.6667": [0, 0, 0], "1.7333": [0, 0, 0], "1.8": [0.31838, -3.05966, -13.14528], "1.9333": [-22.93073, -9.02445, 14.64036], "2.5": [-22.93073, -9.02445, 14.64036], "2.5667": [-12.36449, -8.13627, -2.98672], "2.6667": [0, 0, 0], "2.8": [0, 0, 0], "2.8667": [0, 0, 0]}, "position": {"1.6667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.7333": {"post": [0, -5.9, 0], "lerp_mode": "catmullrom"}, "1.8": {"post": [1.86, -7.34, -0.07], "lerp_mode": "catmullrom"}, "1.9333": {"post": [5.325, -3.575, -0.225], "lerp_mode": "catmullrom"}, "2.0667": {"post": [6.45, -2.345, -0.24], "lerp_mode": "catmullrom"}, "2.3": {"post": [6.39, -2.2, -0.25], "lerp_mode": "catmullrom"}, "2.4667": {"post": [5.575, -2.775, -0.225], "lerp_mode": "catmullrom"}, "2.5667": {"post": [3.42, -5.57, -0.14], "lerp_mode": "catmullrom"}, "2.6667": [0, -5.9, 0], "2.8": [0, -5.9, 0], "2.8667": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [91.47, -398.26, -126.76], "0.3": [135.4661, -414.89607, -145.74556], "1.2333": [91.47237, -398.26212, -126.75784], "1.3667": [67.94594, -402.21263, -86.55696], "1.4667": [100.88876, -412.25625, -127.33969], "1.6": [100.88876, -412.25625, -127.33969], "2.6667": [100.89, -412.26, -127.34], "2.8667": [96.58116, -360.91757, -108.72856], "3.0667": [96.58116, -360.91757, -108.72856], "3.3333": [59.69728, -406.36774, -57.74125], "3.4333": [59.69728, -406.36774, -57.74125], "4.2667": [59.69728, -406.36774, -57.74125], "4.3667": [91.47237, -398.26212, -126.75784]}, "position": {"0.0": [7.75, -13, -3.5], "0.1333": [8.95872, -16.59584, 0.06092], "0.2": [7.77341, -22.66102, 3.20472], "0.3": [0.51156, -27.46069, 10.36215], "1.2333": [1.20272, -29.15603, 9.84726], "1.3667": [6.22787, -24.10779, 4.45804], "1.4667": [7.425, -18.7, 1.9], "1.6": [7.425, -18.7, 1.9], "2.6667": [7.43, -18.7, 1.9], "2.8667": [7.43, -19.55, 1.9], "3.0667": [7.43, -19.55, 1.9], "3.2": [8.30824, -19.45149, -0.07061], "3.3333": [9.90353, -15.15733, -2.18701], "3.4333": [9.59858, -16.27056, -1.45297], "4.2667": [9.59858, -16.27056, -1.45297], "4.3667": [8.8821, -15.1326, -2.56438], "4.4667": [7.75, -13, -3.5]}, "scale": [1, 1.5, 1]}, "mag_and_bullet": {"scale": {"0.0": [1, 1, 1], "3.0": {"pre": [1, 1, 1], "post": [0, 0, 0]}, "4.7": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "gun_and_righthand": {"rotation": {"1.7": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.7667": {"post": [1.02678, 0.00359, -3.14566], "lerp_mode": "catmullrom"}, "1.8667": {"post": [2.27514, 0.29255, -8.26714], "lerp_mode": "catmullrom"}, "2.1": {"post": [1.19454, 0.09593, -4.58574], "lerp_mode": "catmullrom"}, "2.4333": {"post": [1.19454, 0.09593, -4.58574], "lerp_mode": "catmullrom"}, "2.6667": [0, 0, 0], "3.2": [0, 0, 0], "3.3333": {"pre": [-12.98684, -1.05276, 70.22997], "post": [-12.98684, -1.05276, 70.22997], "lerp_mode": "catmullrom"}, "3.4": {"post": [-26.82445, 6.74406, 100.13205], "lerp_mode": "catmullrom"}, "3.4667": [-32.513, 21.027, 113.94539], "4.2667": [-32.513, 21.027, 113.94539], "4.4333": {"pre": [1.95012, 5.05678, 26.81178], "post": [1.95012, 5.05678, 26.81178], "lerp_mode": "catmullrom"}, "4.5667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "righthand": {"rotation": [100.21875, 11.81504, -177.88617], "position": {"0.0": [-6.6, -14.2, 2.2], "0.2": [-8.2, -14.2, 2.2], "0.9333": [-8.2, -14.2, 2.2], "1.0": [-8.2, -14.2, 2.2], "1.1": [-8.325, -14.2, 2.55], "1.2": [-8.2, -14.2, 2.2], "3.1333": [-8.2, -14.2, 2.2], "3.3": [-6.6, -14.2, 2.2]}, "scale": [1, 1.5, 1]}, "constraint": {"rotation": [0.05, 0.2, 0.1], "position": [0.15, 0.2, 0.35]}, "additional_magazine": {"scale": {"0.0": [0, 0, 0], "3.0": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "camera": {"relative_to": {"rotation": "entity"}, "rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.4333": {"post": [3.275, 0.65, -0.225], "lerp_mode": "catmullrom"}, "1.0667": {"post": [3.745, 0.65, -0.3], "lerp_mode": "catmullrom"}, "1.3667": {"post": [1.415, 0.85, -0.8], "lerp_mode": "catmullrom"}, "1.6333": {"post": [1.58, 0.82, -0.15], "lerp_mode": "catmullrom"}, "1.7333": {"post": [2.86, 0.5, 0.525], "lerp_mode": "catmullrom"}, "1.9": {"post": [2.24, -0.76, -0.895], "lerp_mode": "catmullrom"}, "2.0667": {"post": [3.215, -2.375, -0.425], "lerp_mode": "catmullrom"}, "2.2667": {"post": [3.695, -2.62, -1.12], "lerp_mode": "catmullrom"}, "2.5": {"post": [3.465, -2.375, -0.925], "lerp_mode": "catmullrom"}, "2.8": {"post": [0.985, 0, -0.575], "lerp_mode": "catmullrom"}, "2.9": {"post": [1.78, 0.17, 0.54], "lerp_mode": "catmullrom"}, "3.0333": {"post": [0.13, 0.13, -1.215], "lerp_mode": "catmullrom"}, "3.2333": {"post": [0.605, 0, 0.175], "lerp_mode": "catmullrom"}, "3.4": {"post": [-1.695, -0.13, -0.085], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-2.19, -0.2, 0.225], "lerp_mode": "catmullrom"}, "4.0": {"post": [-1.49, -0.2, 0.225], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-0.905, -0.06, 0.18], "lerp_mode": "catmullrom"}, "4.4667": {"post": [0.6, 0, 0.15], "lerp_mode": "catmullrom"}, "4.6": {"post": [0.915, 0, 0.52], "lerp_mode": "catmullrom"}, "4.7": {"post": [-0.3, 0, -0.275], "lerp_mode": "catmullrom"}, "4.8333": {"post": [0.25, 0, 0.05], "lerp_mode": "catmullrom"}, "5.0333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "wfoly_plr_sm_uzulu_raise_first_start"}, "1.1667": {"effect": "wfoly_plr_sm_uzulu_reload_shake"}, "1.5667": {"effect": "wfoly_plr_sm_uzulu_reload_fast_magout_01"}, "2.6333": {"effect": "wfoly_plr_sm_uzulu_reload_fast_magin_01"}, "3.3": {"effect": "wfoly_plr_sm_uzulu_reload_rattle"}, "4.1667": {"effect": "wfoly_plr_sm_uzulu_reload_end"}}}, "inspect_empty": {"animation_length": 6.6, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-11.4, -39.17, -45.465], "lerp_mode": "catmullrom"}, "0.4": {"post": [-12.09, -47.67, -53.42], "lerp_mode": "catmullrom"}, "0.6": {"post": [-10.94, -48.79, -55.465], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-8.36907, -49.45083, -58.20011], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-8.975, -48.64, -58.35], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-16.26359, -34.63452, -39.31901], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-15.32021, -10.31204, -36.61094], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-15.32021, -10.31204, -36.61094], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-13.38322, -12.02676, -31.60918], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-14.66959, -13.28761, -38.49377], "lerp_mode": "catmullrom"}, "2.2": {"post": [-13.61949, -11.00059, -35.08987], "lerp_mode": "catmullrom"}, "3.0": {"post": [-19.02566, -13.12871, -34.34283], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-18.88924, -28.19537, -29.13162], "lerp_mode": "catmullrom"}, "3.4667": {"post": [-21.18, -29.07, -30.26], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-31.30096, 16.43112, 33.32269], "lerp_mode": "catmullrom"}, "3.8": {"post": [-29.6, 27.725, 55.14], "lerp_mode": "catmullrom"}, "3.9667": {"post": [-32.1255, 28.01667, 52.40205], "lerp_mode": "catmullrom"}, "4.0667": {"post": [-31.49494, 31.92693, 56.30693], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-31.77222, 29.43297, 51.97987], "lerp_mode": "catmullrom"}, "4.3": {"post": [-30.29135, 28.69695, 51.5581], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-31.25526, 27.0379, 51.08609], "lerp_mode": "catmullrom"}, "4.6": {"post": [-31.32, 26.69, 51.69], "lerp_mode": "catmullrom"}, "4.6667": {"post": [-26.52054, 19.80088, 47.70698], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-24.0612, 27.49872, 57.97347], "lerp_mode": "catmullrom"}, "4.8": {"post": [-28.57533, 24.89392, 53.73901], "lerp_mode": "catmullrom"}, "4.9333": {"post": [-32.73181, 21.186, 46.62786], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-21.17344, 8.75809, 22.61335], "lerp_mode": "catmullrom"}, "5.2": {"post": [-14.06914, -12.93791, -37.68337], "lerp_mode": "catmullrom"}, "5.3": {"post": [-14.31012, -25.1787, -46.89249], "lerp_mode": "catmullrom"}, "5.4": {"post": [-16.96827, -34.21514, -44.43707], "lerp_mode": "catmullrom"}, "5.5": {"post": [-17.85707, -37.87786, -44.21183], "lerp_mode": "catmullrom"}, "5.5667": {"post": [-17.18915, -38.03604, -46.06745], "lerp_mode": "catmullrom"}, "5.6333": {"post": [-16.26, -37.08, -45.52], "lerp_mode": "catmullrom"}, "5.6667": {"post": [-24.12444, -34.14264, -37.13555], "lerp_mode": "catmullrom"}, "5.7": {"post": [-5.96367, -35.9252, -50.67691], "lerp_mode": "catmullrom"}, "5.7667": {"post": [-13.3314, -31.73339, -31.80023], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-6.65, -32.04, -42.93], "lerp_mode": "catmullrom"}, "5.9333": {"post": [-11.78779, -12.72135, -12.4649], "lerp_mode": "catmullrom"}, "6.0667": {"post": [2.51996, -7.02572, -0.01833], "lerp_mode": "catmullrom"}, "6.1667": {"post": [0.25183, -0.82226, -1.85471], "lerp_mode": "catmullrom"}, "6.2333": {"post": [-0.00274, -0.34214, 0.80421], "lerp_mode": "catmullrom"}, "6.3333": {"post": [0, 0.08125, -0.775], "lerp_mode": "catmullrom"}, "6.4667": {"post": [0, 0, 0.25], "lerp_mode": "catmullrom"}, "6.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.345, -0.28, -0.12], "lerp_mode": "catmullrom"}, "0.3": {"post": [1.6, 3.75, 0.6], "lerp_mode": "catmullrom"}, "0.4333": {"post": [2.325, 4.25, 1.18], "lerp_mode": "catmullrom"}, "0.6": {"post": [2.545, 3.825, 1.53], "lerp_mode": "catmullrom"}, "0.9": {"post": [2.54, 3.695, 1.68], "lerp_mode": "catmullrom"}, "1.0": {"post": [2.53, 3.905, 1.73], "lerp_mode": "catmullrom"}, "1.1333": {"post": [2.395, 3.6, 1.68], "lerp_mode": "catmullrom"}, "1.2667": {"post": [1.435, 1.145, 0.955], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-0.03, 0.85, 1.23], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-0.27, 0.98, 1.105], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-0.345, 0.48, 1.11], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-1.42, 1.93, 1.435], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-1.795, 1.76, 1.44], "lerp_mode": "catmullrom"}, "2.0": {"post": [-1.62, 0.9, 1.44], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-1.47, 0.405, 1.44], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-1.465, 0.16875, 1.46], "lerp_mode": "catmullrom"}, "3.0667": {"post": [-1.395, 0.555, 1.44], "lerp_mode": "catmullrom"}, "3.4": {"post": [-0.685, -0.065, -0.425], "lerp_mode": "catmullrom"}, "3.5333": {"post": [0.415, -0.565, -0.505], "lerp_mode": "catmullrom"}, "3.6333": {"post": [0.49, -0.19, 0.83], "lerp_mode": "catmullrom"}, "3.7333": {"post": [0.565, 1.085, 1.695], "lerp_mode": "catmullrom"}, "3.8333": {"post": [0.55, 1.83, 1.905], "lerp_mode": "catmullrom"}, "3.9333": {"post": [0.52, 1.75, 1.7], "lerp_mode": "catmullrom"}, "4.0333": {"post": [0.52, 1.04, 1.885], "lerp_mode": "catmullrom"}, "4.1667": {"post": [0.52, 1.32, 2.16], "lerp_mode": "catmullrom"}, "4.4": {"post": [0.54, 1.47, 2.005], "lerp_mode": "catmullrom"}, "4.6": {"post": [0.55, 1.33, 1.76], "lerp_mode": "catmullrom"}, "4.6667": {"post": [0.55, 2.135, 1.37], "lerp_mode": "catmullrom"}, "4.7667": {"post": [0.55, 2.055, 1.08], "lerp_mode": "catmullrom"}, "4.9333": {"post": [0.47, 1.18, 0.1], "lerp_mode": "catmullrom"}, "5.0667": {"post": [0.155, 0.18, 0.6], "lerp_mode": "catmullrom"}, "5.2": {"post": [-0.4, 0.55, 1.44], "lerp_mode": "catmullrom"}, "5.3": {"post": [-0.545, 1.75, 0.885], "lerp_mode": "catmullrom"}, "5.4333": {"post": [-0.245, 2.45, 1.14], "lerp_mode": "catmullrom"}, "5.6333": {"post": [0.405, 2.355, 1.315], "lerp_mode": "catmullrom"}, "5.7": {"post": [1.105, 0.68, 1.32], "lerp_mode": "catmullrom"}, "5.8333": {"post": [0.63, 0.98, 1.32], "lerp_mode": "catmullrom"}, "5.9667": {"post": [0.6, -2.65, -3.9], "lerp_mode": "catmullrom"}, "6.0333": {"post": [0.51, -2.965, -2.34], "lerp_mode": "catmullrom"}, "6.1": {"post": [0.31, -0.86, -0.41], "lerp_mode": "catmullrom"}, "6.1667": {"post": [0, -0.05, 0.6], "lerp_mode": "catmullrom"}, "6.2667": {"post": [0, -0.125, -0.15], "lerp_mode": "catmullrom"}, "6.3667": {"post": [0, 0, 0.15], "lerp_mode": "catmullrom"}, "6.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"1.6667": [0, 0, 0], "1.7333": [0, 0, 0], "1.8": [0.31838, -3.05966, -13.14528], "1.9333": [-22.93073, -9.02445, 14.64036], "2.3667": {"pre": [-22.93, -9.02, 14.64], "post": [-22.93, -9.02, 14.64], "lerp_mode": "catmullrom"}, "2.5": {"post": [24.00125, 13.01889, 65.38409], "lerp_mode": "catmullrom"}, "2.7333": {"post": [17.11163, 13.23259, 55.62339], "lerp_mode": "catmullrom"}, "3.0333": [16.41306, 14.098, 52.66719], "3.2": [-13.51108, 17.04604, -10.24615], "3.4667": [0, 0, 0], "4.6": [0, 0, 0], "5.0667": [4.38338, -16.66847, -73.78579], "5.2667": [-22.93073, -9.02445, 14.64036], "5.3333": [-21.36627, -4.32852, -29.00362], "5.4333": [-10.66045, -1.38032, -5.61365], "5.5333": [-1.75, 0, 0], "5.6333": [0, 0, 0], "5.7": [0, 0, 0]}, "position": {"1.6667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.7333": {"post": [0, -5.9, 0], "lerp_mode": "catmullrom"}, "1.8": {"post": [1.86, -7.34, -0.07], "lerp_mode": "catmullrom"}, "1.9333": {"post": [5.325, -3.575, -0.225], "lerp_mode": "catmullrom"}, "2.0667": {"post": [6.45, -2.345, -0.24], "lerp_mode": "catmullrom"}, "2.3333": {"post": [6.5, -2.28, -0.24], "lerp_mode": "catmullrom"}, "2.5333": {"post": [6.00649, -1.96095, -0.06063], "lerp_mode": "catmullrom"}, "2.7333": {"post": [6.696, -2.01753, -0.36326], "lerp_mode": "catmullrom"}, "3.0": {"post": [5.9862, -2.64152, 0.14752], "lerp_mode": "catmullrom"}, "3.2667": {"post": [0.66769, -8.94792, 6.14443], "lerp_mode": "catmullrom"}, "3.4667": [0.18137, -9.65475, 6.78589], "3.6667": [1.8, -7.02, 4.8], "4.6": [1.8, -7.02, 4.8], "5.0667": {"pre": [16.62863, -6.52224, 4.9447], "post": [16.62863, -6.52224, 4.9447], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-1.92155, -18.75609, 9.49435], "lerp_mode": "catmullrom"}, "5.2667": {"post": [-2.39748, -8.71562, 5.21463], "lerp_mode": "catmullrom"}, "5.4333": [0.25, -7.025, 0.275], "5.6333": [0, -5.9, 0], "5.7": [0, 0, 0]}}, "lefthand": {"rotation": {"0.0": [91.47, -398.26, -126.76], "0.3": [135.4661, -414.89607, -145.74556], "1.2": [91.47237, -398.26212, -126.75784], "1.3": [67.94594, -402.21263, -86.55696], "1.4667": [102.01512, -419.10604, -129.90951], "1.6": [102.01512, -419.10604, -129.90951], "3.3667": [102.01512, -419.10604, -129.90951], "3.5": [102.01512, -419.10604, -129.90951], "3.7333": [101.7321, -399.55235, -185.93504], "3.8333": [109.95093, -403.33467, -201.22264], "3.9333": [116.38885, -392.56819, -210.53953], "4.0": [111.29303, -384.42978, -206.15981], "4.0667": [116.38885, -392.56819, -210.53953], "4.3": [115.34933, -388.96859, -208.50867], "4.6": [115.34933, -388.96859, -208.50867], "4.7": [108.37996, -412.69629, -242.39095], "4.8333": [115.6938, -423.84788, -250.94488], "5.1333": [100.88876, -412.25625, -127.33969], "5.5": [100.89, -412.26, -127.34], "5.6667": [96.58116, -360.91757, -108.72856], "5.8667": [96.58116, -360.91757, -108.72856], "6.0": [91.47, -398.26, -126.76]}, "position": {"0.0": [7.75, -13, -3.5], "0.1333": [8.95872, -16.59584, 0.06092], "0.2": [7.77341, -22.66102, 3.20472], "0.3": [0.51156, -27.46069, 10.36215], "1.2": [1.20272, -29.15603, 9.84726], "1.3": [6.22787, -24.10779, 4.45804], "1.4667": [7.225, -18.375, 1.9], "1.6": [7.225, -18.375, 1.9], "3.3667": [7.225, -18.375, 1.9], "3.5667": [11.93877, -18.62905, 1.37239], "3.6": [15.25441, -14.47807, -0.74487], "3.6333": [16.62257, -10.75636, -2.71767], "3.6667": [16.41013, -8.06521, -4.56499], "3.7": [15.30777, -5.44709, -5.87512], "3.7333": [12.96443, -3.59552, -7.01148], "3.7667": [10.00947, -2.29152, -8.13171], "3.8": [6.71451, -2.12146, -9.04695], "3.9333": [6.71451, -2.12146, -9.04695], "4.0": [6.71451, -2.12146, -4.62195], "4.6": [6.71451, -2.12146, -4.62195], "4.7": [11.54637, -2.50368, -1.68523], "4.8": [12.41, -2.56, -0.14], "4.9333": [15.84984, -2.76615, 6.06588], "5.1333": [7.425, -18.7, 1.9], "5.5": [7.43, -18.7, 1.9], "5.6667": [7.43, -19.55, 1.9], "5.8667": [7.43, -19.55, 1.9], "6.0": [7.75, -13, -3.5]}, "scale": [1, 1.5, 1]}, "mag_and_bullet": {"scale": {"0.0": [1, 1, 1], "3.2667": {"pre": [1, 1, 1], "post": [0, 0, 0]}, "5.2333": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "gun_and_righthand": {"rotation": {"1.7": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.7667": {"post": [1.02678, 0.00359, -3.14566], "lerp_mode": "catmullrom"}, "1.8667": {"post": [2.27514, 0.29255, -8.26714], "lerp_mode": "catmullrom"}, "2.1": {"post": [1.19454, 0.09593, -4.58574], "lerp_mode": "catmullrom"}, "2.4333": {"post": [1.19454, 0.09593, -4.58574], "lerp_mode": "catmullrom"}, "2.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "righthand": {"rotation": [100.21875, 11.81504, -177.88617], "position": {"0.0": [-6.6, -14.2, 2.2], "0.2": [-8.2, -14.2, 2.2], "0.9333": [-8.2, -14.2, 2.2], "1.0": [-8.2, -14.2, 2.2], "1.1": [-8.325, -14.2, 2.55], "1.2": [-8.2, -14.2, 2.2], "3.5": [-8.2, -14.2, 2.2], "3.6333": [-6.6, -14.2, 2.2], "5.0667": [-6.6, -14.2, 2.2], "5.2": [-8.2, -14.2, 2.2], "5.8667": [-8.2, -14.2, 2.2], "6.0": [-6.6, -14.2, 2.2]}, "scale": [1, 1.5, 1]}, "constraint": {"rotation": [0.05, 0.2, 0.1], "position": [0.15, 0.2, 0.35]}, "pull": {"position": {"3.9333": [0, 0, 0], "4.0": [0, 0, 3], "4.6": [0, 0, 3], "4.6333": [0, 0, 0]}}, "bolt": {"position": {"0.0": [0, 0, -3], "3.9333": [0, 0, -3], "4.0": [0, 0, 0], "4.6": [0, 0, 0], "4.6333": [0, 0, -3]}}, "additional_magazine": {"scale": {"0.0": [0, 0, 0], "6.3": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.65, 0.14, -0.18], "lerp_mode": "catmullrom"}, "0.4333": {"post": [3.275, 0.65, -0.775], "lerp_mode": "catmullrom"}, "1.0333": {"post": [3.745, 0.65, -1.1], "lerp_mode": "catmullrom"}, "1.3667": {"post": [1.075, 0.65, -0.3], "lerp_mode": "catmullrom"}, "1.7": {"post": [1.22, 0.075, -0.575], "lerp_mode": "catmullrom"}, "1.8333": {"post": [3.29, -1.37, -0.25], "lerp_mode": "catmullrom"}, "1.9333": {"post": [4.17, -2.45, -1.75], "lerp_mode": "catmullrom"}, "2.1333": {"post": [4.03, -2.97, -0.75], "lerp_mode": "catmullrom"}, "2.3333": {"post": [4.32, -3.425, -1.425], "lerp_mode": "catmullrom"}, "2.5667": {"post": [3.59, -3.7, -0.94], "lerp_mode": "catmullrom"}, "2.8333": {"post": [2.645, -3.975, -0.9], "lerp_mode": "catmullrom"}, "3.0667": {"post": [2.25, -4.1, -0.725], "lerp_mode": "catmullrom"}, "3.2667": {"post": [1.34, -4.09, -0.46], "lerp_mode": "catmullrom"}, "3.4": {"post": [1.215, -3.625, 0.45], "lerp_mode": "catmullrom"}, "3.5333": {"post": [2.04, -2, 0.3], "lerp_mode": "catmullrom"}, "3.7333": {"post": [3.495, 0.65, 0.3], "lerp_mode": "catmullrom"}, "3.9667": {"post": [4, 0.99, 0.475], "lerp_mode": "catmullrom"}, "4.0667": {"post": [4.545, 0.99, 1.075], "lerp_mode": "catmullrom"}, "4.2333": {"post": [3.6, 0.93, -0.075], "lerp_mode": "catmullrom"}, "4.4667": {"post": [4.18, 0.88, 0.475], "lerp_mode": "catmullrom"}, "4.5667": {"post": [4.23, 0.86, 0.475], "lerp_mode": "catmullrom"}, "4.6667": {"post": [5.05, 0.75, 1.35], "lerp_mode": "catmullrom"}, "4.8": {"post": [3.235, 0.47, -0.225], "lerp_mode": "catmullrom"}, "4.9667": {"post": [3.415, 0.06, 0.4], "lerp_mode": "catmullrom"}, "5.1667": {"post": [2.12, -0.1, 0.015], "lerp_mode": "catmullrom"}, "5.3": {"post": [1.72, -0.14, 0.175], "lerp_mode": "catmullrom"}, "5.4333": {"post": [1.625, -0.16, -0.035], "lerp_mode": "catmullrom"}, "5.6333": {"post": [2.75, -0.175, -0.3], "lerp_mode": "catmullrom"}, "5.7333": {"post": [3.46, -0.14, 0.835], "lerp_mode": "catmullrom"}, "5.8667": {"post": [1.815, -0.08, -0.83], "lerp_mode": "catmullrom"}, "6.0333": {"post": [0.95, 0, 0.325], "lerp_mode": "catmullrom"}, "6.1667": {"post": [-0.325, 0, -0.225], "lerp_mode": "catmullrom"}, "6.3": {"post": [0.175, 0, 0.45], "lerp_mode": "catmullrom"}, "6.4333": {"post": [-0.125, 0, -0.125], "lerp_mode": "catmullrom"}, "6.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "wfoly_plr_sm_uzulu_raise_first_start"}, "1.1667": {"effect": "wfoly_plr_sm_uzulu_reload_shake"}, "1.5667": {"effect": "wfoly_plr_sm_uzulu_reload_fast_magout_01"}, "2.3333": {"effect": "wfoly_plr_sm_uzulu_reload_empty_arm"}, "3.1333": {"effect": "weap_sm_uzulu_ads_down"}, "3.8667": {"effect": "wfoly_plr_sm_uzulu_reload_empty_fast_charge_01"}, "4.6": {"effect": "wfoly_plr_sm_uzulu_reload_empty_fast_charge_01"}, "5.0333": {"effect": "wfoly_plr_sm_uzulu_reload_rattle"}, "5.4667": {"effect": "wfoly_plr_sm_uzulu_reload_fast_magin_01"}, "5.7": {"effect": "wfoly_plr_sm_uzulu_reload_end"}}}, "shoot": {"animation_length": 0.36667, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0333": [1.94, -0.59, 3.43], "0.0667": [-0.83, 0.24, -0.52], "0.1": [-0.54, 0.16, -1.35], "0.1333": [0.08, -0.01, -1.75], "0.1667": [0.1, -0.02, -2.22], "0.2": [0, 0, -2.25], "0.2333": [-0.01, 0, -1.62], "0.2667": [0, 0, -0.69], "0.3": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0333": [0, -0.12, 1.13], "0.0667": [0, -0.25, 0.9], "0.1": [0, -0.01, -0.04], "0.1333": [0, -0.04, -0.29], "0.1667": [0, -0.1, -0.37], "0.2": [0, -0.1, -0.27], "0.2333": [0, -0.08, -0.08], "0.2667": [0, -0.06, 0.05], "0.3": [0, -0.04, 0.07], "0.3333": [0, -0.02, 0.03], "0.3667": [0, 0, 0]}}, "pull": {"position": {"0.0": [0, 0, 0], "0.0333": [0, 0, 0.25], "0.0667": [0, 0, 0], "0.1": [0, 0, 0.15], "0.1333": [0, 0, 0]}}, "bolt": {"position": {"0.0": [0, 0, 0], "0.0333": [0, 0, -3], "0.0667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": [0, 0, 0], "0.0333": [0, 0, 0.625], "0.0667": [0, 0, -0.37], "0.1": [0, 0, 0.4], "0.1333": [0, 0, -0.12], "0.1667": [0, 0, 0.185], "0.2": [0, 0, 0]}, "scale": 1}}}}}