{"typeDisplayTarget": {"type": "string", "enum": ["create:display_board", "create:sign", "create:lectern", "create:nixie_tube"]}, "typeBannerPattern": {"type": "string", "enum": ["minecraft:half_horizontal", "minecraft:gradient_up", "minecraft:stripe_left", "minecraft:skull", "minecraft:gradient", "minecraft:triangle_top", "immersiveengineering:treated_wood", "immersiveengineering:windmill", "minecraft:triangles_top", "minecraft:square_top_left", "minecraft:triangle_bottom", "minecraft:stripe_downright", "immersiveengineering:wolf", "minecraft:stripe_top", "minecraft:stripe_bottom", "minecraft:diagonal_left", "minecraft:small_stripes", "minecraft:base", "minecraft:square_top_right", "minecraft:creeper", "minecraft:rhombus", "minecraft:border", "minecraft:bricks", "minecraft:triangles_bottom", "minecraft:diagonal_right", "minecraft:cross", "minecraft:diagonal_up_left", "minecraft:flower", "minecraft:half_vertical", "minecraft:half_horizontal_bottom", "minecraft:globe", "minecraft:stripe_downleft", "immersiveengineering:wolf_l", "immersiveengineering:ornate", "minecraft:curly_border", "minecraft:square_bottom_left", "minecraft:half_vertical_right", "minecraft:mojang", "immersiveengineering:wolf_r", "minecraft:piglin", "minecraft:straight_cross", "minecraft:stripe_middle", "minecraft:stripe_right", "minecraft:diagonal_up_right", "minecraft:square_bottom_right", "immersiveengineering:hammer", "immersiveengineering:bevels", "minecraft:stripe_center", "minecraft:circle"]}, "typeWorldgenStructurePlacement": {"type": "string", "enum": ["minecraft:concentric_rings", "cataclysm:cataclysm_random_spread", "minecraft:random_spread"]}, "typeSchedule": {"type": "string", "enum": ["minecraft:empty", "minecraft:simple", "minecraft:villager_baby", "minecraft:villager_default"]}, "typeWorldgenNoise": {"type": "string", "enum": ["minecraft:packed_ice", "minecraft:soul_sand_layer", "minecraft:ore_gap", "minecraft:temperature_large", "minecraft:erosion", "minecraft:offset", "minecraft:cave_layer", "minecraft:patch", "minecraft:jagged", "minecraft:noodle", "minecraft:continentalness", "minecraft:calcite", "minecraft:temperature", "minecraft:vegetation", "minecraft:nether_state_selector", "minecraft:surface_secondary", "minecraft:erosion_large", "minecraft:spaghetti_2d", "minecraft:ice", "minecraft:noodle_thickness", "minecraft:spaghetti_3d_thickness", "minecraft:badlands_pillar_roof", "minecraft:spaghetti_2d_modulator", "minecraft:badlands_surface", "minecraft:iceberg_pillar", "minecraft:gravel_layer", "minecraft:surface_swamp", "minecraft:ore_veininess", "minecraft:spaghetti_2d_thickness", "minecraft:aquifer_fluid_level_floodedness", "minecraft:pillar_thickness", "minecraft:spaghetti_2d_elevation", "minecraft:clay_bands_offset", "minecraft:badlands_pillar", "minecraft:spaghetti_3d_1", "minecraft:spaghetti_3d_2", "minecraft:spaghetti_3d_rarity", "minecraft:continentalness_large", "minecraft:ridge", "minecraft:spaghetti_roughness_modulator", "minecraft:nether_wart", "minecraft:cave_entrance", "minecraft:ore_vein_b", "minecraft:ore_vein_a", "minecraft:aquifer_barrier", "minecraft:netherrack", "minecraft:noodle_ridge_a", "minecraft:vegetation_large", "minecraft:noodle_ridge_b", "minecraft:pillar_rareness", "minecraft:gravel", "minecraft:iceberg_pillar_roof", "minecraft:pillar", "minecraft:powder_snow", "minecraft:aquifer_lava", "minecraft:aquifer_fluid_level_spread", "minecraft:surface", "minecraft:cave_cheese", "minecraft:iceberg_surface", "minecraft:spaghetti_roughness"]}, "typeWorldgenStructurePiece": {"type": "string", "enum": ["minecraft:nescsc", "minecraft:shli", "minecraft:btp", "minecraft:shph", "minecraft:msstairs", "minecraft:shpr", "minecraft:ompenthouse", "minecraft:omdxr", "minecraft:omdxyr", "minecraft:nemt", "minecraft:shlt", "tconstruct:island", "minecraft:iglu", "minecraft:wmp", "minecraft:nerc", "minecraft:nebcr", "cataclysm:ruined_citadel", "minecraft:nefos", "minecraft:omwr", "cataclysm:burning_arena", "minecraft:msroom", "minecraft:omentry", "minecraft:omsimple", "minecraft:orp", "minecraft:omdyzr", "minecraft:tedp", "minecraft:nesc", "minecraft:shrc", "minecraft:mscorridor", "minecraft:nebs", "minecraft:shfc", "cataclysm:cursed_pyramid", "minecraft:omcr", "minecraft:nece", "minecraft:nesclt", "minecraft:nesr", "minecraft:shrt", "minecraft:necsr", "minecraft:nebef", "minecraft:sh5c", "minecraft:omdzr", "minecraft:shipwreck", "minecraft:shssd", "minecraft:ecp", "minecraft:mscrossing", "minecraft:nestart", "minecraft:shsd", "minecraft:shstart", "minecraft:neccs", "minecraft:nectb", "minecraft:shs", "minecraft:omb", "minecraft:tejp", "minecraft:shcc", "minecraft:ae2mtrt", "minecraft:omdyr", "minecraft:rupo", "minecraft:nescrt", "cataclysm:sunken_city", "minecraft:omsimplet", "minecraft:tesh", "minecraft:jigsaw"]}, "typePositionSourceType": {"type": "string", "enum": ["minecraft:block", "minecraft:entity"]}, "typeFloatProviderType": {"type": "string", "enum": ["minecraft:uniform", "minecraft:clamped_normal", "minecraft:trapezoid", "minecraft:constant"]}, "typeLootScoreProviderType": {"type": "string", "enum": ["minecraft:fixed", "minecraft:context"]}, "typeAttribute": {"type": "string", "enum": ["minecraft:generic.max_health", "minecraft:generic.follow_range", "minecraft:generic.knockback_resistance", "minecraft:generic.movement_speed", "minecraft:generic.flying_speed", "minecraft:generic.attack_damage", "minecraft:generic.attack_knockback", "minecraft:generic.attack_speed", "minecraft:generic.armor", "minecraft:generic.armor_toughness", "minecraft:generic.luck", "minecraft:zombie.spawn_reinforcements", "minecraft:horse.jump_strength", "attributeslib:armor_pierce", "attributeslib:armor_shred", "attributeslib:arrow_damage", "attributeslib:arrow_velocity", "attributeslib:cold_damage", "attributeslib:crit_chance", "attributeslib:crit_damage", "attributeslib:current_hp_damage", "attributeslib:dodge_chance", "attributeslib:draw_speed", "attributeslib:experience_gained", "attributeslib:fire_damage", "attributeslib:ghost_health", "attributeslib:healing_received", "attributeslib:life_steal", "attributeslib:mining_speed", "attributeslib:overheal", "attributeslib:prot_pierce", "attributeslib:prot_shred", "attributeslib:elytra_flight", "attributeslib:creative_flight", "forge:swim_speed", "forge:nametag_distance", "forge:entity_gravity", "forge:block_reach", "forge:entity_reach", "forge:step_height_addition", "tconstruct:generic.bouncy", "tconstruct:player.use_item_speed", "tconstruct:generic.protection_cap", "tconstruct:player.critical_damage", "tconstruct:generic.jump_boost", "tconstruct:generic.safe_fall_distance", "tconstruct:player.jump_count", "tconstruct:generic.knockback_multiplier", "tconstruct:player.mining_speed_multiplier", "tconstruct:player.experience_multiplier", "tconstruct:generic.crouch_damage_multiplier", "tconstruct:generic.good_effect_duration_multiplier", "tconstruct:generic.bad_effect_duration_multiplier"]}, "typeMenu": {"type": "string", "enum": ["minecraft:generic_9x1", "minecraft:generic_9x2", "minecraft:generic_9x3", "minecraft:generic_9x4", "minecraft:generic_9x5", "minecraft:generic_9x6", "minecraft:generic_3x3", "minecraft:anvil", "minecraft:beacon", "minecraft:blast_furnace", "minecraft:brewing_stand", "minecraft:crafting", "minecraft:enchantment", "minecraft:furnace", "minecraft:grindstone", "minecraft:hopper", "minecraft:lectern", "minecraft:loom", "minecraft:merchant", "minecraft:shulker_box", "minecraft:smithing", "minecraft:smoker", "minecraft:cartography_table", "minecraft:stonecutter", "ae2:inscriber", "ae2:cellworkbench", "ae2:import_bus", "ae2:energy_level_emitter", "ae2:interface", "ae2:controller_networkstatus", "ae2:portable_fluid_cell", "ae2:export_bus", "ae2:vibrationchamber", "ae2:patternaccessterminal", "ae2:patternterm", "ae2:formationplane", "ae2:<PERSON><PERSON><PERSON>", "ae2:drive", "ae2:craftconfirm", "ae2:portable_item_cell", "ae2:networkstatus", "ae2:storage_level_emitter", "ae2:skychest", "ae2:priority", "ae2:storagebus", "ae2:chest", "ae2:craftingstatus", "ae2:quartzknife", "ae2:wirelessterm", "ae2:molecular_assembler", "ae2:networktool", "ae2:craftamount", "ae2:spatialioport", "ae2:craftingcpu", "ae2:ioport", "ae2:wirelesscraftingterm", "ae2:pattern_provider", "ae2:qnb", "ae2:set_stock_amount", "ae2:condenser", "ae2:item_terminal", "ae2:wireless_access_point", "ae2:craftingterm", "netmusic:cd_burner", "netmusic:computer", "aerlunerpg:hole", "aerlunerpg:bait_1", "aerlunerpg:furnacegui", "aerlunerpg:<PERSON><PERSON><PERSON>", "aerlunerpg:rabbitholegui", "aerlunerpg:icebookdgui", "aerlunerpg:hunter", "aerlunerpg:huntershop", "aerlunerpg:chestgui_2", "aerlunerpg:tryt_678", "aerlunerpg:chestcandy_1", "aerlunerpg:furnacegui_1", "aerlunerpg:bookgui_1", "aerlunerpg:metsgui", "cataclysm:weapon_fusion", "curios:curios_container", "curios:curios_container_v2", "tconstruct:crafting_station", "tconstruct:tinker_station", "tconstruct:part_builder", "tconstruct:modifier_worktable", "tconstruct:tinker_chest", "tconstruct:tool_container", "tconstruct:melter", "tconstruct:smeltery", "tconstruct:single_item", "tconstruct:alloyer", "immersiveengineering:cokeoven", "immersiveengineering:alloysmelter", "immersiveengineering:blastfurnace", "immersiveengineering:blastfurnace_advanced", "immersiveengineering:craftingtable", "immersiveengineering:woodencrate", "immersiveengineering:workbench", "immersiveengineering:circuittable", "immersiveengineering:assembler", "immersiveengineering:sorter", "immersiveengineering:item_batcher", "immersiveengineering:logic_unit", "immersiveengineering:squeezer", "immersiveengineering:fermenter", "immersiveengineering:refinery", "immersiveengineering:arcfurnace", "immersiveengineering:autoworkbench", "immersiveengineering:mixer", "immersiveengineering:turret_gun", "immersiveengineering:turret_chem", "immersiveengineering:<PERSON><PERSON><PERSON>", "immersiveengineering:cloche", "immersiveengineering:toolboxblock", "immersiveengineering:toolbox", "immersiveengineering:revolver", "immersiveengineering:maintenancekit", "immersiveengineering:cart_crate", "createoreexcavation:vein_atlas", "create:schematic_table", "create:schematic<PERSON><PERSON>", "create:filter", "create:attribute_filter", "create:package_filter", "create:crafting_blueprint", "create:linked_controller", "create:toolbox", "create:schedule", "create:stock_keeper_category", "create:stock_keeper_request", "create:package_port", "create:redstone_requester", "create:factory_panel_set_item", "kubejs:menu", "mekanism:module_tweaker", "mekanism:dictionary", "mekanism:portable_teleporter", "mekanism:seismic_reader", "mekanism:qio_frequency_select_item", "mekanism:portable_qio_dashboard", "mekanism:main_robit", "mekanism:inventory_robit", "mekanism:smelting_robit", "mekanism:crafting_robit", "mekanism:repair_robit", "mekanism:chemical_crystallizer", "mekanism:chemical_dissolution_chamber", "mekanism:chemical_infuser", "mekanism:chemical_injection_chamber", "mekanism:chemical_oxidizer", "mekanism:chemical_washer", "mekanism:combiner", "mekanism:crusher", "mekanism:digital_miner", "mekanism:dynamic_tank", "mekanism:electric_pump", "mekanism:electrolytic_separator", "mekanism:energized_smelter", "mekanism:enrichment_chamber", "mekanism:fluidic_plenisher", "mekanism:formulaic_assemblicator", "mekanism:fuelwood_heater", "mekanism:laser_amplifier", "mekanism:laser_tractor_beam", "mekanism:metallurgic_infuser", "mekanism:oredictionificator", "mekanism:osmium_compressor", "mekanism:precision_sawmill", "mekanism:pressurized_reaction_chamber", "mekanism:purification_chamber", "mekanism:quantum_entangloporter", "mekanism:resistive_heater", "mekanism:rotary_condensentrator", "mekanism:security_desk", "mekanism:modification_station", "mekanism:isotopic_centrifuge", "mekanism:nutritional_liquifier", "mekanism:seismic_vibrator", "mekanism:solar_neutron_activator", "mekanism:teleporter", "mekanism:thermal_evaporation_controller", "mekanism:antiprotonic_nucleosynthesizer", "mekanism:pigment_extractor", "mekanism:pigment_mixer", "mekanism:painting_machine", "mekanism:qio_drive_array", "mekanism:qio_dashboard", "mekanism:qio_importer", "mekanism:qio_exporter", "mekanism:qio_redstone_adapter", "mekanism:sps_casing", "mekanism:dimensional_stabilizer", "mekanism:factory", "mekanism:chemical_tank", "mekanism:fluid_tank", "mekanism:energy_cube", "mekanism:induction_matrix", "mekanism:thermoelectric_boiler", "mekanism:personal_storage_item", "mekanism:personal_storage_block", "mekanism:qio_frequency_select_tile", "mekanism:boiler_stats", "mekanism:matrix_stats", "mekanism:digital_miner_config", "mekanism:logistical_sorter"]}, "typeStatType": {"type": "string", "enum": ["minecraft:broken", "minecraft:crafted", "minecraft:custom", "minecraft:dropped", "minecraft:killed", "minecraft:killed_by", "minecraft:mined", "minecraft:picked_up", "minecraft:used"]}, "typeGas": {"type": "string", "enum": ["mekanism:antimatter", "mekanism:brine", "mekanism:chlorine", "mekanism:empty", "mekanism:ethene", "mekanism:fissile_fuel", "mekanism:hydrofluoric_acid", "mekanism:hydrogen", "mekanism:hydrogen_chloride", "mekanism:lithium", "mekanism:nuclear_waste", "mekanism:osmium", "mekanism:oxygen", "mekanism:plutonium", "mekanism:polonium", "mekanism:sodium", "mekanism:spent_nuclear_waste", "mekanism:steam", "mekanism:sulfur_dioxide", "mekanism:sulfur_trioxide", "mekanism:sulfuric_acid", "mekanism:superheated_sodium", "mekanism:uranium_hexafluoride", "mekanism:uranium_oxide", "mekanism:water_vapor"]}, "typeRuleTest": {"type": "string", "enum": ["minecraft:tag_match", "minecraft:blockstate_match", "minecraft:always_true", "minecraft:block_match", "kubejs:invert", "kubejs:always_false", "minecraft:random_blockstate_match", "minecraft:random_block_match", "kubejs:any_match", "kubejs:all_match"]}, "typeFanProcessingType": {"type": "string", "enum": ["create:splashing", "create:haunting", "create:smoking", "create:blasting"]}, "typeCreativeModeTab": {"type": "string", "enum": ["minecraft:hotbar", "cataclysm:cataclysm_block", "create:palettes", "ae2:facades", "minecraft:op_blocks", "minecraft:building_blocks", "tconstruct:tool_parts", "ae2:main", "createoreexcavation:create_ore_excavation", "minecraft:food_and_drinks", "minecraft:search", "minecraft:ingredients", "minecraft:functional_blocks", "tconstruct:world", "minecraft:tools_and_utilities", "ftbquests:default", "cataclysm:cataclysm_item", "kubejs:tab", "immersiveengineering:main", "create:base", "bigger_ae2:bigger_ae2", "aerlunerpg:mith", "tconstruct:tables", "minecraft:colored_blocks", "minecraft:inventory", "tconstruct:fluids", "minecraft:redstone_blocks", "tconstruct:general", "mekanism:mekanism", "minecraft:spawn_eggs", "minecraft:natural_blocks", "minecraft:combat", "netmusic:netmusic", "tconstruct:smeltery", "iron_making_furnace:tab", "tconstruct:tools"]}, "typeLootPoolEntryType": {"type": "string", "enum": ["minecraft:group", "minecraft:item", "minecraft:dynamic", "immersiveengineering:multiblock", "mantle:tag_preference", "minecraft:alternatives", "immersiveengineering:tile_drop", "minecraft:loot_table", "minecraft:empty", "tconstruct:tool_part", "minecraft:sequence", "tconstruct:tag_preference", "immersiveengineering:drop_inv", "minecraft:tag"]}, "typeTrimPattern": {"type": "string", "enum": ["minecraft:sentry", "minecraft:wayfinder", "minecraft:ward", "minecraft:spire", "minecraft:rib", "minecraft:tide", "minecraft:vex", "minecraft:coast", "minecraft:shaper", "minecraft:wild", "minecraft:eye", "minecraft:silence", "minecraft:host", "minecraft:dune", "minecraft:raiser", "minecraft:snout"]}, "typeFluid": {"type": "string", "enum": ["aerlunerpg:acidfluid", "aerlunerpg:acidfluid_2", "aerlunerpg:flowing_acidfluid", "aerlunerpg:flowing_acidfluid_2", "create:chocolate", "create:flowing_chocolate", "create:flowing_honey", "create:flowing_potion", "create:flowing_tea", "create:honey", "create:potion", "create:tea", "immersiveengineering:acetaldehyde", "immersiveengineering:acetaldehyde_flowing", "immersiveengineering:biodiesel", "immersiveengineering:biodiesel_flowing", "immersiveengineering:concrete", "immersiveengineering:concrete_flowing", "immersiveengineering:creosote", "immersiveengineering:creosote_flowing", "immersiveengineering:ethanol", "immersiveengineering:ethanol_flowing", "immersiveengineering:herbicide", "immersiveengineering:herbicide_flowing", "immersiveengineering:phenolic_resin", "immersiveengineering:phenolic_resin_flowing", "immersiveengineering:plantoil", "immersiveengineering:plantoil_flowing", "immersiveengineering:potion", "immersiveengineering:redstone_acid", "immersiveengineering:redstone_acid_flowing", "mekanism:brine", "mekanism:chlorine", "mekanism:ethene", "mekanism:flowing_brine", "mekanism:flowing_chlorine", "mekanism:flowing_ethene", "mekanism:flowing_heavy_water", "mekanism:flowing_hydrofluoric_acid", "mekanism:flowing_hydrogen", "mekanism:flowing_hydrogen_chloride", "mekanism:flowing_lithium", "mekanism:flowing_nutritional_paste", "mekanism:flowing_oxygen", "mekanism:flowing_sodium", "mekanism:flowing_steam", "mekanism:flowing_sulfur_dioxide", "mekanism:flowing_sulfur_trioxide", "mekanism:flowing_sulfuric_acid", "mekanism:flowing_superheated_sodium", "mekanism:flowing_uranium_hexafluoride", "mekanism:flowing_uranium_oxide", "mekanism:heavy_water", "mekanism:hydrofluoric_acid", "mekanism:hydrogen", "mekanism:hydrogen_chloride", "mekanism:lithium", "mekanism:nutritional_paste", "mekanism:oxygen", "mekanism:sodium", "mekanism:steam", "mekanism:sulfur_dioxide", "mekanism:sulfur_trioxide", "mekanism:sulfuric_acid", "mekanism:superheated_sodium", "mekanism:uranium_hexafluoride", "mekanism:uranium_oxide", "minecraft:empty", "minecraft:flowing_lava", "minecraft:flowing_milk", "minecraft:flowing_water", "minecraft:lava", "minecraft:milk", "minecraft:water", "tconstruct:venom", "tconstruct:flowing_venom", "tconstruct:powdered_snow", "tconstruct:earth_slime", "tconstruct:flowing_earth_slime", "tconstruct:sky_slime", "tconstruct:flowing_sky_slime", "tconstruct:ender_slime", "tconstruct:flowing_ender_slime", "tconstruct:magma", "tconstruct:flowing_magma", "tconstruct:ichor", "tconstruct:honey", "tconstruct:flowing_honey", "tconstruct:beetroot_soup", "tconstruct:flowing_beetroot_soup", "tconstruct:mushroom_stew", "tconstruct:flowing_mushroom_stew", "tconstruct:rabbit_stew", "tconstruct:flowing_rabbit_stew", "tconstruct:meat_soup", "tconstruct:flowing_meat_soup", "tconstruct:potion", "tconstruct:seared_stone", "tconstruct:flowing_seared_stone", "tconstruct:scorched_stone", "tconstruct:flowing_scorched_stone", "tconstruct:molten_clay", "tconstruct:flowing_molten_clay", "tconstruct:molten_glass", "tconstruct:flowing_molten_glass", "tconstruct:liquid_soul", "tconstruct:flowing_liquid_soul", "tconstruct:molten_porcelain", "tconstruct:flowing_molten_porcelain", "tconstruct:molten_obsidian", "tconstruct:flowing_molten_obsidian", "tconstruct:molten_ender", "tconstruct:flowing_molten_ender", "tconstruct:blazing_blood", "tconstruct:flowing_blazing_blood", "tconstruct:molten_emerald", "tconstruct:flowing_molten_emerald", "tconstruct:molten_quartz", "tconstruct:flowing_molten_quartz", "tconstruct:molten_amethyst", "tconstruct:flowing_molten_amethyst", "tconstruct:molten_diamond", "tconstruct:flowing_molten_diamond", "tconstruct:molten_debris", "tconstruct:flowing_molten_debris", "tconstruct:molten_iron", "tconstruct:flowing_molten_iron", "tconstruct:molten_gold", "tconstruct:flowing_molten_gold", "tconstruct:molten_copper", "tconstruct:flowing_molten_copper", "tconstruct:molten_cobalt", "tconstruct:flowing_molten_cobalt", "tconstruct:molten_steel", "tconstruct:flowing_molten_steel", "tconstruct:molten_slimesteel", "tconstruct:flowing_molten_slimesteel", "tconstruct:molten_amethyst_bronze", "tconstruct:flowing_molten_amethyst_bronze", "tconstruct:molten_rose_gold", "tconstruct:flowing_molten_rose_gold", "tconstruct:molten_pig_iron", "tconstruct:flowing_molten_pig_iron", "tconstruct:molten_manyullyn", "tconstruct:flowing_molten_manyullyn", "tconstruct:molten_hepatizon", "tconstruct:flowing_molten_hepatizon", "tconstruct:molten_cinderslime", "tconstruct:molten_queens_slime", "tconstruct:flowing_molten_queens_slime", "tconstruct:molten_soulsteel", "tconstruct:flowing_molten_soulsteel", "tconstruct:molten_netherite", "tconstruct:flowing_molten_netherite", "tconstruct:molten_knightslime", "tconstruct:flowing_molten_knightslime", "tconstruct:molten_tin", "tconstruct:flowing_molten_tin", "tconstruct:molten_aluminum", "tconstruct:flowing_molten_aluminum", "tconstruct:molten_lead", "tconstruct:flowing_molten_lead", "tconstruct:molten_silver", "tconstruct:flowing_molten_silver", "tconstruct:molten_nickel", "tconstruct:flowing_molten_nickel", "tconstruct:molten_zinc", "tconstruct:flowing_molten_zinc", "tconstruct:molten_platinum", "tconstruct:flowing_molten_platinum", "tconstruct:molten_tungsten", "tconstruct:flowing_molten_tungsten", "tconstruct:molten_osmium", "tconstruct:flowing_molten_osmium", "tconstruct:molten_uranium", "tconstruct:flowing_molten_uranium", "tconstruct:molten_bronze", "tconstruct:flowing_molten_bronze", "tconstruct:molten_brass", "tconstruct:flowing_molten_brass", "tconstruct:molten_electrum", "tconstruct:flowing_molten_electrum", "tconstruct:molten_invar", "tconstruct:flowing_molten_invar", "tconstruct:molten_constantan", "tconstruct:flowing_molten_constantan", "tconstruct:molten_pewter", "tconstruct:flowing_molten_pewter", "tconstruct:molten_enderium", "tconstruct:flowing_molten_enderium", "tconstruct:molten_lumium", "tconstruct:flowing_molten_lumium", "tconstruct:molten_signalum", "tconstruct:flowing_molten_signalum", "tconstruct:molten_refined_glowstone", "tconstruct:flowing_molten_refined_glowstone", "tconstruct:molten_refined_obsidian", "tconstruct:flowing_molten_refined_obsidian", "tconstruct:molten_nicrosil", "tconstruct:flowing_molten_nicrosil"]}, "typePotatoProjectileEntityHitAction": {"type": "string", "enum": ["create:set_on_fire", "create:food_effects", "create:suspicious_stew", "create:chorus_teleport", "create:cure_zombie_villager", "create:potion_effect"]}, "typeGlobalLootModifierSerializers": {"type": "string", "enum": ["tconstruct:modifier_hook", "immersiveengineering:hemp_seed_drops", "mantle:add_entry", "mantle:replace_item"]}, "typeFrogVariant": {"type": "string", "enum": ["minecraft:temperate", "minecraft:warm", "minecraft:cold"]}, "typeItemAttributeType": {"type": "string", "enum": ["create:renamed", "create:placeable", "create:astral_attunment", "create:consumable", "create:max_enchanted", "create:added_by", "create:in_item_group", "create:badly_damaged", "create:furnace_fuel", "create:shulker_fill_level", "create:blastable", "create:has_color", "create:compostable", "create:has_name", "create:washable", "create:enchanted", "create:damaged", "create:astral_amulet", "create:hauntable", "create:astral_crystal", "create:smeltable", "create:smokable", "create:astral_perk_gem", "create:has_fluid", "create:crushable", "create:fluid_container", "create:in_tag", "create:book_copy", "create:book_author", "create:not_stackable", "create:equipable", "create:has_enchant"]}, "typeFluidType": {"type": "string", "enum": ["aerlunerpg:acidfluid", "aerlunerpg:acidfluid_2", "minecraft:milk", "minecraft:empty", "minecraft:water", "minecraft:lava", "tconstruct:venom", "tconstruct:powdered_snow", "tconstruct:earth_slime", "tconstruct:sky_slime", "tconstruct:ender_slime", "tconstruct:magma", "tconstruct:ichor", "tconstruct:honey", "tconstruct:beetroot_soup", "tconstruct:mushroom_stew", "tconstruct:rabbit_stew", "tconstruct:meat_soup", "tconstruct:potion", "tconstruct:seared_stone", "tconstruct:scorched_stone", "tconstruct:molten_clay", "tconstruct:molten_glass", "tconstruct:liquid_soul", "tconstruct:molten_porcelain", "tconstruct:molten_obsidian", "tconstruct:molten_ender", "tconstruct:blazing_blood", "tconstruct:molten_emerald", "tconstruct:molten_quartz", "tconstruct:molten_amethyst", "tconstruct:molten_diamond", "tconstruct:molten_debris", "tconstruct:molten_iron", "tconstruct:molten_gold", "tconstruct:molten_copper", "tconstruct:molten_cobalt", "tconstruct:molten_steel", "tconstruct:molten_slimesteel", "tconstruct:molten_amethyst_bronze", "tconstruct:molten_rose_gold", "tconstruct:molten_pig_iron", "tconstruct:molten_manyullyn", "tconstruct:molten_hepatizon", "tconstruct:molten_cinderslime", "tconstruct:molten_queens_slime", "tconstruct:molten_soulsteel", "tconstruct:molten_netherite", "tconstruct:molten_knightslime", "tconstruct:molten_tin", "tconstruct:molten_aluminum", "tconstruct:molten_lead", "tconstruct:molten_silver", "tconstruct:molten_nickel", "tconstruct:molten_zinc", "tconstruct:molten_platinum", "tconstruct:molten_tungsten", "tconstruct:molten_osmium", "tconstruct:molten_uranium", "tconstruct:molten_bronze", "tconstruct:molten_brass", "tconstruct:molten_electrum", "tconstruct:molten_invar", "tconstruct:molten_constantan", "tconstruct:molten_pewter", "tconstruct:molten_enderium", "tconstruct:molten_lumium", "tconstruct:molten_signalum", "tconstruct:molten_refined_glowstone", "tconstruct:molten_refined_obsidian", "tconstruct:molten_nicrosil", "immersiveengineering:creosote", "immersiveengineering:plantoil", "immersiveengineering:ethanol", "immersiveengineering:biodiesel", "immersiveengineering:concrete", "immersiveengineering:herbicide", "immersiveengineering:redstone_acid", "immersiveengineering:potion", "immersiveengineering:acetaldehyde", "immersiveengineering:phenolic_resin", "create:potion", "create:tea", "create:honey", "create:chocolate", "mekanism:hydrogen", "mekanism:oxygen", "mekanism:chlorine", "mekanism:sulfur_dioxide", "mekanism:sulfur_trioxide", "mekanism:sulfuric_acid", "mekanism:hydrogen_chloride", "mekanism:hydrofluoric_acid", "mekanism:uranium_oxide", "mekanism:uranium_hexafluoride", "mekanism:ethene", "mekanism:sodium", "mekanism:superheated_sodium", "mekanism:brine", "mekanism:lithium", "mekanism:steam", "mekanism:heavy_water", "mekanism:nutritional_paste"]}, "typeBlock": {"type": "string", "enum": ["ae2:16k_crafting_storage", "ae2:1k_crafting_storage", "ae2:256k_crafting_storage", "ae2:4k_crafting_storage", "ae2:64k_crafting_storage", "ae2:cable_bus", "ae2:cell_workbench", "ae2:charger", "ae2:chest", "ae2:chipped_budding_quartz", "ae2:chiseled_quartz_block", "ae2:chiseled_quartz_slab", "ae2:chiseled_quartz_stairs", "ae2:chiseled_quartz_wall", "ae2:condenser", "ae2:controller", "ae2:crafting_accelerator", "ae2:crafting_monitor", "ae2:crafting_unit", "ae2:crank", "ae2:creative_energy_cell", "ae2:crystal_resonance_generator", "ae2:cut_quartz_block", "ae2:cut_quartz_slab", "ae2:cut_quartz_stairs", "ae2:cut_quartz_wall", "ae2:damaged_budding_quartz", "ae2:debug_chunk_loader", "ae2:debug_cube_gen", "ae2:debug_energy_gen", "ae2:debug_item_gen", "ae2:debug_phantom_node", "ae2:dense_energy_cell", "ae2:drive", "ae2:energy_acceptor", "ae2:energy_cell", "ae2:flawed_budding_quartz", "ae2:flawless_budding_quartz", "ae2:fluix_block", "ae2:fluix_slab", "ae2:fluix_stairs", "ae2:fluix_wall", "ae2:growth_accelerator", "ae2:inscriber", "ae2:interface", "ae2:io_port", "ae2:large_quartz_bud", "ae2:light_detector", "ae2:matrix_frame", "ae2:medium_quartz_bud", "ae2:molecular_assembler", "ae2:mysterious_cube", "ae2:not_so_mysterious_cube", "ae2:paint", "ae2:pattern_provider", "ae2:quantum_link", "ae2:quantum_ring", "ae2:quartz_block", "ae2:quartz_brick_slab", "ae2:quartz_brick_stairs", "ae2:quartz_brick_wall", "ae2:quartz_bricks", "ae2:quartz_cluster", "ae2:quartz_fixture", "ae2:quartz_glass", "ae2:quartz_pillar", "ae2:quartz_pillar_slab", "ae2:quartz_pillar_stairs", "ae2:quartz_pillar_wall", "ae2:quartz_slab", "ae2:quartz_stairs", "ae2:quartz_vibrant_glass", "ae2:quartz_wall", "ae2:sky_stone_block", "ae2:sky_stone_brick", "ae2:sky_stone_brick_slab", "ae2:sky_stone_brick_stairs", "ae2:sky_stone_brick_wall", "ae2:sky_stone_chest", "ae2:sky_stone_slab", "ae2:sky_stone_small_brick", "ae2:sky_stone_small_brick_slab", "ae2:sky_stone_small_brick_stairs", "ae2:sky_stone_small_brick_wall", "ae2:sky_stone_stairs", "ae2:sky_stone_tank", "ae2:sky_stone_wall", "ae2:small_quartz_bud", "ae2:smooth_quartz_block", "ae2:smooth_quartz_slab", "ae2:smooth_quartz_stairs", "ae2:smooth_quartz_wall", "ae2:smooth_sky_stone_block", "ae2:smooth_sky_stone_chest", "ae2:smooth_sky_stone_slab", "ae2:smooth_sky_stone_stairs", "ae2:smooth_sky_stone_wall", "ae2:spatial_anchor", "ae2:spatial_io_port", "ae2:spatial_pylon", "ae2:tiny_tnt", "ae2:vibration_chamber", "ae2:wireless_access_point", "aerlunerpg:acidfluid", "aerlunerpg:acidfluid_2", "aerlunerpg:bite_1", "aerlunerpg:bushsmall", "aerlunerpg:candyctickbig", "aerlunerpg:candysblock", "aerlunerpg:candysblock_2", "aerlunerpg:candysp", "aerlunerpg:candysp_2", "aerlunerpg:candysp_3", "aerlunerpg:candystick", "aerlunerpg:capacitorofsouls", "aerlunerpg:capacitorsouls", "aerlunerpg:caramelore", "aerlunerpg:chestbig", "aerlunerpg:chestbig_2", "aerlunerpg:chestbigwood", "aerlunerpg:chestcandy", "aerlunerpg:chocolate", "aerlunerpg:chocolates", "aerlunerpg:creatorvoid", "aerlunerpg:crowdblock", "aerlunerpg:crowdd_portal", "aerlunerpg:crystalsattack", "aerlunerpg:crystalsmithg", "aerlunerpg:crystalsmithril", "aerlunerpg:crystalsvoid", "aerlunerpg:dirt", "aerlunerpg:entblock", "aerlunerpg:furnacesmall", "aerlunerpg:furnacev", "aerlunerpg:gingerblock", "aerlunerpg:gingerblock_2", "aerlunerpg:gingerblock_3", "aerlunerpg:gingerblocks", "aerlunerpg:gnomehome", "aerlunerpg:grasscold", "aerlunerpg:grasssnow", "aerlunerpg:holespawn_1", "aerlunerpg:iced_portal", "aerlunerpg:icerion", "aerlunerpg:mine", "aerlunerpg:mushroombrothblock", "aerlunerpg:oak", "aerlunerpg:oak_2", "aerlunerpg:oak_3", "aerlunerpg:oak_4", "aerlunerpg:oak_5", "aerlunerpg:oldmantrade", "aerlunerpg:owlarmorblock", "aerlunerpg:pipeblock", "aerlunerpg:rabbithole", "aerlunerpg:shockwave", "aerlunerpg:smallbat", "aerlunerpg:smalltreeblock_1", "aerlunerpg:smalltreeblock_2", "aerlunerpg:snow_2", "aerlunerpg:snowdrop", "aerlunerpg:squirrelblock", "aerlunerpg:stonesmall", "aerlunerpg:stump_1", "aerlunerpg:stunning", "aerlunerpg:sugar", "aerlunerpg:toilet", "aerlunerpg:toilet_2", "aerlunerpg:treesmall", "bigger_ae2:1024_core_crafting_accelerator", "bigger_ae2:16_core_crafting_accelerator", "bigger_ae2:256_core_crafting_accelerator", "bigger_ae2:4_core_crafting_accelerator", "bigger_ae2:64_core_crafting_accelerator", "cataclysm:abyssal_egg", "cataclysm:altar_of_abyss", "cataclysm:altar_of_amethyst", "cataclysm:altar_of_fire", "cataclysm:altar_of_void", "cataclysm:ancient_metal_block", "cataclysm:aptrgangr_head", "cataclysm:aptrgangr_wall_head", "cataclysm:black_steel_block", "cataclysm:black_steel_fence", "cataclysm:black_steel_wall", "cataclysm:blackstone_pillar", "cataclysm:chiseled_end_stone_bricks", "cataclysm:chiseled_obsidian_bricks", "cataclysm:chiseled_purpur_block", "cataclysm:chiseled_stone_brick_pillar", "cataclysm:chorus_fence", "cataclysm:chorus_planks", "cataclysm:chorus_slab", "cataclysm:chorus_stairs", "cataclysm:chorus_stem", "cataclysm:cursed_tombstone", "cataclysm:cursium_block", "cataclysm:door_of_seal", "cataclysm:door_of_seal_part", "cataclysm:draugr_head", "cataclysm:draugr_wall_head", "cataclysm:dungeon_block", "cataclysm:emp", "cataclysm:end_stone_pillar", "cataclysm:end_stone_teleport_trap_bricks", "cataclysm:enderite_block", "cataclysm:frosted_stone_brick_slab", "cataclysm:frosted_stone_brick_stairs", "cataclysm:frosted_stone_brick_wall", "cataclysm:frosted_stone_bricks", "cataclysm:ignitium_block", "cataclysm:kobolediator_skull", "cataclysm:kobolediator_wall_skull", "cataclysm:mechanical_fusion_anvil", "cataclysm:melting_netherrack", "cataclysm:obsidian_brick_slab", "cataclysm:obsidian_brick_stairs", "cataclysm:obsidian_brick_wall", "cataclysm:obsidian_bricks", "cataclysm:obsidian_explosion_trap_bricks", "cataclysm:pointed_icicle", "cataclysm:polished_end_stone", "cataclysm:polished_end_stone_slab", "cataclysm:polished_end_stone_stairs", "cataclysm:polished_sandstone", "cataclysm:purpur_void_rune_trap_block", "cataclysm:purpur_wall", "cataclysm:quartz_brick_wall", "cataclysm:sandstone_falling_trap", "cataclysm:sandstone_ignite_trap", "cataclysm:sandstone_poison_dart_trap", "cataclysm:stone_pillar", "cataclysm:stone_tile_slab", "cataclysm:stone_tile_stairs", "cataclysm:stone_tile_wall", "cataclysm:stone_tiles", "cataclysm:void_infused_end_stone_bricks", "cataclysm:void_lantern_block", "cataclysm:void_stone", "cataclysm:witherite_block", "create:acacia_window", "create:acacia_window_pane", "create:adjustable_chain_gearshift", "create:analog_lever", "create:andesite_alloy_block", "create:andesite_bars", "create:andesite_belt_funnel", "create:andesite_casing", "create:andesite_door", "create:andesite_encased_cogwheel", "create:andesite_encased_large_cogwheel", "create:andesite_encased_shaft", "create:andesite_funnel", "create:andesite_ladder", "create:andesite_pillar", "create:andesite_scaffolding", "create:andesite_tunnel", "create:asurine", "create:asurine_pillar", "create:basin", "create:belt", "create:birch_window", "create:birch_window_pane", "create:black_nixie_tube", "create:black_sail", "create:black_seat", "create:black_toolbox", "create:black_valve_handle", "create:blaze_burner", "create:blue_nixie_tube", "create:blue_sail", "create:blue_seat", "create:blue_toolbox", "create:blue_valve_handle", "create:brass_bars", "create:brass_belt_funnel", "create:brass_block", "create:brass_casing", "create:brass_door", "create:brass_encased_cogwheel", "create:brass_encased_large_cogwheel", "create:brass_encased_shaft", "create:brass_funnel", "create:brass_ladder", "create:brass_scaffolding", "create:brass_tunnel", "create:brown_nixie_tube", "create:brown_sail", "create:brown_seat", "create:brown_toolbox", "create:brown_valve_handle", "create:calcite_pillar", "create:cart_assembler", "create:chocolate", "create:chute", "create:clipboard", "create:clockwork_bearing", "create:clutch", "create:cogwheel", "create:content_observer", "create:contraption_controls", "create:controller_rail", "create:controls", "create:copper_backtank", "create:copper_bars", "create:copper_casing", "create:copper_door", "create:copper_ladder", "create:copper_scaffolding", "create:copper_shingle_slab", "create:copper_shingle_stairs", "create:copper_shingles", "create:copper_tile_slab", "create:copper_tile_stairs", "create:copper_tiles", "create:copper_valve_handle", "create:copycat_bars", "create:copycat_base", "create:copycat_panel", "create:copycat_step", "create:creative_crate", "create:creative_fluid_tank", "create:creative_motor", "create:crimsite", "create:crimsite_pillar", "create:crimson_window", "create:crimson_window_pane", "create:crushing_wheel", "create:crushing_wheel_controller", "create:cuckoo_clock", "create:cut_andesite", "create:cut_andesite_brick_slab", "create:cut_andesite_brick_stairs", "create:cut_andesite_brick_wall", "create:cut_andesite_bricks", "create:cut_andesite_slab", "create:cut_andesite_stairs", "create:cut_andesite_wall", "create:cut_asurine", "create:cut_asurine_brick_slab", "create:cut_asurine_brick_stairs", "create:cut_asurine_brick_wall", "create:cut_asurine_bricks", "create:cut_asurine_slab", "create:cut_asurine_stairs", "create:cut_asurine_wall", "create:cut_calcite", "create:cut_calcite_brick_slab", "create:cut_calcite_brick_stairs", "create:cut_calcite_brick_wall", "create:cut_calcite_bricks", "create:cut_calcite_slab", "create:cut_calcite_stairs", "create:cut_calcite_wall", "create:cut_crimsite", "create:cut_crimsite_brick_slab", "create:cut_crimsite_brick_stairs", "create:cut_crimsite_brick_wall", "create:cut_crimsite_bricks", "create:cut_crimsite_slab", "create:cut_crimsite_stairs", "create:cut_crimsite_wall", "create:cut_deepslate", "create:cut_deepslate_brick_slab", "create:cut_deepslate_brick_stairs", "create:cut_deepslate_brick_wall", "create:cut_deepslate_bricks", "create:cut_deepslate_slab", "create:cut_deepslate_stairs", "create:cut_deepslate_wall", "create:cut_diorite", "create:cut_diorite_brick_slab", "create:cut_diorite_brick_stairs", "create:cut_diorite_brick_wall", "create:cut_diorite_bricks", "create:cut_diorite_slab", "create:cut_diorite_stairs", "create:cut_diorite_wall", "create:cut_dripstone", "create:cut_dripstone_brick_slab", "create:cut_dripstone_brick_stairs", "create:cut_dripstone_brick_wall", "create:cut_dripstone_bricks", "create:cut_dripstone_slab", "create:cut_dripstone_stairs", "create:cut_dripstone_wall", "create:cut_granite", "create:cut_granite_brick_slab", "create:cut_granite_brick_stairs", "create:cut_granite_brick_wall", "create:cut_granite_bricks", "create:cut_granite_slab", "create:cut_granite_stairs", "create:cut_granite_wall", "create:cut_limestone", "create:cut_limestone_brick_slab", "create:cut_limestone_brick_stairs", "create:cut_limestone_brick_wall", "create:cut_limestone_bricks", "create:cut_limestone_slab", "create:cut_limestone_stairs", "create:cut_limestone_wall", "create:cut_ochrum", "create:cut_ochrum_brick_slab", "create:cut_ochrum_brick_stairs", "create:cut_ochrum_brick_wall", "create:cut_ochrum_bricks", "create:cut_ochrum_slab", "create:cut_ochrum_stairs", "create:cut_ochrum_wall", "create:cut_scorchia", "create:cut_scorchia_brick_slab", "create:cut_scorchia_brick_stairs", "create:cut_scorchia_brick_wall", "create:cut_scorchia_bricks", "create:cut_scorchia_slab", "create:cut_scorchia_stairs", "create:cut_scorchia_wall", "create:cut_scoria", "create:cut_scoria_brick_slab", "create:cut_scoria_brick_stairs", "create:cut_scoria_brick_wall", "create:cut_scoria_bricks", "create:cut_scoria_slab", "create:cut_scoria_stairs", "create:cut_scoria_wall", "create:cut_tuff", "create:cut_tuff_brick_slab", "create:cut_tuff_brick_stairs", "create:cut_tuff_brick_wall", "create:cut_tuff_bricks", "create:cut_tuff_slab", "create:cut_tuff_stairs", "create:cut_tuff_wall", "create:cut_veridium", "create:cut_veridium_brick_slab", "create:cut_veridium_brick_stairs", "create:cut_veridium_brick_wall", "create:cut_veridium_bricks", "create:cut_veridium_slab", "create:cut_veridium_stairs", "create:cut_veridium_wall", "create:cyan_nixie_tube", "create:cyan_sail", "create:cyan_seat", "create:cyan_toolbox", "create:cyan_valve_handle", "create:dark_oak_window", "create:dark_oak_window_pane", "create:deepslate_pillar", "create:deepslate_zinc_ore", "create:deployer", "create:depot", "create:diorite_pillar", "create:display_board", "create:display_link", "create:dripstone_pillar", "create:elevator_contact", "create:elevator_pulley", "create:encased_chain_drive", "create:encased_fan", "create:encased_fluid_pipe", "create:experience_block", "create:exposed_copper_shingle_slab", "create:exposed_copper_shingle_stairs", "create:exposed_copper_shingles", "create:exposed_copper_tile_slab", "create:exposed_copper_tile_stairs", "create:exposed_copper_tiles", "create:fake_track", "create:fluid_pipe", "create:fluid_tank", "create:fluid_valve", "create:flywheel", "create:framed_glass", "create:framed_glass_door", "create:framed_glass_pane", "create:framed_glass_trapdoor", "create:gantry_carriage", "create:gantry_shaft", "create:gearbox", "create:gearshift", "create:glass_fluid_pipe", "create:granite_pillar", "create:gray_nixie_tube", "create:gray_sail", "create:gray_seat", "create:gray_toolbox", "create:gray_valve_handle", "create:green_nixie_tube", "create:green_sail", "create:green_seat", "create:green_toolbox", "create:green_valve_handle", "create:hand_crank", "create:haunted_bell", "create:honey", "create:horizontal_framed_glass", "create:horizontal_framed_glass_pane", "create:hose_pulley", "create:industrial_iron_block", "create:item_drain", "create:item_vault", "create:jungle_window", "create:jungle_window_pane", "create:large_bogey", "create:large_cogwheel", "create:large_water_wheel", "create:layered_andesite", "create:layered_asurine", "create:layered_calcite", "create:layered_crimsite", "create:layered_deepslate", "create:layered_diorite", "create:layered_dripstone", "create:layered_granite", "create:layered_limestone", "create:layered_ochrum", "create:layered_scorchia", "create:layered_scoria", "create:layered_tuff", "create:layered_veridium", "create:lectern_controller", "create:light_blue_nixie_tube", "create:light_blue_sail", "create:light_blue_seat", "create:light_blue_toolbox", "create:light_blue_valve_handle", "create:light_gray_nixie_tube", "create:light_gray_sail", "create:light_gray_seat", "create:light_gray_toolbox", "create:light_gray_valve_handle", "create:lime_nixie_tube", "create:lime_sail", "create:lime_seat", "create:lime_toolbox", "create:lime_valve_handle", "create:limestone", "create:limestone_pillar", "create:linear_chassis", "create:lit_blaze_burner", "create:magenta_nixie_tube", "create:magenta_sail", "create:magenta_seat", "create:magenta_toolbox", "create:magenta_valve_handle", "create:mangrove_window", "create:mangrove_window_pane", "create:mechanical_arm", "create:mechanical_bearing", "create:mechanical_crafter", "create:mechanical_drill", "create:mechanical_harvester", "create:mechanical_mixer", "create:mechanical_piston", "create:mechanical_piston_head", "create:mechanical_plough", "create:mechanical_press", "create:mechanical_pump", "create:mechanical_roller", "create:mechanical_saw", "create:metal_bracket", "create:metal_girder", "create:metal_girder_encased_shaft", "create:millstone", "create:minecart_anchor", "create:mysterious_cuckoo_clock", "create:netherite_backtank", "create:nixie_tube", "create:nozzle", "create:oak_window", "create:oak_window_pane", "create:ochrum", "create:ochrum_pillar", "create:orange_sail", "create:orange_seat", "create:orange_toolbox", "create:orange_valve_handle", "create:ornate_iron_window", "create:ornate_iron_window_pane", "create:oxidized_copper_shingle_slab", "create:oxidized_copper_shingle_stairs", "create:oxidized_copper_shingles", "create:oxidized_copper_tile_slab", "create:oxidized_copper_tile_stairs", "create:oxidized_copper_tiles", "create:peculiar_bell", "create:pink_nixie_tube", "create:pink_sail", "create:pink_seat", "create:pink_toolbox", "create:pink_valve_handle", "create:piston_extension_pole", "create:placard", "create:polished_cut_andesite", "create:polished_cut_andesite_slab", "create:polished_cut_andesite_stairs", "create:polished_cut_andesite_wall", "create:polished_cut_asurine", "create:polished_cut_asurine_slab", "create:polished_cut_asurine_stairs", "create:polished_cut_asurine_wall", "create:polished_cut_calcite", "create:polished_cut_calcite_slab", "create:polished_cut_calcite_stairs", "create:polished_cut_calcite_wall", "create:polished_cut_crimsite", "create:polished_cut_crimsite_slab", "create:polished_cut_crimsite_stairs", "create:polished_cut_crimsite_wall", "create:polished_cut_deepslate", "create:polished_cut_deepslate_slab", "create:polished_cut_deepslate_stairs", "create:polished_cut_deepslate_wall", "create:polished_cut_diorite", "create:polished_cut_diorite_slab", "create:polished_cut_diorite_stairs", "create:polished_cut_diorite_wall", "create:polished_cut_dripstone", "create:polished_cut_dripstone_slab", "create:polished_cut_dripstone_stairs", "create:polished_cut_dripstone_wall", "create:polished_cut_granite", "create:polished_cut_granite_slab", "create:polished_cut_granite_stairs", "create:polished_cut_granite_wall", "create:polished_cut_limestone", "create:polished_cut_limestone_slab", "create:polished_cut_limestone_stairs", "create:polished_cut_limestone_wall", "create:polished_cut_ochrum", "create:polished_cut_ochrum_slab", "create:polished_cut_ochrum_stairs", "create:polished_cut_ochrum_wall", "create:polished_cut_scorchia", "create:polished_cut_scorchia_slab", "create:polished_cut_scorchia_stairs", "create:polished_cut_scorchia_wall", "create:polished_cut_scoria", "create:polished_cut_scoria_slab", "create:polished_cut_scoria_stairs", "create:polished_cut_scoria_wall", "create:polished_cut_tuff", "create:polished_cut_tuff_slab", "create:polished_cut_tuff_stairs", "create:polished_cut_tuff_wall", "create:polished_cut_veridium", "create:polished_cut_veridium_slab", "create:polished_cut_veridium_stairs", "create:polished_cut_veridium_wall", "create:portable_fluid_interface", "create:portable_storage_interface", "create:powered_latch", "create:powered_shaft", "create:powered_toggle_latch", "create:pulley_magnet", "create:pulse_extender", "create:pulse_repeater", "create:purple_nixie_tube", "create:purple_sail", "create:purple_seat", "create:purple_toolbox", "create:purple_valve_handle", "create:radial_chassis", "create:railway_casing", "create:raw_zinc_block", "create:red_nixie_tube", "create:red_sail", "create:red_seat", "create:red_toolbox", "create:red_valve_handle", "create:redstone_contact", "create:redstone_link", "create:refined_radiance_casing", "create:rope", "create:rope_pulley", "create:rose_quartz_block", "create:rose_quartz_lamp", "create:rose_quartz_tiles", "create:rotation_speed_controller", "create:sail_frame", "create:schematic_table", "create:schematic<PERSON><PERSON>", "create:scorchia", "create:scorchia_pillar", "create:scoria", "create:scoria_pillar", "create:secondary_linear_chassis", "create:sequenced_gearshift", "create:shadow_steel_casing", "create:shaft", "create:small_andesite_brick_slab", "create:small_andesite_brick_stairs", "create:small_andesite_brick_wall", "create:small_andesite_bricks", "create:small_asurine_brick_slab", "create:small_asurine_brick_stairs", "create:small_asurine_brick_wall", "create:small_asurine_bricks", "create:small_bogey", "create:small_calcite_brick_slab", "create:small_calcite_brick_stairs", "create:small_calcite_brick_wall", "create:small_calcite_bricks", "create:small_crimsite_brick_slab", "create:small_crimsite_brick_stairs", "create:small_crimsite_brick_wall", "create:small_crimsite_bricks", "create:small_deepslate_brick_slab", "create:small_deepslate_brick_stairs", "create:small_deepslate_brick_wall", "create:small_deepslate_bricks", "create:small_diorite_brick_slab", "create:small_diorite_brick_stairs", "create:small_diorite_brick_wall", "create:small_diorite_bricks", "create:small_dripstone_brick_slab", "create:small_dripstone_brick_stairs", "create:small_dripstone_brick_wall", "create:small_dripstone_bricks", "create:small_granite_brick_slab", "create:small_granite_brick_stairs", "create:small_granite_brick_wall", "create:small_granite_bricks", "create:small_limestone_brick_slab", "create:small_limestone_brick_stairs", "create:small_limestone_brick_wall", "create:small_limestone_bricks", "create:small_ochrum_brick_slab", "create:small_ochrum_brick_stairs", "create:small_ochrum_brick_wall", "create:small_ochrum_bricks", "create:small_rose_quartz_tiles", "create:small_scorchia_brick_slab", "create:small_scorchia_brick_stairs", "create:small_scorchia_brick_wall", "create:small_scorchia_bricks", "create:small_scoria_brick_slab", "create:small_scoria_brick_stairs", "create:small_scoria_brick_wall", "create:small_scoria_bricks", "create:small_tuff_brick_slab", "create:small_tuff_brick_stairs", "create:small_tuff_brick_wall", "create:small_tuff_bricks", "create:small_veridium_brick_slab", "create:small_veridium_brick_stairs", "create:small_veridium_brick_wall", "create:small_veridium_bricks", "create:smart_chute", "create:smart_fluid_pipe", "create:speedometer", "create:spout", "create:spruce_window", "create:spruce_window_pane", "create:steam_engine", "create:steam_whistle", "create:steam_whistle_extension", "create:sticker", "create:sticky_mechanical_piston", "create:stockpile_switch", "create:stressometer", "create:tiled_glass", "create:tiled_glass_pane", "create:track", "create:track_observer", "create:track_signal", "create:track_station", "create:train_door", "create:train_trapdoor", "create:tuff_pillar", "create:turntable", "create:veridium", "create:veridium_pillar", "create:vertical_framed_glass", "create:vertical_framed_glass_pane", "create:warped_window", "create:warped_window_pane", "create:water_wheel", "create:water_wheel_structure", "create:waxed_copper_shingle_slab", "create:waxed_copper_shingle_stairs", "create:waxed_copper_shingles", "create:waxed_copper_tile_slab", "create:waxed_copper_tile_stairs", "create:waxed_copper_tiles", "create:waxed_exposed_copper_shingle_slab", "create:waxed_exposed_copper_shingle_stairs", "create:waxed_exposed_copper_shingles", "create:waxed_exposed_copper_tile_slab", "create:waxed_exposed_copper_tile_stairs", "create:waxed_exposed_copper_tiles", "create:waxed_oxidized_copper_shingle_slab", "create:waxed_oxidized_copper_shingle_stairs", "create:waxed_oxidized_copper_shingles", "create:waxed_oxidized_copper_tile_slab", "create:waxed_oxidized_copper_tile_stairs", "create:waxed_oxidized_copper_tiles", "create:waxed_weathered_copper_shingle_slab", "create:waxed_weathered_copper_shingle_stairs", "create:waxed_weathered_copper_shingles", "create:waxed_weathered_copper_tile_slab", "create:waxed_weathered_copper_tile_stairs", "create:waxed_weathered_copper_tiles", "create:weathered_copper_shingle_slab", "create:weathered_copper_shingle_stairs", "create:weathered_copper_shingles", "create:weathered_copper_tile_slab", "create:weathered_copper_tile_stairs", "create:weathered_copper_tiles", "create:weighted_ejector", "create:white_nixie_tube", "create:white_sail", "create:white_seat", "create:white_toolbox", "create:white_valve_handle", "create:windmill_bearing", "create:wooden_bracket", "create:yellow_nixie_tube", "create:yellow_sail", "create:yellow_seat", "create:yellow_toolbox", "create:yellow_valve_handle", "create:zinc_block", "create:zinc_ore", "createoreexcavation:drilling_machine", "createoreexcavation:extractor", "createoreexcavation:io_block", "createoreexcavation:kinetic_input", "createoreexcavation:multiblock", "createoreexcavation:sample_drill", "ftbquests:aux_task_screen", "ftbquests:barrier", "ftbquests:detector", "ftbquests:loot_crate_opener", "ftbquests:screen_1", "ftbquests:screen_3", "ftbquests:screen_5", "ftbquests:screen_7", "ftbquests:stage_barrier", "immersiveengineering:acetaldehyde_fluid_block", "immersiveengineering:advanced_blast_furnace", "immersiveengineering:alloy_smelter", "immersiveengineering:alloybrick", "immersiveengineering:alu_fence", "immersiveengineering:alu_post", "immersiveengineering:alu_scaffolding_grate_top", "immersiveengineering:alu_scaffolding_standard", "immersiveengineering:alu_scaffolding_wooden_top", "immersiveengineering:alu_slope", "immersiveengineering:alu_wallmount", "immersiveengineering:arc_furnace", "immersiveengineering:assembler", "immersiveengineering:auto_workbench", "immersiveengineering:balloon", "immersiveengineering:biodiesel_fluid_block", "immersiveengineering:blast_furnace", "immersiveengineering:blastbrick", "immersiveengineering:blastbrick_reinforced", "immersiveengineering:blastfurnace_preheater", "immersiveengineering:bottling_machine", "immersiveengineering:breaker_switch", "immersiveengineering:bucket_wheel", "immersiveengineering:capacitor_creative", "immersiveengineering:capacitor_hv", "immersiveengineering:capacitor_lv", "immersiveengineering:capacitor_mv", "immersiveengineering:charging_station", "immersiveengineering:chute_aluminum", "immersiveengineering:chute_copper", "immersiveengineering:chute_iron", "immersiveengineering:chute_steel", "immersiveengineering:circuit_table", "immersiveengineering:clinker_brick", "immersiveengineering:clinker_brick_quoin", "immersiveengineering:clinker_brick_sill", "immersiveengineering:cloche", "immersiveengineering:coil_hv", "immersiveengineering:coil_lv", "immersiveengineering:coil_mv", "immersiveengineering:coke", "immersiveengineering:coke_oven", "immersiveengineering:cokebrick", "immersiveengineering:concrete", "immersiveengineering:concrete_brick", "immersiveengineering:concrete_brick_cracked", "immersiveengineering:concrete_chiseled", "immersiveengineering:concrete_fluid_block", "immersiveengineering:concrete_leaded", "immersiveengineering:concrete_pillar", "immersiveengineering:concrete_quarter", "immersiveengineering:concrete_sheet", "immersiveengineering:concrete_sprayed", "immersiveengineering:concrete_three_quarter", "immersiveengineering:concrete_tile", "immersiveengineering:connector_bundled", "immersiveengineering:connector_hv", "immersiveengineering:connector_hv_relay", "immersiveengineering:connector_lv", "immersiveengineering:connector_lv_relay", "immersiveengineering:connector_mv", "immersiveengineering:connector_mv_relay", "immersiveengineering:connector_probe", "immersiveengineering:connector_redstone", "immersiveengineering:connector_structural", "immersiveengineering:conveyor_basic", "immersiveengineering:conveyor_dropper", "immersiveengineering:conveyor_extract", "immersiveengineering:conveyor_redstone", "immersiveengineering:conveyor_splitter", "immersiveengineering:conveyor_vertical", "immersiveengineering:coresample", "immersiveengineering:craftingtable", "immersiveengineering:crate", "immersiveengineering:creosote_fluid_block", "immersiveengineering:crusher", "immersiveengineering:current_transformer", "immersiveengineering:cushion", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:deepslate_ore_lead", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:deepslate_ore_uranium", "immersiveengineering:diesel_generator", "immersiveengineering:duroplast", "immersiveengineering:dynamo", "immersiveengineering:electric_lantern", "immersiveengineering:electromagnet", "immersiveengineering:ethanol_fluid_block", "immersiveengineering:excavator", "immersiveengineering:fake_light", "immersiveengineering:feedthrough", "immersiveengineering:fermenter", "immersiveengineering:fiberboard", "immersiveengineering:floodlight", "immersiveengineering:fluid_pipe", "immersiveengineering:fluid_placer", "immersiveengineering:fluid_pump", "immersiveengineering:fluid_sorter", "immersiveengineering:furnace_heater", "immersiveengineering:generator", "immersiveengineering:grit_sand", "immersiveengineering:gunpowder_barrel", "immersiveengineering:heavy_engineering", "immersiveengineering:hemp", "immersiveengineering:hempcrete", "immersiveengineering:hempcrete_brick", "immersiveengineering:hempcrete_brick_cracked", "immersiveengineering:hempcrete_chiseled", "immersiveengineering:hempcrete_pillar", "immersiveengineering:herbicide_fluid_block", "immersiveengineering:insulating_glass", "immersiveengineering:item_batcher", "immersiveengineering:lantern", "immersiveengineering:light_engineering", "immersiveengineering:lightning_rod", "immersiveengineering:logic_unit", "immersiveengineering:metal_barrel", "immersiveengineering:metal_ladder_alu", "immersiveengineering:metal_ladder_none", "immersiveengineering:metal_ladder_steel", "immersiveengineering:metal_press", "immersiveengineering:mixer", "immersiveengineering:ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:ore_nickel", "immersiveengineering:ore_silver", "immersiveengineering:ore_uranium", "immersiveengineering:phenolic_resin_fluid_block", "immersiveengineering:plantoil_fluid_block", "immersiveengineering:post_transformer", "immersiveengineering:potted_hemp", "immersiveengineering:radiator", "immersiveengineering:raw_block_aluminum", "immersiveengineering:raw_block_lead", "immersiveengineering:raw_block_nickel", "immersiveengineering:raw_block_silver", "immersiveengineering:raw_block_uranium", "immersiveengineering:razor_wire", "immersiveengineering:redstone_acid_fluid_block", "immersiveengineering:redstone_breaker", "immersiveengineering:refinery", "immersiveengineering:reinforced_crate", "immersiveengineering:rs_engineering", "immersiveengineering:sample_drill", "immersiveengineering:sawdust", "immersiveengineering:sawmill", "immersiveengineering:shader_banner", "immersiveengineering:shader_banner_wall", "immersiveengineering:sheetmetal_aluminum", "immersiveengineering:sheetmetal_colored_black", "immersiveengineering:sheetmetal_colored_blue", "immersiveengineering:sheetmetal_colored_brown", "immersiveengineering:sheetmetal_colored_cyan", "immersiveengineering:sheetmetal_colored_gray", "immersiveengineering:sheetmetal_colored_green", "immersiveengineering:sheetmetal_colored_light_blue", "immersiveengineering:sheetmetal_colored_light_gray", "immersiveengineering:sheetmetal_colored_lime", "immersiveengineering:sheetmetal_colored_magenta", "immersiveengineering:sheetmetal_colored_orange", "immersiveengineering:sheetmetal_colored_pink", "immersiveengineering:sheetmetal_colored_purple", "immersiveengineering:sheetmetal_colored_red", "immersiveengineering:sheetmetal_colored_white", "immersiveengineering:sheetmetal_colored_yellow", "immersiveengineering:sheetmetal_constantan", "immersiveengineering:sheetmetal_copper", "immersiveengineering:sheetmetal_electrum", "immersiveengineering:sheetmetal_gold", "immersiveengineering:sheetmetal_iron", "immersiveengineering:sheetmetal_lead", "immersiveengineering:sheetmetal_nickel", "immersiveengineering:sheetmetal_silver", "immersiveengineering:sheetmetal_steel", "immersiveengineering:sheetmetal_uranium", "immersiveengineering:silo", "immersiveengineering:slab_alloybrick", "immersiveengineering:slab_alu_scaffolding_grate_top", "immersiveengineering:slab_alu_scaffolding_standard", "immersiveengineering:slab_alu_scaffolding_wooden_top", "immersiveengineering:slab_blastbrick", "immersiveengineering:slab_blastbrick_reinforced", "immersiveengineering:slab_clinker_brick", "immersiveengineering:slab_coke", "immersiveengineering:slab_cokebrick", "immersiveengineering:slab_concrete", "immersiveengineering:slab_concrete_brick", "immersiveengineering:slab_concrete_leaded", "immersiveengineering:slab_concrete_tile", "immersiveengineering:slab_hempcrete", "immersiveengineering:slab_hempcrete_brick", "immersiveengineering:slab_insulating_glass", "immersiveengineering:slab_sheetmetal_aluminum", "immersiveengineering:slab_sheetmetal_colored_black", "immersiveengineering:slab_sheetmetal_colored_blue", "immersiveengineering:slab_sheetmetal_colored_brown", "immersiveengineering:slab_sheetmetal_colored_cyan", "immersiveengineering:slab_sheetmetal_colored_gray", "immersiveengineering:slab_sheetmetal_colored_green", "immersiveengineering:slab_sheetmetal_colored_light_blue", "immersiveengineering:slab_sheetmetal_colored_light_gray", "immersiveengineering:slab_sheetmetal_colored_lime", "immersiveengineering:slab_sheetmetal_colored_magenta", "immersiveengineering:slab_sheetmetal_colored_orange", "immersiveengineering:slab_sheetmetal_colored_pink", "immersiveengineering:slab_sheetmetal_colored_purple", "immersiveengineering:slab_sheetmetal_colored_red", "immersiveengineering:slab_sheetmetal_colored_white", "immersiveengineering:slab_sheetmetal_colored_yellow", "immersiveengineering:slab_sheetmetal_constantan", "immersiveengineering:slab_sheetmetal_copper", "immersiveengineering:slab_sheetmetal_electrum", "immersiveengineering:slab_sheetmetal_gold", "immersiveengineering:slab_sheetmetal_iron", "immersiveengineering:slab_sheetmetal_lead", "immersiveengineering:slab_sheetmetal_nickel", "immersiveengineering:slab_sheetmetal_silver", "immersiveengineering:slab_sheetmetal_steel", "immersiveengineering:slab_sheetmetal_uranium", "immersiveengineering:slab_slag_brick", "immersiveengineering:slab_steel_scaffolding_grate_top", "immersiveengineering:slab_steel_scaffolding_standard", "immersiveengineering:slab_steel_scaffolding_wooden_top", "immersiveengineering:slab_storage_aluminum", "immersiveengineering:slab_storage_constantan", "immersiveengineering:slab_storage_electrum", "immersiveengineering:slab_storage_lead", "immersiveengineering:slab_storage_nickel", "immersiveengineering:slab_storage_silver", "immersiveengineering:slab_storage_steel", "immersiveengineering:slab_storage_uranium", "immersiveengineering:slab_treated_wood_horizontal", "immersiveengineering:slab_treated_wood_packaged", "immersiveengineering:slab_treated_wood_vertical", "immersiveengineering:slag_brick", "immersiveengineering:slag_glass", "immersiveengineering:slag_gravel", "immersiveengineering:sorter", "immersiveengineering:squeezer", "immersiveengineering:stairs_alu_scaffolding_grate_top", "immersiveengineering:stairs_alu_scaffolding_standard", "immersiveengineering:stairs_alu_scaffolding_wooden_top", "immersiveengineering:stairs_clinker_brick", "immersiveengineering:stairs_concrete", "immersiveengineering:stairs_concrete_brick", "immersiveengineering:stairs_concrete_leaded", "immersiveengineering:stairs_concrete_tile", "immersiveengineering:stairs_hempcrete", "immersiveengineering:stairs_hempcrete_brick", "immersiveengineering:stairs_slag_brick", "immersiveengineering:stairs_steel_scaffolding_grate_top", "immersiveengineering:stairs_steel_scaffolding_standard", "immersiveengineering:stairs_steel_scaffolding_wooden_top", "immersiveengineering:stairs_treated_wood_horizontal", "immersiveengineering:stairs_treated_wood_packaged", "immersiveengineering:stairs_treated_wood_vertical", "immersiveengineering:steel_fence", "immersiveengineering:steel_post", "immersiveengineering:steel_scaffolding_grate_top", "immersiveengineering:steel_scaffolding_standard", "immersiveengineering:steel_scaffolding_wooden_top", "immersiveengineering:steel_slope", "immersiveengineering:steel_wallmount", "immersiveengineering:storage_aluminum", "immersiveengineering:storage_constantan", "immersiveengineering:storage_electrum", "immersiveengineering:storage_lead", "immersiveengineering:storage_nickel", "immersiveengineering:storage_silver", "immersiveengineering:storage_steel", "immersiveengineering:storage_uranium", "immersiveengineering:strip_curtain", "immersiveengineering:tank", "immersiveengineering:tesla_coil", "immersiveengineering:thermoelectric_generator", "immersiveengineering:toolbox_block", "immersiveengineering:transformer", "immersiveengineering:transformer_hv", "immersiveengineering:treated_fence", "immersiveengineering:treated_post", "immersiveengineering:treated_scaffold", "immersiveengineering:treated_wallmount", "immersiveengineering:treated_wood_horizontal", "immersiveengineering:treated_wood_packaged", "immersiveengineering:treated_wood_vertical", "immersiveengineering:turntable", "immersiveengineering:turret_chem", "immersiveengineering:turret_gun", "immersiveengineering:wall_clinker_brick", "immersiveengineering:wall_slag_brick", "immersiveengineering:watermill", "immersiveengineering:windmill", "immersiveengineering:wooden_barrel", "immersiveengineering:workbench", "iron_making_furnace:custom_furnace", "iron_making_furnace:iron_making_furnace_bricks", "iron_making_furnace:iron_making_furnace_controller", "mekanism:advanced_bin", "mekanism:advanced_chemical_tank", "mekanism:advanced_combining_factory", "mekanism:advanced_compressing_factory", "mekanism:advanced_crushing_factory", "mekanism:advanced_energy_cube", "mekanism:advanced_enriching_factory", "mekanism:advanced_fluid_tank", "mekanism:advanced_induction_cell", "mekanism:advanced_induction_provider", "mekanism:advanced_infusing_factory", "mekanism:advanced_injecting_factory", "mekanism:advanced_logistical_transporter", "mekanism:advanced_mechanical_pipe", "mekanism:advanced_pressurized_tube", "mekanism:advanced_purifying_factory", "mekanism:advanced_sawing_factory", "mekanism:advanced_smelting_factory", "mekanism:advanced_thermodynamic_conductor", "mekanism:advanced_universal_cable", "mekanism:antiprotonic_nucleosynthesizer", "mekanism:basic_bin", "mekanism:basic_chemical_tank", "mekanism:basic_combining_factory", "mekanism:basic_compressing_factory", "mekanism:basic_crushing_factory", "mekanism:basic_energy_cube", "mekanism:basic_enriching_factory", "mekanism:basic_fluid_tank", "mekanism:basic_induction_cell", "mekanism:basic_induction_provider", "mekanism:basic_infusing_factory", "mekanism:basic_injecting_factory", "mekanism:basic_logistical_transporter", "mekanism:basic_mechanical_pipe", "mekanism:basic_pressurized_tube", "mekanism:basic_purifying_factory", "mekanism:basic_sawing_factory", "mekanism:basic_smelting_factory", "mekanism:basic_thermodynamic_conductor", "mekanism:basic_universal_cable", "mekanism:block_bronze", "mekanism:block_charcoal", "mekanism:block_fluorite", "mekanism:block_lead", "mekanism:block_osmium", "mekanism:block_raw_lead", "mekanism:block_raw_osmium", "mekanism:block_raw_tin", "mekanism:block_raw_uranium", "mekanism:block_refined_glowstone", "mekanism:block_refined_obsidian", "mekanism:block_salt", "mekanism:block_steel", "mekanism:block_tin", "mekanism:block_uranium", "mekanism:boiler_casing", "mekanism:boiler_valve", "mekanism:bounding_block", "mekanism:brine", "mekanism:cardboard_box", "mekanism:chargepad", "mekanism:chemical_crystallizer", "mekanism:chemical_dissolution_chamber", "mekanism:chemical_infuser", "mekanism:chemical_injection_chamber", "mekanism:chemical_oxidizer", "mekanism:chemical_washer", "mekanism:chlorine", "mekanism:combiner", "mekanism:creative_bin", "mekanism:creative_chemical_tank", "mekanism:creative_energy_cube", "mekanism:creative_fluid_tank", "mekanism:crusher", "mekanism:deepslate_fluorite_ore", "mekanism:deepslate_lead_ore", "mekanism:deepslate_osmium_ore", "mekanism:deepslate_tin_ore", "mekanism:deepslate_uranium_ore", "mekanism:digital_miner", "mekanism:dimensional_stabilizer", "mekanism:diversion_transporter", "mekanism:dynamic_tank", "mekanism:dynamic_valve", "mekanism:electric_pump", "mekanism:electrolytic_separator", "mekanism:elite_bin", "mekanism:elite_chemical_tank", "mekanism:elite_combining_factory", "mekanism:elite_compressing_factory", "mekanism:elite_crushing_factory", "mekanism:elite_energy_cube", "mekanism:elite_enriching_factory", "mekanism:elite_fluid_tank", "mekanism:elite_induction_cell", "mekanism:elite_induction_provider", "mekanism:elite_infusing_factory", "mekanism:elite_injecting_factory", "mekanism:elite_logistical_transporter", "mekanism:elite_mechanical_pipe", "mekanism:elite_pressurized_tube", "mekanism:elite_purifying_factory", "mekanism:elite_sawing_factory", "mekanism:elite_smelting_factory", "mekanism:elite_thermodynamic_conductor", "mekanism:elite_universal_cable", "mekanism:energized_smelter", "mekanism:enrichment_chamber", "mekanism:ethene", "mekanism:fluidic_plenisher", "mekanism:fluorite_ore", "mekanism:formulaic_assemblicator", "mekanism:fuelwood_heater", "mekanism:heavy_water", "mekanism:hydrofluoric_acid", "mekanism:hydrogen", "mekanism:hydrogen_chloride", "mekanism:induction_casing", "mekanism:induction_port", "mekanism:industrial_alarm", "mekanism:isotopic_centrifuge", "mekanism:laser", "mekanism:laser_amplifier", "mekanism:laser_tractor_beam", "mekanism:lead_ore", "mekanism:lithium", "mekanism:logistical_sorter", "mekanism:metallurgic_infuser", "mekanism:modification_station", "mekanism:nutritional_liquifier", "mekanism:nutritional_paste", "mekanism:oredictionificator", "mekanism:osmium_compressor", "mekanism:osmium_ore", "mekanism:oxygen", "mekanism:painting_machine", "mekanism:personal_barrel", "mekanism:personal_chest", "mekanism:pigment_extractor", "mekanism:pigment_mixer", "mekanism:precision_sawmill", "mekanism:pressure_disperser", "mekanism:pressurized_reaction_chamber", "mekanism:purification_chamber", "mekanism:qio_dashboard", "mekanism:qio_drive_array", "mekanism:qio_exporter", "mekanism:qio_importer", "mekanism:qio_redstone_adapter", "mekanism:quantum_entangloporter", "mekanism:radioactive_waste_barrel", "mekanism:resistive_heater", "mekanism:restrictive_transporter", "mekanism:rotary_condensentrator", "mekanism:security_desk", "mekanism:seismic_vibrator", "mekanism:sodium", "mekanism:solar_neutron_activator", "mekanism:sps_casing", "mekanism:sps_port", "mekanism:steam", "mekanism:steel_casing", "mekanism:structural_glass", "mekanism:sulfur_dioxide", "mekanism:sulfur_trioxide", "mekanism:sulfuric_acid", "mekanism:supercharged_coil", "mekanism:superheated_sodium", "mekanism:superheating_element", "mekanism:teleporter", "mekanism:teleporter_frame", "mekanism:thermal_evaporation_block", "mekanism:thermal_evaporation_controller", "mekanism:thermal_evaporation_valve", "mekanism:tin_ore", "mekanism:ultimate_bin", "mekanism:ultimate_chemical_tank", "mekanism:ultimate_combining_factory", "mekanism:ultimate_compressing_factory", "mekanism:ultimate_crushing_factory", "mekanism:ultimate_energy_cube", "mekanism:ultimate_enriching_factory", "mekanism:ultimate_fluid_tank", "mekanism:ultimate_induction_cell", "mekanism:ultimate_induction_provider", "mekanism:ultimate_infusing_factory", "mekanism:ultimate_injecting_factory", "mekanism:ultimate_logistical_transporter", "mekanism:ultimate_mechanical_pipe", "mekanism:ultimate_pressurized_tube", "mekanism:ultimate_purifying_factory", "mekanism:ultimate_sawing_factory", "mekanism:ultimate_smelting_factory", "mekanism:ultimate_thermodynamic_conductor", "mekanism:ultimate_universal_cable", "mekanism:uranium_hexafluoride", "mekanism:uranium_ore", "mekanism:uranium_oxide", "minecraft:acacia_button", "minecraft:acacia_door", "minecraft:acacia_fence", "minecraft:acacia_fence_gate", "minecraft:acacia_hanging_sign", "minecraft:acacia_leaves", "minecraft:acacia_log", "minecraft:acacia_planks", "minecraft:acacia_pressure_plate", "minecraft:acacia_sapling", "minecraft:acacia_sign", "minecraft:acacia_slab", "minecraft:acacia_stairs", "minecraft:acacia_trapdoor", "minecraft:acacia_wall_hanging_sign", "minecraft:acacia_wall_sign", "minecraft:acacia_wood", "minecraft:activator_rail", "minecraft:air", "minecraft:allium", "minecraft:amethyst_block", "minecraft:amethyst_cluster", "minecraft:ancient_debris", "minecraft:andesite", "minecraft:andesite_slab", "minecraft:andesite_stairs", "minecraft:andesite_wall", "minecraft:anvil", "minecraft:attached_melon_stem", "minecraft:attached_pumpkin_stem", "minecraft:azalea", "minecraft:azalea_leaves", "minecraft:azure_bluet", "minecraft:bamboo", "minecraft:bamboo_block", "minecraft:bamboo_button", "minecraft:bamboo_door", "minecraft:bamboo_fence", "minecraft:bamboo_fence_gate", "minecraft:bamboo_hanging_sign", "minecraft:bamboo_mosaic", "minecraft:bamboo_mosaic_slab", "minecraft:bamboo_mosaic_stairs", "minecraft:bamboo_planks", "minecraft:bamboo_pressure_plate", "minecraft:bamboo_sapling", "minecraft:bamboo_sign", "minecraft:bamboo_slab", "minecraft:bamboo_stairs", "minecraft:bamboo_trapdoor", "minecraft:bamboo_wall_hanging_sign", "minecraft:bamboo_wall_sign", "minecraft:barrel", "minecraft:barrier", "minecraft:basalt", "minecraft:beacon", "minecraft:bedrock", "minecraft:bee_nest", "minecraft:beehive", "minecraft:beetroots", "minecraft:bell", "minecraft:big_dripleaf", "minecraft:big_dripleaf_stem", "minecraft:birch_button", "minecraft:birch_door", "minecraft:birch_fence", "minecraft:birch_fence_gate", "minecraft:birch_hanging_sign", "minecraft:birch_leaves", "minecraft:birch_log", "minecraft:birch_planks", "minecraft:birch_pressure_plate", "minecraft:birch_sapling", "minecraft:birch_sign", "minecraft:birch_slab", "minecraft:birch_stairs", "minecraft:birch_trapdoor", "minecraft:birch_wall_hanging_sign", "minecraft:birch_wall_sign", "minecraft:birch_wood", "minecraft:black_banner", "minecraft:black_bed", "minecraft:black_candle", "minecraft:black_candle_cake", "minecraft:black_carpet", "minecraft:black_concrete", "minecraft:black_concrete_powder", "minecraft:black_glazed_terracotta", "minecraft:black_shulker_box", "minecraft:black_stained_glass", "minecraft:black_stained_glass_pane", "minecraft:black_terracotta", "minecraft:black_wall_banner", "minecraft:black_wool", "minecraft:blackstone", "minecraft:blackstone_slab", "minecraft:blackstone_stairs", "minecraft:blackstone_wall", "minecraft:blast_furnace", "minecraft:blue_banner", "minecraft:blue_bed", "minecraft:blue_candle", "minecraft:blue_candle_cake", "minecraft:blue_carpet", "minecraft:blue_concrete", "minecraft:blue_concrete_powder", "minecraft:blue_glazed_terracotta", "minecraft:blue_ice", "minecraft:blue_orchid", "minecraft:blue_shulker_box", "minecraft:blue_stained_glass", "minecraft:blue_stained_glass_pane", "minecraft:blue_terracotta", "minecraft:blue_wall_banner", "minecraft:blue_wool", "minecraft:bone_block", "minecraft:bookshelf", "minecraft:brain_coral", "minecraft:brain_coral_block", "minecraft:brain_coral_fan", "minecraft:brain_coral_wall_fan", "minecraft:brewing_stand", "minecraft:brick_slab", "minecraft:brick_stairs", "minecraft:brick_wall", "minecraft:bricks", "minecraft:brown_banner", "minecraft:brown_bed", "minecraft:brown_candle", "minecraft:brown_candle_cake", "minecraft:brown_carpet", "minecraft:brown_concrete", "minecraft:brown_concrete_powder", "minecraft:brown_glazed_terracotta", "minecraft:brown_mushroom", "minecraft:brown_mushroom_block", "minecraft:brown_shulker_box", "minecraft:brown_stained_glass", "minecraft:brown_stained_glass_pane", "minecraft:brown_terracotta", "minecraft:brown_wall_banner", "minecraft:brown_wool", "minecraft:bubble_column", "minecraft:bubble_coral", "minecraft:bubble_coral_block", "minecraft:bubble_coral_fan", "minecraft:bubble_coral_wall_fan", "minecraft:budding_amethyst", "minecraft:cactus", "minecraft:cake", "minecraft:calcite", "minecraft:calibrated_sculk_sensor", "minecraft:campfire", "minecraft:candle", "minecraft:candle_cake", "minecraft:carrots", "minecraft:cartography_table", "minecraft:carved_pumpkin", "minecraft:cauldron", "minecraft:cave_air", "minecraft:cave_vines", "minecraft:cave_vines_plant", "minecraft:chain", "minecraft:chain_command_block", "minecraft:cherry_button", "minecraft:cherry_door", "minecraft:cherry_fence", "minecraft:cherry_fence_gate", "minecraft:cherry_hanging_sign", "minecraft:cherry_leaves", "minecraft:cherry_log", "minecraft:cherry_planks", "minecraft:cherry_pressure_plate", "minecraft:cherry_sapling", "minecraft:cherry_sign", "minecraft:cherry_slab", "minecraft:cherry_stairs", "minecraft:cherry_trapdoor", "minecraft:cherry_wall_hanging_sign", "minecraft:cherry_wall_sign", "minecraft:cherry_wood", "minecraft:chest", "minecraft:chipped_anvil", "minecraft:chiseled_bookshelf", "minecraft:chiseled_deepslate", "minecraft:chiseled_nether_bricks", "minecraft:chiseled_polished_blackstone", "minecraft:chiseled_quartz_block", "minecraft:chiseled_red_sandstone", "minecraft:chiseled_sandstone", "minecraft:chiseled_stone_bricks", "minecraft:chorus_flower", "minecraft:chorus_plant", "minecraft:clay", "minecraft:coal_block", "minecraft:coal_ore", "minecraft:coarse_dirt", "minecraft:cobbled_deepslate", "minecraft:cobbled_deepslate_slab", "minecraft:cobbled_deepslate_stairs", "minecraft:cobbled_deepslate_wall", "minecraft:cobblestone", "minecraft:cobblestone_slab", "minecraft:cobblestone_stairs", "minecraft:cobblestone_wall", "minecraft:cobweb", "minecraft:cocoa", "minecraft:command_block", "minecraft:comparator", "minecraft:composter", "minecraft:conduit", "minecraft:copper_block", "minecraft:copper_ore", "minecraft:cornflower", "minecraft:cracked_deepslate_bricks", "minecraft:cracked_deepslate_tiles", "minecraft:cracked_nether_bricks", "minecraft:cracked_polished_blackstone_bricks", "minecraft:cracked_stone_bricks", "minecraft:crafting_table", "minecraft:creeper_head", "minecraft:creeper_wall_head", "minecraft:crimson_button", "minecraft:crimson_door", "minecraft:crimson_fence", "minecraft:crimson_fence_gate", "minecraft:crimson_fungus", "minecraft:crimson_hanging_sign", "minecraft:crimson_hyphae", "minecraft:crimson_nylium", "minecraft:crimson_planks", "minecraft:crimson_pressure_plate", "minecraft:crimson_roots", "minecraft:crimson_sign", "minecraft:crimson_slab", "minecraft:crimson_stairs", "minecraft:crimson_stem", "minecraft:crimson_trapdoor", "minecraft:crimson_wall_hanging_sign", "minecraft:crimson_wall_sign", "minecraft:crying_obsidian", "minecraft:cut_copper", "minecraft:cut_copper_slab", "minecraft:cut_copper_stairs", "minecraft:cut_red_sandstone", "minecraft:cut_red_sandstone_slab", "minecraft:cut_sandstone", "minecraft:cut_sandstone_slab", "minecraft:cyan_banner", "minecraft:cyan_bed", "minecraft:cyan_candle", "minecraft:cyan_candle_cake", "minecraft:cyan_carpet", "minecraft:cyan_concrete", "minecraft:cyan_concrete_powder", "minecraft:cyan_glazed_terracotta", "minecraft:cyan_shulker_box", "minecraft:cyan_stained_glass", "minecraft:cyan_stained_glass_pane", "minecraft:cyan_terracotta", "minecraft:cyan_wall_banner", "minecraft:cyan_wool", "minecraft:damaged_anvil", "minecraft:dandelion", "minecraft:dark_oak_button", "minecraft:dark_oak_door", "minecraft:dark_oak_fence", "minecraft:dark_oak_fence_gate", "minecraft:dark_oak_hanging_sign", "minecraft:dark_oak_leaves", "minecraft:dark_oak_log", "minecraft:dark_oak_planks", "minecraft:dark_oak_pressure_plate", "minecraft:dark_oak_sapling", "minecraft:dark_oak_sign", "minecraft:dark_oak_slab", "minecraft:dark_oak_stairs", "minecraft:dark_oak_trapdoor", "minecraft:dark_oak_wall_hanging_sign", "minecraft:dark_oak_wall_sign", "minecraft:dark_oak_wood", "minecraft:dark_prismarine", "minecraft:dark_prismarine_slab", "minecraft:dark_prismarine_stairs", "minecraft:daylight_detector", "minecraft:dead_brain_coral", "minecraft:dead_brain_coral_block", "minecraft:dead_brain_coral_fan", "minecraft:dead_brain_coral_wall_fan", "minecraft:dead_bubble_coral", "minecraft:dead_bubble_coral_block", "minecraft:dead_bubble_coral_fan", "minecraft:dead_bubble_coral_wall_fan", "minecraft:dead_bush", "minecraft:dead_fire_coral", "minecraft:dead_fire_coral_block", "minecraft:dead_fire_coral_fan", "minecraft:dead_fire_coral_wall_fan", "minecraft:dead_horn_coral", "minecraft:dead_horn_coral_block", "minecraft:dead_horn_coral_fan", "minecraft:dead_horn_coral_wall_fan", "minecraft:dead_tube_coral", "minecraft:dead_tube_coral_block", "minecraft:dead_tube_coral_fan", "minecraft:dead_tube_coral_wall_fan", "minecraft:decorated_pot", "minecraft:deepslate", "minecraft:deepslate_brick_slab", "minecraft:deepslate_brick_stairs", "minecraft:deepslate_brick_wall", "minecraft:deepslate_bricks", "minecraft:deepslate_coal_ore", "minecraft:deepslate_copper_ore", "minecraft:deepslate_diamond_ore", "minecraft:deepslate_emerald_ore", "minecraft:deepslate_gold_ore", "minecraft:deepslate_iron_ore", "minecraft:deepslate_lapis_ore", "minecraft:deepslate_redstone_ore", "minecraft:deepslate_tile_slab", "minecraft:deepslate_tile_stairs", "minecraft:deepslate_tile_wall", "minecraft:deepslate_tiles", "minecraft:detector_rail", "minecraft:diamond_block", "minecraft:diamond_ore", "minecraft:diorite", "minecraft:diorite_slab", "minecraft:diorite_stairs", "minecraft:diorite_wall", "minecraft:dirt", "minecraft:dirt_path", "minecraft:dispenser", "minecraft:dragon_egg", "minecraft:dragon_head", "minecraft:dragon_wall_head", "minecraft:dried_kelp_block", "minecraft:dripstone_block", "minecraft:dropper", "minecraft:emerald_block", "minecraft:emerald_ore", "minecraft:enchanting_table", "minecraft:end_gateway", "minecraft:end_portal", "minecraft:end_portal_frame", "minecraft:end_rod", "minecraft:end_stone", "minecraft:end_stone_brick_slab", "minecraft:end_stone_brick_stairs", "minecraft:end_stone_brick_wall", "minecraft:end_stone_bricks", "minecraft:ender_chest", "minecraft:exposed_copper", "minecraft:exposed_cut_copper", "minecraft:exposed_cut_copper_slab", "minecraft:exposed_cut_copper_stairs", "minecraft:farmland", "minecraft:fern", "minecraft:fire", "minecraft:fire_coral", "minecraft:fire_coral_block", "minecraft:fire_coral_fan", "minecraft:fire_coral_wall_fan", "minecraft:fletching_table", "minecraft:flower_pot", "minecraft:flowering_azalea", "minecraft:flowering_azalea_leaves", "minecraft:frogspawn", "minecraft:frosted_ice", "minecraft:furnace", "minecraft:gilded_blackstone", "minecraft:glass", "minecraft:glass_pane", "minecraft:glow_lichen", "minecraft:glowstone", "minecraft:gold_block", "minecraft:gold_ore", "minecraft:granite", "minecraft:granite_slab", "minecraft:granite_stairs", "minecraft:granite_wall", "minecraft:grass", "minecraft:grass_block", "minecraft:gravel", "minecraft:gray_banner", "minecraft:gray_bed", "minecraft:gray_candle", "minecraft:gray_candle_cake", "minecraft:gray_carpet", "minecraft:gray_concrete", "minecraft:gray_concrete_powder", "minecraft:gray_glazed_terracotta", "minecraft:gray_shulker_box", "minecraft:gray_stained_glass", "minecraft:gray_stained_glass_pane", "minecraft:gray_terracotta", "minecraft:gray_wall_banner", "minecraft:gray_wool", "minecraft:green_banner", "minecraft:green_bed", "minecraft:green_candle", "minecraft:green_candle_cake", "minecraft:green_carpet", "minecraft:green_concrete", "minecraft:green_concrete_powder", "minecraft:green_glazed_terracotta", "minecraft:green_shulker_box", "minecraft:green_stained_glass", "minecraft:green_stained_glass_pane", "minecraft:green_terracotta", "minecraft:green_wall_banner", "minecraft:green_wool", "minecraft:grindstone", "minecraft:hanging_roots", "minecraft:hay_block", "minecraft:heavy_weighted_pressure_plate", "minecraft:honey_block", "minecraft:honeycomb_block", "minecraft:hopper", "minecraft:horn_coral", "minecraft:horn_coral_block", "minecraft:horn_coral_fan", "minecraft:horn_coral_wall_fan", "minecraft:ice", "minecraft:infested_chiseled_stone_bricks", "minecraft:infested_cobblestone", "minecraft:infested_cracked_stone_bricks", "minecraft:infested_deepslate", "minecraft:infested_mossy_stone_bricks", "minecraft:infested_stone", "minecraft:infested_stone_bricks", "minecraft:iron_bars", "minecraft:iron_block", "minecraft:iron_door", "minecraft:iron_ore", "minecraft:iron_trapdoor", "minecraft:jack_o_lantern", "minecraft:jigsaw", "minecraft:jukebox", "minecraft:jungle_button", "minecraft:jungle_door", "minecraft:jungle_fence", "minecraft:jungle_fence_gate", "minecraft:jungle_hanging_sign", "minecraft:jungle_leaves", "minecraft:jungle_log", "minecraft:jungle_planks", "minecraft:jungle_pressure_plate", "minecraft:jungle_sapling", "minecraft:jungle_sign", "minecraft:jungle_slab", "minecraft:jungle_stairs", "minecraft:jungle_trapdoor", "minecraft:jungle_wall_hanging_sign", "minecraft:jungle_wall_sign", "minecraft:jungle_wood", "minecraft:kelp", "minecraft:kelp_plant", "minecraft:ladder", "minecraft:lantern", "minecraft:lapis_block", "minecraft:lapis_ore", "minecraft:large_amethyst_bud", "minecraft:large_fern", "minecraft:lava", "minecraft:lava_cauldron", "minecraft:lectern", "minecraft:lever", "minecraft:light", "minecraft:light_blue_banner", "minecraft:light_blue_bed", "minecraft:light_blue_candle", "minecraft:light_blue_candle_cake", "minecraft:light_blue_carpet", "minecraft:light_blue_concrete", "minecraft:light_blue_concrete_powder", "minecraft:light_blue_glazed_terracotta", "minecraft:light_blue_shulker_box", "minecraft:light_blue_stained_glass", "minecraft:light_blue_stained_glass_pane", "minecraft:light_blue_terracotta", "minecraft:light_blue_wall_banner", "minecraft:light_blue_wool", "minecraft:light_gray_banner", "minecraft:light_gray_bed", "minecraft:light_gray_candle", "minecraft:light_gray_candle_cake", "minecraft:light_gray_carpet", "minecraft:light_gray_concrete", "minecraft:light_gray_concrete_powder", "minecraft:light_gray_glazed_terracotta", "minecraft:light_gray_shulker_box", "minecraft:light_gray_stained_glass", "minecraft:light_gray_stained_glass_pane", "minecraft:light_gray_terracotta", "minecraft:light_gray_wall_banner", "minecraft:light_gray_wool", "minecraft:light_weighted_pressure_plate", "minecraft:lightning_rod", "minecraft:lilac", "minecraft:lily_of_the_valley", "minecraft:lily_pad", "minecraft:lime_banner", "minecraft:lime_bed", "minecraft:lime_candle", "minecraft:lime_candle_cake", "minecraft:lime_carpet", "minecraft:lime_concrete", "minecraft:lime_concrete_powder", "minecraft:lime_glazed_terracotta", "minecraft:lime_shulker_box", "minecraft:lime_stained_glass", "minecraft:lime_stained_glass_pane", "minecraft:lime_terracotta", "minecraft:lime_wall_banner", "minecraft:lime_wool", "minecraft:lodestone", "minecraft:loom", "minecraft:magenta_banner", "minecraft:magenta_bed", "minecraft:magenta_candle", "minecraft:magenta_candle_cake", "minecraft:magenta_carpet", "minecraft:magenta_concrete", "minecraft:magenta_concrete_powder", "minecraft:magenta_glazed_terracotta", "minecraft:magenta_shulker_box", "minecraft:magenta_stained_glass", "minecraft:magenta_stained_glass_pane", "minecraft:magenta_terracotta", "minecraft:magenta_wall_banner", "minecraft:magenta_wool", "minecraft:magma_block", "minecraft:mangrove_button", "minecraft:mangrove_door", "minecraft:mangrove_fence", "minecraft:mangrove_fence_gate", "minecraft:mangrove_hanging_sign", "minecraft:mangrove_leaves", "minecraft:mangrove_log", "minecraft:mangrove_planks", "minecraft:mangrove_pressure_plate", "minecraft:mangrove_propagule", "minecraft:mangrove_roots", "minecraft:mangrove_sign", "minecraft:mangrove_slab", "minecraft:mangrove_stairs", "minecraft:mangrove_trapdoor", "minecraft:mangrove_wall_hanging_sign", "minecraft:mangrove_wall_sign", "minecraft:mangrove_wood", "minecraft:medium_amethyst_bud", "minecraft:melon", "minecraft:melon_stem", "minecraft:moss_block", "minecraft:moss_carpet", "minecraft:mossy_cobblestone", "minecraft:mossy_cobblestone_slab", "minecraft:mossy_cobblestone_stairs", "minecraft:mossy_cobblestone_wall", "minecraft:mossy_stone_brick_slab", "minecraft:mossy_stone_brick_stairs", "minecraft:mossy_stone_brick_wall", "minecraft:mossy_stone_bricks", "minecraft:moving_piston", "minecraft:mud", "minecraft:mud_brick_slab", "minecraft:mud_brick_stairs", "minecraft:mud_brick_wall", "minecraft:mud_bricks", "minecraft:muddy_mangrove_roots", "minecraft:mushroom_stem", "minecraft:mycelium", "minecraft:nether_brick_fence", "minecraft:nether_brick_slab", "minecraft:nether_brick_stairs", "minecraft:nether_brick_wall", "minecraft:nether_bricks", "minecraft:nether_gold_ore", "minecraft:nether_portal", "minecraft:nether_quartz_ore", "minecraft:nether_sprouts", "minecraft:nether_wart", "minecraft:nether_wart_block", "minecraft:netherite_block", "minecraft:netherrack", "minecraft:note_block", "minecraft:oak_button", "minecraft:oak_door", "minecraft:oak_fence", "minecraft:oak_fence_gate", "minecraft:oak_hanging_sign", "minecraft:oak_leaves", "minecraft:oak_log", "minecraft:oak_planks", "minecraft:oak_pressure_plate", "minecraft:oak_sapling", "minecraft:oak_sign", "minecraft:oak_slab", "minecraft:oak_stairs", "minecraft:oak_trapdoor", "minecraft:oak_wall_hanging_sign", "minecraft:oak_wall_sign", "minecraft:oak_wood", "minecraft:observer", "minecraft:obsidian", "minecraft:ochre_froglight", "minecraft:orange_banner", "minecraft:orange_bed", "minecraft:orange_candle", "minecraft:orange_candle_cake", "minecraft:orange_carpet", "minecraft:orange_concrete", "minecraft:orange_concrete_powder", "minecraft:orange_glazed_terracotta", "minecraft:orange_shulker_box", "minecraft:orange_stained_glass", "minecraft:orange_stained_glass_pane", "minecraft:orange_terracotta", "minecraft:orange_tulip", "minecraft:orange_wall_banner", "minecraft:orange_wool", "minecraft:oxeye_daisy", "minecraft:oxidized_copper", "minecraft:oxidized_cut_copper", "minecraft:oxidized_cut_copper_slab", "minecraft:oxidized_cut_copper_stairs", "minecraft:packed_ice", "minecraft:packed_mud", "minecraft:pearlescent_froglight", "minecraft:peony", "minecraft:petrified_oak_slab", "minecraft:piglin_head", "minecraft:piglin_wall_head", "minecraft:pink_banner", "minecraft:pink_bed", "minecraft:pink_candle", "minecraft:pink_candle_cake", "minecraft:pink_carpet", "minecraft:pink_concrete", "minecraft:pink_concrete_powder", "minecraft:pink_glazed_terracotta", "minecraft:pink_petals", "minecraft:pink_shulker_box", "minecraft:pink_stained_glass", "minecraft:pink_stained_glass_pane", "minecraft:pink_terracotta", "minecraft:pink_tulip", "minecraft:pink_wall_banner", "minecraft:pink_wool", "minecraft:piston", "minecraft:piston_head", "minecraft:pitcher_crop", "minecraft:pitcher_plant", "minecraft:player_head", "minecraft:player_wall_head", "minecraft:podzol", "minecraft:pointed_dripstone", "minecraft:polished_andesite", "minecraft:polished_andesite_slab", "minecraft:polished_andesite_stairs", "minecraft:polished_basalt", "minecraft:polished_blackstone", "minecraft:polished_blackstone_brick_slab", "minecraft:polished_blackstone_brick_stairs", "minecraft:polished_blackstone_brick_wall", "minecraft:polished_blackstone_bricks", "minecraft:polished_blackstone_button", "minecraft:polished_blackstone_pressure_plate", "minecraft:polished_blackstone_slab", "minecraft:polished_blackstone_stairs", "minecraft:polished_blackstone_wall", "minecraft:polished_deepslate", "minecraft:polished_deepslate_slab", "minecraft:polished_deepslate_stairs", "minecraft:polished_deepslate_wall", "minecraft:polished_diorite", "minecraft:polished_diorite_slab", "minecraft:polished_diorite_stairs", "minecraft:polished_granite", "minecraft:polished_granite_slab", "minecraft:polished_granite_stairs", "minecraft:poppy", "minecraft:potatoes", "minecraft:potted_acacia_sapling", "minecraft:potted_allium", "minecraft:potted_azalea_bush", "minecraft:potted_azure_bluet", "minecraft:potted_bamboo", "minecraft:potted_birch_sapling", "minecraft:potted_blue_orchid", "minecraft:potted_brown_mushroom", "minecraft:potted_cactus", "minecraft:potted_cherry_sapling", "minecraft:potted_cornflower", "minecraft:potted_crimson_fungus", "minecraft:potted_crimson_roots", "minecraft:potted_dandelion", "minecraft:potted_dark_oak_sapling", "minecraft:potted_dead_bush", "minecraft:potted_fern", "minecraft:potted_flowering_azalea_bush", "minecraft:potted_jungle_sapling", "minecraft:potted_lily_of_the_valley", "minecraft:potted_mangrove_propagule", "minecraft:potted_oak_sapling", "minecraft:potted_orange_tulip", "minecraft:potted_oxeye_daisy", "minecraft:potted_pink_tulip", "minecraft:potted_poppy", "minecraft:potted_red_mushroom", "minecraft:potted_red_tulip", "minecraft:potted_spruce_sapling", "minecraft:potted_torchflower", "minecraft:potted_warped_fungus", "minecraft:potted_warped_roots", "minecraft:potted_white_tulip", "minecraft:potted_wither_rose", "minecraft:powder_snow", "minecraft:powder_snow_cauldron", "minecraft:powered_rail", "minecraft:prismarine", "minecraft:prismarine_brick_slab", "minecraft:prismarine_brick_stairs", "minecraft:prismarine_bricks", "minecraft:prismarine_slab", "minecraft:prismarine_stairs", "minecraft:prismarine_wall", "minecraft:pumpkin", "minecraft:pumpkin_stem", "minecraft:purple_banner", "minecraft:purple_bed", "minecraft:purple_candle", "minecraft:purple_candle_cake", "minecraft:purple_carpet", "minecraft:purple_concrete", "minecraft:purple_concrete_powder", "minecraft:purple_glazed_terracotta", "minecraft:purple_shulker_box", "minecraft:purple_stained_glass", "minecraft:purple_stained_glass_pane", "minecraft:purple_terracotta", "minecraft:purple_wall_banner", "minecraft:purple_wool", "minecraft:purpur_block", "minecraft:purpur_pillar", "minecraft:purpur_slab", "minecraft:purpur_stairs", "minecraft:quartz_block", "minecraft:quartz_bricks", "minecraft:quartz_pillar", "minecraft:quartz_slab", "minecraft:quartz_stairs", "minecraft:rail", "minecraft:raw_copper_block", "minecraft:raw_gold_block", "minecraft:raw_iron_block", "minecraft:red_banner", "minecraft:red_bed", "minecraft:red_candle", "minecraft:red_candle_cake", "minecraft:red_carpet", "minecraft:red_concrete", "minecraft:red_concrete_powder", "minecraft:red_glazed_terracotta", "minecraft:red_mushroom", "minecraft:red_mushroom_block", "minecraft:red_nether_brick_slab", "minecraft:red_nether_brick_stairs", "minecraft:red_nether_brick_wall", "minecraft:red_nether_bricks", "minecraft:red_sand", "minecraft:red_sandstone", "minecraft:red_sandstone_slab", "minecraft:red_sandstone_stairs", "minecraft:red_sandstone_wall", "minecraft:red_shulker_box", "minecraft:red_stained_glass", "minecraft:red_stained_glass_pane", "minecraft:red_terracotta", "minecraft:red_tulip", "minecraft:red_wall_banner", "minecraft:red_wool", "minecraft:redstone_block", "minecraft:redstone_lamp", "minecraft:redstone_ore", "minecraft:redstone_torch", "minecraft:redstone_wall_torch", "minecraft:redstone_wire", "minecraft:reinforced_deepslate", "minecraft:repeater", "minecraft:repeating_command_block", "minecraft:respawn_anchor", "minecraft:rooted_dirt", "minecraft:rose_bush", "minecraft:sand", "minecraft:sandstone", "minecraft:sandstone_slab", "minecraft:sandstone_stairs", "minecraft:sandstone_wall", "minecraft:scaffolding", "minecraft:sculk", "minecraft:sculk_catalyst", "minecraft:sculk_sensor", "minecraft:sculk_shrieker", "minecraft:sculk_vein", "minecraft:sea_lantern", "minecraft:sea_pickle", "minecraft:seagrass", "minecraft:shroomlight", "minecraft:shulker_box", "minecraft:skeleton_skull", "minecraft:skeleton_wall_skull", "minecraft:slime_block", "minecraft:small_amethyst_bud", "minecraft:small_dripleaf", "minecraft:smithing_table", "minecraft:smoker", "minecraft:smooth_basalt", "minecraft:smooth_quartz", "minecraft:smooth_quartz_slab", "minecraft:smooth_quartz_stairs", "minecraft:smooth_red_sandstone", "minecraft:smooth_red_sandstone_slab", "minecraft:smooth_red_sandstone_stairs", "minecraft:smooth_sandstone", "minecraft:smooth_sandstone_slab", "minecraft:smooth_sandstone_stairs", "minecraft:smooth_stone", "minecraft:smooth_stone_slab", "minecraft:sniffer_egg", "minecraft:snow", "minecraft:snow_block", "minecraft:soul_campfire", "minecraft:soul_fire", "minecraft:soul_lantern", "minecraft:soul_sand", "minecraft:soul_soil", "minecraft:soul_torch", "minecraft:soul_wall_torch", "minecraft:spawner", "minecraft:sponge", "minecraft:spore_blossom", "minecraft:spruce_button", "minecraft:spruce_door", "minecraft:spruce_fence", "minecraft:spruce_fence_gate", "minecraft:spruce_hanging_sign", "minecraft:spruce_leaves", "minecraft:spruce_log", "minecraft:spruce_planks", "minecraft:spruce_pressure_plate", "minecraft:spruce_sapling", "minecraft:spruce_sign", "minecraft:spruce_slab", "minecraft:spruce_stairs", "minecraft:spruce_trapdoor", "minecraft:spruce_wall_hanging_sign", "minecraft:spruce_wall_sign", "minecraft:spruce_wood", "minecraft:sticky_piston", "minecraft:stone", "minecraft:stone_brick_slab", "minecraft:stone_brick_stairs", "minecraft:stone_brick_wall", "minecraft:stone_bricks", "minecraft:stone_button", "minecraft:stone_pressure_plate", "minecraft:stone_slab", "minecraft:stone_stairs", "minecraft:stonecutter", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood", "minecraft:stripped_bamboo_block", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood", "minecraft:stripped_crimson_hyphae", "minecraft:stripped_crimson_stem", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood", "minecraft:stripped_warped_hyphae", "minecraft:stripped_warped_stem", "minecraft:structure_block", "minecraft:structure_void", "minecraft:sugar_cane", "minecraft:sunflower", "minecraft:suspicious_gravel", "minecraft:suspicious_sand", "minecraft:sweet_berry_bush", "minecraft:tall_grass", "minecraft:tall_seagrass", "minecraft:target", "minecraft:terracotta", "minecraft:tinted_glass", "minecraft:tnt", "minecraft:torch", "minecraft:torchflower", "minecraft:torchflower_crop", "minecraft:trapped_chest", "minecraft:tripwire", "minecraft:tripwire_hook", "minecraft:tube_coral", "minecraft:tube_coral_block", "minecraft:tube_coral_fan", "minecraft:tube_coral_wall_fan", "minecraft:tuff", "minecraft:turtle_egg", "minecraft:twisting_vines", "minecraft:twisting_vines_plant", "minecraft:verdant_froglight", "minecraft:vine", "minecraft:void_air", "minecraft:wall_torch", "minecraft:warped_button", "minecraft:warped_door", "minecraft:warped_fence", "minecraft:warped_fence_gate", "minecraft:warped_fungus", "minecraft:warped_hanging_sign", "minecraft:warped_hyphae", "minecraft:warped_nylium", "minecraft:warped_planks", "minecraft:warped_pressure_plate", "minecraft:warped_roots", "minecraft:warped_sign", "minecraft:warped_slab", "minecraft:warped_stairs", "minecraft:warped_stem", "minecraft:warped_trapdoor", "minecraft:warped_wall_hanging_sign", "minecraft:warped_wall_sign", "minecraft:warped_wart_block", "minecraft:water", "minecraft:water_cauldron", "minecraft:waxed_copper_block", "minecraft:waxed_cut_copper", "minecraft:waxed_cut_copper_slab", "minecraft:waxed_cut_copper_stairs", "minecraft:waxed_exposed_copper", "minecraft:waxed_exposed_cut_copper", "minecraft:waxed_exposed_cut_copper_slab", "minecraft:waxed_exposed_cut_copper_stairs", "minecraft:waxed_oxidized_copper", "minecraft:waxed_oxidized_cut_copper", "minecraft:waxed_oxidized_cut_copper_slab", "minecraft:waxed_oxidized_cut_copper_stairs", "minecraft:waxed_weathered_copper", "minecraft:waxed_weathered_cut_copper", "minecraft:waxed_weathered_cut_copper_slab", "minecraft:waxed_weathered_cut_copper_stairs", "minecraft:weathered_copper", "minecraft:weathered_cut_copper", "minecraft:weathered_cut_copper_slab", "minecraft:weathered_cut_copper_stairs", "minecraft:weeping_vines", "minecraft:weeping_vines_plant", "minecraft:wet_sponge", "minecraft:wheat", "minecraft:white_banner", "minecraft:white_bed", "minecraft:white_candle", "minecraft:white_candle_cake", "minecraft:white_carpet", "minecraft:white_concrete", "minecraft:white_concrete_powder", "minecraft:white_glazed_terracotta", "minecraft:white_shulker_box", "minecraft:white_stained_glass", "minecraft:white_stained_glass_pane", "minecraft:white_terracotta", "minecraft:white_tulip", "minecraft:white_wall_banner", "minecraft:white_wool", "minecraft:wither_rose", "minecraft:wither_skeleton_skull", "minecraft:wither_skeleton_wall_skull", "minecraft:yellow_banner", "minecraft:yellow_bed", "minecraft:yellow_candle", "minecraft:yellow_candle_cake", "minecraft:yellow_carpet", "minecraft:yellow_concrete", "minecraft:yellow_concrete_powder", "minecraft:yellow_glazed_terracotta", "minecraft:yellow_shulker_box", "minecraft:yellow_stained_glass", "minecraft:yellow_stained_glass_pane", "minecraft:yellow_terracotta", "minecraft:yellow_wall_banner", "minecraft:yellow_wool", "minecraft:zombie_head", "minecraft:zombie_wall_head", "netmusic:cd_burner", "netmusic:computer", "netmusic:music_player", "cataclysm:void_crystal", "cataclysm:purpur_tiles", "cataclysm:purpur_tile_slab", "cataclysm:purpur_tile_stairs", "cataclysm:purpur_tile_wall", "cataclysm:void_purpur_tiles", "cataclysm:purpur_tile_pillar", "cataclysm:obsidian_pillar", "cataclysm:polished_obsidian", "cataclysm:polished_obsidian_slab", "cataclysm:polished_obsidian_stairs", "cataclysm:polished_obsidian_wall", "cataclysm:boss_respawner", "cataclysm:goddess_statue", "cataclysm:chorus_trapdoor", "cataclysm:prismarine_brick_fence", "cataclysm:prismarine_brick_wall", "cataclysm:azure_seastone", "cataclysm:azure_seastone_slab", "cataclysm:azure_seastone_stairs", "cataclysm:azure_seastone_wall", "cataclysm:azure_seastone_fence", "cataclysm:azure_seastone_tiles", "cataclysm:chiseled_azure_seastone", "cataclysm:azure_seastone_bricks", "cataclysm:azure_seastone_brick_slab", "cataclysm:azure_seastone_brick_stairs", "cataclysm:azure_seastone_brick_wall", "cataclysm:azure_seastone_mural_empty", "cataclysm:azure_seastone_mural_clawdian", "cataclysm:azure_seastone_mural_cindaria", "cataclysm:azure_seastone_mural_hippocamtus", "cataclysm:azure_seastone_mural_urchinkin", "cataclysm:azure_seastone_mural_thunder", "cataclysm:azure_seastone_mural_sea", "cataclysm:azure_seastone_mural_underworld", "cataclysm:azure_seastone_mural_harvest", "cataclysm:azure_seastone_mural_smithing", "cataclysm:azure_seastone_mural_wisdom", "cataclysm:curved_azure_seastone_urchinkin", "cataclysm:curved_azure_seastone_cindaria_1", "cataclysm:curved_azure_seastone_cindaria_2", "cataclysm:curved_azure_seastone_cindaria_3", "cataclysm:curved_azure_seastone_cindaria_4", "cataclysm:curved_azure_seastone_hippocamtus_1", "cataclysm:curved_azure_seastone_hippocamtus_2", "cataclysm:curved_azure_seastone_hippocamtus_3", "cataclysm:curved_azure_seastone_hippocamtus_4", "cataclysm:curved_azure_seastone_clawdian_1", "cataclysm:curved_azure_seastone_clawdian_2", "cataclysm:curved_azure_seastone_clawdian_3", "cataclysm:curved_azure_seastone_clawdian_4", "cataclysm:curved_azure_seastone_scylla_1", "cataclysm:curved_azure_seastone_scylla_2", "cataclysm:curved_azure_seastone_scylla_3", "cataclysm:curved_azure_seastone_scylla_4", "cataclysm:curved_azure_seastone_scylla_5", "cataclysm:curved_azure_seastone_scylla_6", "cataclysm:curved_azure_seastone_scylla_7", "cataclysm:curved_azure_seastone_scylla_8", "cataclysm:curved_azure_seastone_scylla_9", "cataclysm:polished_azure_seastone", "cataclysm:polished_azure_seastone_slab", "cataclysm:polished_azure_seastone_stairs", "cataclysm:polished_azure_seastone_wall", "cataclysm:azure_seastone_pillar", "cataclysm:azure_seastone_pillar_wall", "cataclysm:chiseled_azure_seastone_pillar", "cataclysm:chiseled_azure_seastone_pillar_wall", "tconstruct:glow", "tconstruct:clear_glass", "tconstruct:clear_tinted_glass", "tconstruct:clear_glass_pane", "tconstruct:white_clear_stained_glass", "tconstruct:orange_clear_stained_glass", "tconstruct:magenta_clear_stained_glass", "tconstruct:light_blue_clear_stained_glass", "tconstruct:yellow_clear_stained_glass", "tconstruct:lime_clear_stained_glass", "tconstruct:pink_clear_stained_glass", "tconstruct:gray_clear_stained_glass", "tconstruct:light_gray_clear_stained_glass", "tconstruct:cyan_clear_stained_glass", "tconstruct:purple_clear_stained_glass", "tconstruct:blue_clear_stained_glass", "tconstruct:brown_clear_stained_glass", "tconstruct:green_clear_stained_glass", "tconstruct:red_clear_stained_glass", "tconstruct:black_clear_stained_glass", "tconstruct:white_clear_stained_glass_pane", "tconstruct:orange_clear_stained_glass_pane", "tconstruct:magenta_clear_stained_glass_pane", "tconstruct:light_blue_clear_stained_glass_pane", "tconstruct:yellow_clear_stained_glass_pane", "tconstruct:lime_clear_stained_glass_pane", "tconstruct:pink_clear_stained_glass_pane", "tconstruct:gray_clear_stained_glass_pane", "tconstruct:light_gray_clear_stained_glass_pane", "tconstruct:cyan_clear_stained_glass_pane", "tconstruct:purple_clear_stained_glass_pane", "tconstruct:blue_clear_stained_glass_pane", "tconstruct:brown_clear_stained_glass_pane", "tconstruct:green_clear_stained_glass_pane", "tconstruct:red_clear_stained_glass_pane", "tconstruct:black_clear_stained_glass_pane", "tconstruct:soul_glass", "tconstruct:soul_glass_pane", "tconstruct:gold_bars", "tconstruct:obsidian_pane", "tconstruct:gold_platform", "tconstruct:iron_platform", "tconstruct:cobalt_platform", "tconstruct:copper_platform", "tconstruct:exposed_copper_platform", "tconstruct:weathered_copper_platform", "tconstruct:oxidized_copper_platform", "tconstruct:waxed_copper_platform", "tconstruct:waxed_exposed_copper_platform", "tconstruct:waxed_weathered_copper_platform", "tconstruct:waxed_oxidized_copper_platform", "tconstruct:cheese_block", "tconstruct:cobalt_block", "tconstruct:steel_block", "tconstruct:slimesteel_block", "tconstruct:amethyst_bronze_block", "tconstruct:rose_gold_block", "tconstruct:pig_iron_block", "tconstruct:cinderslime_block", "tconstruct:queens_slime_block", "tconstruct:manyullyn_block", "tconstruct:hepatizon_block", "tconstruct:soulsteel_block", "tconstruct:knightslime_block", "tconstruct:nahuatl", "tconstruct:nahuatl_slab", "tconstruct:nahuatl_stairs", "tconstruct:nahuatl_fence", "tconstruct:blazewood", "tconstruct:blazewood_slab", "tconstruct:blazewood_stairs", "tconstruct:blazewood_fence", "tconstruct:punji", "tconstruct:earth_cake", "tconstruct:sky_cake", "tconstruct:ichor_cake", "tconstruct:ender_cake", "tconstruct:blood_cake", "tconstruct:magma_cake", "tconstruct:cobalt_ore", "tconstruct:raw_cobalt_block", "tconstruct:sky_slime", "tconstruct:ichor_slime", "tconstruct:ender_slime", "tconstruct:earth_congealed_slime", "tconstruct:sky_congealed_slime", "tconstruct:ichor_congealed_slime", "tconstruct:ender_congealed_slime", "tconstruct:earth_slime_dirt", "tconstruct:sky_slime_dirt", "tconstruct:ichor_slime_dirt", "tconstruct:ender_slime_dirt", "tconstruct:earth_vanilla_slime_grass", "tconstruct:sky_vanilla_slime_grass", "tconstruct:ichor_vanilla_slime_grass", "tconstruct:ender_vanilla_slime_grass", "tconstruct:blood_vanilla_slime_grass", "tconstruct:earth_earth_slime_grass", "tconstruct:sky_earth_slime_grass", "tconstruct:ichor_earth_slime_grass", "tconstruct:ender_earth_slime_grass", "tconstruct:blood_earth_slime_grass", "tconstruct:earth_sky_slime_grass", "tconstruct:sky_sky_slime_grass", "tconstruct:ichor_sky_slime_grass", "tconstruct:ender_sky_slime_grass", "tconstruct:blood_sky_slime_grass", "tconstruct:earth_ender_slime_grass", "tconstruct:sky_ender_slime_grass", "tconstruct:ichor_ender_slime_grass", "tconstruct:ender_ender_slime_grass", "tconstruct:blood_ender_slime_grass", "tconstruct:earth_ichor_slime_grass", "tconstruct:sky_ichor_slime_grass", "tconstruct:ichor_ichor_slime_grass", "tconstruct:ender_ichor_slime_grass", "tconstruct:blood_ichor_slime_grass", "tconstruct:greenheart_planks", "tconstruct:greenheart_planks_slab", "tconstruct:greenheart_planks_stairs", "tconstruct:greenheart_fence", "tconstruct:stripped_greenheart_log", "tconstruct:stripped_greenheart_wood", "tconstruct:greenheart_log", "tconstruct:greenheart_wood", "tconstruct:greenheart_door", "tconstruct:greenheart_trapdoor", "tconstruct:greenheart_fence_gate", "tconstruct:greenheart_pressure_plate", "tconstruct:greenheart_button", "tconstruct:greenheart_sign", "tconstruct:greenheart_wall_sign", "tconstruct:greenheart_hanging_sign", "tconstruct:greenheart_wall_hanging_sign", "tconstruct:skyroot_planks", "tconstruct:skyroot_planks_slab", "tconstruct:skyroot_planks_stairs", "tconstruct:skyroot_fence", "tconstruct:stripped_skyroot_log", "tconstruct:stripped_skyroot_wood", "tconstruct:skyroot_log", "tconstruct:skyroot_wood", "tconstruct:skyroot_door", "tconstruct:skyroot_trapdoor", "tconstruct:skyroot_fence_gate", "tconstruct:skyroot_pressure_plate", "tconstruct:skyroot_button", "tconstruct:skyroot_sign", "tconstruct:skyroot_wall_sign", "tconstruct:skyroot_hanging_sign", "tconstruct:skyroot_wall_hanging_sign", "tconstruct:bloodshroom_planks", "tconstruct:bloodshroom_planks_slab", "tconstruct:bloodshroom_planks_stairs", "tconstruct:bloodshroom_fence", "tconstruct:stripped_bloodshroom_log", "tconstruct:stripped_bloodshroom_wood", "tconstruct:bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:bloodshroom_door", "tconstruct:bloodshroom_trapdoor", "tconstruct:bloodshroom_fence_gate", "tconstruct:bloodshroom_pressure_plate", "tconstruct:bloodshroom_button", "tconstruct:bloodshroom_sign", "tconstruct:bloodshroom_wall_sign", "tconstruct:bloodshroom_hanging_sign", "tconstruct:bloodshroom_wall_hanging_sign", "tconstruct:enderbark_planks", "tconstruct:enderbark_planks_slab", "tconstruct:enderbark_planks_stairs", "tconstruct:enderbark_fence", "tconstruct:stripped_enderbark_log", "tconstruct:stripped_enderbark_wood", "tconstruct:enderbark_log", "tconstruct:enderbark_wood", "tconstruct:enderbark_door", "tconstruct:enderbark_trapdoor", "tconstruct:enderbark_fence_gate", "tconstruct:enderbark_pressure_plate", "tconstruct:enderbark_button", "tconstruct:enderbark_sign", "tconstruct:enderbark_wall_sign", "tconstruct:enderbark_hanging_sign", "tconstruct:enderbark_wall_hanging_sign", "tconstruct:enderbark_roots", "tconstruct:earth_enderbark_roots", "tconstruct:sky_enderbark_roots", "tconstruct:ichor_enderbark_roots", "tconstruct:ender_enderbark_roots", "tconstruct:earth_slime_fern", "tconstruct:sky_slime_fern", "tconstruct:ichor_slime_fern", "tconstruct:ender_slime_fern", "tconstruct:blood_slime_fern", "tconstruct:earth_slime_tall_grass", "tconstruct:sky_slime_tall_grass", "tconstruct:ichor_slime_tall_grass", "tconstruct:ender_slime_tall_grass", "tconstruct:blood_slime_tall_grass", "tconstruct:potted_earth_slime_fern", "tconstruct:potted_sky_slime_fern", "tconstruct:potted_ichor_slime_fern", "tconstruct:potted_ender_slime_fern", "tconstruct:potted_blood_slime_fern", "tconstruct:earth_slime_sapling", "tconstruct:sky_slime_sapling", "tconstruct:blood_slime_sapling", "tconstruct:ichor_slime_sapling", "tconstruct:ender_slime_sapling", "tconstruct:potted_earth_slime_sapling", "tconstruct:potted_sky_slime_sapling", "tconstruct:potted_ichor_slime_sapling", "tconstruct:potted_ender_slime_sapling", "tconstruct:potted_blood_slime_sapling", "tconstruct:earth_slime_leaves", "tconstruct:sky_slime_leaves", "tconstruct:ichor_slime_leaves", "tconstruct:blood_slime_leaves", "tconstruct:ender_slime_leaves", "tconstruct:sky_slime_vine", "tconstruct:ender_slime_vine", "tconstruct:earth_slime_crystal_block", "tconstruct:budding_earth_slime_crystal", "tconstruct:earth_slime_crystal_cluster", "tconstruct:small_earth_slime_crystal_bud", "tconstruct:medium_earth_slime_crystal_bud", "tconstruct:large_earth_slime_crystal_bud", "tconstruct:sky_slime_crystal_block", "tconstruct:budding_sky_slime_crystal", "tconstruct:sky_slime_crystal_cluster", "tconstruct:small_sky_slime_crystal_bud", "tconstruct:medium_sky_slime_crystal_bud", "tconstruct:large_sky_slime_crystal_bud", "tconstruct:ichor_slime_crystal_block", "tconstruct:budding_ichor_slime_crystal", "tconstruct:ichor_slime_crystal_cluster", "tconstruct:small_ichor_slime_crystal_bud", "tconstruct:medium_ichor_slime_crystal_bud", "tconstruct:large_ichor_slime_crystal_bud", "tconstruct:ender_slime_crystal_block", "tconstruct:budding_ender_slime_crystal", "tconstruct:ender_slime_crystal_cluster", "tconstruct:small_ender_slime_crystal_bud", "tconstruct:medium_ender_slime_crystal_bud", "tconstruct:large_ender_slime_crystal_bud", "tconstruct:blaze_head", "tconstruct:end<PERSON>_head", "tconstruct:stray_head", "tconstruct:husk_head", "tconstruct:drowned_head", "tconstruct:spider_head", "tconstruct:cave_spider_head", "tconstruct:piglin_brute_head", "tconstruct:zombified_piglin_head", "tconstruct:blaze_wall_head", "tconstruct:enderman_wall_head", "tconstruct:stray_wall_head", "tconstruct:husk_wall_head", "tconstruct:drowned_wall_head", "tconstruct:spider_wall_head", "tconstruct:cave_spider_wall_head", "tconstruct:piglin_brute_wall_head", "tconstruct:zombified_piglin_wall_head", "tconstruct:crafting_station", "tconstruct:tinker_station", "tconstruct:part_builder", "tconstruct:tinkers_chest", "tconstruct:part_chest", "tconstruct:cast_chest", "tconstruct:modifier_worktable", "tconstruct:tinkers_anvil", "tconstruct:scorched_anvil", "tconstruct:grout", "tconstruct:nether_grout", "tconstruct:seared_stone", "tconstruct:seared_stone_slab", "tconstruct:seared_stone_stairs", "tconstruct:seared_cobble", "tconstruct:seared_cobble_slab", "tconstruct:seared_cobble_stairs", "tconstruct:seared_cobble_wall", "tconstruct:seared_paver", "tconstruct:seared_paver_slab", "tconstruct:seared_paver_stairs", "tconstruct:seared_bricks", "tconstruct:seared_bricks_slab", "tconstruct:seared_bricks_stairs", "tconstruct:seared_bricks_wall", "tconstruct:seared_cracked_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks", "tconstruct:seared_ladder", "tconstruct:seared_glass", "tconstruct:seared_soul_glass", "tconstruct:seared_tinted_glass", "tconstruct:seared_glass_pane", "tconstruct:seared_soul_glass_pane", "tconstruct:seared_drain", "tconstruct:seared_duct", "tconstruct:seared_chute", "tconstruct:scorched_stone", "tconstruct:polished_scorched_stone", "tconstruct:scorched_bricks", "tconstruct:scorched_bricks_slab", "tconstruct:scorched_bricks_stairs", "tconstruct:scorched_bricks_fence", "tconstruct:scorched_road", "tconstruct:scorched_road_slab", "tconstruct:scorched_road_stairs", "tconstruct:chiseled_scorched_bricks", "tconstruct:scorched_ladder", "tconstruct:scorched_glass", "tconstruct:scorched_soul_glass", "tconstruct:scorched_tinted_glass", "tconstruct:scorched_glass_pane", "tconstruct:scorched_soul_glass_pane", "tconstruct:scorched_drain", "tconstruct:scorched_duct", "tconstruct:scorched_chute", "tconstruct:seared_fuel_tank", "tconstruct:seared_fuel_gauge", "tconstruct:seared_ingot_tank", "tconstruct:seared_ingot_gauge", "tconstruct:seared_lantern", "tconstruct:seared_faucet", "tconstruct:seared_channel", "tconstruct:seared_basin", "tconstruct:seared_table", "tconstruct:seared_casting_tank", "tconstruct:scorched_fuel_tank", "tconstruct:scorched_fuel_gauge", "tconstruct:scorched_ingot_tank", "tconstruct:scorched_ingot_gauge", "tconstruct:scorched_lantern", "tconstruct:scorched_faucet", "tconstruct:scorched_channel", "tconstruct:scorched_basin", "tconstruct:scorched_table", "tconstruct:scorched_proxy_tank", "tconstruct:copper_gauge", "tconstruct:obsidian_gauge", "tconstruct:seared_fluid_cannon", "tconstruct:scorched_fluid_cannon", "tconstruct:smeltery_controller", "tconstruct:foundry_controller", "tconstruct:seared_melter", "tconstruct:seared_heater", "tconstruct:scorched_alloyer", "tconstruct:venom_fluid", "tconstruct:earth_slime_fluid", "tconstruct:sky_slime_fluid", "tconstruct:ender_slime_fluid", "tconstruct:magma_fluid", "tconstruct:honey_fluid", "tconstruct:beetroot_soup_fluid", "tconstruct:mushroom_stew_fluid", "tconstruct:rabbit_stew_fluid", "tconstruct:meat_soup_fluid", "tconstruct:seared_stone_fluid", "tconstruct:scorched_stone_fluid", "tconstruct:molten_clay_fluid", "tconstruct:molten_glass_fluid", "tconstruct:liquid_soul_fluid", "tconstruct:molten_porcelain_fluid", "tconstruct:molten_obsidian_fluid", "tconstruct:molten_ender_fluid", "tconstruct:blazing_blood_fluid", "tconstruct:molten_emerald_fluid", "tconstruct:molten_quartz_fluid", "tconstruct:molten_amethyst_fluid", "tconstruct:molten_diamond_fluid", "tconstruct:molten_debris_fluid", "tconstruct:molten_iron_fluid", "tconstruct:molten_gold_fluid", "tconstruct:molten_copper_fluid", "tconstruct:molten_cobalt_fluid", "tconstruct:molten_steel_fluid", "tconstruct:molten_slimesteel_fluid", "tconstruct:molten_amethyst_bronze_fluid", "tconstruct:molten_rose_gold_fluid", "tconstruct:molten_pig_iron_fluid", "tconstruct:molten_manyullyn_fluid", "tconstruct:molten_hepatizon_fluid", "tconstruct:molten_queens_slime_fluid", "tconstruct:molten_soulsteel_fluid", "tconstruct:molten_netherite_fluid", "tconstruct:molten_knightslime_fluid", "tconstruct:molten_tin_fluid", "tconstruct:molten_aluminum_fluid", "tconstruct:molten_lead_fluid", "tconstruct:molten_silver_fluid", "tconstruct:molten_nickel_fluid", "tconstruct:molten_zinc_fluid", "tconstruct:molten_platinum_fluid", "tconstruct:molten_tungsten_fluid", "tconstruct:molten_osmium_fluid", "tconstruct:molten_uranium_fluid", "tconstruct:molten_bronze_fluid", "tconstruct:molten_brass_fluid", "tconstruct:molten_electrum_fluid", "tconstruct:molten_invar_fluid", "tconstruct:molten_constantan_fluid", "tconstruct:molten_pewter_fluid", "tconstruct:molten_enderium_fluid", "tconstruct:molten_lumium_fluid", "tconstruct:molten_signalum_fluid", "tconstruct:molten_refined_glowstone_fluid", "tconstruct:molten_refined_obsidian_fluid", "tconstruct:molten_nicrosil_fluid", "create:chain_conveyor", "create:item_hatch", "create:packager", "create:repackager", "create:package_frogport", "create:white_postbox", "create:orange_postbox", "create:magenta_postbox", "create:light_blue_postbox", "create:yellow_postbox", "create:lime_postbox", "create:pink_postbox", "create:gray_postbox", "create:light_gray_postbox", "create:cyan_postbox", "create:purple_postbox", "create:blue_postbox", "create:brown_postbox", "create:green_postbox", "create:red_postbox", "create:black_postbox", "create:stock_link", "create:stock_ticker", "create:redstone_requester", "create:factory_gauge", "create:white_table_cloth", "create:orange_table_cloth", "create:magenta_table_cloth", "create:light_blue_table_cloth", "create:yellow_table_cloth", "create:lime_table_cloth", "create:pink_table_cloth", "create:gray_table_cloth", "create:light_gray_table_cloth", "create:cyan_table_cloth", "create:purple_table_cloth", "create:blue_table_cloth", "create:brown_table_cloth", "create:green_table_cloth", "create:red_table_cloth", "create:black_table_cloth", "create:andesite_table_cloth", "create:brass_table_cloth", "create:copper_table_cloth", "create:pulse_timer", "create:desk_bell", "create:weathered_iron_block", "create:cardboard_block", "create:bound_cardboard_block", "create:cherry_window", "create:bamboo_window", "create:industrial_iron_window", "create:weathered_iron_window", "create:cherry_window_pane", "create:bamboo_window_pane", "create:industrial_iron_window_pane", "create:weathered_iron_window_pane", "undefined:bronze_fluid_tank"]}, "typeWorldgenCarver": {"type": "string", "enum": ["minecraft:cave", "minecraft:nether_cave", "minecraft:canyon"]}, "typeHeightProviderType": {"type": "string", "enum": ["minecraft:uniform", "minecraft:biased_to_bottom", "immersiveengineering:ie_range", "minecraft:very_biased_to_bottom", "minecraft:trapezoid", "mekanism:configurable", "minecraft:constant", "minecraft:weighted_list"]}, "typeWorldgenDensityFunction": {"type": "string", "enum": ["minecraft:overworld_amplified/sloped_cheese", "minecraft:shift_x", "minecraft:overworld/ridges_folded", "minecraft:overworld/caves/noodle", "minecraft:overworld/jaggedness", "minecraft:overworld_large_biomes/offset", "minecraft:overworld/depth", "minecraft:overworld/continents", "minecraft:zero", "minecraft:overworld/base_3d_noise", "minecraft:overworld/caves/pillars", "minecraft:overworld/caves/spaghetti_2d_thickness_modulator", "minecraft:overworld_large_biomes/continents", "minecraft:overworld_amplified/offset", "minecraft:overworld_large_biomes/depth", "minecraft:overworld/sloped_cheese", "minecraft:overworld/factor", "minecraft:overworld/ridges", "minecraft:overworld/caves/spaghetti_roughness_function", "minecraft:shift_z", "minecraft:overworld_amplified/depth", "minecraft:overworld_amplified/jaggedness", "minecraft:overworld_large_biomes/erosion", "minecraft:overworld/caves/spaghetti_2d", "minecraft:overworld_large_biomes/factor", "minecraft:overworld_amplified/factor", "minecraft:nether/base_3d_noise", "minecraft:end/base_3d_noise", "minecraft:overworld_large_biomes/sloped_cheese", "minecraft:overworld/erosion", "minecraft:overworld/offset", "minecraft:end/sloped_cheese", "minecraft:overworld/caves/entrances", "minecraft:overworld_large_biomes/jaggedness", "minecraft:y"]}, "typeLootNbtProviderType": {"type": "string", "enum": ["minecraft:context", "minecraft:storage"]}, "typeWorldgenDensityFunctionType": {"type": "string", "enum": ["minecraft:quarter_negative", "minecraft:max", "minecraft:add", "minecraft:blend_alpha", "minecraft:half_negative", "minecraft:y_clamped_gradient", "minecraft:min", "minecraft:clamp", "minecraft:range_choice", "minecraft:cache_once", "minecraft:noise", "minecraft:shifted_noise", "minecraft:blend_density", "minecraft:weird_scaled_sampler", "minecraft:cache_all_in_cell", "minecraft:shift", "minecraft:squeeze", "minecraft:mul", "minecraft:cube", "minecraft:square", "minecraft:old_blended_noise", "minecraft:constant", "minecraft:spline", "minecraft:interpolated", "minecraft:shift_b", "minecraft:abs", "minecraft:blend_offset", "minecraft:cache_2d", "minecraft:flat_cache", "minecraft:shift_a", "minecraft:end_islands", "minecraft:beardifier"]}, "typeBlockEntityType": {"type": "string", "enum": ["minecraft:furnace", "minecraft:chest", "minecraft:trapped_chest", "minecraft:ender_chest", "minecraft:jukebox", "minecraft:dispenser", "minecraft:dropper", "minecraft:sign", "minecraft:hanging_sign", "minecraft:mob_spawner", "minecraft:piston", "minecraft:brewing_stand", "minecraft:enchanting_table", "minecraft:end_portal", "minecraft:beacon", "minecraft:skull", "minecraft:daylight_detector", "minecraft:hopper", "minecraft:comparator", "minecraft:banner", "minecraft:structure_block", "minecraft:end_gateway", "minecraft:command_block", "minecraft:shulker_box", "minecraft:bed", "minecraft:conduit", "minecraft:barrel", "minecraft:smoker", "minecraft:blast_furnace", "minecraft:lectern", "minecraft:bell", "minecraft:jigsaw", "minecraft:campfire", "minecraft:beehive", "minecraft:sculk_sensor", "minecraft:calibrated_sculk_sensor", "minecraft:sculk_catalyst", "minecraft:sculk_shrieker", "minecraft:chiseled_bookshelf", "minecraft:brushable_block", "minecraft:decorated_pot", "ae2:sky_tank", "ae2:inscriber", "ae2:debug_energy_gen", "ae2:light_detector", "ae2:interface", "ae2:creative_energy_cell", "ae2:charger", "ae2:quantum_ring", "ae2:crafting_unit", "ae2:sky_chest", "ae2:cable_bus", "ae2:spatial_anchor", "ae2:crank", "ae2:paint", "ae2:growth_accelerator", "ae2:drive", "ae2:io_port", "ae2:debug_cube_gen", "ae2:debug_chunk_loader", "ae2:crafting_storage", "ae2:debug_phantom_node", "ae2:cell_workbench", "ae2:chest", "ae2:crafting_monitor", "ae2:molecular_assembler", "ae2:controller", "ae2:energy_cell", "ae2:spatial_pylon", "ae2:debug_item_gen", "ae2:energy_acceptor", "ae2:spatial_io_port", "ae2:pattern_provider", "ae2:crystal_resonance_generator", "ae2:condenser", "ae2:dense_energy_cell", "ae2:wireless_access_point", "ae2:vibration_chamber", "bigger_ae2:crafting_accelerator", "netmusic:music_player", "aerlunerpg:crystalsmithril", "aerlunerpg:capacitorsouls", "aerlunerpg:capacitorofsouls", "aerlunerpg:crystalsvoid", "aerlunerpg:crystalsattack", "aerlunerpg:bite_1", "aerlunerpg:furnacev", "aerlunerpg:creatorvoid", "aerlunerpg:holespawn_1", "aerlunerpg:smallbat", "aerlunerpg:candystick", "aerlunerpg:candyctickbig", "aerlunerpg:rabbithole", "aerlunerpg:oldmantrade", "aerlunerpg:mushroombrothblock", "aerlunerpg:squirrelblock", "aerlunerpg:owlarmorblock", "aerlunerpg:pipeblock", "aerlunerpg:stunning", "aerlunerpg:entblock", "aerlunerpg:shockwave", "aerlunerpg:gnomehome", "aerlunerpg:chestbig", "aerlunerpg:toilet", "aerlunerpg:toilet_2", "aerlunerpg:mine", "aerlunerpg:chestcandy", "aerlunerpg:furnacesmall", "aerlunerpg:chestbigwood", "cataclysm:obsidian_explosion_trap_bricks", "cataclysm:sadsotne_ignite_trap", "cataclysm:altar_of_void", "cataclysm:altar_of_fire", "cataclysm:altar_of_amethyst", "cataclysm:altar_of_abyss", "cataclysm:cursed_tombstone", "cataclysm:abyssal_egg", "cataclysm:emp", "cataclysm:cataclysm_skull", "cataclysm:mechanical_fusion_anvil", "cataclysm:door_of_seal", "cataclysm:goddess_statue", "cataclysm:boss_respawner", "tconstruct:crafting_station", "tconstruct:tinker_station", "tconstruct:part_builder", "tconstruct:modifier_worktable", "tconstruct:modifier_chest", "tconstruct:part_chest", "tconstruct:cast_chest", "tconstruct:smeltery_component", "tconstruct:drain", "tconstruct:chute", "tconstruct:duct", "tconstruct:tank", "tconstruct:fluid_cannon", "tconstruct:lantern", "tconstruct:melter", "tconstruct:smeltery", "tconstruct:foundry", "tconstruct:heater", "tconstruct:alloyer", "tconstruct:faucet", "tconstruct:channel", "tconstruct:gauge", "tconstruct:basin", "tconstruct:table", "tconstruct:proxy_tank", "tconstruct:casting_tank", "ftbquests:barrier", "ftbquests:stage_barrier", "ftbquests:detector", "ftbquests:loot_crate_opener", "ftbquests:core_task_screen", "ftbquests:aux_task_screen", "immersiveengineering:balloon", "immersiveengineering:stripcurtain", "immersiveengineering:<PERSON><PERSON><PERSON>", "immersiveengineering:coresample", "immersiveengineering:craftingtable", "immersiveengineering:woodencrate", "immersiveengineering:woodenbarrel", "immersiveengineering:modworkbench", "immersiveengineering:circuittable", "immersiveengineering:sorter", "immersiveengineering:itembatcher", "immersiveengineering:turntable", "immersiveengineering:<PERSON><PERSON><PERSON>", "immersiveengineering:logic<PERSON>t", "immersiveengineering:watermill_master", "immersiveengineering:watermill_dummy", "immersiveengineering:windmill", "immersiveengineering:razorwire", "immersiveengineering:toolbox", "immersiveengineering:structuralarm", "immersiveengineering:connectorstructural", "immersiveengineering:transformer", "immersiveengineering:posttransformer", "immersiveengineering:transformerhv", "immersiveengineering:breakerswitch", "immersiveengineering:redstonebreaker", "immersiveengineering:energymeter_master", "immersiveengineering:energymeter_dummy", "immersiveengineering:connectorredstone", "immersiveengineering:connectorprobe", "immersiveengineering:connectorbundled", "immersiveengineering:feedthrough", "immersiveengineering:capacitorlv", "immersiveengineering:capacitormv", "immersiveengineering:capacitorhv", "immersiveengineering:capacitorcreative", "immersiveengineering:metalbarrel", "immersiveengineering:fluidpump_master", "immersiveengineering:fluidpump_dummy", "immersiveengineering:fluidplacer", "immersiveengineering:blastfurnacepreheater_master", "immersiveengineering:blastfurnacepreheater_dummy", "immersiveengineering:furnaceheater", "immersiveengineering:dynamo", "immersiveengineering:thermoelectricgen", "immersiveengineering:electriclantern", "immersiveengineering:chargingstation", "immersiveengineering:fluidpipe", "immersiveengineering:sampledrill_master", "immersiveengineering:sampledrill_dummy", "immersiveengineering:teslacoil_master", "immersiveengineering:teslacoil_dummy", "immersiveengineering:floodlight", "immersiveengineering:turretchem_master", "immersiveengineering:turretchem_dummy", "immersiveengineering:turretgun_master", "immersiveengineering:turretgun_dummy", "immersiveengineering:cloche_master", "immersiveengineering:cloche_dummy", "immersiveengineering:chute", "immersiveengineering:electromagnet", "immersiveengineering:fakelight", "immersiveengineering:lv_conn", "immersiveengineering:lv_relay", "immersiveengineering:mv_conn", "immersiveengineering:mv_relay", "immersiveengineering:hv_conn", "immersiveengineering:hv_relay", "immersiveengineering:basic", "immersiveengineering:redstone", "immersiveengineering:dropper", "immersiveengineering:vertical", "immersiveengineering:splitter", "immersiveengineering:extract", "immersiveengineering:coke_oven_master", "immersiveengineering:coke_oven_dummy", "immersiveengineering:blast_furnace_master", "immersiveengineering:blast_furnace_dummy", "immersiveengineering:advanced_blast_furnace_master", "immersiveengineering:advanced_blast_furnace_dummy", "immersiveengineering:alloy_smelter_master", "immersiveengineering:alloy_smelter_dummy", "immersiveengineering:lightning_rod_master", "immersiveengineering:lightning_rod_dummy", "immersiveengineering:crusher_master", "immersiveengineering:crusher_dummy", "immersiveengineering:fermenter_master", "immersiveengineering:fermenter_dummy", "immersiveengineering:diesel_generator_master", "immersiveengineering:diesel_generator_dummy", "immersiveengineering:metal_press_master", "immersiveengineering:metal_press_dummy", "immersiveengineering:assembler_master", "immersiveengineering:assembler_dummy", "immersiveengineering:auto_workbench_master", "immersiveengineering:auto_workbench_dummy", "immersiveengineering:bottling_machine_master", "immersiveengineering:bottling_machine_dummy", "immersiveengineering:silo_master", "immersiveengineering:silo_dummy", "immersiveengineering:tank_master", "immersiveengineering:tank_dummy", "immersiveengineering:mixer_master", "immersiveengineering:mixer_dummy", "immersiveengineering:refinery_master", "immersiveengineering:refinery_dummy", "immersiveengineering:squeezer_master", "immersiveengineering:squeezer_dummy", "immersiveengineering:bucket_wheel_master", "immersiveengineering:bucket_wheel_dummy", "immersiveengineering:excavator_master", "immersiveengineering:excavator_dummy", "immersiveengineering:sawmill_master", "immersiveengineering:sawmill_dummy", "immersiveengineering:arc_furnace_master", "immersiveengineering:arc_furnace_dummy", "createoreexcavation:drill", "createoreexcavation:kinetic_input", "createoreexcavation:multiblock", "createoreexcavation:io", "createoreexcavation:extractor", "createoreexcavation:sample_drill", "create:schematic<PERSON><PERSON>", "create:schematic_table", "create:simple_kinetic", "create:motor", "create:gearbox", "create:encased_shaft", "create:encased_cogwheel", "create:encased_large_cogwheel", "create:adjustable_chain_gearshift", "create:encased_fan", "create:nozzle", "create:clutch", "create:gearshift", "create:turntable", "create:hand_crank", "create:valve_handle", "create:cuckoo_clock", "create:gantry_shaft", "create:gantry_pinion", "create:chain_conveyor", "create:mechanical_pump", "create:smart_fluid_pipe", "create:fluid_pipe", "create:encased_fluid_pipe", "create:glass_fluid_pipe", "create:fluid_valve", "create:fluid_tank", "create:creative_fluid_tank", "create:hose_pulley", "create:spout", "create:item_drain", "create:belt", "create:chute", "create:smart_chute", "create:andesite_tunnel", "create:brass_tunnel", "create:mechanical_arm", "create:item_vault", "create:item_hatch", "create:packager", "create:repackager", "create:package_frogport", "create:package_postbox", "create:table_cloth", "create:packager_link", "create:stock_ticker", "create:redstone_requester", "create:mechanical_piston", "create:windmill_bearing", "create:mechanical_bearing", "create:clockwork_bearing", "create:rope_pulley", "create:elevator_pulley", "create:elevator_contact", "create:chassis", "create:sticker", "create:contraption_controls", "create:drill", "create:saw", "create:harvester", "create:mechanical_roller", "create:portable_storage_interface", "create:portable_fluid_interface", "create:steam_engine", "create:steam_whistle", "create:powered_shaft", "create:flywheel", "create:millstone", "create:crushing_wheel", "create:crushing_wheel_controller", "create:water_wheel", "create:large_water_wheel", "create:mechanical_press", "create:mechanical_mixer", "create:deployer", "create:basin", "create:blaze_heater", "create:mechanical_crafter", "create:sequenced_gearshift", "create:rotation_speed_controller", "create:speedometer", "create:stressometer", "create:analog_lever", "create:placard", "create:factory_panel", "create:cart_assembler", "create:redstone_link", "create:nixie_tube", "create:display_link", "create:stockpile_switch", "create:creative_crate", "create:depot", "create:weighted_ejector", "create:funnel", "create:content_observer", "create:pulse_extender", "create:pulse_repeater", "create:pulse_timer", "create:lectern_controller", "create:backtank", "create:peculiar_bell", "create:cursed_bell", "create:desk_bell", "create:toolbox", "create:track", "create:fake_track", "create:bogey", "create:track_station", "create:sliding_door", "create:copycat", "create:flap_display", "create:track_signal", "create:track_observer", "create:clipboard", "mantle:sign", "mantle:hanging_sign", "mekanism:basic_combining_factory", "mekanism:basic_compressing_factory", "mekanism:basic_crushing_factory", "mekanism:basic_enriching_factory", "mekanism:basic_infusing_factory", "mekanism:basic_injecting_factory", "mekanism:basic_purifying_factory", "mekanism:basic_sawing_factory", "mekanism:basic_smelting_factory", "mekanism:advanced_combining_factory", "mekanism:advanced_compressing_factory", "mekanism:advanced_crushing_factory", "mekanism:advanced_enriching_factory", "mekanism:advanced_infusing_factory", "mekanism:advanced_injecting_factory", "mekanism:advanced_purifying_factory", "mekanism:advanced_sawing_factory", "mekanism:advanced_smelting_factory", "mekanism:elite_combining_factory", "mekanism:elite_compressing_factory", "mekanism:elite_crushing_factory", "mekanism:elite_enriching_factory", "mekanism:elite_infusing_factory", "mekanism:elite_injecting_factory", "mekanism:elite_purifying_factory", "mekanism:elite_sawing_factory", "mekanism:elite_smelting_factory", "mekanism:ultimate_combining_factory", "mekanism:ultimate_compressing_factory", "mekanism:ultimate_crushing_factory", "mekanism:ultimate_enriching_factory", "mekanism:ultimate_infusing_factory", "mekanism:ultimate_injecting_factory", "mekanism:ultimate_purifying_factory", "mekanism:ultimate_sawing_factory", "mekanism:ultimate_smelting_factory", "mekanism:bounding_block", "mekanism:boiler_casing", "mekanism:boiler_valve", "mekanism:cardboard_box", "mekanism:chargepad", "mekanism:chemical_crystallizer", "mekanism:chemical_dissolution_chamber", "mekanism:chemical_infuser", "mekanism:chemical_injection_chamber", "mekanism:chemical_oxidizer", "mekanism:chemical_washer", "mekanism:combiner", "mekanism:crusher", "mekanism:digital_miner", "mekanism:dynamic_tank", "mekanism:dynamic_valve", "mekanism:electric_pump", "mekanism:electrolytic_separator", "mekanism:energized_smelter", "mekanism:enrichment_chamber", "mekanism:fluidic_plenisher", "mekanism:formulaic_assemblicator", "mekanism:fuelwood_heater", "mekanism:induction_casing", "mekanism:induction_port", "mekanism:laser", "mekanism:laser_amplifier", "mekanism:laser_tractor_beam", "mekanism:logistical_sorter", "mekanism:metallurgic_infuser", "mekanism:oredictionificator", "mekanism:osmium_compressor", "mekanism:personal_barrel", "mekanism:personal_chest", "mekanism:precision_sawmill", "mekanism:pressure_disperser", "mekanism:pressurized_reaction_chamber", "mekanism:purification_chamber", "mekanism:quantum_entangloporter", "mekanism:resistive_heater", "mekanism:modification_station", "mekanism:isotopic_centrifuge", "mekanism:nutritional_liquifier", "mekanism:rotary_condensentrator", "mekanism:security_desk", "mekanism:seismic_vibrator", "mekanism:solar_neutron_activator", "mekanism:structural_glass", "mekanism:superheating_element", "mekanism:teleporter", "mekanism:thermal_evaporation_block", "mekanism:thermal_evaporation_controller", "mekanism:thermal_evaporation_valve", "mekanism:radioactive_waste_barrel", "mekanism:industrial_alarm", "mekanism:antiprotonic_nucleosynthesizer", "mekanism:pigment_extractor", "mekanism:pigment_mixer", "mekanism:painting_machine", "mekanism:sps_casing", "mekanism:sps_port", "mekanism:supercharged_coil", "mekanism:dimensional_stabilizer", "mekanism:qio_drive_array", "mekanism:qio_dashboard", "mekanism:qio_importer", "mekanism:qio_exporter", "mekanism:qio_redstone_adapter", "mekanism:diversion_transporter", "mekanism:restrictive_transporter", "mekanism:basic_logistical_transporter", "mekanism:advanced_logistical_transporter", "mekanism:elite_logistical_transporter", "mekanism:ultimate_logistical_transporter", "mekanism:basic_mechanical_pipe", "mekanism:advanced_mechanical_pipe", "mekanism:elite_mechanical_pipe", "mekanism:ultimate_mechanical_pipe", "mekanism:basic_pressurized_tube", "mekanism:advanced_pressurized_tube", "mekanism:elite_pressurized_tube", "mekanism:ultimate_pressurized_tube", "mekanism:basic_thermodynamic_conductor", "mekanism:advanced_thermodynamic_conductor", "mekanism:elite_thermodynamic_conductor", "mekanism:ultimate_thermodynamic_conductor", "mekanism:basic_universal_cable", "mekanism:advanced_universal_cable", "mekanism:elite_universal_cable", "mekanism:ultimate_universal_cable", "mekanism:basic_energy_cube", "mekanism:advanced_energy_cube", "mekanism:elite_energy_cube", "mekanism:ultimate_energy_cube", "mekanism:creative_energy_cube", "mekanism:basic_chemical_tank", "mekanism:advanced_chemical_tank", "mekanism:elite_chemical_tank", "mekanism:ultimate_chemical_tank", "mekanism:creative_chemical_tank", "mekanism:basic_fluid_tank", "mekanism:advanced_fluid_tank", "mekanism:elite_fluid_tank", "mekanism:ultimate_fluid_tank", "mekanism:creative_fluid_tank", "mekanism:basic_bin", "mekanism:advanced_bin", "mekanism:elite_bin", "mekanism:ultimate_bin", "mekanism:creative_bin", "mekanism:basic_induction_cell", "mekanism:advanced_induction_cell", "mekanism:elite_induction_cell", "mekanism:ultimate_induction_cell", "mekanism:basic_induction_provider", "mekanism:advanced_induction_provider", "mekanism:elite_induction_provider", "mekanism:ultimate_induction_provider"]}, "typeDamageType": {"type": "string", "enum": ["immersiveengineering:revolver_wolfpack", "create:fan_fire", "minecraft:cramming", "tconstruct:fluid_spike_ranged", "minecraft:trident", "tconstruct:fluid_impact_melee", "minecraft:sting", "attributeslib:detonation", "minecraft:thrown", "immersiveengineering:revolver_buckshot_turret", "tconstruct:smeltery_magic", "cataclysm:lightning", "immersiveengineering:sawblade", "immersiveengineering:revolver_homing_turret", "minecraft:bad_respawn_point", "minecraft:generic_kill", "immersiveengineering:acid", "minecraft:arrow", "minecraft:sweet_berry_bush", "tconstruct:fluid_fire_melee", "create:mechanical_drill", "tconstruct:melee_arrow", "minecraft:out_of_world", "minecraft:drown", "tconstruct:mob_explosion_melee", "tconstruct:entangled", "ae2:matter_cannon", "create:potato_cannon", "minecraft:dry_out", "cataclysm:deathlaser", "tconstruct:smeltery_heat", "immersiveengineering:revolver_wolfpack_turret", "cataclysm:emp", "cataclysm:maledictio", "minecraft:explosion", "minecraft:generic", "immersiveengineering:revolver_armorpiercing", "minecraft:indirect_magic", "immersiveengineering:revolver_casull", "immersiveengineering:revolver_dragonsbreath", "immersiveengineering:sawblade_turret", "immersiveengineering:crushed", "cataclysm:abyssal_burn", "minecraft:lava", "immersiveengineering:revolver_casull_turret", "immersiveengineering:tesla", "tconstruct:bleeding", "minecraft:unattributed_fireball", "minecraft:stalagmite", "minecraft:mob_attack", "minecraft:freeze", "cataclysm:laser", "immersiveengineering:revolver_silver", "minecraft:starve", "cataclysm:storm_bringer", "minecraft:fireworks", "minecraft:falling_anvil", "minecraft:in_wall", "immersiveengineering:revolver_buckshot", "create:cuckoo_surprise", "cataclysm:penetrate", "cataclysm:maledictio_sagitta", "create:fan_lava", "tconstruct:explosion_melee", "tconstruct:explosion_ranged", "create:mechanical_roller", "immersiveengineering:tesla_primary", "create:run_over", "cataclysm:maledictio_magicae", "minecraft:thorns", "minecraft:mob_projectile", "tconstruct:fluid_cold_ranged", "tconstruct:fluid_fire_ranged", "minecraft:hot_floor", "tconstruct:mob_explosion_ranged", "minecraft:player_attack", "tconstruct:fluid_spike_melee", "create:mechanical_saw", "minecraft:sonic_boom", "minecraft:mob_attack_no_aggro", "tconstruct:piercing", "minecraft:fireball", "minecraft:player_explosion", "minecraft:falling_stalactite", "cataclysm:shredder", "tconstruct:self_destruct", "minecraft:dragon_breath", "minecraft:fly_into_wall", "immersiveengineering:revolver_potion", "immersiveengineering:razor_wire", "immersiveengineering:razor_shock", "immersiveengineering:revolver_armorpiercing_turret", "minecraft:wither", "tconstruct:fluid_magic_melee", "minecraft:falling_block", "cataclysm:maledictio_anima", "minecraft:outside_border", "tconstruct:fluid_cold_melee", "attributeslib:bleeding", "minecraft:wither_skull", "immersiveengineering:revolver_potion_turret", "create:crush", "immersiveengineering:railgun", "minecraft:on_fire", "attributeslib:fire_damage", "tconstruct:fluid_impact_ranged", "tconstruct:water_melee", "immersiveengineering:revolver_silver_turret", "immersiveengineering:revolver_dragonsbreath_turret", "immersiveengineering:wire_shock", "minecraft:in_fire", "minecraft:magic", "immersiveengineering:sawmill", "minecraft:cactus", "attributeslib:current_hp_damage", "immersiveengineering:revolver_homing", "tconstruct:fluid_magic_ranged", "minecraft:fall", "tconstruct:water_ranged", "mekanism:radiation", "mekanism:laser", "cataclysm:sword_dance", "attributeslib:cold_damage", "cataclysm:flame_strike", "minecraft:lightning_bolt", "immersiveengineering:railgun_turret"]}, "typePotatoProjectileRenderMode": {"type": "string", "enum": ["create:billboard", "create:stuck_to_entity", "create:tumble", "create:toward_motion"]}, "typeChatType": {"type": "string", "enum": ["minecraft:chat", "minecraft:say_command", "minecraft:team_msg_command_outgoing", "minecraft:emote_command", "minecraft:msg_command_outgoing", "minecraft:msg_command_incoming", "minecraft:team_msg_command_incoming"]}, "typeCatVariant": {"type": "string", "enum": ["minecraft:calico", "minecraft:british_shorthair", "minecraft:persian", "minecraft:white", "minecraft:red", "minecraft:all_black", "minecraft:black", "minecraft:siamese", "minecraft:tabby", "minecraft:ragdoll", "minecraft:jellie"]}, "typePigment": {"type": "string", "enum": ["mekanism:aqua", "mekanism:black", "mekanism:blue", "mekanism:brown", "mekanism:cyan", "mekanism:dark_red", "mekanism:empty", "mekanism:gray", "mekanism:green", "mekanism:light_blue", "mekanism:light_gray", "mekanism:lime", "mekanism:magenta", "mekanism:orange", "mekanism:pink", "mekanism:purple", "mekanism:red", "mekanism:white", "mekanism:yellow"]}, "typePaintingVariant": {"type": "string", "enum": ["minecraft:alban", "minecraft:aztec", "minecraft:aztec2", "minecraft:bomb", "minecraft:burning_skull", "minecraft:bust", "minecraft:courbet", "minecraft:creebet", "minecraft:donkey_kong", "minecraft:earth", "minecraft:fighters", "minecraft:fire", "minecraft:graham", "minecraft:kebab", "minecraft:match", "minecraft:pigscene", "minecraft:plant", "minecraft:pointer", "minecraft:pool", "minecraft:sea", "minecraft:skeleton", "minecraft:skull_and_roses", "minecraft:stage", "minecraft:sunset", "minecraft:void", "minecraft:wanderer", "minecraft:wasteland", "minecraft:water", "minecraft:wind", "minecraft:wither"]}, "typeWorldgenPlacedFeature": {"type": "string", "enum": ["aerlunerpg:stump_1", "minecraft:disk_sand", "mekanism:ore_osmium_middle", "minecraft:patch_large_fern", "minecraft:spruce_checked", "minecraft:trees_old_growth_pine_taiga", "minecraft:spring_open", "minecraft:flower_meadow", "mekanism:ore_tin_small_retrogen", "mekanism:salt_retrogen", "tconstruct:cobalt_ore_large", "minecraft:patch_grass_taiga", "minecraft:blue_ice", "minecraft:blackstone_blobs", "minecraft:sea_pickle", "aerlunerpg:oldmanst", "minecraft:ore_granite_lower", "minecraft:patch_crimson_roots", "minecraft:ore_copper", "minecraft:patch_tall_grass", "minecraft:trees_jungle", "minecraft:amethyst_geode", "minecraft:ore_copper_large", "minecraft:monster_room_deep", "minecraft:lush_caves_ceiling_vegetation", "minecraft:ore_soul_sand", "mekanism:ore_uranium_small_retrogen", "minecraft:fancy_oak_bees_0002", "minecraft:flower_default", "tconstruct:ichor_geode", "minecraft:trees_meadow", "minecraft:patch_berry_rare", "minecraft:patch_cactus", "minecraft:birch_checked", "minecraft:trees_savanna", "minecraft:patch_sunflower", "minecraft:trees_flower_forest", "minecraft:bamboo_vegetation", "minecraft:red_mushroom_taiga", "minecraft:end_island_decorated", "mekanism:salt", "mekanism:ore_lead_normal_retrogen", "minecraft:ice_patch", "minecraft:red_mushroom_normal", "minecraft:basalt_blobs", "minecraft:patch_cactus_desert", "minecraft:red_mushroom_nether", "minecraft:ore_coal_lower", "minecraft:crimson_forest_vegetation", "minecraft:crimson_fungi", "kubejs:diamond_block_ore_lower", "minecraft:forest_rock", "minecraft:ore_gold_nether", "minecraft:patch_grass_savanna", "minecraft:pointed_dripstone", "minecraft:disk_clay", "minecraft:ore_quartz_deltas", "minecraft:jungle_bush", "minecraft:underwater_magma", "minecraft:birch_bees_002", "minecraft:sculk_patch_ancient_city", "minecraft:patch_grass_badlands", "minecraft:flower_flower_forest", "minecraft:large_dripstone", "minecraft:lush_caves_clay", "minecraft:void_start_platform", "aerlunerpg:bushsmall", "mekanism:ore_uranium_small", "minecraft:classic_vines_cave_feature", "minecraft:trees_badlands", "minecraft:kelp_cold", "minecraft:ore_debris_small", "minecraft:spring_water", "minecraft:ore_blackstone", "minecraft:birch_tall", "minecraft:brown_mushroom_swamp", "minecraft:fossil_lower", "minecraft:oak_checked", "aerlunerpg:crystalsvoids", "minecraft:forest_flowers", "aerlunerpg:icerion", "minecraft:patch_pumpkin", "immersiveengineering:lead", "minecraft:seagrass_swamp", "minecraft:ore_diamond", "minecraft:spruce_on_snow", "minecraft:sculk_patch_deep_dark", "minecraft:trees_snowy", "minecraft:spring_lava_frozen", "minecraft:disk_gravel", "minecraft:patch_dead_bush_2", "minecraft:nether_sprouts", "minecraft:birch_bees_0002", "minecraft:oak", "immersiveengineering:deep_nickel", "aerlunerpg:grasssnow", "minecraft:dark_oak_checked", "minecraft:glowstone", "aerlunerpg:smalltreeblock_2", "minecraft:super_birch_bees", "minecraft:seagrass_simple", "create:striated_ores_overworld", "minecraft:trees_taiga", "minecraft:ore_clay", "minecraft:ore_gold", "minecraft:seagrass_warm", "tconstruct:cobalt_ore_small", "minecraft:ice_spike", "minecraft:patch_sugar_cane_badlands", "minecraft:acacia", "minecraft:ore_diamond_large", "minecraft:dripstone_cluster", "minecraft:pile_ice", "minecraft:trees_grove", "minecraft:ore_infested", "minecraft:super_birch_bees_0002", "mekanism:ore_osmium_small", "minecraft:patch_cactus_decorated", "minecraft:flower_plain", "minecraft:ore_gold_deltas", "mekanism:ore_osmium_small_retrogen", "mekanism:ore_osmium_upper", "minecraft:patch_melon_sparse", "minecraft:spring_closed_double", "minecraft:ore_iron_small", "minecraft:ore_gravel", "minecraft:brown_mushroom_taiga", "minecraft:cave_vines", "minecraft:bamboo_light", "aerlunerpg:smalltreeblock_1", "minecraft:brown_mushroom_normal", "minecraft:patch_grass_jungle", "minecraft:ore_diamond_buried", "minecraft:spring_lava", "minecraft:trees_birch", "minecraft:pile_snow", "minecraft:ore_gravel_nether", "minecraft:patch_sugar_cane_desert", "aerlunerpg:snowdrop", "tconstruct:ender_geode", "minecraft:patch_melon", "minecraft:flower_plains", "minecraft:pine_checked", "aerlunerpg:stonesmall", "mekanism:ore_osmium_middle_retrogen", "immersiveengineering:silver", "minecraft:mega_jungle_tree_checked", "minecraft:vines", "mekanism:ore_fluorite_buried", "immersiveengineering:mineral_veins", "minecraft:seagrass_deep", "minecraft:fancy_oak_bees_002", "minecraft:patch_berry_common", "minecraft:ore_lapis", "mekanism:ore_fluorite_buried_retrogen", "minecraft:seagrass_cold", "minecraft:iceberg_blue", "minecraft:ore_magma", "minecraft:trees_water", "mekanism:ore_fluorite_normal_retrogen", "minecraft:freeze_top_layer", "mekanism:ore_lead_normal", "minecraft:red_mushroom_old_growth", "minecraft:ore_iron_middle", "minecraft:fancy_oak_checked", "minecraft:spore_blossom", "minecraft:grass_bonemeal", "minecraft:ore_gold_extra", "mekanism:ore_tin_large_retrogen", "minecraft:end_spike", "immersiveengineering:nickel", "minecraft:ore_lapis_buried", "minecraft:trees_swamp", "minecraft:ore_granite_upper", "aerlunerpg:rabbitgoles", "minecraft:glowstone_extra", "minecraft:oak_bees_002", "minecraft:seagrass_river", "immersiveengineering:bauxite", "minecraft:flower_warm", "minecraft:lush_caves_vegetation", "minecraft:pile_hay", "minecraft:basalt_pillar", "minecraft:monster_room", "minecraft:jungle_tree", "aerlunerpg:caveice_tree", "minecraft:trees_windswept_hills", "minecraft:mangrove_checked", "minecraft:patch_grass_forest", "minecraft:trees_mangrove", "minecraft:patch_soul_fire", "minecraft:spruce", "minecraft:ore_andesite_upper", "minecraft:seagrass_deep_cold", "minecraft:fossil_upper", "minecraft:lake_lava_surface", "minecraft:trees_cherry", "minecraft:ore_iron_upper", "minecraft:weeping_vines", "minecraft:trees_windswept_forest", "minecraft:ore_tuff", "minecraft:patch_dead_bush_badlands", "minecraft:ore_coal_upper", "minecraft:mega_pine_checked", "kubejs:diamond_block_ore_upper", "minecraft:end_gateway_return", "minecraft:small_basalt_columns", "minecraft:trees_birch_and_oak", "minecraft:trees_old_growth_spruce_taiga", "aerlunerpg:caramelore", "tconstruct:sky_geode", "minecraft:chorus_plant", "minecraft:ore_dirt", "minecraft:desert_well", "minecraft:ore_diorite_upper", "minecraft:patch_grass_plain", "minecraft:patch_fire", "minecraft:patch_tall_grass_2", "minecraft:warped_fungi", "minecraft:acacia_checked", "minecraft:fancy_oak_bees", "create:striated_ores_nether", "minecraft:warm_ocean_vegetation", "minecraft:flower_cherry", "minecraft:flower_forest_flowers", "minecraft:trees_windswept_savanna", "minecraft:delta", "create:zinc_ore", "minecraft:ore_ancient_debris_large", "minecraft:ore_diorite_lower", "minecraft:flower_swamp", "mekanism:ore_uranium_buried_retrogen", "minecraft:trees_plains", "mekanism:ore_tin_small", "minecraft:patch_taiga_grass", "minecraft:ore_andesite_lower", "minecraft:spring_closed", "minecraft:large_basalt_columns", "minecraft:mega_spruce_checked", "minecraft:warped_forest_vegetation", "minecraft:patch_grass_normal", "minecraft:ore_quartz_nether", "minecraft:pile_pumpkin", "tconstruct:earth_geode", "minecraft:brown_mushroom_nether", "minecraft:tall_mangrove_checked", "minecraft:twisting_vines", "minecraft:patch_waterlily", "minecraft:dark_forest_vegetation", "minecraft:kelp_warm", "minecraft:patch_sugar_cane", "minecraft:iceberg_packed", "minecraft:patch_grass_taiga_2", "mekanism:ore_osmium_upper_retrogen", "minecraft:brown_mushroom_old_growth", "mekanism:ore_fluorite_normal", "minecraft:sculk_vein", "minecraft:seagrass_normal", "minecraft:spring_delta", "minecraft:mushroom_island_vegetation", "minecraft:ore_gold_lower", "minecraft:ore_emerald", "minecraft:patch_sugar_cane_swamp", "minecraft:trees_sparse_jungle", "minecraft:seagrass_deep_warm", "minecraft:rooted_azalea_tree", "aerlunerpg:treesmall", "minecraft:pine", "minecraft:cherry_bees_005", "minecraft:oak_bees_0002", "minecraft:pine_on_snow", "minecraft:glow_lichen", "minecraft:cherry_checked", "minecraft:patch_dead_bush", "mekanism:ore_uranium_buried", "minecraft:bamboo", "aerlunerpg:crystalsmithg", "minecraft:ore_redstone_lower", "minecraft:disk_grass", "minecraft:red_mushroom_swamp", "aerlunerpg:bushs", "minecraft:ore_redstone", "mekanism:ore_tin_large", "minecraft:patch_berry_bush", "minecraft:pile_melon", "immersiveengineering:uranium", "minecraft:lake_lava_underground"]}, "typePotatoProjectileType": {"type": "string", "enum": ["create:cake", "create:enchanted_golden_apple", "create:poison_potato", "create:sweet_berry", "create:apple", "create:fallback", "create:carrot", "create:blaze_cake", "create:melon_block", "create:pumpkin_pie", "create:beetroot", "create:pumpkin_block", "create:potato", "create:golden_carrot", "create:fish", "create:suspicious_stew", "create:honeyed_apple", "create:chocolate_berry", "create:melon_slice", "create:pufferfish", "create:glistering_melon", "create:glow_berry", "create:golden_apple", "create:chorus_fruit", "create:baked_potato"]}, "typeMountedItemStorageType": {"type": "string", "enum": ["create:creative_crate", "create:simple", "create:depot", "create:chest", "create:fallback", "create:dispenser", "create:vault", "create:toolbox"]}, "typeTrimMaterial": {"type": "string", "enum": ["cataclysm:ignitium", "tconstruct:queens_slime", "minecraft:lapis", "minecraft:quartz", "cataclysm:cursium", "minecraft:gold", "tconstruct:rose_gold", "tconstruct:steel", "minecraft:amethyst", "tconstruct:amethyst_bronze", "tconstruct:manyullyn", "tconstruct:hepa<PERSON><PERSON>", "minecraft:copper", "cataclysm:ancient_metal", "tconstruct:pig_iron", "cataclysm:witherite", "tconstruct:ichor", "minecraft:netherite", "tconstruct:earthslime", "minecraft:diamond", "tconstruct:enderslime", "tconstruct:skyslime", "tconstruct:slimesteel", "minecraft:iron", "tconstruct:cobalt", "minecraft:redstone", "minecraft:emerald", "tconstruct:cinderslime"]}, "typeEntityType": {"type": "string", "enum": ["ae2:tiny_tnt_primed", "aerlunerpg:bearcandy", "aerlunerpg:bearcandy_2", "aerlunerpg:bearcandy_3", "aerlunerpg:bearcandy_4", "aerlunerpg:bedbug", "aerlunerpg:boar", "aerlunerpg:boar_3", "aerlunerpg:boarbig", "aerlunerpg:boar<PERSON>s", "aerlunerpg:butterfly", "aerlunerpg:buttermini", "aerlunerpg:buttermini_2", "aerlunerpg:cannongnome", "aerlunerpg:centipede", "aerlunerpg:christmastree", "aerlunerpg:cookie", "aerlunerpg:cookie_2", "aerlunerpg:crowd", "aerlunerpg:cthulhu", "aerlunerpg:deerbig", "aerlunerpg:deerbig_2", "aerlunerpg:deerevil", "aerlunerpg:donut", "aerlunerpg:ent", "aerlunerpg:entpentity", "aerlunerpg:evilgnome", "aerlunerpg:evilgnome_2", "aerlunerpg:eyebeatle", "aerlunerpg:ghost", "aerlunerpg:gingerbreadman", "aerlunerpg:gingirmanevil", "aerlunerpg:g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aerlunerpg:goblin", "aerlunerpg:gobliny", "aerlunerpg:golem", "aerlunerpg:house", "aerlunerpg:laser", "aerlunerpg:projectile_acidproj", "aerlunerpg:projectile_acidproj_2", "aerlunerpg:projectile_armp", "aerlunerpg:projectile_armp_2", "aerlunerpg:projectile_boarbosspr", "aerlunerpg:projectile_cannonp", "aerlunerpg:projectile_demonic_headp", "aerlunerpg:projectile_elkap", "aerlunerpg:projectile_entp", "aerlunerpg:projectile_froozy", "aerlunerpg:projectile_golemproj", "aerlunerpg:projectile_icefireproj", "aerlunerpg:projectile_icerodfire", "aerlunerpg:projectile_icerodp", "aerlunerpg:projectile_magicp", "aerlunerpg:projectile_pumpproj", "aerlunerpg:projectile_smallbeatleprojectile", "aerlunerpg:rabbit", "aerlunerpg:rabbitmount", "aerlunerpg:robotgnome", "aerlunerpg:shoggotsmall", "aerlunerpg:smallbeatle", "aerlunerpg:snowman", "aerlunerpg:snowman_2", "aerlunerpg:snowman_3", "aerlunerpg:steve", "aerlunerpg:stump", "aerlunerpg:stump_2", "aerlunerpg:test", "aerlunerpg:warm", "aerlunerpg:zombieghost", "cataclysm:abyss_blast", "cataclysm:abyss_blast_portal", "cataclysm:abyss_mark", "cataclysm:abyss_mine", "cataclysm:abyss_orb", "cataclysm:abyss_portal", "cataclysm:amethyst_cluster_projectile", "cataclysm:amethyst_crab", "cataclysm:ancient_ancient_remnant", "cataclysm:ancient_desert_stele", "cataclysm:ancient_remnant", "cataclysm:aptrgangr", "cataclysm:ashen_breath", "cataclysm:axe_blade", "cataclysm:blazing_bone", "cataclysm:cm_falling_block", "cataclysm:<PERSON>_bar<PERSON>he", "cataclysm:coral_golem", "cataclysm:coral_spear", "cataclysm:coralssus", "cataclysm:death_laser_beam", "cataclysm:deepling", "cataclysm:deepling_angler", "cataclysm:deepling_brute", "cataclysm:deepling_priest", "cataclysm:deepling_warlock", "cataclysm:dimensional_rift", "cataclysm:draugr", "cataclysm:earthquake", "cataclysm:elite_draugr", "cataclysm:ender_golem", "cataclysm:ender_guardian", "cataclysm:ender_guardian_bullet", "cataclysm:ender<PERSON><PERSON>", "cataclysm:eye_of_dungeon", "cataclysm:flame_strike", "cataclysm:ignis", "cataclysm:ignis_abyss_fireball", "cataclysm:ignis_fireball", "cataclysm:ignited_berserker", "cataclysm:ignited_revenant", "cataclysm:kobolediator", "cataclysm:koboleton", "cataclysm:laser_beam", "cataclysm:lava_bomb", "cataclysm:lionfish", "cataclysm:lionfish_spike", "cataclysm:maledictus", "cataclysm:mini_abyss_blast", "cataclysm:modern_remnant", "cataclysm:nameless_sorcerer", "cataclysm:netherite_monstrosity", "cataclysm:phantom_arrow", "cataclysm:phantom_halberd", "cataclysm:poison_dart", "cataclysm:portal_abyss_blast", "cataclysm:royal_draugr", "cataclysm:sandstorm", "cataclysm:sandstorm_projectile", "cataclysm:screen_shake", "cataclysm:the_baby_leviathan", "cataclysm:the_harbinger", "cataclysm:the_leviathan", "cataclysm:the_leviathan_tongue", "cataclysm:the_prowler", "cataclysm:the_watcher", "cataclysm:tidal_hook", "cataclysm:tidal_tentacle", "cataclysm:void_howitzer", "cataclysm:void_rune", "cataclysm:void_scatter_arrow", "cataclysm:void_shard", "cataclysm:void_vortex", "cataclysm:wadjet", "cataclysm:wall_watcher", "cataclysm:wither_homing_missile", "cataclysm:wither_howitzer", "cataclysm:wither_missile", "cataclysm:wither_smoke_effect", "create:carriage_contraption", "create:contraption", "create:crafting_blueprint", "create:gantry_contraption", "create:potato_projectile", "create:seat", "create:stationary_contraption", "create:super_glue", "immersiveengineering:bulwark", "immersiveengineering:cart_metalbarrel", "immersiveengineering:cart_reinforcedcrate", "immersiveengineering:cart_woodenbarrel", "immersiveengineering:cart_woodencrate", "immersiveengineering:chemthrower_shot", "immersiveengineering:commando", "immersiveengineering:explosive", "immersiveengineering:fluorescent_tube", "immersiveengineering:fusilier", "immersiveengineering:railgun_shot", "immersiveengineering:revolver_shot", "immersiveengineering:revolver_shot_flare", "immersiveengineering:revolver_shot_homing", "immersiveengineering:revolver_shot_wolfpack", "immersiveengineering:sawblade", "immersiveengineering:skyline_hook", "mekanism:flame", "mekanism:robit", "minecraft:allay", "minecraft:area_effect_cloud", "minecraft:armor_stand", "minecraft:arrow", "minecraft:axolotl", "minecraft:bat", "minecraft:bee", "minecraft:blaze", "minecraft:block_display", "minecraft:boat", "minecraft:camel", "minecraft:cat", "minecraft:cave_spider", "minecraft:chest_boat", "minecraft:chest_minecart", "minecraft:chicken", "minecraft:cod", "minecraft:command_block_minecart", "minecraft:cow", "minecraft:creeper", "minecraft:dolphin", "minecraft:donkey", "minecraft:dragon_fireball", "minecraft:drowned", "minecraft:egg", "minecraft:elder_guardian", "minecraft:end_crystal", "minecraft:ender_dragon", "minecraft:ender_pearl", "minecraft:enderman", "minecraft:endermite", "minecraft:evoker", "minecraft:evoker_fangs", "minecraft:experience_bottle", "minecraft:experience_orb", "minecraft:eye_of_ender", "minecraft:falling_block", "minecraft:fireball", "minecraft:firework_rocket", "minecraft:fishing_bobber", "minecraft:fox", "minecraft:frog", "minecraft:furnace_minecart", "minecraft:ghast", "minecraft:giant", "minecraft:glow_item_frame", "minecraft:glow_squid", "minecraft:goat", "minecraft:guardian", "minecraft:hoglin", "minecraft:hopper_minecart", "minecraft:horse", "minecraft:husk", "minecraft:illusioner", "minecraft:interaction", "minecraft:iron_golem", "minecraft:item", "minecraft:item_display", "minecraft:item_frame", "minecraft:leash_knot", "minecraft:lightning_bolt", "minecraft:llama", "minecraft:llama_spit", "minecraft:magma_cube", "minecraft:marker", "minecraft:minecart", "minecraft:mooshroom", "minecraft:mule", "minecraft:ocelot", "minecraft:painting", "minecraft:panda", "minecraft:parrot", "minecraft:phantom", "minecraft:pig", "minecraft:piglin", "minecraft:piglin_brute", "minecraft:pillager", "minecraft:player", "minecraft:polar_bear", "minecraft:potion", "minecraft:pufferfish", "minecraft:rabbit", "minecraft:ravager", "minecraft:salmon", "minecraft:sheep", "minecraft:shulker", "minecraft:shulker_bullet", "minecraft:silverfish", "minecraft:skeleton", "minecraft:skeleton_horse", "minecraft:slime", "minecraft:small_fireball", "minecraft:sniffer", "minecraft:snow_golem", "minecraft:snowball", "minecraft:spawner_minecart", "minecraft:spectral_arrow", "minecraft:spider", "minecraft:squid", "minecraft:stray", "minecraft:strider", "minecraft:tadpole", "minecraft:text_display", "minecraft:tnt", "minecraft:tnt_minecart", "minecraft:trader_llama", "minecraft:trident", "minecraft:tropical_fish", "minecraft:turtle", "minecraft:vex", "minecraft:villager", "minecraft:vindicator", "minecraft:wandering_trader", "minecraft:warden", "minecraft:witch", "minecraft:wither", "minecraft:wither_skeleton", "minecraft:wither_skull", "minecraft:wolf", "minecraft:zoglin", "minecraft:zombie", "minecraft:zombie_horse", "minecraft:zombie_villager", "minecraft:zombified_piglin", "cataclysm:old_netherite_monstrosity", "cataclysm:netherite_ministrosity", "cataclysm:flare_bomb", "cataclysm:flame_jet", "cataclysm:lightning_storm", "cataclysm:urchin_spike", "cataclysm:spark", "cataclysm:lightning_area_effect", "cataclysm:bolt_strike", "cataclysm:wave", "cataclysm:storm_serpent", "cataclysm:accretion", "cataclysm:scylla_ceraunus", "cataclysm:player_c<PERSON><PERSON>us", "cataclysm:cursed_sandstorm", "cataclysm:hippocamtus", "cataclysm:cindaria", "cataclysm:clawdian", "cataclysm:scylla", "cataclysm:urch<PERSON>in", "cataclysm:drowned_host", "cataclysm:symbiocto", "cataclysm:octo_ink", "cataclysm:water_spear", "cataclysm:lightning_spear", "tconstruct:fancy_item_frame", "tconstruct:glow_ball", "tconstruct:efln_ball", "tconstruct:quartz_shuriken", "tconstruct:flint_shuriken", "tconstruct:sky_slime", "tconstruct:ender_slime", "tconstruct:terracube", "tconstruct:fluid_spit", "tconstruct:indestructible_item", "tconstruct:crystalshot", "create:package"]}, "typeDecoratedPotPatterns": {"type": "string", "enum": ["minecraft:friend_pottery_pattern", "minecraft:miner_pottery_pattern", "minecraft:archer_pottery_pattern", "minecraft:blade_pottery_pattern", "minecraft:mourner_pottery_pattern", "minecraft:sheaf_pottery_pattern", "minecraft:decorated_pot_side", "minecraft:howl_pottery_pattern", "minecraft:heartbreak_pottery_pattern", "minecraft:danger_pottery_pattern", "minecraft:angler_pottery_pattern", "minecraft:burn_pottery_pattern", "minecraft:snort_pottery_pattern", "minecraft:prize_pottery_pattern", "minecraft:arms_up_pottery_pattern", "minecraft:skull_pottery_pattern", "minecraft:brewer_pottery_pattern", "minecraft:plenty_pottery_pattern", "minecraft:heart_pottery_pattern", "minecraft:shelter_pottery_pattern", "minecraft:decorated_pot_base", "minecraft:explorer_pottery_pattern"]}, "typeWorldgenFoliagePlacerType": {"type": "string", "enum": ["minecraft:blob_foliage_placer", "minecraft:spruce_foliage_placer", "minecraft:pine_foliage_placer", "minecraft:acacia_foliage_placer", "minecraft:bush_foliage_placer", "minecraft:fancy_foliage_placer", "minecraft:jungle_foliage_placer", "minecraft:mega_pine_foliage_placer", "minecraft:dark_oak_foliage_placer", "minecraft:random_spread_foliage_placer", "minecraft:cherry_foliage_placer"]}, "typePotatoProjectileBlockHitAction": {"type": "string", "enum": ["create:plant_crop", "create:place_block_on_ground"]}, "typeWorldgenBlockStateProviderType": {"type": "string", "enum": ["minecraft:simple_state_provider", "minecraft:weighted_state_provider", "minecraft:noise_threshold_provider", "minecraft:noise_provider", "minecraft:dual_noise_provider", "minecraft:rotated_block_provider", "minecraft:randomized_int_state_provider"]}, "typeVillagerType": {"type": "string", "enum": ["minecraft:snow", "minecraft:desert", "minecraft:plains", "minecraft:jungle", "minecraft:taiga", "minecraft:savanna", "minecraft:swamp"]}, "typePointOfInterestType": {"type": "string", "enum": ["ae2:fluix_researcher", "aerlunerpg:crowdd_portal", "aerlunerpg:iced_portal", "immersiveengineering:circuit_table", "immersiveengineering:craftingtable", "immersiveengineering:<PERSON><PERSON><PERSON>", "immersiveengineering:turntable", "immersiveengineering:workbench", "minecraft:armorer", "minecraft:bee_nest", "minecraft:beehive", "minecraft:butcher", "minecraft:cartographer", "minecraft:cleric", "minecraft:farmer", "minecraft:fisherman", "minecraft:fletcher", "minecraft:home", "minecraft:leatherworker", "minecraft:librarian", "minecraft:lightning_rod", "minecraft:lodestone", "minecraft:mason", "minecraft:meeting", "minecraft:nether_portal", "minecraft:shepherd", "minecraft:toolsmith", "minecraft:weaponsmith"]}, "typeWorldgenTreeDecoratorType": {"type": "string", "enum": ["minecraft:trunk_vine", "minecraft:leave_vine", "minecraft:cocoa", "minecraft:beehive", "minecraft:alter_ground", "minecraft:attached_to_leaves", "tconstruct:leave_vines"]}, "typeWorldgenMaterialCondition": {"type": "string", "enum": ["minecraft:hole", "minecraft:noise_threshold", "minecraft:biome", "minecraft:not", "minecraft:above_preliminary_surface", "minecraft:water", "minecraft:stone_depth", "minecraft:temperature", "minecraft:vertical_gradient", "minecraft:y_above", "minecraft:steep"]}, "typeWorldgenFeatureSizeType": {"type": "string", "enum": ["minecraft:three_layers_feature_size", "minecraft:two_layers_feature_size"]}, "typeRuleBlockEntityModifier": {"type": "string", "enum": ["minecraft:append_loot", "minecraft:clear", "minecraft:append_static", "minecraft:passthrough"]}, "typeWorldgenPlacementModifierType": {"type": "string", "enum": ["minecraft:block_predicate_filter", "minecraft:random_offset", "minecraft:noise_based_count", "minecraft:height_range", "minecraft:rarity_filter", "minecraft:noise_threshold_count", "minecraft:environment_scan", "minecraft:in_square", "minecraft:surface_water_depth_filter", "minecraft:surface_relative_threshold_filter", "minecraft:count_on_every_layer", "create:config_filter", "minecraft:biome", "mekanism:disableable", "minecraft:count", "minecraft:carving_mask", "minecraft:heightmap", "immersiveengineering:ie_count"]}, "typeWorldgenFeature": {"type": "string", "enum": ["minecraft:no_op", "minecraft:tree", "minecraft:flower", "minecraft:no_bonemeal_flower", "minecraft:random_patch", "minecraft:block_pile", "minecraft:spring_feature", "minecraft:chorus_plant", "minecraft:replace_single_block", "minecraft:void_start_platform", "minecraft:desert_well", "minecraft:fossil", "minecraft:huge_red_mushroom", "minecraft:huge_brown_mushroom", "minecraft:ice_spike", "minecraft:glowstone_blob", "minecraft:freeze_top_layer", "minecraft:vines", "minecraft:block_column", "minecraft:vegetation_patch", "minecraft:waterlogged_vegetation_patch", "minecraft:root_system", "minecraft:multiface_growth", "minecraft:underwater_magma", "minecraft:monster_room", "minecraft:blue_ice", "minecraft:iceberg", "minecraft:forest_rock", "minecraft:disk", "minecraft:lake", "minecraft:ore", "minecraft:end_spike", "minecraft:end_island", "minecraft:end_gateway", "minecraft:seagrass", "minecraft:kelp", "minecraft:coral_tree", "minecraft:coral_mushroom", "minecraft:coral_claw", "minecraft:sea_pickle", "minecraft:simple_block", "minecraft:bamboo", "minecraft:huge_fungus", "minecraft:nether_forest_vegetation", "minecraft:weeping_vines", "minecraft:twisting_vines", "minecraft:basalt_columns", "minecraft:delta_feature", "minecraft:netherrack_replace_blobs", "minecraft:fill_layer", "minecraft:bonus_chest", "minecraft:basalt_pillar", "minecraft:scattered_ore", "minecraft:random_selector", "minecraft:simple_random_selector", "minecraft:random_boolean_selector", "minecraft:geode", "minecraft:dripstone_cluster", "minecraft:large_dripstone", "minecraft:pointed_dripstone", "minecraft:sculk_patch", "aerlunerpg:rabbitgoles", "aerlunerpg:oldmanst", "aerlunerpg:structure_feature", "cataclysm:end_ice_spike_feature", "tconstruct:slime_tree", "tconstruct:slime_fungus", "immersiveengineering:mineral_vein", "immersiveengineering:ie_ore", "create:layered_ore", "mekanism:disk", "mekanism:ore", "mekanism:ore_retrogen"]}, "typeWorldgenRootPlacerType": {"type": "string", "enum": ["minecraft:mangrove_root_placer", "tconstruct:extra_root_variants"]}, "typeWorldgenTemplatePool": {"type": "string", "enum": ["minecraft:bastion/hoglin_stable/connectors", "minecraft:bastion/blocks/gold", "minecraft:village/savanna/houses", "minecraft:village/common/sheep", "minecraft:village/snowy/terminators", "minecraft:bastion/treasure/roofs", "minecraft:village/taiga/town_centers", "minecraft:village/savanna/zombie/villagers", "minecraft:bastion/hoglin_stable/small_stables/outer", "cataclysm:acropolis/mob/clawdian", "cataclysm:soul_black_smith/start_pool", "minecraft:village/snowy/town_centers", "minecraft:village/taiga/zombie/decor", "minecraft:bastion/treasure/extensions/small_pool", "cataclysm:soul_black_smith/two", "minecraft:village/desert/zombie/houses", "minecraft:village/common/cats", "minecraft:pillager_outpost/feature_plates", "minecraft:trail_ruins/decor", "minecraft:bastion/hoglin_stable/posts", "minecraft:pillager_outpost/towers", "minecraft:bastion/treasure/walls/mid", "minecraft:village/savanna/decor", "minecraft:bastion/units/pathways", "minecraft:bastion/treasure/bases/centers", "minecraft:village/desert/decor", "minecraft:bastion/bridge/starting_pieces", "minecraft:pillager_outpost/features", "minecraft:village/taiga/zombie/villagers", "cataclysm:frosted_prison/mob/aptrgangr", "cataclysm:ancient_factory/start_pool", "cataclysm:soul_black_smith/eight", "minecraft:bastion/bridge/ramparts", "cataclysm:soul_black_smith/mob/monstrosity", "cataclysm:soul_black_smith/three", "cataclysm:frosted_prison/mid_7", "cataclysm:frosted_prison/mid_9", "minecraft:bastion/treasure/stairs", "cataclysm:frosted_prison/mid_2", "cataclysm:frosted_prison/mid_3", "cataclysm:frosted_prison/mid_5", "aerlunerpg:midleetree_6", "cataclysm:frosted_prison/mid_1", "minecraft:bastion/units/edge_wall_units", "minecraft:village/desert/villagers", "minecraft:ancient_city/city/entrance", "minecraft:village/plains/zombie/decor", "minecraft:village/savanna/terminators", "cataclysm:acropolis/mob/scylla", "cataclysm:frosted_prison/mob/royal_draugr", "cataclysm:acropolis/30", "cataclysm:acropolis/31", "cataclysm:acropolis/32", "minecraft:bastion/hoglin_stable/rampart_plates", "minecraft:bastion/units/fillers/stage_0", "minecraft:trail_ruins/roads", "minecraft:village/plains/town_centers", "minecraft:bastion/treasure/walls/top", "minecraft:village/taiga/zombie/houses", "minecraft:bastion/treasure/entrances", "minecraft:village/plains/zombie/streets", "minecraft:bastion/hoglin_stable/small_stables/inner", "minecraft:village/snowy/zombie/decor", "minecraft:bastion/units/large_ramparts", "minecraft:village/plains/villagers", "minecraft:bastion/units/stages/rot/stage_1", "minecraft:bastion/units/ramparts", "cataclysm:acropolis/20", "cataclysm:soul_black_smith/one", "cataclysm:acropolis/21", "cataclysm:acropolis/26", "aerlunerpg:candyhome", "cataclysm:acropolis/27", "cataclysm:acropolis/28", "cataclysm:acropolis/29", "cataclysm:acropolis/22", "cataclysm:acropolis/24", "minecraft:village/plains/zombie/houses", "cataclysm:acropolis/25", "cataclysm:acropolis/mob/octohost_sword", "minecraft:village/taiga/houses", "cataclysm:acropolis/19", "cataclysm:frosted_prison/mid_10", "minecraft:village/desert/zombie/villagers", "minecraft:bastion/treasure/walls/outer", "minecraft:trail_ruins/buildings/grouped", "cataclysm:acropolis/10", "aerlunerpg:spt_3", "cataclysm:ancient_factory/mob/the_harbinger", "aerlunerpg:smallhouse", "cataclysm:acropolis/15", "cataclysm:acropolis/16", "cataclysm:acropolis/17", "minecraft:ancient_city/city_center", "cataclysm:frosted_prison/upper_6", "cataclysm:acropolis/18", "cataclysm:frosted_prison/mid_11", "cataclysm:frosted_prison/upper_7", "cataclysm:acropolis/11", "cataclysm:acropolis/12", "cataclysm:acropolis/13", "cataclysm:acropolis/14", "minecraft:village/savanna/zombie/streets", "minecraft:bastion/treasure/walls", "cataclysm:frosted_prison/mob/elite_draugr", "aerlunerpg:gnoemtown_1", "aerlunerpg:spt_2", "minecraft:village/savanna/zombie/terminators", "minecraft:village/desert/town_centers", "minecraft:village/savanna/zombie/houses", "aerlunerpg:bushsmall_3", "minecraft:trail_ruins/tower", "minecraft:village/desert/streets", "minecraft:bastion/treasure/brains", "cataclysm:acropolis/mob/cindaria", "cataclysm:acropolis/mob/octohost_trident", "aerlunerpg:bushsmall_1", "minecraft:bastion/units/rampart_plates", "aerlunerpg:bushsmall_2", "minecraft:bastion/treasure/corners/top", "minecraft:ancient_city/walls/no_corners", "minecraft:village/common/well_bottoms", "cataclysm:frosted_prison/mob/draugr", "minecraft:village/snowy/zombie/villagers", "minecraft:village/desert/zombie/decor", "cataclysm:ancient_factory/mob/the_prowler", "minecraft:bastion/mobs/hoglin", "minecraft:village/savanna/town_centers", "minecraft:village/snowy/streets", "minecraft:village/taiga/streets", "minecraft:bastion/bridge/legs", "minecraft:village/taiga/terminators", "minecraft:bastion/treasure/extensions/houses", "minecraft:bastion/units/stages/stage_1", "minecraft:bastion/units/stages/stage_2", "minecraft:bastion/units/stages/stage_3", "minecraft:bastion/hoglin_stable/stairs", "minecraft:bastion/hoglin_stable/wall_bases", "minecraft:trail_ruins/tower/additions", "cataclysm:frosted_prison/top_6", "minecraft:bastion/treasure/walls/bottom", "minecraft:bastion/units/stages/stage_0", "minecraft:village/desert/zombie/terminators", "cataclysm:acropolis/7", "cataclysm:acropolis/6", "minecraft:village/common/butcher_animals", "cataclysm:acropolis/9", "cataclysm:soul_black_smith/six", "cataclysm:acropolis/8", "minecraft:bastion/hoglin_stable/starting_pieces", "minecraft:village/plains/trees", "minecraft:village/taiga/decor", "minecraft:bastion/treasure/ramparts", "minecraft:village/plains/streets", "minecraft:empty", "cataclysm:soul_black_smith/five", "minecraft:bastion/treasure/corners/middle", "aerlunerpg:bigtree", "minecraft:village/common/iron_golem", "cataclysm:frosted_prison/bottom_8", "cataclysm:frosted_prison/bottom_7", "cataclysm:frosted_prison/bottom_9", "cataclysm:frosted_prison/bottom_4", "cataclysm:frosted_prison/bottom_3", "minecraft:bastion/hoglin_stable/large_stables/outer", "cataclysm:frosted_prison/bottom_6", "cataclysm:frosted_prison/bottom_5", "minecraft:bastion/units/wall_units", "cataclysm:frosted_prison/bottom_2", "cataclysm:frosted_prison/bottom_1", "minecraft:bastion/treasure/corners/edges", "minecraft:trail_ruins/buildings", "minecraft:village/desert/zombie/streets", "cataclysm:frosted_prison/bottom_11", "minecraft:village/savanna/villagers", "cataclysm:frosted_prison/bottom_10", "cataclysm:ancient_factory/mob/the_watcher", "minecraft:ancient_city/walls", "cataclysm:frosted_prison/bottom_12", "cataclysm:acropolis/3", "cataclysm:acropolis/2", "cataclysm:acropolis/5", "cataclysm:acropolis/4", "minecraft:village/plains/decor", "cataclysm:acropolis/1", "minecraft:village/snowy/trees", "minecraft:village/savanna/zombie/decor", "minecraft:bastion/bridge/connectors", "minecraft:village/snowy/zombie/streets", "minecraft:bastion/units/walls/wall_bases", "minecraft:village/savanna/trees", "minecraft:bastion/treasure/bases", "minecraft:bastion/mobs/piglin_melee", "aerlunerpg:midletree_2", "minecraft:village/desert/camel", "cataclysm:soul_black_smith/mob/ministrosity", "minecraft:bastion/hoglin_stable/walls", "cataclysm:frosted_prison/start_pool", "minecraft:village/snowy/decor", "minecraft:village/snowy/villagers", "cataclysm:acropolis/mob/urchinkin", "aerlunerpg:midletree_5", "minecraft:village/plains/terminators", "minecraft:bastion/units/edges", "aerlunerpg:midletree_4", "aerlunerpg:midletree_3", "cataclysm:acropolis/start_pool", "minecraft:village/desert/terminators", "minecraft:pillager_outpost/base_plates", "minecraft:trail_ruins/tower/tower_top", "minecraft:village/plains/houses", "minecraft:village/taiga/villagers", "minecraft:ancient_city/sculk", "cataclysm:soul_black_smith/seven", "aerlunerpg:midletree", "minecraft:ancient_city/city_center/walls", "aerlunerpg:bigtree_2", "aerlunerpg:bigtree_3", "cataclysm:acropolis/mob/hippocamtus", "minecraft:bastion/treasure/corners/bottom", "aerlunerpg:spooketree", "aerlunerpg:bigtree_8", "minecraft:ancient_city/structures", "aerlunerpg:bigtree_4", "aerlunerpg:bigtree_5", "aerlunerpg:bigtree_6", "minecraft:bastion/bridge/rampart_plates", "aerlunerpg:bigtree_7", "cataclysm:ancient_factory/e", "cataclysm:ancient_factory/d", "minecraft:bastion/treasure/connectors", "minecraft:village/savanna/streets", "minecraft:bastion/bridge/bridge_pieces", "minecraft:bastion/units/center_pieces", "cataclysm:ancient_factory/a", "minecraft:bastion/hoglin_stable/mirrored_starting_pieces", "minecraft:bastion/mobs/piglin", "minecraft:village/plains/zombie/villagers", "minecraft:bastion/treasure/extensions/large_pool", "minecraft:village/desert/houses", "minecraft:bastion/starts", "minecraft:village/common/animals", "minecraft:bastion/hoglin_stable/large_stables/inner", "minecraft:bastion/bridge/walls", "minecraft:village/taiga/zombie/streets", "minecraft:village/snowy/houses", "cataclysm:ancient_factory/w", "aerlunerpg:elkas<PERSON><PERSON>", "cataclysm:ancient_factory/q", "minecraft:bastion/hoglin_stable/ramparts", "minecraft:village/snowy/zombie/houses"]}, "typeDisplaySource": {"type": "string", "enum": ["create:train_status", "create:scoreboard", "create:item_names", "create:time_of_day", "create:computer", "create:fill_level", "create:count_fluids", "create:stopwatch", "create:list_items", "create:observed_train_name", "create:redstone_power", "create:count_items", "create:entity_name", "create:kinetic_speed", "create:kinetic_stress", "create:gauge_status", "create:death_count", "create:item_throughput", "create:boiler", "create:accumulate_items", "create:list_fluids", "create:station_summary", "create:enchant_power", "create:nixie_tube", "create:read_package_address", "create:current_floor"]}, "typeMemoryModuleType": {"type": "string", "enum": ["minecraft:admiring_disabled", "minecraft:admiring_item", "minecraft:angry_at", "minecraft:ate_recently", "minecraft:attack_cooling_down", "minecraft:attack_target", "minecraft:avoid_target", "minecraft:breed_target", "minecraft:cant_reach_walk_target_since", "minecraft:celebrate_location", "minecraft:dancing", "minecraft:dig_cooldown", "minecraft:disable_walk_to_admire_item", "minecraft:disturbance_location", "minecraft:doors_to_close", "minecraft:dummy", "minecraft:gaze_cooldown_ticks", "minecraft:golem_detected_recently", "minecraft:has_hunting_cooldown", "minecraft:heard_bell_time", "minecraft:hiding_place", "minecraft:home", "minecraft:hunted_recently", "minecraft:hurt_by", "minecraft:hurt_by_entity", "minecraft:interactable_doors", "minecraft:interaction_target", "minecraft:is_emerging", "minecraft:is_in_water", "minecraft:is_panicking", "minecraft:is_pregnant", "minecraft:is_sniffing", "minecraft:is_tempted", "minecraft:item_pickup_cooldown_ticks", "minecraft:job_site", "minecraft:last_slept", "minecraft:last_woken", "minecraft:last_worked_at_poi", "minecraft:liked_noteblock", "minecraft:liked_noteblock_cooldown_ticks", "minecraft:liked_player", "minecraft:long_jump_cooling_down", "minecraft:long_jump_mid_jump", "minecraft:look_target", "minecraft:meeting_point", "minecraft:mobs", "minecraft:nearby_adult_piglins", "minecraft:nearest_attackable", "minecraft:nearest_bed", "minecraft:nearest_hostile", "minecraft:nearest_player_holding_wanted_item", "minecraft:nearest_players", "minecraft:nearest_repellent", "minecraft:nearest_targetable_player_not_wearing_gold", "minecraft:nearest_visible_adult", "minecraft:nearest_visible_adult_hoglins", "minecraft:nearest_visible_adult_piglin", "minecraft:nearest_visible_adult_piglins", "minecraft:nearest_visible_baby_hoglin", "minecraft:nearest_visible_huntable_hoglin", "minecraft:nearest_visible_nemesis", "minecraft:nearest_visible_player", "minecraft:nearest_visible_targetable_player", "minecraft:nearest_visible_wanted_item", "minecraft:nearest_visible_zombified", "minecraft:pacified", "minecraft:path", "minecraft:play_dead_ticks", "minecraft:potential_job_site", "minecraft:ram_cooldown_ticks", "minecraft:ram_target", "minecraft:recent_projectile", "minecraft:ride_target", "minecraft:roar_sound_cooldown", "minecraft:roar_sound_delay", "minecraft:roar_target", "minecraft:secondary_job_site", "minecraft:sniff_cooldown", "minecraft:sniffer_digging", "minecraft:sniffer_explored_positions", "minecraft:sniffer_happy", "minecraft:sniffer_sniffing_target", "minecraft:sonic_boom_cooldown", "minecraft:sonic_boom_sound_cooldown", "minecraft:sonic_boom_sound_delay", "minecraft:temptation_cooldown_ticks", "minecraft:tempting_player", "minecraft:time_trying_to_reach_admire_item", "minecraft:touch_cooldown", "minecraft:universal_anger", "minecraft:unreachable_tongue_targets", "minecraft:vibration_cooldown", "minecraft:visible_adult_hoglin_count", "minecraft:visible_adult_piglin_count", "minecraft:visible_mobs", "minecraft:visible_villager_babies", "minecraft:walk_target"]}, "typeWorldgenStructurePoolElement": {"type": "string", "enum": ["minecraft:empty_pool_element", "minecraft:list_pool_element", "cataclysm:cataclysm_element", "minecraft:legacy_single_pool_element", "minecraft:single_pool_element", "minecraft:feature_pool_element"]}, "typeSlurry": {"type": "string", "enum": ["mekanism:clean_copper", "mekanism:clean_gold", "mekanism:clean_iron", "mekanism:clean_lead", "mekanism:clean_osmium", "mekanism:clean_tin", "mekanism:clean_uranium", "mekanism:dirty_copper", "mekanism:dirty_gold", "mekanism:dirty_iron", "mekanism:dirty_lead", "mekanism:dirty_osmium", "mekanism:dirty_tin", "mekanism:dirty_uranium", "mekanism:empty"]}, "typeLootNumberProviderType": {"type": "string", "enum": ["minecraft:uniform", "minecraft:score", "minecraft:binomial", "minecraft:constant"]}, "typeContraptionType": {"type": "string", "enum": ["create:clockwork", "create:bearing", "create:piston", "create:gantry", "create:carriage", "create:mounted", "create:stabilized", "create:pulley", "create:elevator"]}, "typePosRuleTest": {"type": "string", "enum": ["minecraft:linear_pos", "minecraft:axis_aligned_linear_pos", "minecraft:always_true"]}, "typeMountedFluidStorageType": {"type": "string", "enum": ["create:creative_fluid_tank", "create:fluid_tank"]}, "typeInstrument": {"type": "string", "enum": ["minecraft:seek_goat_horn", "minecraft:sing_goat_horn", "minecraft:call_goat_horn", "minecraft:feel_goat_horn", "minecraft:ponder_goat_horn", "minecraft:admire_goat_horn", "minecraft:dream_goat_horn", "minecraft:yearn_goat_horn"]}, "typeGameEvent": {"type": "string", "enum": ["minecraft:entity_mount", "minecraft:block_activate", "minecraft:shear", "minecraft:block_place", "minecraft:prime_fuse", "mekanism:gravity_modulate_boosted", "minecraft:drink", "mekanism:jetpack_burn", "minecraft:elytra_glide", "minecraft:block_change", "minecraft:resonate_10", "minecraft:resonate_11", "minecraft:shriek", "minecraft:eat", "minecraft:entity_damage", "minecraft:entity_shake", "minecraft:explode", "minecraft:teleport", "minecraft:entity_interact", "mekanism:gravity_modulate", "minecraft:projectile_shoot", "minecraft:resonate_12", "minecraft:resonate_13", "minecraft:resonate_14", "minecraft:resonate_15", "minecraft:projectile_land", "minecraft:block_close", "minecraft:item_interact_start", "minecraft:splash", "minecraft:fluid_place", "minecraft:fluid_pickup", "minecraft:equip", "minecraft:step", "minecraft:block_destroy", "minecraft:container_close", "minecraft:note_block_play", "minecraft:lightning_strike", "minecraft:entity_place", "minecraft:entity_roar", "minecraft:hit_ground", "minecraft:item_interact_finish", "minecraft:block_open", "minecraft:jukebox_play", "mekanism:seismic_vibration", "minecraft:instrument_play", "minecraft:jukebox_stop_play", "minecraft:resonate_3", "minecraft:resonate_4", "minecraft:resonate_1", "minecraft:resonate_2", "minecraft:resonate_7", "minecraft:resonate_8", "minecraft:resonate_5", "minecraft:resonate_6", "minecraft:resonate_9", "minecraft:block_attach", "minecraft:entity_die", "minecraft:entity_dismount", "minecraft:flap", "minecraft:block_detach", "minecraft:sculk_sensor_tendrils_clicking", "minecraft:block_deactivate", "minecraft:container_open", "minecraft:swim"]}, "typePotion": {"type": "string", "enum": ["minecraft:awkward", "minecraft:empty", "minecraft:fire_resistance", "minecraft:harming", "minecraft:healing", "minecraft:invisibility", "minecraft:leaping", "minecraft:long_fire_resistance", "minecraft:long_invisibility", "minecraft:long_leaping", "minecraft:long_night_vision", "minecraft:long_poison", "minecraft:long_regeneration", "minecraft:long_slow_falling", "minecraft:long_slowness", "minecraft:long_strength", "minecraft:long_swiftness", "minecraft:long_turtle_master", "minecraft:long_water_breathing", "minecraft:long_weakness", "minecraft:luck", "minecraft:mundane", "minecraft:night_vision", "minecraft:poison", "minecraft:regeneration", "minecraft:slow_falling", "minecraft:slowness", "minecraft:strength", "minecraft:strong_harming", "minecraft:strong_healing", "minecraft:strong_leaping", "minecraft:strong_poison", "minecraft:strong_regeneration", "minecraft:strong_slowness", "minecraft:strong_strength", "minecraft:strong_swiftness", "minecraft:strong_turtle_master", "minecraft:swiftness", "minecraft:thick", "minecraft:turtle_master", "minecraft:water", "minecraft:water_breathing", "minecraft:weakness", "tconstruct:_experienced", "tconstruct:strong_experienced", "tconstruct:long_experienced", "tconstruct:_ricochet", "tconstruct:strong_ricochet", "tconstruct:long_ricochet", "tconstruct:_levitation", "tconstruct:strong_levitation", "tconstruct:long_levitation", "tconstruct:_enderference", "tconstruct:long_enderference"]}, "typeInfuseType": {"type": "string", "enum": ["mekanism:bio", "mekanism:carbon", "mekanism:diamond", "mekanism:empty", "mekanism:fungi", "mekanism:gold", "mekanism:redstone", "mekanism:refined_obsidian", "mekanism:tin"]}, "typeLegacySkyboxDeserializer": {"type": "string", "enum": ["fabricskyboxes:square_textured_skybox_legacy_deserializer", "fabricskyboxes:mono_color_skybox_legacy_deserializer"]}, "typeVillagerProfession": {"type": "string", "enum": ["ae2:fluix_researcher", "immersiveengineering:electrician", "immersiveengineering:engineer", "immersiveengineering:gunsmith", "immersiveengineering:machinist", "immersiveengineering:outfitter", "minecraft:armorer", "minecraft:butcher", "minecraft:cartographer", "minecraft:cleric", "minecraft:farmer", "minecraft:fisherman", "minecraft:fletcher", "minecraft:leatherworker", "minecraft:librarian", "minecraft:mason", "minecraft:nitwit", "minecraft:none", "minecraft:shepherd", "minecraft:toolsmith", "minecraft:weaponsmith"]}, "typeDimensionType": {"type": "string", "enum": ["minecraft:overworld_caves", "aerlunerpg:iced", "minecraft:overworld", "ae2:spatial_storage", "minecraft:the_end", "aerlunerpg:crowdd", "minecraft:the_nether"]}, "typeArmInteractionPointType": {"type": "string", "enum": ["create:campfire", "create:deployer", "create:respawn_anchor", "create:depot", "create:millstone", "create:composter", "create:belt", "create:chute", "create:crafter", "create:jukebox", "create:crushing_wheels", "create:funnel", "create:basin", "create:saw", "create:blaze_burner", "create:packager"]}, "typeWorldgenWorldPreset": {"type": "string", "enum": ["minecraft:debug_all_block_states", "minecraft:single_biome_surface", "minecraft:amplified", "minecraft:flat", "minecraft:normal", "minecraft:large_biomes"]}, "typeModdedBiomeSlices": {"type": "string", "enum": ["lion<PERSON><PERSON><PERSON>:originals"]}, "typeWorldgenStructureSet": {"type": "string", "enum": ["aerlunerpg:bushsmall_3", "ae2:meteorite", "minecraft:ocean_ruins", "minecraft:buried_treasures", "aerlunerpg:bushsmall_1", "aerlunerpg:bushsmall_2", "aerlunerpg:midletree_2", "aerlunerpg:midletree_5", "aerlunerpg:midletree_4", "aerlunerpg:midletree_3", "minecraft:end_cities", "cataclysm:ruined_citadel", "minecraft:shipwrecks", "cataclysm:burning_arena", "aerlunerpg:midletree", "aerlunerpg:bigtree_2", "aerlunerpg:bigtree_3", "tconstruct:overworld_sky_island", "minecraft:ocean_monuments", "cataclysm:ancient_factory", "aerlunerpg:spooketree", "aerlunerpg:bigtree_8", "aerlunerpg:bigtree_4", "minecraft:pillager_outposts", "minecraft:swamp_huts", "aerlunerpg:bigtree_5", "aerlunerpg:bigtree_6", "aerlunerpg:bigtree_7", "cataclysm:cursed_pyramid", "aerlunerpg:candyhome", "minecraft:ancient_cities", "minecraft:villages", "cataclysm:frosted_prison", "minecraft:nether_fossils", "minecraft:mineshafts", "aerlunerpg:bigtree", "minecraft:strongholds", "tconstruct:end_sky_island", "tconstruct:overworld_ocean_island", "minecraft:igloos", "minecraft:desert_pyramids", "aerlunerpg:spt_3", "aerlunerpg:smallhouse", "minecraft:woodland_mansions", "cataclysm:soul_black_smith", "minecraft:trail_ruins", "tconstruct:nether_ocean_island", "minecraft:nether_complexes", "aerlunerpg:gnoemtown_1", "cataclysm:sunken_city", "minecraft:jungle_temples", "aerlunerpg:spt_2", "cataclysm:acropolis", "aerlunerpg:elkas<PERSON><PERSON>", "aerlunerpg:midleetree_6", "minecraft:ruined_portals"]}, "typeLootFunctionType": {"type": "string", "enum": ["minecraft:enchant_with_levels", "tconstruct:chrysophilite_bonus", "immersiveengineering:conveyor_cover", "lootify:set_any_damage", "minecraft:copy_name", "minecraft:furnace_smelt", "minecraft:copy_nbt", "minecraft:set_banner_pattern", "mantle:fill_retextured_block", "minecraft:set_lore", "tconstruct:add_tool_data", "minecraft:apply_bonus", "immersiveengineering:property_count", "minecraft:reference", "minecraft:fill_player_head", "minecraft:set_name", "immersiveengineering:windmill", "minecraft:set_count", "minecraft:copy_state", "tconstruct:modifier_bonus", "immersiveengineering:revolverperk", "curios:set_curio_attributes", "mantle:set_fluid", "minecraft:set_stew_effect", "mekanism:personal_storage_contents", "minecraft:set_attributes", "minecraft:set_damage", "minecraft:set_loot_table", "lootify:ominous_banner", "minecraft:set_nbt", "minecraft:explosion_decay", "immersiveengineering:secret_bluprintz", "minecraft:exploration_map", "minecraft:set_instrument", "minecraft:looting_enchant", "minecraft:enchant_randomly", "minecraft:set_enchantments", "minecraft:set_potion", "minecraft:set_contents", "minecraft:limit_count"]}, "typeSkyboxType": {"type": "string", "enum": ["fabricskyboxes:overworld", "fabricskyboxes:end", "fabricskyboxes:square_textured", "fabricskyboxes:single_sprite_square_textured", "fabricskyboxes:animated_square_textured", "fabricskyboxes:monocolor", "fabricskyboxes:single_sprite_animated_square_textured", "fabricskyboxes:multi_texture"]}, "typeActivity": {"type": "string", "enum": ["minecraft:core", "minecraft:idle", "minecraft:work", "minecraft:play", "minecraft:rest", "minecraft:meet", "minecraft:panic", "minecraft:raid", "minecraft:pre_raid", "minecraft:hide", "minecraft:fight", "minecraft:celebrate", "minecraft:admire_item", "minecraft:avoid", "minecraft:ride", "minecraft:play_dead", "minecraft:long_jump", "minecraft:ram", "minecraft:tongue", "minecraft:swim", "minecraft:lay_spawn", "minecraft:sniff", "minecraft:investigate", "minecraft:roar", "minecraft:emerge", "minecraft:dig"]}, "typeLootConditionType": {"type": "string", "enum": ["minecraft:value_check", "minecraft:location_check", "tconstruct:has_chrysophilite", "minecraft:random_chance_with_looting", "tconstruct:has_modifier", "lootify:wither_kill", "minecraft:inverted", "minecraft:reference", "minecraft:all_of", "mantle:tag_empty", "minecraft:time_check", "minecraft:weather_check", "lootify:spawns_with", "minecraft:any_of", "minecraft:random_chance", "minecraft:killed_by_player", "minecraft:survives_explosion", "mantle:block_tag", "minecraft:table_bonus", "minecraft:damage_source_properties", "minecraft:entity_properties", "mantle:tag_filled", "minecraft:entity_scores", "tconstruct:config", "tconstruct:block_or_entity", "minecraft:block_state_property", "forge:loot_table_id", "tconstruct:tag_not_empty", "lootify:creeper", "minecraft:match_tool", "forge:can_tool_perform_action"]}, "typeWorldgenChunkGenerator": {"type": "string", "enum": ["minecraft:debug", "ae2:spatial_storage", "minecraft:flat", "minecraft:noise"]}, "typeRecipeSerializer": {"type": "string", "enum": ["minecraft:crafting_shaped", "minecraft:crafting_shapeless", "minecraft:crafting_special_armordye", "minecraft:crafting_special_bookcloning", "minecraft:crafting_special_mapcloning", "minecraft:crafting_special_mapextending", "minecraft:crafting_special_firework_rocket", "minecraft:crafting_special_firework_star", "minecraft:crafting_special_firework_star_fade", "minecraft:crafting_special_tippedarrow", "minecraft:crafting_special_bannerduplicate", "minecraft:crafting_special_shielddecoration", "minecraft:crafting_special_shulkerboxcoloring", "minecraft:crafting_special_suspiciousstew", "minecraft:crafting_special_repairitem", "minecraft:smelting", "minecraft:blasting", "minecraft:smoking", "minecraft:campfire_cooking", "minecraft:stonecutting", "minecraft:smithing_transform", "minecraft:smithing_trim", "minecraft:crafting_decorated_pot", "ae2:inscriber", "ae2:facade", "ae2:entropy", "ae2:matter_cannon", "ae2:transform", "ae2:charger", "cataclysm:weapon_fusion", "cataclysm:amethyst_bless", "forge:conditional", "tconstruct:material", "tconstruct:tool_building", "tconstruct:tinker_station_part_swapping", "tconstruct:tinker_station_damaging", "tconstruct:crafting_shaped_material", "tconstruct:part_builder", "tconstruct:item_part_builder", "tconstruct:part_builder_tool_recycling", "tconstruct:tinker_station_repair", "tconstruct:crafting_table_repair", "tconstruct:modifier", "tconstruct:incremental_modifier", "tconstruct:swappable_modifier", "tconstruct:multilevel_modifier", "tconstruct:overslime_modifier", "tconstruct:crafting_overslime_modifier", "tconstruct:modifier_salvage", "tconstruct:armor_dyeing_modifier", "tconstruct:armor_trim_modifier", "tconstruct:modifier_repair", "tconstruct:crafting_modifier_repair", "tconstruct:modifier_material_repair", "tconstruct:crafting_modifier_material_repair", "tconstruct:remove_modifier", "tconstruct:extract_modifier", "tconstruct:modifier_sorting", "tconstruct:modifier_set_worktable", "tconstruct:enchantment_converting", "tconstruct:severing", "tconstruct:ageable_severing", "tconstruct:player_beheading", "tconstruct:snow_golem_beheading", "tconstruct:mooshroom_demushrooming", "tconstruct:sheep_shearing", "tconstruct:casting_basin", "tconstruct:casting_table", "tconstruct:basin_filling", "tconstruct:table_filling", "tconstruct:basin_duplication", "tconstruct:table_duplication", "tconstruct:casting_basin_potion", "tconstruct:casting_table_potion", "tconstruct:retextured_casting_basin", "tconstruct:retextured_casting_table", "tconstruct:basin_casting_material", "tconstruct:table_casting_material", "tconstruct:basin_casting_composite", "tconstruct:table_casting_composite", "tconstruct:basin_tool_casting", "tconstruct:table_tool_casting", "tconstruct:material_fluid", "tconstruct:basin_casting_part_swapping", "tconstruct:table_casting_part_swapping", "tconstruct:molding_basin", "tconstruct:molding_table", "tconstruct:melting", "tconstruct:ore_melting", "tconstruct:damagable_melting", "tconstruct:material_melting", "tconstruct:melting_fuel", "tconstruct:entity_melting", "tconstruct:alloy", "immersiveengineering:crafting_special_speedloader_load", "immersiveengineering:crafting_special_flare_bullet_color", "immersiveengineering:crafting_special_potion_bullet_fill", "immersiveengineering:crafting_special_jerrycan_refill", "immersiveengineering:powerpack", "immersiveengineering:hammer_crushing", "immersiveengineering:earmuffs", "immersiveengineering:rgb", "immersiveengineering:turn_and_copy", "immersiveengineering:revolver_assembly", "immersiveengineering:revolver_cycle", "immersiveengineering:ie_item_repair", "immersiveengineering:shader_bag", "immersiveengineering:damage_tool", "immersiveengineering:shaped_fluid", "immersiveengineering:shapeless_fluid", "immersiveengineering:no_container_item", "immersiveengineering:alloy", "immersiveengineering:blast_furnace", "immersiveengineering:blast_furnace_fuel", "immersiveengineering:coke_oven", "immersiveengineering:cloche", "immersiveengineering:fertilizer", "immersiveengineering:blueprint", "immersiveengineering:metal_press", "immersiveengineering:arc_furnace", "immersiveengineering:bottling_machine", "immersiveengineering:crusher", "immersiveengineering:sawmill", "immersiveengineering:fermenter", "immersiveengineering:squeezer", "immersiveengineering:refinery", "immersiveengineering:mixer", "immersiveengineering:mineral_mix", "immersiveengineering:generator_fuel", "immersiveengineering:thermoelectric_source", "immersiveengineering:windmill_biome", "immersiveengineering:generated_list", "createoreexcavation:drilling", "createoreexcavation:extracting", "createoreexcavation:vein", "create:conversion", "create:crushing", "create:cutting", "create:milling", "create:basin", "create:mixing", "create:compacting", "create:pressing", "create:sandpaper_polishing", "create:splashing", "create:haunting", "create:deploying", "create:filling", "create:emptying", "create:item_application", "create:mechanical_crafting", "create:sequenced_assembly", "create:toolbox_dyeing", "create:item_copying", "mantle:crafting_shaped_fallback", "mantle:crafting_shaped_retextured", "almostunified:client_recipe_tracker", "kubejs:shaped", "kubejs:shapeless", "mekanism:crushing", "mekanism:enriching", "mekanism:smelting", "mekanism:chemical_infusing", "mekanism:combining", "mekanism:separating", "mekanism:washing", "mekanism:evaporating", "mekanism:activating", "mekanism:centrifuging", "mekanism:crystallizing", "mekanism:dissolution", "mekanism:compressing", "mekanism:purifying", "mekanism:injecting", "mekanism:nucleosynthesizing", "mekanism:energy_conversion", "mekanism:gas_conversion", "mekanism:oxidizing", "mekanism:infusion_conversion", "mekanism:pigment_extracting", "mekanism:pigment_mixing", "mekanism:metallurgic_infusing", "mekanism:painting", "mekanism:reaction", "mekanism:rotary", "mekanism:sawing", "mekanism:mek_data", "mekanism:bin_insert", "mekanism:bin_extract"]}, "typeWorldgenStructureType": {"type": "string", "enum": ["minecraft:end_city", "minecraft:ocean_ruin", "minecraft:woodland_mansion", "cataclysm:cursed_pyramid", "minecraft:desert_pyramid", "minecraft:mineshaft", "minecraft:nether_fossil", "cataclysm:ruined_citadel", "minecraft:igloo", "minecraft:fortress", "minecraft:jungle_temple", "cataclysm:burning_arena", "minecraft:shipwreck", "minecraft:ae2mtrt", "cataclysm:cataclysm_jigsaw", "minecraft:stronghold", "minecraft:swamp_hut", "tconstruct:island", "cataclysm:sunken_city", "minecraft:ruined_portal", "minecraft:ocean_monument", "minecraft:buried_treasure", "minecraft:jigsaw"]}, "typeWorldgenFlatLevelGeneratorPreset": {"type": "string", "enum": ["minecraft:desert", "minecraft:tunnelers_dream", "minecraft:bottomless_pit", "minecraft:overworld", "minecraft:classic_flat", "minecraft:redstone_ready", "minecraft:snowy_kingdom", "minecraft:the_void", "minecraft:water_world"]}, "typeItem": {"type": "string", "enum": ["ae2:16k_crafting_storage", "ae2:1k_crafting_storage", "ae2:256k_crafting_storage", "ae2:4k_crafting_storage", "ae2:64k_crafting_storage", "ae2:advanced_card", "ae2:annihilation_core", "ae2:annihilation_plane", "ae2:basic_card", "ae2:black_covered_cable", "ae2:black_covered_dense_cable", "ae2:black_glass_cable", "ae2:black_lumen_paint_ball", "ae2:black_paint_ball", "ae2:black_smart_cable", "ae2:black_smart_dense_cable", "ae2:blank_pattern", "ae2:blue_covered_cable", "ae2:blue_covered_dense_cable", "ae2:blue_glass_cable", "ae2:blue_lumen_paint_ball", "ae2:blue_paint_ball", "ae2:blue_smart_cable", "ae2:blue_smart_dense_cable", "ae2:brown_covered_cable", "ae2:brown_covered_dense_cable", "ae2:brown_glass_cable", "ae2:brown_lumen_paint_ball", "ae2:brown_paint_ball", "ae2:brown_smart_cable", "ae2:brown_smart_dense_cable", "ae2:cable_anchor", "ae2:cable_bus", "ae2:cable_energy_acceptor", "ae2:cable_interface", "ae2:cable_pattern_provider", "ae2:calculation_processor", "ae2:calculation_processor_press", "ae2:capacity_card", "ae2:cell_component_16k", "ae2:cell_component_1k", "ae2:cell_component_256k", "ae2:cell_component_4k", "ae2:cell_component_64k", "ae2:cell_workbench", "ae2:certus_quartz_axe", "ae2:certus_quartz_crystal", "ae2:certus_quartz_cutting_knife", "ae2:certus_quartz_dust", "ae2:certus_quartz_hoe", "ae2:certus_quartz_pickaxe", "ae2:certus_quartz_shovel", "ae2:certus_quartz_sword", "ae2:certus_quartz_wrench", "ae2:charged_certus_quartz_crystal", "ae2:charged_staff", "ae2:charger", "ae2:chest", "ae2:chipped_budding_quartz", "ae2:chiseled_quartz_block", "ae2:chiseled_quartz_slab", "ae2:chiseled_quartz_stairs", "ae2:chiseled_quartz_wall", "ae2:color_applicator", "ae2:condenser", "ae2:controller", "ae2:conversion_monitor", "ae2:crafting_accelerator", "ae2:crafting_card", "ae2:crafting_monitor", "ae2:crafting_pattern", "ae2:crafting_terminal", "ae2:crafting_unit", "ae2:crank", "ae2:creative_energy_cell", "ae2:creative_fluid_cell", "ae2:creative_item_cell", "ae2:crystal_resonance_generator", "ae2:cut_quartz_block", "ae2:cut_quartz_slab", "ae2:cut_quartz_stairs", "ae2:cut_quartz_wall", "ae2:cyan_covered_cable", "ae2:cyan_covered_dense_cable", "ae2:cyan_glass_cable", "ae2:cyan_lumen_paint_ball", "ae2:cyan_paint_ball", "ae2:cyan_smart_cable", "ae2:cyan_smart_dense_cable", "ae2:damaged_budding_quartz", "ae2:dark_monitor", "ae2:debug_card", "ae2:debug_chunk_loader", "ae2:debug_cube_gen", "ae2:debug_energy_gen", "ae2:debug_eraser", "ae2:debug_item_gen", "ae2:debug_meteorite_placer", "ae2:debug_phantom_node", "ae2:debug_replicator_card", "ae2:dense_energy_cell", "ae2:drive", "ae2:ender_dust", "ae2:energy_acceptor", "ae2:energy_card", "ae2:energy_cell", "ae2:energy_level_emitter", "ae2:engineering_processor", "ae2:engineering_processor_press", "ae2:entropy_manipulator", "ae2:equal_distribution_card", "ae2:export_bus", "ae2:facade", "ae2:fe_p2p_tunnel", "ae2:flawed_budding_quartz", "ae2:flawless_budding_quartz", "ae2:fluid_cell_housing", "ae2:fluid_p2p_tunnel", "ae2:fluid_storage_cell_16k", "ae2:fluid_storage_cell_1k", "ae2:fluid_storage_cell_256k", "ae2:fluid_storage_cell_4k", "ae2:fluid_storage_cell_64k", "ae2:fluix_axe", "ae2:fluix_block", "ae2:fluix_covered_cable", "ae2:fluix_covered_dense_cable", "ae2:fluix_crystal", "ae2:fluix_dust", "ae2:fluix_glass_cable", "ae2:fluix_hoe", "ae2:fluix_pearl", "ae2:fluix_pickaxe", "ae2:fluix_shovel", "ae2:fluix_slab", "ae2:fluix_smart_cable", "ae2:fluix_smart_dense_cable", "ae2:fluix_stairs", "ae2:fluix_sword", "ae2:fluix_upgrade_smithing_template", "ae2:fluix_wall", "ae2:formation_core", "ae2:formation_plane", "ae2:fuzzy_card", "ae2:gray_covered_cable", "ae2:gray_covered_dense_cable", "ae2:gray_glass_cable", "ae2:gray_lumen_paint_ball", "ae2:gray_paint_ball", "ae2:gray_smart_cable", "ae2:gray_smart_dense_cable", "ae2:green_covered_cable", "ae2:green_covered_dense_cable", "ae2:green_glass_cable", "ae2:green_lumen_paint_ball", "ae2:green_paint_ball", "ae2:green_smart_cable", "ae2:green_smart_dense_cable", "ae2:growth_accelerator", "ae2:guide", "ae2:import_bus", "ae2:inscriber", "ae2:interface", "ae2:inverted_toggle_bus", "ae2:inverter_card", "ae2:io_port", "ae2:item_cell_housing", "ae2:item_p2p_tunnel", "ae2:item_storage_cell_16k", "ae2:item_storage_cell_1k", "ae2:item_storage_cell_256k", "ae2:item_storage_cell_4k", "ae2:item_storage_cell_64k", "ae2:large_quartz_bud", "ae2:level_emitter", "ae2:light_blue_covered_cable", "ae2:light_blue_covered_dense_cable", "ae2:light_blue_glass_cable", "ae2:light_blue_lumen_paint_ball", "ae2:light_blue_paint_ball", "ae2:light_blue_smart_cable", "ae2:light_blue_smart_dense_cable", "ae2:light_detector", "ae2:light_gray_covered_cable", "ae2:light_gray_covered_dense_cable", "ae2:light_gray_glass_cable", "ae2:light_gray_lumen_paint_ball", "ae2:light_gray_paint_ball", "ae2:light_gray_smart_cable", "ae2:light_gray_smart_dense_cable", "ae2:light_p2p_tunnel", "ae2:lime_covered_cable", "ae2:lime_covered_dense_cable", "ae2:lime_glass_cable", "ae2:lime_lumen_paint_ball", "ae2:lime_paint_ball", "ae2:lime_smart_cable", "ae2:lime_smart_dense_cable", "ae2:logic_processor", "ae2:logic_processor_press", "ae2:magenta_covered_cable", "ae2:magenta_covered_dense_cable", "ae2:magenta_glass_cable", "ae2:magenta_lumen_paint_ball", "ae2:magenta_paint_ball", "ae2:magenta_smart_cable", "ae2:magenta_smart_dense_cable", "ae2:matrix_frame", "ae2:matter_ball", "ae2:matter_cannon", "ae2:me_p2p_tunnel", "ae2:medium_quartz_bud", "ae2:memory_card", "ae2:meteorite_compass", "ae2:molecular_assembler", "ae2:monitor", "ae2:mysterious_cube", "ae2:name_press", "ae2:nether_quartz_axe", "ae2:nether_quartz_cutting_knife", "ae2:nether_quartz_hoe", "ae2:nether_quartz_pickaxe", "ae2:nether_quartz_shovel", "ae2:nether_quartz_sword", "ae2:nether_quartz_wrench", "ae2:network_tool", "ae2:not_so_mysterious_cube", "ae2:orange_covered_cable", "ae2:orange_covered_dense_cable", "ae2:orange_glass_cable", "ae2:orange_lumen_paint_ball", "ae2:orange_paint_ball", "ae2:orange_smart_cable", "ae2:orange_smart_dense_cable", "ae2:paint", "ae2:pattern_access_terminal", "ae2:pattern_encoding_terminal", "ae2:pattern_provider", "ae2:pink_covered_cable", "ae2:pink_covered_dense_cable", "ae2:pink_glass_cable", "ae2:pink_lumen_paint_ball", "ae2:pink_paint_ball", "ae2:pink_smart_cable", "ae2:pink_smart_dense_cable", "ae2:portable_fluid_cell_16k", "ae2:portable_fluid_cell_1k", "ae2:portable_fluid_cell_256k", "ae2:portable_fluid_cell_4k", "ae2:portable_fluid_cell_64k", "ae2:portable_item_cell_16k", "ae2:portable_item_cell_1k", "ae2:portable_item_cell_256k", "ae2:portable_item_cell_4k", "ae2:portable_item_cell_64k", "ae2:printed_calculation_processor", "ae2:printed_engineering_processor", "ae2:printed_logic_processor", "ae2:printed_silicon", "ae2:processing_pattern", "ae2:purple_covered_cable", "ae2:purple_covered_dense_cable", "ae2:purple_glass_cable", "ae2:purple_lumen_paint_ball", "ae2:purple_paint_ball", "ae2:purple_smart_cable", "ae2:purple_smart_dense_cable", "ae2:quantum_entangled_singularity", "ae2:quantum_link", "ae2:quantum_ring", "ae2:quartz_block", "ae2:quartz_brick_slab", "ae2:quartz_brick_stairs", "ae2:quartz_brick_wall", "ae2:quartz_bricks", "ae2:quartz_cluster", "ae2:quartz_fiber", "ae2:quartz_fixture", "ae2:quartz_glass", "ae2:quartz_pillar", "ae2:quartz_pillar_slab", "ae2:quartz_pillar_stairs", "ae2:quartz_pillar_wall", "ae2:quartz_slab", "ae2:quartz_stairs", "ae2:quartz_vibrant_glass", "ae2:quartz_wall", "ae2:red_covered_cable", "ae2:red_covered_dense_cable", "ae2:red_glass_cable", "ae2:red_lumen_paint_ball", "ae2:red_paint_ball", "ae2:red_smart_cable", "ae2:red_smart_dense_cable", "ae2:redstone_card", "ae2:redstone_p2p_tunnel", "ae2:semi_dark_monitor", "ae2:silicon", "ae2:silicon_press", "ae2:singularity", "ae2:sky_dust", "ae2:sky_stone_block", "ae2:sky_stone_brick", "ae2:sky_stone_brick_slab", "ae2:sky_stone_brick_stairs", "ae2:sky_stone_brick_wall", "ae2:sky_stone_chest", "ae2:sky_stone_slab", "ae2:sky_stone_small_brick", "ae2:sky_stone_small_brick_slab", "ae2:sky_stone_small_brick_stairs", "ae2:sky_stone_small_brick_wall", "ae2:sky_stone_stairs", "ae2:sky_stone_tank", "ae2:sky_stone_wall", "ae2:small_quartz_bud", "ae2:smithing_table_pattern", "ae2:smooth_quartz_block", "ae2:smooth_quartz_slab", "ae2:smooth_quartz_stairs", "ae2:smooth_quartz_wall", "ae2:smooth_sky_stone_block", "ae2:smooth_sky_stone_chest", "ae2:smooth_sky_stone_slab", "ae2:smooth_sky_stone_stairs", "ae2:smooth_sky_stone_wall", "ae2:spatial_anchor", "ae2:spatial_cell_component_128", "ae2:spatial_cell_component_16", "ae2:spatial_cell_component_2", "ae2:spatial_io_port", "ae2:spatial_pylon", "ae2:spatial_storage_cell_128", "ae2:spatial_storage_cell_16", "ae2:spatial_storage_cell_2", "ae2:speed_card", "ae2:stonecutting_pattern", "ae2:storage_bus", "ae2:storage_monitor", "ae2:terminal", "ae2:tiny_tnt", "ae2:toggle_bus", "ae2:vibration_chamber", "ae2:view_cell", "ae2:void_card", "ae2:white_covered_cable", "ae2:white_covered_dense_cable", "ae2:white_glass_cable", "ae2:white_lumen_paint_ball", "ae2:white_paint_ball", "ae2:white_smart_cable", "ae2:white_smart_dense_cable", "ae2:wireless_access_point", "ae2:wireless_booster", "ae2:wireless_crafting_terminal", "ae2:wireless_receiver", "ae2:wireless_terminal", "ae2:wrapped_generic_stack", "ae2:yellow_covered_cable", "ae2:yellow_covered_dense_cable", "ae2:yellow_glass_cable", "ae2:yellow_lumen_paint_ball", "ae2:yellow_paint_ball", "ae2:yellow_smart_cable", "ae2:yellow_smart_dense_cable", "ae2things:disk_drive_16k", "ae2things:disk_drive_1k", "ae2things:disk_drive_256k", "ae2things:disk_drive_4k", "ae2things:disk_drive_64k", "ae2things:disk_housing", "aerlunerpg:acid", "aerlunerpg:arm", "aerlunerpg:bearcandy_2_spawn_egg", "aerlunerpg:bearcandy_3_spawn_egg", "aerlunerpg:bearcandy_4_spawn_egg", "aerlunerpg:bearcandy_spawn_egg", "aerlunerpg:bearcandyitem", "aerlunerpg:bearcandytame", "aerlunerpg:bedbug_spawn_egg", "aerlunerpg:bite_1", "aerlunerpg:biteitem_1", "aerlunerpg:boar_3_spawn_egg", "aerlunerpg:boar_spawn_egg", "aerlunerpg:boarbig_spawn_egg", "aerlunerpg:boarboss_spawn_egg", "aerlunerpg:boarhead", "aerlunerpg:bookgui", "aerlunerpg:bookice_0", "aerlunerpg:bookice_1", "aerlunerpg:bushsmall", "aerlunerpg:butterfly_spawn_egg", "aerlunerpg:buttermini_2_spawn_egg", "aerlunerpg:buttermini_spawn_egg", "aerlunerpg:candyctickbig", "aerlunerpg:candysblock", "aerlunerpg:candysblock_2", "aerlunerpg:candysp", "aerlunerpg:candysp_2", "aerlunerpg:candysp_3", "aerlunerpg:candystick", "aerlunerpg:candystick_1", "aerlunerpg:cannongnome_spawn_egg", "aerlunerpg:capacitorofsouls", "aerlunerpg:capacitorsouls", "aerlunerpg:caramel", "aerlunerpg:caramelore", "aerlunerpg:centipede_spawn_egg", "aerlunerpg:chestbig", "aerlunerpg:chestbig_2", "aerlunerpg:chestbigwood", "aerlunerpg:chestcandy", "aerlunerpg:chip_1", "aerlunerpg:chip_2", "aerlunerpg:chocolate", "aerlunerpg:chocolates", "aerlunerpg:christmastree_spawn_egg", "aerlunerpg:co<PERSON>ie", "aerlunerpg:coockierecipe", "aerlunerpg:coockietame", "aerlunerpg:cookie_2_spawn_egg", "aerlunerpg:cookie_spawn_egg", "aerlunerpg:coreofthevoid", "aerlunerpg:creatorvoid", "aerlunerpg:crowd_spawn_egg", "aerlunerpg:crowdblock", "aerlunerpg:crowdd", "aerlunerpg:crystalsattack", "aerlunerpg:crystalsmithg", "aerlunerpg:crystalsmithril", "aerlunerpg:crystalsvoid", "aerlunerpg:cthulhu_spawn_egg", "aerlunerpg:ctp", "aerlunerpg:cudgel", "aerlunerpg:cudgelragemetsmall", "aerlunerpg:deerbig_2_spawn_egg", "aerlunerpg:deerbig_spawn_egg", "aerlunerpg:deerevil_spawn_egg", "aerlunerpg:demongun", "aerlunerpg:dirt", "aerlunerpg:disposablebait", "aerlunerpg:donut_spawn_egg", "aerlunerpg:dust", "aerlunerpg:energybigdark", "aerlunerpg:energydark", "aerlunerpg:ent_spawn_egg", "aerlunerpg:entblock", "aerlunerpg:evilgnome_2_spawn_egg", "aerlunerpg:evilgnome_spawn_egg", "aerlunerpg:eyebeatle_spawn_egg", "aerlunerpg:filgist", "aerlunerpg:firemetsmall", "aerlunerpg:foodalien_1", "aerlunerpg:foodalien_2", "aerlunerpg:foodalien_3", "aerlunerpg:furarmor_boots", "aerlunerpg:furarmor_chestplate", "aerlunerpg:furarmor_leggings", "aerlunerpg:furnacesmall", "aerlunerpg:furnacev", "aerlunerpg:ghost_spawn_egg", "aerlunerpg:giftblue", "aerlunerpg:giftsmal", "aerlunerpg:gingerblock", "aerlunerpg:gingerblock_2", "aerlunerpg:gingerblock_3", "aerlunerpg:gingerblocks", "aerlunerpg:gingerbreadman_spawn_egg", "aerlunerpg:gingerbreadmanitem", "aerlunerpg:gingerbreadmanitemtame", "aerlunerpg:gingerbre<PERSON>mann", "aerlunerpg:gingirmanevil_spawn_egg", "aerlunerpg:gingirmanevilred_spawn_egg", "aerlunerpg:gnomehead", "aerlunerpg:gnomehome", "aerlunerpg:goblin_spawn_egg", "aerlunerpg:gobliny_spawn_egg", "aerlunerpg:golem_spawn_egg", "aerlunerpg:grasscold", "aerlunerpg:grasssnow", "aerlunerpg:hatcc_helmet", "aerlunerpg:holespawn_1", "aerlunerpg:house_spawn_egg", "aerlunerpg:icebookd", "aerlunerpg:icecrystal", "aerlunerpg:iced", "aerlunerpg:icefireitem", "aerlunerpg:icemet", "aerlunerpg:icerion", "aerlunerpg:icerod", "aerlunerpg:icerodfiremet", "aerlunerpg:icerodmagicmet", "aerlunerpg:icerodparitem", "aerlunerpg:icesword", "aerlunerpg:laser_spawn_egg", "aerlunerpg:leather", "aerlunerpg:lifeen", "aerlunerpg:lightning", "aerlunerpg:luck_1", "aerlunerpg:magicmetsmall", "aerlunerpg:magicparitem", "aerlunerpg:mine", "aerlunerpg:mithril", "aerlunerpg:mithrildrill", "aerlunerpg:mushroombrothblock", "aerlunerpg:mushroombrothitem", "aerlunerpg:new_yearhat_helmet", "aerlunerpg:oak", "aerlunerpg:oak_2", "aerlunerpg:oak_3", "aerlunerpg:oak_4", "aerlunerpg:oak_5", "aerlunerpg:oldmantrade", "aerlunerpg:owlarmor_helmet", "aerlunerpg:owlarmorblock", "aerlunerpg:parasite", "aerlunerpg:piercing<PERSON>ar", "aerlunerpg:pipe_helmet", "aerlunerpg:pipeblock", "aerlunerpg:portalitem", "aerlunerpg:potcandy", "aerlunerpg:pumphead_helmet", "aerlunerpg:rabbit_spawn_egg", "aerlunerpg:rabbithole", "aerlunerpg:rabbitmount_spawn_egg", "aerlunerpg:rabpetitem", "aerlunerpg:ragemetsmall", "aerlunerpg:recipebear", "aerlunerpg:recipeman", "aerlunerpg:robotgnome_spawn_egg", "aerlunerpg:scrollfireice", "aerlunerpg:scrollicsh", "aerlunerpg:shockwave", "aerlunerpg:shoggotsmall_spawn_egg", "aerlunerpg:smallbat", "aerlunerpg:smallbatt", "aerlunerpg:smallbeatle_spawn_egg", "aerlunerpg:smallbeatleitem", "aerlunerpg:smalltreeblock_1", "aerlunerpg:smalltreeblock_2", "aerlunerpg:snow_2", "aerlunerpg:snowdrop", "aerlunerpg:snowman_2_spawn_egg", "aerlunerpg:snowman_3_spawn_egg", "aerlunerpg:snowman_spawn_egg", "aerlunerpg:squirrel", "aerlunerpg:squirrelblock", "aerlunerpg:steve_spawn_egg", "aerlunerpg:stonesmall", "aerlunerpg:strangecandy", "aerlunerpg:stump_1", "aerlunerpg:stump_2_spawn_egg", "aerlunerpg:stump_spawn_egg", "aerlunerpg:stunning", "aerlunerpg:sugar", "aerlunerpg:sup", "aerlunerpg:test_spawn_egg", "aerlunerpg:toilet", "aerlunerpg:toilet_2", "aerlunerpg:tp", "aerlunerpg:treesmall", "aerlunerpg:trophyboar", "aerlunerpg:trophyeyes", "aerlunerpg:trophyrabbit", "aerlunerpg:tryrtyry", "aerlunerpg:voidkey", "aerlunerpg:voidshards", "aerlunerpg:warm_spawn_egg", "aerlunerpg:woodshield", "aerlunerpg:zombieghost_spawn_egg", "bigger_ae2:1024_core_crafting_accelerator", "bigger_ae2:16_core_crafting_accelerator", "bigger_ae2:256_core_crafting_accelerator", "bigger_ae2:4_core_crafting_accelerator", "bigger_ae2:64_core_crafting_accelerator", "bigger_ae2:advanced_fluid_cell_housing", "bigger_ae2:advanced_item_cell_housing", "bigger_ae2:digital_singularity_cell_component", "bigger_ae2:digital_singularity_fluid_storage_cell", "bigger_ae2:digital_singularity_item_storage_cell", "bigger_ae2:quantum_cell_component", "bigger_ae2:quantum_fluid_storage_cell", "bigger_ae2:quantum_item_storage_cell", "cataclysm:abyss_eye", "cataclysm:abyssal_egg", "cataclysm:abyssal_sacrifice", "cataclysm:altar_of_abyss", "cataclysm:altar_of_amethyst", "cataclysm:altar_of_fire", "cataclysm:altar_of_void", "cataclysm:amethyst_crab_meat", "cataclysm:amethyst_crab_shell", "cataclysm:amethyst_crab_spawn_egg", "cataclysm:ancient_metal_block", "cataclysm:ancient_metal_ingot", "cataclysm:ancient_metal_nugget", "cataclysm:ancient_remnant_spawn_egg", "cataclysm:ancient_spear", "cataclysm:aptrgangr_head", "cataclysm:aptrgangr_spawn_egg", "cataclysm:athame", "cataclysm:black_steel_axe", "cataclysm:black_steel_block", "cataclysm:black_steel_fence", "cataclysm:black_steel_hoe", "cataclysm:black_steel_ingot", "cataclysm:black_steel_nugget", "cataclysm:black_steel_pickaxe", "cataclysm:black_steel_shovel", "cataclysm:black_steel_sword", "cataclysm:black_steel_targe", "cataclysm:black_steel_wall", "cataclysm:blackstone_pillar", "cataclysm:blazing_bone", "cataclysm:blessed_amethyst_crab_meat", "cataclysm:bloom_stone_pauldrons", "cataclysm:bone_reptile_chestplate", "cataclysm:bone_reptile_helmet", "cataclysm:bulwark_of_the_flame", "cataclysm:burning_ashes", "cataclysm:chain_of_soul_binding", "cataclysm:chiseled_end_stone_bricks", "cataclysm:chiseled_obsidian_bricks", "cataclysm:chiseled_purpur_block", "cataclysm:chiseled_stone_brick_pillar", "cataclysm:chorus_fence", "cataclysm:chorus_planks", "cataclysm:chorus_slab", "cataclysm:chorus_stairs", "cataclysm:chorus_stem", "cataclysm:<PERSON>_bar<PERSON>he", "cataclysm:coral_chunk", "cataclysm:coral_golem_spawn_egg", "cataclysm:coral_spear", "cataclysm:coralssus_spawn_egg", "cataclysm:crystallized_coral", "cataclysm:crystallized_coral_fragments", "cataclysm:cursed_bow", "cataclysm:cursed_eye", "cataclysm:cursed_tombstone", "cataclysm:cursium_block", "cataclysm:cursium_boots", "cataclysm:cursium_chestplate", "cataclysm:cursium_helmet", "cataclysm:cursium_ingot", "cataclysm:cursium_leggings", "cataclysm:cursium_upgrade_smithing_template", "cataclysm:deepling_angler_spawn_egg", "cataclysm:deepling_brute_spawn_egg", "cataclysm:deepling_priest_spawn_egg", "cataclysm:deepling_spawn_egg", "cataclysm:deepling_warlock_spawn_egg", "cataclysm:desert_eye", "cataclysm:door_of_seal", "cataclysm:draugr_head", "cataclysm:draugr_spawn_egg", "cataclysm:dungeon_block", "cataclysm:dying_ember", "cataclysm:elite_draugr_spawn_egg", "cataclysm:emp", "cataclysm:end_stone_pillar", "cataclysm:end_stone_teleport_trap_bricks", "cataclysm:ender_golem_spawn_egg", "cataclysm:ender_guardian_spawn_egg", "cataclysm:enderite_block", "cataclysm:enderite_ingot", "cataclysm:endermaptera_spawn_egg", "cataclysm:final_fractal", "cataclysm:flame_eye", "cataclysm:frosted_stone_brick_slab", "cataclysm:frosted_stone_brick_stairs", "cataclysm:frosted_stone_brick_wall", "cataclysm:frosted_stone_bricks", "cataclysm:gauntlet_of_bulwark", "cataclysm:gauntlet_of_guard", "cataclysm:ignis_spawn_egg", "cataclysm:ignited_berserker_spawn_egg", "cataclysm:ignited_revenant_spawn_egg", "cataclysm:ignitium_block", "cataclysm:ignitium_boots", "cataclysm:ignitium_chestplate", "cataclysm:ignitium_elytra_chestplate", "cataclysm:ignitium_helmet", "cataclysm:ignitium_ingot", "cataclysm:ignitium_leggings", "cataclysm:ignitium_upgrade_smithing_template", "cataclysm:infernal_forge", "cataclysm:<PERSON><PERSON><PERSON>", "cataclysm:kobolediator_skull", "cataclysm:kobolediator_spawn_egg", "cataclysm:koboleton_bone", "cataclysm:koboleton_spawn_egg", "cataclysm:laser_gatling", "cataclysm:lionfish", "cataclysm:lionfish_spawn_egg", "cataclysm:lionfish_spike", "cataclysm:maledictus_spawn_egg", "cataclysm:meat_shredder", "cataclysm:mech_eye", "cataclysm:mechanical_fusion_anvil", "cataclysm:modern_remnant_bucket", "cataclysm:modern_remnant_spawn_egg", "cataclysm:monstrous_eye", "cataclysm:monstrous_helm", "cataclysm:monstrous_horn", "cataclysm:music_disc_ancient_remnant", "cataclysm:music_disc_ender_guardian", "cataclysm:music_disc_ignis", "cataclysm:music_disc_maledictus", "cataclysm:music_disc_netherite_monstrosity", "cataclysm:music_disc_the_harbinger", "cataclysm:music_disc_the_leviathan", "cataclysm:nameless_sorcerer_spawn_egg", "cataclysm:necklace_of_the_desert", "cataclysm:netherite_monstrosity_spawn_egg", "cataclysm:obsidian_brick_slab", "cataclysm:obsidian_brick_stairs", "cataclysm:obsidian_brick_wall", "cataclysm:obsidian_bricks", "cataclysm:obsidian_explosion_trap_bricks", "cataclysm:pointed_icicle", "cataclysm:polished_end_stone", "cataclysm:polished_end_stone_slab", "cataclysm:polished_end_stone_stairs", "cataclysm:polished_sandstone", "cataclysm:purpur_void_rune_trap_block", "cataclysm:purpur_wall", "cataclysm:quartz_brick_wall", "cataclysm:remnant_skull", "cataclysm:royal_draugr_spawn_egg", "cataclysm:sandstone_falling_trap", "cataclysm:sandstone_ignite_trap", "cataclysm:sandstone_poison_dart_trap", "cataclysm:sandstorm_in_a_bottle", "cataclysm:soul_render", "cataclysm:sticky_gloves", "cataclysm:stone_pillar", "cataclysm:stone_tile_slab", "cataclysm:stone_tile_stairs", "cataclysm:stone_tile_wall", "cataclysm:stone_tiles", "cataclysm:the_annihilator", "cataclysm:the_baby_leviathan_bucket", "cataclysm:the_baby_leviathan_spawn_egg", "cataclysm:the_harbinger_spawn_egg", "cataclysm:the_incinerator", "cataclysm:the_leviathan_spawn_egg", "cataclysm:the_prowler_spawn_egg", "cataclysm:the_watcher_spawn_egg", "cataclysm:tidal_claws", "cataclysm:void_assault_shoulder_weapon", "cataclysm:void_core", "cataclysm:void_eye", "cataclysm:void_forge", "cataclysm:void_infused_end_stone_bricks", "cataclysm:void_jaw", "cataclysm:void_lantern_block", "cataclysm:void_scatter_arrow", "cataclysm:void_shard", "cataclysm:void_stone", "cataclysm:wadjet_spawn_egg", "cataclysm:wither_assault_shoulder_weapon", "cataclysm:witherite_block", "cataclysm:witherite_ingot", "cataclysm:zwei<PERSON>", "create:acacia_window", "create:acacia_window_pane", "create:adjustable_chain_gearshift", "create:analog_lever", "create:andesite_alloy", "create:andesite_alloy_block", "create:andesite_bars", "create:andesite_casing", "create:andesite_door", "create:andesite_encased_cogwheel", "create:andesite_encased_large_cogwheel", "create:andesite_encased_shaft", "create:andesite_funnel", "create:andesite_ladder", "create:andesite_pillar", "create:andesite_scaffolding", "create:andesite_tunnel", "create:asurine", "create:asurine_pillar", "create:attribute_filter", "create:bar_of_chocolate", "create:basin", "create:belt_connector", "create:birch_window", "create:birch_window_pane", "create:black_seat", "create:black_toolbox", "create:black_valve_handle", "create:blaze_burner", "create:blaze_cake", "create:blaze_cake_base", "create:blue_seat", "create:blue_toolbox", "create:blue_valve_handle", "create:brass_bars", "create:brass_block", "create:brass_casing", "create:brass_door", "create:brass_encased_cogwheel", "create:brass_encased_large_cogwheel", "create:brass_encased_shaft", "create:brass_funnel", "create:brass_hand", "create:brass_ingot", "create:brass_ladder", "create:brass_nugget", "create:brass_scaffolding", "create:brass_sheet", "create:brass_tunnel", "create:brown_seat", "create:brown_toolbox", "create:brown_valve_handle", "create:builders_tea", "create:calcite_pillar", "create:cart_assembler", "create:chest_minecart_contraption", "create:chocolate_bucket", "create:chocolate_glazed_berries", "create:chromatic_compound", "create:chute", "create:cinder_flour", "create:clipboard", "create:clockwork_bearing", "create:clutch", "create:cogwheel", "create:content_observer", "create:contraption_controls", "create:controller_rail", "create:controls", "create:copper_backtank", "create:copper_backtank_placeable", "create:copper_bars", "create:copper_casing", "create:copper_diving_boots", "create:copper_diving_helmet", "create:copper_door", "create:copper_ladder", "create:copper_nugget", "create:copper_scaffolding", "create:copper_sheet", "create:copper_shingle_slab", "create:copper_shingle_stairs", "create:copper_shingles", "create:copper_tile_slab", "create:copper_tile_stairs", "create:copper_tiles", "create:copper_valve_handle", "create:copycat_panel", "create:copycat_step", "create:crafter_slot_cover", "create:crafting_blueprint", "create:creative_blaze_cake", "create:creative_crate", "create:creative_fluid_tank", "create:creative_motor", "create:crimsite", "create:crimsite_pillar", "create:crimson_window", "create:crimson_window_pane", "create:crushed_raw_aluminum", "create:crushed_raw_copper", "create:crushed_raw_gold", "create:crushed_raw_iron", "create:crushed_raw_lead", "create:crushed_raw_nickel", "create:crushed_raw_osmium", "create:crushed_raw_platinum", "create:crushed_raw_quicksilver", "create:crushed_raw_silver", "create:crushed_raw_tin", "create:crushed_raw_uranium", "create:crushed_raw_zinc", "create:crushing_wheel", "create:cuckoo_clock", "create:cut_andesite", "create:cut_andesite_brick_slab", "create:cut_andesite_brick_stairs", "create:cut_andesite_brick_wall", "create:cut_andesite_bricks", "create:cut_andesite_slab", "create:cut_andesite_stairs", "create:cut_andesite_wall", "create:cut_asurine", "create:cut_asurine_brick_slab", "create:cut_asurine_brick_stairs", "create:cut_asurine_brick_wall", "create:cut_asurine_bricks", "create:cut_asurine_slab", "create:cut_asurine_stairs", "create:cut_asurine_wall", "create:cut_calcite", "create:cut_calcite_brick_slab", "create:cut_calcite_brick_stairs", "create:cut_calcite_brick_wall", "create:cut_calcite_bricks", "create:cut_calcite_slab", "create:cut_calcite_stairs", "create:cut_calcite_wall", "create:cut_crimsite", "create:cut_crimsite_brick_slab", "create:cut_crimsite_brick_stairs", "create:cut_crimsite_brick_wall", "create:cut_crimsite_bricks", "create:cut_crimsite_slab", "create:cut_crimsite_stairs", "create:cut_crimsite_wall", "create:cut_deepslate", "create:cut_deepslate_brick_slab", "create:cut_deepslate_brick_stairs", "create:cut_deepslate_brick_wall", "create:cut_deepslate_bricks", "create:cut_deepslate_slab", "create:cut_deepslate_stairs", "create:cut_deepslate_wall", "create:cut_diorite", "create:cut_diorite_brick_slab", "create:cut_diorite_brick_stairs", "create:cut_diorite_brick_wall", "create:cut_diorite_bricks", "create:cut_diorite_slab", "create:cut_diorite_stairs", "create:cut_diorite_wall", "create:cut_dripstone", "create:cut_dripstone_brick_slab", "create:cut_dripstone_brick_stairs", "create:cut_dripstone_brick_wall", "create:cut_dripstone_bricks", "create:cut_dripstone_slab", "create:cut_dripstone_stairs", "create:cut_dripstone_wall", "create:cut_granite", "create:cut_granite_brick_slab", "create:cut_granite_brick_stairs", "create:cut_granite_brick_wall", "create:cut_granite_bricks", "create:cut_granite_slab", "create:cut_granite_stairs", "create:cut_granite_wall", "create:cut_limestone", "create:cut_limestone_brick_slab", "create:cut_limestone_brick_stairs", "create:cut_limestone_brick_wall", "create:cut_limestone_bricks", "create:cut_limestone_slab", "create:cut_limestone_stairs", "create:cut_limestone_wall", "create:cut_ochrum", "create:cut_ochrum_brick_slab", "create:cut_ochrum_brick_stairs", "create:cut_ochrum_brick_wall", "create:cut_ochrum_bricks", "create:cut_ochrum_slab", "create:cut_ochrum_stairs", "create:cut_ochrum_wall", "create:cut_scorchia", "create:cut_scorchia_brick_slab", "create:cut_scorchia_brick_stairs", "create:cut_scorchia_brick_wall", "create:cut_scorchia_bricks", "create:cut_scorchia_slab", "create:cut_scorchia_stairs", "create:cut_scorchia_wall", "create:cut_scoria", "create:cut_scoria_brick_slab", "create:cut_scoria_brick_stairs", "create:cut_scoria_brick_wall", "create:cut_scoria_bricks", "create:cut_scoria_slab", "create:cut_scoria_stairs", "create:cut_scoria_wall", "create:cut_tuff", "create:cut_tuff_brick_slab", "create:cut_tuff_brick_stairs", "create:cut_tuff_brick_wall", "create:cut_tuff_bricks", "create:cut_tuff_slab", "create:cut_tuff_stairs", "create:cut_tuff_wall", "create:cut_veridium", "create:cut_veridium_brick_slab", "create:cut_veridium_brick_stairs", "create:cut_veridium_brick_wall", "create:cut_veridium_bricks", "create:cut_veridium_slab", "create:cut_veridium_stairs", "create:cut_veridium_wall", "create:cyan_seat", "create:cyan_toolbox", "create:cyan_valve_handle", "create:dark_oak_window", "create:dark_oak_window_pane", "create:deepslate_pillar", "create:deepslate_zinc_ore", "create:deployer", "create:depot", "create:diorite_pillar", "create:display_board", "create:display_link", "create:dough", "create:dripstone_pillar", "create:electron_tube", "create:elevator_contact", "create:elevator_pulley", "create:empty_blaze_burner", "create:empty_schematic", "create:encased_chain_drive", "create:encased_fan", "create:experience_block", "create:experience_nugget", "create:exposed_copper_shingle_slab", "create:exposed_copper_shingle_stairs", "create:exposed_copper_shingles", "create:exposed_copper_tile_slab", "create:exposed_copper_tile_stairs", "create:exposed_copper_tiles", "create:extendo_grip", "create:filter", "create:fluid_pipe", "create:fluid_tank", "create:fluid_valve", "create:flywheel", "create:framed_glass", "create:framed_glass_door", "create:framed_glass_pane", "create:framed_glass_trapdoor", "create:furnace_minecart_contraption", "create:gantry_carriage", "create:gantry_shaft", "create:gearbox", "create:gearshift", "create:goggles", "create:golden_sheet", "create:granite_pillar", "create:gray_seat", "create:gray_toolbox", "create:gray_valve_handle", "create:green_seat", "create:green_toolbox", "create:green_valve_handle", "create:hand_crank", "create:handheld_worldshaper", "create:haunted_bell", "create:honey_bucket", "create:honeyed_apple", "create:horizontal_framed_glass", "create:horizontal_framed_glass_pane", "create:hose_pulley", "create:incomplete_precision_mechanism", "create:incomplete_track", "create:industrial_iron_block", "create:iron_sheet", "create:item_drain", "create:item_vault", "create:jungle_window", "create:jungle_window_pane", "create:large_cogwheel", "create:large_water_wheel", "create:layered_andesite", "create:layered_asurine", "create:layered_calcite", "create:layered_crimsite", "create:layered_deepslate", "create:layered_diorite", "create:layered_dripstone", "create:layered_granite", "create:layered_limestone", "create:layered_ochrum", "create:layered_scorchia", "create:layered_scoria", "create:layered_tuff", "create:layered_veridium", "create:light_blue_seat", "create:light_blue_toolbox", "create:light_blue_valve_handle", "create:light_gray_seat", "create:light_gray_toolbox", "create:light_gray_valve_handle", "create:lime_seat", "create:lime_toolbox", "create:lime_valve_handle", "create:limestone", "create:limestone_pillar", "create:linear_chassis", "create:linked_controller", "create:magenta_seat", "create:magenta_toolbox", "create:magenta_valve_handle", "create:mangrove_window", "create:mangrove_window_pane", "create:mechanical_arm", "create:mechanical_bearing", "create:mechanical_crafter", "create:mechanical_drill", "create:mechanical_harvester", "create:mechanical_mixer", "create:mechanical_piston", "create:mechanical_plough", "create:mechanical_press", "create:mechanical_pump", "create:mechanical_roller", "create:mechanical_saw", "create:metal_bracket", "create:metal_girder", "create:millstone", "create:minecart_contraption", "create:minecart_coupling", "create:mysterious_cuckoo_clock", "create:netherite_backtank", "create:netherite_backtank_placeable", "create:netherite_diving_boots", "create:netherite_diving_helmet", "create:nixie_tube", "create:nozzle", "create:oak_window", "create:oak_window_pane", "create:ochrum", "create:ochrum_pillar", "create:orange_seat", "create:orange_toolbox", "create:orange_valve_handle", "create:ornate_iron_window", "create:ornate_iron_window_pane", "create:oxidized_copper_shingle_slab", "create:oxidized_copper_shingle_stairs", "create:oxidized_copper_shingles", "create:oxidized_copper_tile_slab", "create:oxidized_copper_tile_stairs", "create:oxidized_copper_tiles", "create:peculiar_bell", "create:pink_seat", "create:pink_toolbox", "create:pink_valve_handle", "create:piston_extension_pole", "create:placard", "create:polished_cut_andesite", "create:polished_cut_andesite_slab", "create:polished_cut_andesite_stairs", "create:polished_cut_andesite_wall", "create:polished_cut_asurine", "create:polished_cut_asurine_slab", "create:polished_cut_asurine_stairs", "create:polished_cut_asurine_wall", "create:polished_cut_calcite", "create:polished_cut_calcite_slab", "create:polished_cut_calcite_stairs", "create:polished_cut_calcite_wall", "create:polished_cut_crimsite", "create:polished_cut_crimsite_slab", "create:polished_cut_crimsite_stairs", "create:polished_cut_crimsite_wall", "create:polished_cut_deepslate", "create:polished_cut_deepslate_slab", "create:polished_cut_deepslate_stairs", "create:polished_cut_deepslate_wall", "create:polished_cut_diorite", "create:polished_cut_diorite_slab", "create:polished_cut_diorite_stairs", "create:polished_cut_diorite_wall", "create:polished_cut_dripstone", "create:polished_cut_dripstone_slab", "create:polished_cut_dripstone_stairs", "create:polished_cut_dripstone_wall", "create:polished_cut_granite", "create:polished_cut_granite_slab", "create:polished_cut_granite_stairs", "create:polished_cut_granite_wall", "create:polished_cut_limestone", "create:polished_cut_limestone_slab", "create:polished_cut_limestone_stairs", "create:polished_cut_limestone_wall", "create:polished_cut_ochrum", "create:polished_cut_ochrum_slab", "create:polished_cut_ochrum_stairs", "create:polished_cut_ochrum_wall", "create:polished_cut_scorchia", "create:polished_cut_scorchia_slab", "create:polished_cut_scorchia_stairs", "create:polished_cut_scorchia_wall", "create:polished_cut_scoria", "create:polished_cut_scoria_slab", "create:polished_cut_scoria_stairs", "create:polished_cut_scoria_wall", "create:polished_cut_tuff", "create:polished_cut_tuff_slab", "create:polished_cut_tuff_stairs", "create:polished_cut_tuff_wall", "create:polished_cut_veridium", "create:polished_cut_veridium_slab", "create:polished_cut_veridium_stairs", "create:polished_cut_veridium_wall", "create:polished_rose_quartz", "create:portable_fluid_interface", "create:portable_storage_interface", "create:potato_cannon", "create:powdered_obsidian", "create:powered_latch", "create:powered_toggle_latch", "create:precision_mechanism", "create:propeller", "create:pulse_extender", "create:pulse_repeater", "create:purple_seat", "create:purple_toolbox", "create:purple_valve_handle", "create:radial_chassis", "create:railway_casing", "create:raw_zinc", "create:raw_zinc_block", "create:red_sand_paper", "create:red_seat", "create:red_toolbox", "create:red_valve_handle", "create:redstone_contact", "create:redstone_link", "create:refined_radiance", "create:refined_radiance_casing", "create:rope_pulley", "create:rose_quartz", "create:rose_quartz_block", "create:rose_quartz_lamp", "create:rose_quartz_tiles", "create:rotation_speed_controller", "create:sail_frame", "create:sand_paper", "create:schedule", "create:schematic", "create:schematic_and_quill", "create:schematic_table", "create:schematic<PERSON><PERSON>", "create:scorchia", "create:scorchia_pillar", "create:scoria", "create:scoria_pillar", "create:secondary_linear_chassis", "create:sequenced_gearshift", "create:shadow_steel", "create:shadow_steel_casing", "create:shaft", "create:small_andesite_brick_slab", "create:small_andesite_brick_stairs", "create:small_andesite_brick_wall", "create:small_andesite_bricks", "create:small_asurine_brick_slab", "create:small_asurine_brick_stairs", "create:small_asurine_brick_wall", "create:small_asurine_bricks", "create:small_calcite_brick_slab", "create:small_calcite_brick_stairs", "create:small_calcite_brick_wall", "create:small_calcite_bricks", "create:small_crimsite_brick_slab", "create:small_crimsite_brick_stairs", "create:small_crimsite_brick_wall", "create:small_crimsite_bricks", "create:small_deepslate_brick_slab", "create:small_deepslate_brick_stairs", "create:small_deepslate_brick_wall", "create:small_deepslate_bricks", "create:small_diorite_brick_slab", "create:small_diorite_brick_stairs", "create:small_diorite_brick_wall", "create:small_diorite_bricks", "create:small_dripstone_brick_slab", "create:small_dripstone_brick_stairs", "create:small_dripstone_brick_wall", "create:small_dripstone_bricks", "create:small_granite_brick_slab", "create:small_granite_brick_stairs", "create:small_granite_brick_wall", "create:small_granite_bricks", "create:small_limestone_brick_slab", "create:small_limestone_brick_stairs", "create:small_limestone_brick_wall", "create:small_limestone_bricks", "create:small_ochrum_brick_slab", "create:small_ochrum_brick_stairs", "create:small_ochrum_brick_wall", "create:small_ochrum_bricks", "create:small_rose_quartz_tiles", "create:small_scorchia_brick_slab", "create:small_scorchia_brick_stairs", "create:small_scorchia_brick_wall", "create:small_scorchia_bricks", "create:small_scoria_brick_slab", "create:small_scoria_brick_stairs", "create:small_scoria_brick_wall", "create:small_scoria_bricks", "create:small_tuff_brick_slab", "create:small_tuff_brick_stairs", "create:small_tuff_brick_wall", "create:small_tuff_bricks", "create:small_veridium_brick_slab", "create:small_veridium_brick_stairs", "create:small_veridium_brick_wall", "create:small_veridium_bricks", "create:smart_chute", "create:smart_fluid_pipe", "create:speedometer", "create:spout", "create:spruce_window", "create:spruce_window_pane", "create:steam_engine", "create:steam_whistle", "create:sticker", "create:sticky_mechanical_piston", "create:stockpile_switch", "create:stressometer", "create:sturdy_sheet", "create:super_glue", "create:sweet_roll", "create:tiled_glass", "create:tiled_glass_pane", "create:track", "create:track_observer", "create:track_signal", "create:track_station", "create:train_door", "create:train_trapdoor", "create:tree_fertilizer", "create:tuff_pillar", "create:turntable", "create:unprocessed_obsidian_sheet", "create:veridium", "create:veridium_pillar", "create:vertical_framed_glass", "create:vertical_framed_glass_pane", "create:vertical_gearbox", "create:wand_of_symmetry", "create:warped_window", "create:warped_window_pane", "create:water_wheel", "create:waxed_copper_shingle_slab", "create:waxed_copper_shingle_stairs", "create:waxed_copper_shingles", "create:waxed_copper_tile_slab", "create:waxed_copper_tile_stairs", "create:waxed_copper_tiles", "create:waxed_exposed_copper_shingle_slab", "create:waxed_exposed_copper_shingle_stairs", "create:waxed_exposed_copper_shingles", "create:waxed_exposed_copper_tile_slab", "create:waxed_exposed_copper_tile_stairs", "create:waxed_exposed_copper_tiles", "create:waxed_oxidized_copper_shingle_slab", "create:waxed_oxidized_copper_shingle_stairs", "create:waxed_oxidized_copper_shingles", "create:waxed_oxidized_copper_tile_slab", "create:waxed_oxidized_copper_tile_stairs", "create:waxed_oxidized_copper_tiles", "create:waxed_weathered_copper_shingle_slab", "create:waxed_weathered_copper_shingle_stairs", "create:waxed_weathered_copper_shingles", "create:waxed_weathered_copper_tile_slab", "create:waxed_weathered_copper_tile_stairs", "create:waxed_weathered_copper_tiles", "create:weathered_copper_shingle_slab", "create:weathered_copper_shingle_stairs", "create:weathered_copper_shingles", "create:weathered_copper_tile_slab", "create:weathered_copper_tile_stairs", "create:weathered_copper_tiles", "create:weighted_ejector", "create:wheat_flour", "create:whisk", "create:white_sail", "create:white_seat", "create:white_toolbox", "create:white_valve_handle", "create:windmill_bearing", "create:wooden_bracket", "create:wrench", "create:yellow_seat", "create:yellow_toolbox", "create:yellow_valve_handle", "create:zinc_block", "create:zinc_ingot", "create:zinc_nugget", "create:zinc_ore", "createoreexcavation:diamond_drill", "createoreexcavation:drill", "createoreexcavation:drilling_machine", "createoreexcavation:extractor", "createoreexcavation:netherite_drill", "createoreexcavation:raw_diamond", "createoreexcavation:raw_emerald", "createoreexcavation:raw_redstone", "createoreexcavation:sample_drill", "createoreexcavation:vein_atlas", "createoreexcavation:vein_finder", "ftbquests:barrier", "ftbquests:book", "ftbquests:custom_icon", "ftbquests:detector", "ftbquests:loot_crate_opener", "ftbquests:lootcrate", "ftbquests:missing_item", "ftbquests:screen_1", "ftbquests:screen_3", "ftbquests:screen_5", "ftbquests:screen_7", "ftbquests:stage_barrier", "ftbquests:task_screen_configurator", "immersiveengineering:acetaldehyde_bucket", "immersiveengineering:advanced_blast_furnace", "immersiveengineering:alloy_smelter", "immersiveengineering:alloybrick", "immersiveengineering:alu_fence", "immersiveengineering:alu_post", "immersiveengineering:alu_scaffolding_grate_top", "immersiveengineering:alu_scaffolding_standard", "immersiveengineering:alu_scaffolding_wooden_top", "immersiveengineering:alu_slope", "immersiveengineering:alu_wallmount", "immersiveengineering:arc_furnace", "immersiveengineering:armor_faraday_boots", "immersiveengineering:armor_faraday_chestplate", "immersiveengineering:armor_faraday_helmet", "immersiveengineering:armor_faraday_leggings", "immersiveengineering:armor_piercing", "immersiveengineering:armor_steel_boots", "immersiveengineering:armor_steel_chestplate", "immersiveengineering:armor_steel_helmet", "immersiveengineering:armor_steel_leggings", "immersiveengineering:assembler", "immersiveengineering:auto_workbench", "immersiveengineering:axe_steel", "immersiveengineering:balloon", "immersiveengineering:bannerpattern_bevels", "immersiveengineering:banner<PERSON><PERSON>n_hammer", "immersiveengineering:bannerpattern_ornate", "immersiveengineering:bannerpattern_treated_wood", "immersiveengineering:bannerpattern_windmill", "immersiveengineering:banner<PERSON><PERSON><PERSON>_wolf", "immersiveengineering:banner<PERSON><PERSON><PERSON>_wolf_l", "immersiveengineering:banner<PERSON><PERSON><PERSON>_wolf_r", "immersiveengineering:biodiesel_bucket", "immersiveengineering:blast_furnace", "immersiveengineering:blastbrick", "immersiveengineering:blastbrick_reinforced", "immersiveengineering:blastfurnace_preheater", "immersiveengineering:blueprint", "immersiveengineering:bottling_machine", "immersiveengineering:breaker_switch", "immersiveengineering:bucket_wheel", "immersiveengineering:buckshot", "immersiveengineering:bulwark_spawn_egg", "immersiveengineering:buzzsaw", "immersiveengineering:capacitor_creative", "immersiveengineering:capacitor_hv", "immersiveengineering:capacitor_lv", "immersiveengineering:capacitor_mv", "immersiveengineering:casull", "immersiveengineering:charging_station", "immersiveengineering:chemthrower", "immersiveengineering:chute_aluminum", "immersiveengineering:chute_copper", "immersiveengineering:chute_iron", "immersiveengineering:chute_steel", "immersiveengineering:circuit_board", "immersiveengineering:circuit_table", "immersiveengineering:clinker_brick", "immersiveengineering:clinker_brick_quoin", "immersiveengineering:clinker_brick_sill", "immersiveengineering:cloche", "immersiveengineering:coal_coke", "immersiveengineering:coil_hv", "immersiveengineering:coil_lv", "immersiveengineering:coil_mv", "immersiveengineering:coke", "immersiveengineering:coke_oven", "immersiveengineering:cokebrick", "immersiveengineering:commando_spawn_egg", "immersiveengineering:component_electronic", "immersiveengineering:component_electronic_adv", "immersiveengineering:component_iron", "immersiveengineering:component_steel", "immersiveengineering:concrete", "immersiveengineering:concrete_brick", "immersiveengineering:concrete_brick_cracked", "immersiveengineering:concrete_bucket", "immersiveengineering:concrete_chiseled", "immersiveengineering:concrete_leaded", "immersiveengineering:concrete_pillar", "immersiveengineering:concrete_quarter", "immersiveengineering:concrete_sheet", "immersiveengineering:concrete_sprayed", "immersiveengineering:concrete_three_quarter", "immersiveengineering:concrete_tile", "immersiveengineering:connector_bundled", "immersiveengineering:connector_hv", "immersiveengineering:connector_hv_relay", "immersiveengineering:connector_lv", "immersiveengineering:connector_lv_relay", "immersiveengineering:connector_mv", "immersiveengineering:connector_mv_relay", "immersiveengineering:connector_probe", "immersiveengineering:connector_redstone", "immersiveengineering:connector_structural", "immersiveengineering:conveyor_basic", "immersiveengineering:conveyor_dropper", "immersiveengineering:conveyor_extract", "immersiveengineering:conveyor_redstone", "immersiveengineering:conveyor_splitter", "immersiveengineering:conveyor_vertical", "immersiveengineering:coresample", "immersiveengineering:craftingtable", "immersiveengineering:crate", "immersiveengineering:creosote_bucket", "immersiveengineering:crusher", "immersiveengineering:current_transformer", "immersiveengineering:cushion", "immersiveengineering:deepslate_ore_aluminum", "immersiveengineering:deepslate_ore_lead", "immersiveengineering:deepslate_ore_nickel", "immersiveengineering:deepslate_ore_silver", "immersiveengineering:deepslate_ore_uranium", "immersiveengineering:diesel_generator", "immersiveengineering:dragons_breath", "immersiveengineering:drill", "immersiveengineering:drillhead_iron", "immersiveengineering:drillhead_steel", "immersiveengineering:duroplast", "immersiveengineering:dust_aluminum", "immersiveengineering:dust_coke", "immersiveengineering:dust_constantan", "immersiveengineering:dust_copper", "immersiveengineering:dust_electrum", "immersiveengineering:dust_gold", "immersiveengineering:dust_hop_graphite", "immersiveengineering:dust_iron", "immersiveengineering:dust_lead", "immersiveengineering:dust_nickel", "immersiveengineering:dust_saltpeter", "immersiveengineering:dust_silver", "immersiveengineering:dust_steel", "immersiveengineering:dust_sulfur", "immersiveengineering:dust_uranium", "immersiveengineering:dust_wood", "immersiveengineering:dynamo", "immersiveengineering:earmuffs", "immersiveengineering:electric_lantern", "immersiveengineering:electromagnet", "immersiveengineering:electron_tube", "immersiveengineering:empty_casing", "immersiveengineering:empty_shell", "immersiveengineering:ersatz_leather", "immersiveengineering:ethanol_bucket", "immersiveengineering:excavator", "immersiveengineering:fake_icon_birthday", "immersiveengineering:fake_icon_bttf", "immersiveengineering:fake_icon_drillbreak", "immersiveengineering:fake_icon_fried", "immersiveengineering:fake_icon_lucky", "immersiveengineering:fake_icon_ravenholm", "immersiveengineering:feedthrough", "immersiveengineering:fermenter", "immersiveengineering:fertilizer", "immersiveengineering:fiberboard", "immersiveengineering:firework", "immersiveengineering:flare", "immersiveengineering:floodlight", "immersiveengineering:fluid_pipe", "immersiveengineering:fluid_placer", "immersiveengineering:fluid_pump", "immersiveengineering:fluid_sorter", "immersiveengineering:fluorescent_tube", "immersiveengineering:furnace_heater", "immersiveengineering:fusilier_spawn_egg", "immersiveengineering:generator", "immersiveengineering:glider", "immersiveengineering:graphite_electrode", "immersiveengineering:grindingdisk", "immersiveengineering:grit_sand", "immersiveengineering:gunpart_barrel", "immersiveengineering:gunpart_drum", "immersiveengineering:gunpart_hammer", "immersiveengineering:gunpowder_barrel", "immersiveengineering:hammer", "immersiveengineering:he", "immersiveengineering:heavy_engineering", "immersiveengineering:hemp_fabric", "immersiveengineering:hemp_fiber", "immersiveengineering:hempcrete", "immersiveengineering:hempcrete_brick", "immersiveengineering:hempcrete_brick_cracked", "immersiveengineering:hempcrete_chiseled", "immersiveengineering:hempcrete_pillar", "immersiveengineering:herbicide_bucket", "immersiveengineering:hoe_steel", "immersiveengineering:homing", "immersiveengineering:ingot_aluminum", "immersiveengineering:ingot_constantan", "immersiveengineering:ingot_electrum", "immersiveengineering:ingot_hop_graphite", "immersiveengineering:ingot_lead", "immersiveengineering:ingot_nickel", "immersiveengineering:ingot_silver", "immersiveengineering:ingot_steel", "immersiveengineering:ingot_uranium", "immersiveengineering:insulating_glass", "immersiveengineering:item_batcher", "immersiveengineering:jerrycan", "immersiveengineering:lantern", "immersiveengineering:light_bulb", "immersiveengineering:light_engineering", "immersiveengineering:lightning_rod", "immersiveengineering:logic_circuit", "immersiveengineering:logic_unit", "immersiveengineering:maintenance_kit", "immersiveengineering:manual", "immersiveengineering:metal_barrel", "immersiveengineering:metal_ladder_alu", "immersiveengineering:metal_ladder_none", "immersiveengineering:metal_ladder_steel", "immersiveengineering:metal_press", "immersiveengineering:minecart_metalbarrel", "immersiveengineering:minecart_reinforcedcrate", "immersiveengineering:minecart_woodenbarrel", "immersiveengineering:minecart_woodencrate", "immersiveengineering:mixer", "immersiveengineering:mold_bullet_casing", "immersiveengineering:mold_gear", "immersiveengineering:mold_packing_4", "immersiveengineering:mold_packing_9", "immersiveengineering:mold_plate", "immersiveengineering:mold_rod", "immersiveengineering:mold_unpacking", "immersiveengineering:mold_wire", "immersiveengineering:nugget_aluminum", "immersiveengineering:nugget_constantan", "immersiveengineering:nugget_copper", "immersiveengineering:nugget_electrum", "immersiveengineering:nugget_lead", "immersiveengineering:nugget_nickel", "immersiveengineering:nugget_silver", "immersiveengineering:nugget_steel", "immersiveengineering:nugget_uranium", "immersiveengineering:ore_aluminum", "immersiveengineering:ore_lead", "immersiveengineering:ore_nickel", "immersiveengineering:ore_silver", "immersiveengineering:ore_uranium", "immersiveengineering:phenolic_resin_bucket", "immersiveengineering:pickaxe_steel", "immersiveengineering:plantoil_bucket", "immersiveengineering:plate_aluminum", "immersiveengineering:plate_constantan", "immersiveengineering:plate_copper", "immersiveengineering:plate_duroplast", "immersiveengineering:plate_electrum", "immersiveengineering:plate_gold", "immersiveengineering:plate_iron", "immersiveengineering:plate_lead", "immersiveengineering:plate_nickel", "immersiveengineering:plate_silver", "immersiveengineering:plate_steel", "immersiveengineering:plate_uranium", "immersiveengineering:potion", "immersiveengineering:potion_bucket", "immersiveengineering:powerpack", "immersiveengineering:radiator", "immersiveengineering:railgun", "immersiveengineering:raw_aluminum", "immersiveengineering:raw_block_aluminum", "immersiveengineering:raw_block_lead", "immersiveengineering:raw_block_nickel", "immersiveengineering:raw_block_silver", "immersiveengineering:raw_block_uranium", "immersiveengineering:raw_lead", "immersiveengineering:raw_nickel", "immersiveengineering:raw_silver", "immersiveengineering:raw_uranium", "immersiveengineering:razor_wire", "immersiveengineering:redstone_acid_bucket", "immersiveengineering:redstone_breaker", "immersiveengineering:refinery", "immersiveengineering:reinforced_crate", "immersiveengineering:revolver", "immersiveengineering:rockcutter", "immersiveengineering:rs_engineering", "immersiveengineering:sample_drill", "immersiveengineering:sawblade", "immersiveengineering:sawdust", "immersiveengineering:sawmill", "immersiveengineering:screwdriver", "immersiveengineering:seed", "immersiveengineering:shader", "immersiveengineering:shader_bag_common", "immersiveengineering:shader_bag_epic", "immersiveengineering:shader_bag_ie_masterwork", "immersiveengineering:shader_bag_rare", "immersiveengineering:shader_bag_uncommon", "immersiveengineering:sheetmetal_aluminum", "immersiveengineering:sheetmetal_colored_black", "immersiveengineering:sheetmetal_colored_blue", "immersiveengineering:sheetmetal_colored_brown", "immersiveengineering:sheetmetal_colored_cyan", "immersiveengineering:sheetmetal_colored_gray", "immersiveengineering:sheetmetal_colored_green", "immersiveengineering:sheetmetal_colored_light_blue", "immersiveengineering:sheetmetal_colored_light_gray", "immersiveengineering:sheetmetal_colored_lime", "immersiveengineering:sheetmetal_colored_magenta", "immersiveengineering:sheetmetal_colored_orange", "immersiveengineering:sheetmetal_colored_pink", "immersiveengineering:sheetmetal_colored_purple", "immersiveengineering:sheetmetal_colored_red", "immersiveengineering:sheetmetal_colored_white", "immersiveengineering:sheetmetal_colored_yellow", "immersiveengineering:sheetmetal_constantan", "immersiveengineering:sheetmetal_copper", "immersiveengineering:sheetmetal_electrum", "immersiveengineering:sheetmetal_gold", "immersiveengineering:sheetmetal_iron", "immersiveengineering:sheetmetal_lead", "immersiveengineering:sheetmetal_nickel", "immersiveengineering:sheetmetal_silver", "immersiveengineering:sheetmetal_steel", "immersiveengineering:sheetmetal_uranium", "immersiveengineering:shield", "immersiveengineering:shovel_steel", "immersiveengineering:silo", "immersiveengineering:silver", "immersiveengineering:skyhook", "immersiveengineering:slab_alloybrick", "immersiveengineering:slab_alu_scaffolding_grate_top", "immersiveengineering:slab_alu_scaffolding_standard", "immersiveengineering:slab_alu_scaffolding_wooden_top", "immersiveengineering:slab_blastbrick", "immersiveengineering:slab_blastbrick_reinforced", "immersiveengineering:slab_clinker_brick", "immersiveengineering:slab_coke", "immersiveengineering:slab_cokebrick", "immersiveengineering:slab_concrete", "immersiveengineering:slab_concrete_brick", "immersiveengineering:slab_concrete_leaded", "immersiveengineering:slab_concrete_tile", "immersiveengineering:slab_hempcrete", "immersiveengineering:slab_hempcrete_brick", "immersiveengineering:slab_insulating_glass", "immersiveengineering:slab_sheetmetal_aluminum", "immersiveengineering:slab_sheetmetal_colored_black", "immersiveengineering:slab_sheetmetal_colored_blue", "immersiveengineering:slab_sheetmetal_colored_brown", "immersiveengineering:slab_sheetmetal_colored_cyan", "immersiveengineering:slab_sheetmetal_colored_gray", "immersiveengineering:slab_sheetmetal_colored_green", "immersiveengineering:slab_sheetmetal_colored_light_blue", "immersiveengineering:slab_sheetmetal_colored_light_gray", "immersiveengineering:slab_sheetmetal_colored_lime", "immersiveengineering:slab_sheetmetal_colored_magenta", "immersiveengineering:slab_sheetmetal_colored_orange", "immersiveengineering:slab_sheetmetal_colored_pink", "immersiveengineering:slab_sheetmetal_colored_purple", "immersiveengineering:slab_sheetmetal_colored_red", "immersiveengineering:slab_sheetmetal_colored_white", "immersiveengineering:slab_sheetmetal_colored_yellow", "immersiveengineering:slab_sheetmetal_constantan", "immersiveengineering:slab_sheetmetal_copper", "immersiveengineering:slab_sheetmetal_electrum", "immersiveengineering:slab_sheetmetal_gold", "immersiveengineering:slab_sheetmetal_iron", "immersiveengineering:slab_sheetmetal_lead", "immersiveengineering:slab_sheetmetal_nickel", "immersiveengineering:slab_sheetmetal_silver", "immersiveengineering:slab_sheetmetal_steel", "immersiveengineering:slab_sheetmetal_uranium", "immersiveengineering:slab_slag_brick", "immersiveengineering:slab_steel_scaffolding_grate_top", "immersiveengineering:slab_steel_scaffolding_standard", "immersiveengineering:slab_steel_scaffolding_wooden_top", "immersiveengineering:slab_storage_aluminum", "immersiveengineering:slab_storage_constantan", "immersiveengineering:slab_storage_electrum", "immersiveengineering:slab_storage_lead", "immersiveengineering:slab_storage_nickel", "immersiveengineering:slab_storage_silver", "immersiveengineering:slab_storage_steel", "immersiveengineering:slab_storage_uranium", "immersiveengineering:slab_treated_wood_horizontal", "immersiveengineering:slab_treated_wood_packaged", "immersiveengineering:slab_treated_wood_vertical", "immersiveengineering:slag", "immersiveengineering:slag_brick", "immersiveengineering:slag_glass", "immersiveengineering:slag_gravel", "immersiveengineering:sorter", "immersiveengineering:speedloader", "immersiveengineering:squeezer", "immersiveengineering:stairs_alu_scaffolding_grate_top", "immersiveengineering:stairs_alu_scaffolding_standard", "immersiveengineering:stairs_alu_scaffolding_wooden_top", "immersiveengineering:stairs_clinker_brick", "immersiveengineering:stairs_concrete", "immersiveengineering:stairs_concrete_brick", "immersiveengineering:stairs_concrete_leaded", "immersiveengineering:stairs_concrete_tile", "immersiveengineering:stairs_hempcrete", "immersiveengineering:stairs_hempcrete_brick", "immersiveengineering:stairs_slag_brick", "immersiveengineering:stairs_steel_scaffolding_grate_top", "immersiveengineering:stairs_steel_scaffolding_standard", "immersiveengineering:stairs_steel_scaffolding_wooden_top", "immersiveengineering:stairs_treated_wood_horizontal", "immersiveengineering:stairs_treated_wood_packaged", "immersiveengineering:stairs_treated_wood_vertical", "immersiveengineering:steel_fence", "immersiveengineering:steel_post", "immersiveengineering:steel_scaffolding_grate_top", "immersiveengineering:steel_scaffolding_standard", "immersiveengineering:steel_scaffolding_wooden_top", "immersiveengineering:steel_slope", "immersiveengineering:steel_wallmount", "immersiveengineering:stick_aluminum", "immersiveengineering:stick_iron", "immersiveengineering:stick_steel", "immersiveengineering:stick_treated", "immersiveengineering:storage_aluminum", "immersiveengineering:storage_constantan", "immersiveengineering:storage_electrum", "immersiveengineering:storage_lead", "immersiveengineering:storage_nickel", "immersiveengineering:storage_silver", "immersiveengineering:storage_steel", "immersiveengineering:storage_uranium", "immersiveengineering:strip_curtain", "immersiveengineering:survey_tools", "immersiveengineering:sword_steel", "immersiveengineering:tank", "immersiveengineering:tesla_coil", "immersiveengineering:thermoelectric_generator", "immersiveengineering:toolbox", "immersiveengineering:toolupgrade_buzzsaw_spareblades", "immersiveengineering:toolupgrade_chemthrower_focus", "immersiveengineering:toolupgrade_chemthrower_multitank", "immersiveengineering:toolupgrade_drill_capacity", "immersiveengineering:toolupgrade_drill_damage", "immersiveengineering:toolupgrade_drill_fortune", "immersiveengineering:toolupgrade_drill_lube", "immersiveengineering:toolupgrade_drill_waterproof", "immersiveengineering:toolupgrade_powerpack_antenna", "immersiveengineering:toolupgrade_powerpack_induction", "immersiveengineering:toolupgrade_powerpack_magnet", "immersiveengineering:toolupgrade_powerpack_tesla", "immersiveengineering:toolupgrade_railgun_capacitors", "immersiveengineering:toolupgrade_railgun_scope", "immersiveengineering:toolupgrade_revolver_bayonet", "immersiveengineering:toolupgrade_revolver_electro", "immersiveengineering:toolupgrade_revolver_magazine", "immersiveengineering:toolupgrade_shield_flash", "immersiveengineering:toolupgrade_shield_magnet", "immersiveengineering:toolupgrade_shield_shock", "immersiveengineering:transformer", "immersiveengineering:transformer_hv", "immersiveengineering:treated_fence", "immersiveengineering:treated_post", "immersiveengineering:treated_scaffold", "immersiveengineering:treated_wallmount", "immersiveengineering:treated_wood_horizontal", "immersiveengineering:treated_wood_packaged", "immersiveengineering:treated_wood_vertical", "immersiveengineering:turntable", "immersiveengineering:turret_chem", "immersiveengineering:turret_gun", "immersiveengineering:voltmeter", "immersiveengineering:wall_clinker_brick", "immersiveengineering:wall_slag_brick", "immersiveengineering:watermill", "immersiveengineering:waterwheel_segment", "immersiveengineering:windmill", "immersiveengineering:windmill_blade", "immersiveengineering:windmill_sail", "immersiveengineering:wire_aluminum", "immersiveengineering:wire_copper", "immersiveengineering:wire_electrum", "immersiveengineering:wire_lead", "immersiveengineering:wire_steel", "immersiveengineering:wirecoil_copper", "immersiveengineering:wirecoil_copper_ins", "immersiveengineering:wirecoil_electrum", "immersiveengineering:wirecoil_electrum_ins", "immersiveengineering:wirecoil_redstone", "immersiveengineering:wirecoil_steel", "immersiveengineering:wirecoil_structure_rope", "immersiveengineering:wirecoil_structure_steel", "immersiveengineering:wirecutter", "immersiveengineering:wolfpack", "immersiveengineering:wooden_barrel", "immersiveengineering:wooden_grip", "immersiveengineering:workbench", "iron_making_furnace:iron_making_furnace_brick", "mekanism:advanced_bin", "mekanism:advanced_chemical_tank", "mekanism:advanced_combining_factory", "mekanism:advanced_compressing_factory", "mekanism:advanced_control_circuit", "mekanism:advanced_crushing_factory", "mekanism:advanced_energy_cube", "mekanism:advanced_enriching_factory", "mekanism:advanced_fluid_tank", "mekanism:advanced_induction_cell", "mekanism:advanced_induction_provider", "mekanism:advanced_infusing_factory", "mekanism:advanced_injecting_factory", "mekanism:advanced_logistical_transporter", "mekanism:advanced_mechanical_pipe", "mekanism:advanced_pressurized_tube", "mekanism:advanced_purifying_factory", "mekanism:advanced_sawing_factory", "mekanism:advanced_smelting_factory", "mekanism:advanced_thermodynamic_conductor", "mekanism:advanced_tier_installer", "mekanism:advanced_universal_cable", "mekanism:alloy_atomic", "mekanism:alloy_infused", "mekanism:alloy_reinforced", "mekanism:antiprotonic_nucleosynthesizer", "mekanism:atomic_disassembler", "mekanism:basic_bin", "mekanism:basic_chemical_tank", "mekanism:basic_combining_factory", "mekanism:basic_compressing_factory", "mekanism:basic_control_circuit", "mekanism:basic_crushing_factory", "mekanism:basic_energy_cube", "mekanism:basic_enriching_factory", "mekanism:basic_fluid_tank", "mekanism:basic_induction_cell", "mekanism:basic_induction_provider", "mekanism:basic_infusing_factory", "mekanism:basic_injecting_factory", "mekanism:basic_logistical_transporter", "mekanism:basic_mechanical_pipe", "mekanism:basic_pressurized_tube", "mekanism:basic_purifying_factory", "mekanism:basic_sawing_factory", "mekanism:basic_smelting_factory", "mekanism:basic_thermodynamic_conductor", "mekanism:basic_tier_installer", "mekanism:basic_universal_cable", "mekanism:bio_fuel", "mekanism:block_bronze", "mekanism:block_charcoal", "mekanism:block_fluorite", "mekanism:block_lead", "mekanism:block_osmium", "mekanism:block_raw_lead", "mekanism:block_raw_osmium", "mekanism:block_raw_tin", "mekanism:block_raw_uranium", "mekanism:block_refined_glowstone", "mekanism:block_refined_obsidian", "mekanism:block_salt", "mekanism:block_steel", "mekanism:block_tin", "mekanism:block_uranium", "mekanism:boiler_casing", "mekanism:boiler_valve", "mekanism:bounding_block", "mekanism:brine_bucket", "mekanism:canteen", "mekanism:cardboard_box", "mekanism:chargepad", "mekanism:chemical_crystallizer", "mekanism:chemical_dissolution_chamber", "mekanism:chemical_infuser", "mekanism:chemical_injection_chamber", "mekanism:chemical_oxidizer", "mekanism:chemical_washer", "mekanism:chlorine_bucket", "mekanism:clump_copper", "mekanism:clump_gold", "mekanism:clump_iron", "mekanism:clump_lead", "mekanism:clump_osmium", "mekanism:clump_tin", "mekanism:clump_uranium", "mekanism:combiner", "mekanism:configuration_card", "mekanism:configurator", "mekanism:crafting_formula", "mekanism:creative_bin", "mekanism:creative_chemical_tank", "mekanism:creative_energy_cube", "mekanism:creative_fluid_tank", "mekanism:crusher", "mekanism:crystal_copper", "mekanism:crystal_gold", "mekanism:crystal_iron", "mekanism:crystal_lead", "mekanism:crystal_osmium", "mekanism:crystal_tin", "mekanism:crystal_uranium", "mekanism:deepslate_fluorite_ore", "mekanism:deepslate_lead_ore", "mekanism:deepslate_osmium_ore", "mekanism:deepslate_tin_ore", "mekanism:deepslate_uranium_ore", "mekanism:dictionary", "mekanism:digital_miner", "mekanism:dimensional_stabilizer", "mekanism:dirty_dust_copper", "mekanism:dirty_dust_gold", "mekanism:dirty_dust_iron", "mekanism:dirty_dust_lead", "mekanism:dirty_dust_osmium", "mekanism:dirty_dust_tin", "mekanism:dirty_dust_uranium", "mekanism:dirty_netherite_scrap", "mekanism:diversion_transporter", "mekanism:dosimeter", "mekanism:dust_bronze", "mekanism:dust_charcoal", "mekanism:dust_coal", "mekanism:dust_copper", "mekanism:dust_diamond", "mekanism:dust_emerald", "mekanism:dust_fluorite", "mekanism:dust_gold", "mekanism:dust_iron", "mekanism:dust_lapis_lazuli", "mekanism:dust_lead", "mekanism:dust_lithium", "mekanism:dust_netherite", "mekanism:dust_obsidian", "mekanism:dust_osmium", "mekanism:dust_quartz", "mekanism:dust_refined_obsidian", "mekanism:dust_steel", "mekanism:dust_sulfur", "mekanism:dust_tin", "mekanism:dust_uranium", "mekanism:dye_base", "mekanism:dynamic_tank", "mekanism:dynamic_valve", "mekanism:electric_bow", "mekanism:electric_pump", "mekanism:electrolytic_core", "mekanism:electrolytic_separator", "mekanism:elite_bin", "mekanism:elite_chemical_tank", "mekanism:elite_combining_factory", "mekanism:elite_compressing_factory", "mekanism:elite_control_circuit", "mekanism:elite_crushing_factory", "mekanism:elite_energy_cube", "mekanism:elite_enriching_factory", "mekanism:elite_fluid_tank", "mekanism:elite_induction_cell", "mekanism:elite_induction_provider", "mekanism:elite_infusing_factory", "mekanism:elite_injecting_factory", "mekanism:elite_logistical_transporter", "mekanism:elite_mechanical_pipe", "mekanism:elite_pressurized_tube", "mekanism:elite_purifying_factory", "mekanism:elite_sawing_factory", "mekanism:elite_smelting_factory", "mekanism:elite_thermodynamic_conductor", "mekanism:elite_tier_installer", "mekanism:elite_universal_cable", "mekanism:energized_smelter", "mekanism:energy_tablet", "mekanism:enriched_carbon", "mekanism:enriched_diamond", "mekanism:enriched_gold", "mekanism:enriched_iron", "mekanism:enriched_redstone", "mekanism:enriched_refined_obsidian", "mekanism:enriched_tin", "mekanism:enrichment_chamber", "mekanism:ethene_bucket", "mekanism:flamethrower", "mekanism:fluidic_plenisher", "mekanism:fluorite_gem", "mekanism:fluorite_ore", "mekanism:formulaic_assemblicator", "mekanism:free_runners", "mekanism:free_runners_armored", "mekanism:fuelwood_heater", "mekanism:gauge_dropper", "mekanism:geiger_counter", "mekanism:hazmat_boots", "mekanism:hazmat_gown", "mekanism:hazmat_mask", "mekanism:hazmat_pants", "mekanism:hdpe_elytra", "mekanism:hdpe_pellet", "mekanism:hdpe_rod", "mekanism:hdpe_sheet", "mekanism:hdpe_stick", "mekanism:heavy_water_bucket", "mekanism:hydrofluoric_acid_bucket", "mekanism:hydrogen_bucket", "mekanism:hydrogen_chloride_bucket", "mekanism:induction_casing", "mekanism:induction_port", "mekanism:industrial_alarm", "mekanism:ingot_bronze", "mekanism:ingot_lead", "mekanism:ingot_osmium", "mekanism:ingot_refined_glowstone", "mekanism:ingot_refined_obsidian", "mekanism:ingot_steel", "mekanism:ingot_tin", "mekanism:ingot_uranium", "mekanism:isotopic_centrifuge", "mekanism:jetpack", "mekanism:jetpack_armored", "mekanism:laser", "mekanism:laser_amplifier", "mekanism:laser_tractor_beam", "mekanism:lead_ore", "mekanism:lithium_bucket", "mekanism:logistical_sorter", "mekanism:meka_tool", "mekanism:mekas<PERSON>_bodyarmor", "mekanism:mekasuit_boots", "mekanism:mekasuit_helmet", "mekanism:mekasuit_pants", "mekanism:metallurgic_infuser", "mekanism:modification_station", "mekanism:module_attack_amplification_unit", "mekanism:module_base", "mekanism:module_blasting_unit", "mekanism:module_charge_distribution_unit", "mekanism:module_color_modulation_unit", "mekanism:module_dosimeter_unit", "mekanism:module_electrolytic_breathing_unit", "mekanism:module_elytra_unit", "mekanism:module_energy_unit", "mekanism:module_excavation_escalation_unit", "mekanism:module_farming_unit", "mekanism:module_fortune_unit", "mekanism:module_frost_walker_unit", "mekanism:module_geiger_unit", "mekanism:module_gravitational_modulating_unit", "mekanism:module_gyroscopic_stabilization_unit", "mekanism:module_hydraulic_propulsion_unit", "mekanism:module_hydrostatic_repulsor_unit", "mekanism:module_inhalation_purification_unit", "mekanism:module_jetpack_unit", "mekanism:module_laser_dissipation_unit", "mekanism:module_locomotive_boosting_unit", "mekanism:module_magnetic_attraction_unit", "mekanism:module_motorized_servo_unit", "mekanism:module_nutritional_injection_unit", "mekanism:module_radiation_shielding_unit", "mekanism:module_shearing_unit", "mekanism:module_silk_touch_unit", "mekanism:module_teleportation_unit", "mekanism:module_vein_mining_unit", "mekanism:module_vision_enhancement_unit", "mekanism:network_reader", "mekanism:nugget_bronze", "mekanism:nugget_lead", "mekanism:nugget_osmium", "mekanism:nugget_refined_glowstone", "mekanism:nugget_refined_obsidian", "mekanism:nugget_steel", "mekanism:nugget_tin", "mekanism:nugget_uranium", "mekanism:nutritional_liquifier", "mekanism:nutritional_paste_bucket", "mekanism:oredictionificator", "mekanism:osmium_compressor", "mekanism:osmium_ore", "mekanism:oxygen_bucket", "mekanism:painting_machine", "mekanism:pellet_antimatter", "mekanism:pellet_plutonium", "mekanism:pellet_polonium", "mekanism:personal_barrel", "mekanism:personal_chest", "mekanism:pigment_extractor", "mekanism:pigment_mixer", "mekanism:portable_qio_dashboard", "mekanism:portable_teleporter", "mekanism:precision_sawmill", "mekanism:pressure_disperser", "mekanism:pressurized_reaction_chamber", "mekanism:purification_chamber", "mekanism:qio_dashboard", "mekanism:qio_drive_array", "mekanism:qio_drive_base", "mekanism:qio_drive_hyper_dense", "mekanism:qio_drive_supermassive", "mekanism:qio_drive_time_dilating", "mekanism:qio_exporter", "mekanism:qio_importer", "mekanism:qio_redstone_adapter", "mekanism:quantum_entangloporter", "mekanism:radioactive_waste_barrel", "mekanism:raw_lead", "mekanism:raw_osmium", "mekanism:raw_tin", "mekanism:raw_uranium", "mekanism:reprocessed_fissile_fragment", "mekanism:resistive_heater", "mekanism:restrictive_transporter", "mekanism:robit", "mekanism:rotary_condensentrator", "mekanism:salt", "mekanism:sawdust", "mekanism:scuba_mask", "mekanism:scuba_tank", "mekanism:security_desk", "mekanism:seismic_reader", "mekanism:seismic_vibrator", "mekanism:shard_copper", "mekanism:shard_gold", "mekanism:shard_iron", "mekanism:shard_lead", "mekanism:shard_osmium", "mekanism:shard_tin", "mekanism:shard_uranium", "mekanism:sodium_bucket", "mekanism:solar_neutron_activator", "mekanism:sps_casing", "mekanism:sps_port", "mekanism:steam_bucket", "mekanism:steel_casing", "mekanism:structural_glass", "mekanism:substrate", "mekanism:sulfur_dioxide_bucket", "mekanism:sulfur_trioxide_bucket", "mekanism:sulfuric_acid_bucket", "mekanism:supercharged_coil", "mekanism:superheated_sodium_bucket", "mekanism:superheating_element", "mekanism:teleportation_core", "mekanism:teleporter", "mekanism:teleporter_frame", "mekanism:thermal_evaporation_block", "mekanism:thermal_evaporation_controller", "mekanism:thermal_evaporation_valve", "mekanism:tin_ore", "mekanism:ultimate_bin", "mekanism:ultimate_chemical_tank", "mekanism:ultimate_combining_factory", "mekanism:ultimate_compressing_factory", "mekanism:ultimate_control_circuit", "mekanism:ultimate_crushing_factory", "mekanism:ultimate_energy_cube", "mekanism:ultimate_enriching_factory", "mekanism:ultimate_fluid_tank", "mekanism:ultimate_induction_cell", "mekanism:ultimate_induction_provider", "mekanism:ultimate_infusing_factory", "mekanism:ultimate_injecting_factory", "mekanism:ultimate_logistical_transporter", "mekanism:ultimate_mechanical_pipe", "mekanism:ultimate_pressurized_tube", "mekanism:ultimate_purifying_factory", "mekanism:ultimate_sawing_factory", "mekanism:ultimate_smelting_factory", "mekanism:ultimate_thermodynamic_conductor", "mekanism:ultimate_tier_installer", "mekanism:ultimate_universal_cable", "mekanism:upgrade_anchor", "mekanism:upgrade_energy", "mekanism:upgrade_filter", "mekanism:upgrade_gas", "mekanism:upgrade_muffling", "mekanism:upgrade_speed", "mekanism:upgrade_stone_generator", "mekanism:uranium_hexafluoride_bucket", "mekanism:uranium_ore", "mekanism:uranium_oxide_bucket", "mekanism:yellow_cake_uranium", "minecraft:acacia_boat", "minecraft:acacia_button", "minecraft:acacia_chest_boat", "minecraft:acacia_door", "minecraft:acacia_fence", "minecraft:acacia_fence_gate", "minecraft:acacia_hanging_sign", "minecraft:acacia_leaves", "minecraft:acacia_log", "minecraft:acacia_planks", "minecraft:acacia_pressure_plate", "minecraft:acacia_sapling", "minecraft:acacia_sign", "minecraft:acacia_slab", "minecraft:acacia_stairs", "minecraft:acacia_trapdoor", "minecraft:acacia_wood", "minecraft:activator_rail", "minecraft:air", "minecraft:allay_spawn_egg", "minecraft:allium", "minecraft:amethyst_block", "minecraft:amethyst_cluster", "minecraft:amethyst_shard", "minecraft:ancient_debris", "minecraft:andesite", "minecraft:andesite_slab", "minecraft:andesite_stairs", "minecraft:andesite_wall", "minecraft:angler_pottery_sherd", "minecraft:anvil", "minecraft:apple", "minecraft:archer_pottery_sherd", "minecraft:armor_stand", "minecraft:arms_up_pottery_sherd", "minecraft:arrow", "minecraft:axolotl_bucket", "minecraft:axolotl_spawn_egg", "minecraft:azalea", "minecraft:azalea_leaves", "minecraft:azure_bluet", "minecraft:baked_potato", "minecraft:bamboo", "minecraft:bamboo_block", "minecraft:bamboo_button", "minecraft:bamboo_chest_raft", "minecraft:bamboo_door", "minecraft:bamboo_fence", "minecraft:bamboo_fence_gate", "minecraft:bamboo_hanging_sign", "minecraft:bamboo_mosaic", "minecraft:bamboo_mosaic_slab", "minecraft:bamboo_mosaic_stairs", "minecraft:bamboo_planks", "minecraft:bamboo_pressure_plate", "minecraft:bamboo_raft", "minecraft:bamboo_sign", "minecraft:bamboo_slab", "minecraft:bamboo_stairs", "minecraft:bamboo_trapdoor", "minecraft:barrel", "minecraft:barrier", "minecraft:basalt", "minecraft:bat_spawn_egg", "minecraft:beacon", "minecraft:bedrock", "minecraft:bee_nest", "minecraft:bee_spawn_egg", "minecraft:beef", "minecraft:beehive", "minecraft:beetroot", "minecraft:beetroot_seeds", "minecraft:beetroot_soup", "minecraft:bell", "minecraft:big_dripleaf", "minecraft:birch_boat", "minecraft:birch_button", "minecraft:birch_chest_boat", "minecraft:birch_door", "minecraft:birch_fence", "minecraft:birch_fence_gate", "minecraft:birch_hanging_sign", "minecraft:birch_leaves", "minecraft:birch_log", "minecraft:birch_planks", "minecraft:birch_pressure_plate", "minecraft:birch_sapling", "minecraft:birch_sign", "minecraft:birch_slab", "minecraft:birch_stairs", "minecraft:birch_trapdoor", "minecraft:birch_wood", "minecraft:black_banner", "minecraft:black_bed", "minecraft:black_candle", "minecraft:black_carpet", "minecraft:black_concrete", "minecraft:black_concrete_powder", "minecraft:black_dye", "minecraft:black_glazed_terracotta", "minecraft:black_shulker_box", "minecraft:black_stained_glass", "minecraft:black_stained_glass_pane", "minecraft:black_terracotta", "minecraft:black_wool", "minecraft:blackstone", "minecraft:blackstone_slab", "minecraft:blackstone_stairs", "minecraft:blackstone_wall", "minecraft:blade_pottery_sherd", "minecraft:blast_furnace", "minecraft:blaze_powder", "minecraft:blaze_rod", "minecraft:blaze_spawn_egg", "minecraft:blue_banner", "minecraft:blue_bed", "minecraft:blue_candle", "minecraft:blue_carpet", "minecraft:blue_concrete", "minecraft:blue_concrete_powder", "minecraft:blue_dye", "minecraft:blue_glazed_terracotta", "minecraft:blue_ice", "minecraft:blue_orchid", "minecraft:blue_shulker_box", "minecraft:blue_stained_glass", "minecraft:blue_stained_glass_pane", "minecraft:blue_terracotta", "minecraft:blue_wool", "minecraft:bone", "minecraft:bone_block", "minecraft:bone_meal", "minecraft:book", "minecraft:bookshelf", "minecraft:bow", "minecraft:bowl", "minecraft:brain_coral", "minecraft:brain_coral_block", "minecraft:brain_coral_fan", "minecraft:bread", "minecraft:brewer_pottery_sherd", "minecraft:brewing_stand", "minecraft:brick", "minecraft:brick_slab", "minecraft:brick_stairs", "minecraft:brick_wall", "minecraft:bricks", "minecraft:brown_banner", "minecraft:brown_bed", "minecraft:brown_candle", "minecraft:brown_carpet", "minecraft:brown_concrete", "minecraft:brown_concrete_powder", "minecraft:brown_dye", "minecraft:brown_glazed_terracotta", "minecraft:brown_mushroom", "minecraft:brown_mushroom_block", "minecraft:brown_shulker_box", "minecraft:brown_stained_glass", "minecraft:brown_stained_glass_pane", "minecraft:brown_terracotta", "minecraft:brown_wool", "minecraft:brush", "minecraft:bubble_coral", "minecraft:bubble_coral_block", "minecraft:bubble_coral_fan", "minecraft:bucket", "minecraft:budding_amethyst", "minecraft:bundle", "minecraft:burn_pottery_sherd", "minecraft:cactus", "minecraft:cake", "minecraft:calcite", "minecraft:calibrated_sculk_sensor", "minecraft:camel_spawn_egg", "minecraft:campfire", "minecraft:candle", "minecraft:carrot", "minecraft:carrot_on_a_stick", "minecraft:cartography_table", "minecraft:carved_pumpkin", "minecraft:cat_spawn_egg", "minecraft:cauldron", "minecraft:cave_spider_spawn_egg", "minecraft:chain", "minecraft:chain_command_block", "minecraft:chainmail_boots", "minecraft:chainmail_chestplate", "minecraft:chainmail_helmet", "minecraft:chainmail_leggings", "minecraft:charcoal", "minecraft:cherry_boat", "minecraft:cherry_button", "minecraft:cherry_chest_boat", "minecraft:cherry_door", "minecraft:cherry_fence", "minecraft:cherry_fence_gate", "minecraft:cherry_hanging_sign", "minecraft:cherry_leaves", "minecraft:cherry_log", "minecraft:cherry_planks", "minecraft:cherry_pressure_plate", "minecraft:cherry_sapling", "minecraft:cherry_sign", "minecraft:cherry_slab", "minecraft:cherry_stairs", "minecraft:cherry_trapdoor", "minecraft:cherry_wood", "minecraft:chest", "minecraft:chest_minecart", "minecraft:chicken", "minecraft:chicken_spawn_egg", "minecraft:chipped_anvil", "minecraft:chiseled_bookshelf", "minecraft:chiseled_deepslate", "minecraft:chiseled_nether_bricks", "minecraft:chiseled_polished_blackstone", "minecraft:chiseled_quartz_block", "minecraft:chiseled_red_sandstone", "minecraft:chiseled_sandstone", "minecraft:chiseled_stone_bricks", "minecraft:chorus_flower", "minecraft:chorus_fruit", "minecraft:chorus_plant", "minecraft:clay", "minecraft:clay_ball", "minecraft:clock", "minecraft:coal", "minecraft:coal_block", "minecraft:coal_ore", "minecraft:coarse_dirt", "minecraft:coast_armor_trim_smithing_template", "minecraft:cobbled_deepslate", "minecraft:cobbled_deepslate_slab", "minecraft:cobbled_deepslate_stairs", "minecraft:cobbled_deepslate_wall", "minecraft:cobblestone", "minecraft:cobblestone_slab", "minecraft:cobblestone_stairs", "minecraft:cobblestone_wall", "minecraft:cobweb", "minecraft:cocoa_beans", "minecraft:cod", "minecraft:cod_bucket", "minecraft:cod_spawn_egg", "minecraft:command_block", "minecraft:command_block_minecart", "minecraft:comparator", "minecraft:compass", "minecraft:composter", "minecraft:conduit", "minecraft:cooked_beef", "minecraft:cooked_chicken", "minecraft:cooked_cod", "minecraft:cooked_mutton", "minecraft:cooked_porkchop", "minecraft:cooked_rabbit", "minecraft:cooked_salmon", "minecraft:cookie", "minecraft:copper_block", "minecraft:copper_ingot", "minecraft:copper_ore", "minecraft:cornflower", "minecraft:cow_spawn_egg", "minecraft:cracked_deepslate_bricks", "minecraft:cracked_deepslate_tiles", "minecraft:cracked_nether_bricks", "minecraft:cracked_polished_blackstone_bricks", "minecraft:cracked_stone_bricks", "minecraft:crafting_table", "minecraft:creeper_banner_pattern", "minecraft:creeper_head", "minecraft:creeper_spawn_egg", "minecraft:crimson_button", "minecraft:crimson_door", "minecraft:crimson_fence", "minecraft:crimson_fence_gate", "minecraft:crimson_fungus", "minecraft:crimson_hanging_sign", "minecraft:crimson_hyphae", "minecraft:crimson_nylium", "minecraft:crimson_planks", "minecraft:crimson_pressure_plate", "minecraft:crimson_roots", "minecraft:crimson_sign", "minecraft:crimson_slab", "minecraft:crimson_stairs", "minecraft:crimson_stem", "minecraft:crimson_trapdoor", "minecraft:crossbow", "minecraft:crying_obsidian", "minecraft:cut_copper", "minecraft:cut_copper_slab", "minecraft:cut_copper_stairs", "minecraft:cut_red_sandstone", "minecraft:cut_red_sandstone_slab", "minecraft:cut_sandstone", "minecraft:cut_sandstone_slab", "minecraft:cyan_banner", "minecraft:cyan_bed", "minecraft:cyan_candle", "minecraft:cyan_carpet", "minecraft:cyan_concrete", "minecraft:cyan_concrete_powder", "minecraft:cyan_dye", "minecraft:cyan_glazed_terracotta", "minecraft:cyan_shulker_box", "minecraft:cyan_stained_glass", "minecraft:cyan_stained_glass_pane", "minecraft:cyan_terracotta", "minecraft:cyan_wool", "minecraft:damaged_anvil", "minecraft:dandelion", "minecraft:danger_pottery_sherd", "minecraft:dark_oak_boat", "minecraft:dark_oak_button", "minecraft:dark_oak_chest_boat", "minecraft:dark_oak_door", "minecraft:dark_oak_fence", "minecraft:dark_oak_fence_gate", "minecraft:dark_oak_hanging_sign", "minecraft:dark_oak_leaves", "minecraft:dark_oak_log", "minecraft:dark_oak_planks", "minecraft:dark_oak_pressure_plate", "minecraft:dark_oak_sapling", "minecraft:dark_oak_sign", "minecraft:dark_oak_slab", "minecraft:dark_oak_stairs", "minecraft:dark_oak_trapdoor", "minecraft:dark_oak_wood", "minecraft:dark_prismarine", "minecraft:dark_prismarine_slab", "minecraft:dark_prismarine_stairs", "minecraft:daylight_detector", "minecraft:dead_brain_coral", "minecraft:dead_brain_coral_block", "minecraft:dead_brain_coral_fan", "minecraft:dead_bubble_coral", "minecraft:dead_bubble_coral_block", "minecraft:dead_bubble_coral_fan", "minecraft:dead_bush", "minecraft:dead_fire_coral", "minecraft:dead_fire_coral_block", "minecraft:dead_fire_coral_fan", "minecraft:dead_horn_coral", "minecraft:dead_horn_coral_block", "minecraft:dead_horn_coral_fan", "minecraft:dead_tube_coral", "minecraft:dead_tube_coral_block", "minecraft:dead_tube_coral_fan", "minecraft:debug_stick", "minecraft:decorated_pot", "minecraft:deepslate", "minecraft:deepslate_brick_slab", "minecraft:deepslate_brick_stairs", "minecraft:deepslate_brick_wall", "minecraft:deepslate_bricks", "minecraft:deepslate_coal_ore", "minecraft:deepslate_copper_ore", "minecraft:deepslate_diamond_ore", "minecraft:deepslate_emerald_ore", "minecraft:deepslate_gold_ore", "minecraft:deepslate_iron_ore", "minecraft:deepslate_lapis_ore", "minecraft:deepslate_redstone_ore", "minecraft:deepslate_tile_slab", "minecraft:deepslate_tile_stairs", "minecraft:deepslate_tile_wall", "minecraft:deepslate_tiles", "minecraft:detector_rail", "minecraft:diamond", "minecraft:diamond_axe", "minecraft:diamond_block", "minecraft:diamond_boots", "minecraft:diamond_chestplate", "minecraft:diamond_helmet", "minecraft:diamond_hoe", "minecraft:diamond_horse_armor", "minecraft:diamond_leggings", "minecraft:diamond_ore", "minecraft:diamond_pickaxe", "minecraft:diamond_shovel", "minecraft:diamond_sword", "minecraft:diorite", "minecraft:diorite_slab", "minecraft:diorite_stairs", "minecraft:diorite_wall", "minecraft:dirt", "minecraft:dirt_path", "minecraft:disc_fragment_5", "minecraft:dispenser", "minecraft:dolphin_spawn_egg", "minecraft:donkey_spawn_egg", "minecraft:dragon_breath", "minecraft:dragon_egg", "minecraft:dragon_head", "minecraft:dried_kelp", "minecraft:dried_kelp_block", "minecraft:dripstone_block", "minecraft:dropper", "minecraft:drowned_spawn_egg", "minecraft:dune_armor_trim_smithing_template", "minecraft:echo_shard", "minecraft:egg", "minecraft:elder_guardian_spawn_egg", "minecraft:elytra", "minecraft:emerald", "minecraft:emerald_block", "minecraft:emerald_ore", "minecraft:enchanted_book", "minecraft:enchanted_golden_apple", "minecraft:enchanting_table", "minecraft:end_crystal", "minecraft:end_portal_frame", "minecraft:end_rod", "minecraft:end_stone", "minecraft:end_stone_brick_slab", "minecraft:end_stone_brick_stairs", "minecraft:end_stone_brick_wall", "minecraft:end_stone_bricks", "minecraft:ender_chest", "minecraft:ender_dragon_spawn_egg", "minecraft:ender_eye", "minecraft:ender_pearl", "minecraft:enderman_spawn_egg", "minecraft:endermite_spawn_egg", "minecraft:evoker_spawn_egg", "minecraft:experience_bottle", "minecraft:explorer_pottery_sherd", "minecraft:exposed_copper", "minecraft:exposed_cut_copper", "minecraft:exposed_cut_copper_slab", "minecraft:exposed_cut_copper_stairs", "minecraft:eye_armor_trim_smithing_template", "minecraft:farmland", "minecraft:feather", "minecraft:fermented_spider_eye", "minecraft:fern", "minecraft:filled_map", "minecraft:fire_charge", "minecraft:fire_coral", "minecraft:fire_coral_block", "minecraft:fire_coral_fan", "minecraft:firework_rocket", "minecraft:firework_star", "minecraft:fishing_rod", "minecraft:fletching_table", "minecraft:flint", "minecraft:flint_and_steel", "minecraft:flower_banner_pattern", "minecraft:flower_pot", "minecraft:flowering_azalea", "minecraft:flowering_azalea_leaves", "minecraft:fox_spawn_egg", "minecraft:friend_pottery_sherd", "minecraft:frog_spawn_egg", "minecraft:frogspawn", "minecraft:furnace", "minecraft:furnace_minecart", "minecraft:ghast_spawn_egg", "minecraft:ghast_tear", "minecraft:gilded_blackstone", "minecraft:glass", "minecraft:glass_bottle", "minecraft:glass_pane", "minecraft:glistering_melon_slice", "minecraft:globe_banner_pattern", "minecraft:glow_berries", "minecraft:glow_ink_sac", "minecraft:glow_item_frame", "minecraft:glow_lichen", "minecraft:glow_squid_spawn_egg", "minecraft:glowstone", "minecraft:glowstone_dust", "minecraft:goat_horn", "minecraft:goat_spawn_egg", "minecraft:gold_block", "minecraft:gold_ingot", "minecraft:gold_nugget", "minecraft:gold_ore", "minecraft:golden_apple", "minecraft:golden_axe", "minecraft:golden_boots", "minecraft:golden_carrot", "minecraft:golden_chestplate", "minecraft:golden_helmet", "minecraft:golden_hoe", "minecraft:golden_horse_armor", "minecraft:golden_leggings", "minecraft:golden_pickaxe", "minecraft:golden_shovel", "minecraft:golden_sword", "minecraft:granite", "minecraft:granite_slab", "minecraft:granite_stairs", "minecraft:granite_wall", "minecraft:grass", "minecraft:grass_block", "minecraft:gravel", "minecraft:gray_banner", "minecraft:gray_bed", "minecraft:gray_candle", "minecraft:gray_carpet", "minecraft:gray_concrete", "minecraft:gray_concrete_powder", "minecraft:gray_dye", "minecraft:gray_glazed_terracotta", "minecraft:gray_shulker_box", "minecraft:gray_stained_glass", "minecraft:gray_stained_glass_pane", "minecraft:gray_terracotta", "minecraft:gray_wool", "minecraft:green_banner", "minecraft:green_bed", "minecraft:green_candle", "minecraft:green_carpet", "minecraft:green_concrete", "minecraft:green_concrete_powder", "minecraft:green_dye", "minecraft:green_glazed_terracotta", "minecraft:green_shulker_box", "minecraft:green_stained_glass", "minecraft:green_stained_glass_pane", "minecraft:green_terracotta", "minecraft:green_wool", "minecraft:grindstone", "minecraft:guardian_spawn_egg", "minecraft:gunpowder", "minecraft:hanging_roots", "minecraft:hay_block", "minecraft:heart_of_the_sea", "minecraft:heart_pottery_sherd", "minecraft:heartbreak_pottery_sherd", "minecraft:heavy_weighted_pressure_plate", "minecraft:hoglin_spawn_egg", "minecraft:honey_block", "minecraft:honey_bottle", "minecraft:honeycomb", "minecraft:honeycomb_block", "minecraft:hopper", "minecraft:hopper_minecart", "minecraft:horn_coral", "minecraft:horn_coral_block", "minecraft:horn_coral_fan", "minecraft:horse_spawn_egg", "minecraft:host_armor_trim_smithing_template", "minecraft:howl_pottery_sherd", "minecraft:husk_spawn_egg", "minecraft:ice", "minecraft:infested_chiseled_stone_bricks", "minecraft:infested_cobblestone", "minecraft:infested_cracked_stone_bricks", "minecraft:infested_deepslate", "minecraft:infested_mossy_stone_bricks", "minecraft:infested_stone", "minecraft:infested_stone_bricks", "minecraft:ink_sac", "minecraft:iron_axe", "minecraft:iron_bars", "minecraft:iron_block", "minecraft:iron_boots", "minecraft:iron_chestplate", "minecraft:iron_door", "minecraft:iron_golem_spawn_egg", "minecraft:iron_helmet", "minecraft:iron_hoe", "minecraft:iron_horse_armor", "minecraft:iron_ingot", "minecraft:iron_leggings", "minecraft:iron_nugget", "minecraft:iron_ore", "minecraft:iron_pickaxe", "minecraft:iron_shovel", "minecraft:iron_sword", "minecraft:iron_trapdoor", "minecraft:item_frame", "minecraft:jack_o_lantern", "minecraft:jigsaw", "minecraft:jukebox", "minecraft:jungle_boat", "minecraft:jungle_button", "minecraft:jungle_chest_boat", "minecraft:jungle_door", "minecraft:jungle_fence", "minecraft:jungle_fence_gate", "minecraft:jungle_hanging_sign", "minecraft:jungle_leaves", "minecraft:jungle_log", "minecraft:jungle_planks", "minecraft:jungle_pressure_plate", "minecraft:jungle_sapling", "minecraft:jungle_sign", "minecraft:jungle_slab", "minecraft:jungle_stairs", "minecraft:jungle_trapdoor", "minecraft:jungle_wood", "minecraft:kelp", "minecraft:knowledge_book", "minecraft:ladder", "minecraft:lantern", "minecraft:lapis_block", "minecraft:lapis_lazuli", "minecraft:lapis_ore", "minecraft:large_amethyst_bud", "minecraft:large_fern", "minecraft:lava_bucket", "minecraft:lead", "minecraft:leather", "minecraft:leather_boots", "minecraft:leather_chestplate", "minecraft:leather_helmet", "minecraft:leather_horse_armor", "minecraft:leather_leggings", "minecraft:lectern", "minecraft:lever", "minecraft:light", "minecraft:light_blue_banner", "minecraft:light_blue_bed", "minecraft:light_blue_candle", "minecraft:light_blue_carpet", "minecraft:light_blue_concrete", "minecraft:light_blue_concrete_powder", "minecraft:light_blue_dye", "minecraft:light_blue_glazed_terracotta", "minecraft:light_blue_shulker_box", "minecraft:light_blue_stained_glass", "minecraft:light_blue_stained_glass_pane", "minecraft:light_blue_terracotta", "minecraft:light_blue_wool", "minecraft:light_gray_banner", "minecraft:light_gray_bed", "minecraft:light_gray_candle", "minecraft:light_gray_carpet", "minecraft:light_gray_concrete", "minecraft:light_gray_concrete_powder", "minecraft:light_gray_dye", "minecraft:light_gray_glazed_terracotta", "minecraft:light_gray_shulker_box", "minecraft:light_gray_stained_glass", "minecraft:light_gray_stained_glass_pane", "minecraft:light_gray_terracotta", "minecraft:light_gray_wool", "minecraft:light_weighted_pressure_plate", "minecraft:lightning_rod", "minecraft:lilac", "minecraft:lily_of_the_valley", "minecraft:lily_pad", "minecraft:lime_banner", "minecraft:lime_bed", "minecraft:lime_candle", "minecraft:lime_carpet", "minecraft:lime_concrete", "minecraft:lime_concrete_powder", "minecraft:lime_dye", "minecraft:lime_glazed_terracotta", "minecraft:lime_shulker_box", "minecraft:lime_stained_glass", "minecraft:lime_stained_glass_pane", "minecraft:lime_terracotta", "minecraft:lime_wool", "minecraft:lingering_potion", "minecraft:llama_spawn_egg", "minecraft:lodestone", "minecraft:loom", "minecraft:magenta_banner", "minecraft:magenta_bed", "minecraft:magenta_candle", "minecraft:magenta_carpet", "minecraft:magenta_concrete", "minecraft:magenta_concrete_powder", "minecraft:magenta_dye", "minecraft:magenta_glazed_terracotta", "minecraft:magenta_shulker_box", "minecraft:magenta_stained_glass", "minecraft:magenta_stained_glass_pane", "minecraft:magenta_terracotta", "minecraft:magenta_wool", "minecraft:magma_block", "minecraft:magma_cream", "minecraft:magma_cube_spawn_egg", "minecraft:mangrove_boat", "minecraft:mangrove_button", "minecraft:mangrove_chest_boat", "minecraft:mangrove_door", "minecraft:mangrove_fence", "minecraft:mangrove_fence_gate", "minecraft:mangrove_hanging_sign", "minecraft:mangrove_leaves", "minecraft:mangrove_log", "minecraft:mangrove_planks", "minecraft:mangrove_pressure_plate", "minecraft:mangrove_propagule", "minecraft:mangrove_roots", "minecraft:mangrove_sign", "minecraft:mangrove_slab", "minecraft:mangrove_stairs", "minecraft:mangrove_trapdoor", "minecraft:mangrove_wood", "minecraft:map", "minecraft:medium_amethyst_bud", "minecraft:melon", "minecraft:melon_seeds", "minecraft:melon_slice", "minecraft:milk_bucket", "minecraft:minecart", "minecraft:miner_pottery_sherd", "minecraft:mojang_banner_pattern", "minecraft:mooshroom_spawn_egg", "minecraft:moss_block", "minecraft:moss_carpet", "minecraft:mossy_cobblestone", "minecraft:mossy_cobblestone_slab", "minecraft:mossy_cobblestone_stairs", "minecraft:mossy_cobblestone_wall", "minecraft:mossy_stone_brick_slab", "minecraft:mossy_stone_brick_stairs", "minecraft:mossy_stone_brick_wall", "minecraft:mossy_stone_bricks", "minecraft:mourner_pottery_sherd", "minecraft:mud", "minecraft:mud_brick_slab", "minecraft:mud_brick_stairs", "minecraft:mud_brick_wall", "minecraft:mud_bricks", "minecraft:muddy_mangrove_roots", "minecraft:mule_spawn_egg", "minecraft:mushroom_stem", "minecraft:mushroom_stew", "minecraft:music_disc_11", "minecraft:music_disc_13", "minecraft:music_disc_5", "minecraft:music_disc_blocks", "minecraft:music_disc_cat", "minecraft:music_disc_chirp", "minecraft:music_disc_far", "minecraft:music_disc_mall", "minecraft:music_disc_mellohi", "minecraft:music_disc_otherside", "minecraft:music_disc_pigstep", "minecraft:music_disc_relic", "minecraft:music_disc_stal", "minecraft:music_disc_strad", "minecraft:music_disc_wait", "minecraft:music_disc_ward", "minecraft:mutton", "minecraft:mycelium", "minecraft:name_tag", "minecraft:nautilus_shell", "minecraft:nether_brick", "minecraft:nether_brick_fence", "minecraft:nether_brick_slab", "minecraft:nether_brick_stairs", "minecraft:nether_brick_wall", "minecraft:nether_bricks", "minecraft:nether_gold_ore", "minecraft:nether_quartz_ore", "minecraft:nether_sprouts", "minecraft:nether_star", "minecraft:nether_wart", "minecraft:nether_wart_block", "minecraft:netherite_axe", "minecraft:netherite_block", "minecraft:netherite_boots", "minecraft:netherite_chestplate", "minecraft:netherite_helmet", "minecraft:netherite_hoe", "minecraft:netherite_ingot", "minecraft:netherite_leggings", "minecraft:netherite_pickaxe", "minecraft:netherite_scrap", "minecraft:netherite_shovel", "minecraft:netherite_sword", "minecraft:netherite_upgrade_smithing_template", "minecraft:netherrack", "minecraft:note_block", "minecraft:oak_boat", "minecraft:oak_button", "minecraft:oak_chest_boat", "minecraft:oak_door", "minecraft:oak_fence", "minecraft:oak_fence_gate", "minecraft:oak_hanging_sign", "minecraft:oak_leaves", "minecraft:oak_log", "minecraft:oak_planks", "minecraft:oak_pressure_plate", "minecraft:oak_sapling", "minecraft:oak_sign", "minecraft:oak_slab", "minecraft:oak_stairs", "minecraft:oak_trapdoor", "minecraft:oak_wood", "minecraft:observer", "minecraft:obsidian", "minecraft:ocelot_spawn_egg", "minecraft:ochre_froglight", "minecraft:orange_banner", "minecraft:orange_bed", "minecraft:orange_candle", "minecraft:orange_carpet", "minecraft:orange_concrete", "minecraft:orange_concrete_powder", "minecraft:orange_dye", "minecraft:orange_glazed_terracotta", "minecraft:orange_shulker_box", "minecraft:orange_stained_glass", "minecraft:orange_stained_glass_pane", "minecraft:orange_terracotta", "minecraft:orange_tulip", "minecraft:orange_wool", "minecraft:oxeye_daisy", "minecraft:oxidized_copper", "minecraft:oxidized_cut_copper", "minecraft:oxidized_cut_copper_slab", "minecraft:oxidized_cut_copper_stairs", "minecraft:packed_ice", "minecraft:packed_mud", "minecraft:painting", "minecraft:panda_spawn_egg", "minecraft:paper", "minecraft:parrot_spawn_egg", "minecraft:pearlescent_froglight", "minecraft:peony", "minecraft:petrified_oak_slab", "minecraft:phantom_membrane", "minecraft:phantom_spawn_egg", "minecraft:pig_spawn_egg", "minecraft:piglin_banner_pattern", "minecraft:piglin_brute_spawn_egg", "minecraft:piglin_head", "minecraft:piglin_spawn_egg", "minecraft:pillager_spawn_egg", "minecraft:pink_banner", "minecraft:pink_bed", "minecraft:pink_candle", "minecraft:pink_carpet", "minecraft:pink_concrete", "minecraft:pink_concrete_powder", "minecraft:pink_dye", "minecraft:pink_glazed_terracotta", "minecraft:pink_petals", "minecraft:pink_shulker_box", "minecraft:pink_stained_glass", "minecraft:pink_stained_glass_pane", "minecraft:pink_terracotta", "minecraft:pink_tulip", "minecraft:pink_wool", "minecraft:piston", "minecraft:pitcher_plant", "minecraft:pitcher_pod", "minecraft:player_head", "minecraft:plenty_pottery_sherd", "minecraft:podzol", "minecraft:pointed_dripstone", "minecraft:poisonous_potato", "minecraft:polar_bear_spawn_egg", "minecraft:polished_andesite", "minecraft:polished_andesite_slab", "minecraft:polished_andesite_stairs", "minecraft:polished_basalt", "minecraft:polished_blackstone", "minecraft:polished_blackstone_brick_slab", "minecraft:polished_blackstone_brick_stairs", "minecraft:polished_blackstone_brick_wall", "minecraft:polished_blackstone_bricks", "minecraft:polished_blackstone_button", "minecraft:polished_blackstone_pressure_plate", "minecraft:polished_blackstone_slab", "minecraft:polished_blackstone_stairs", "minecraft:polished_blackstone_wall", "minecraft:polished_deepslate", "minecraft:polished_deepslate_slab", "minecraft:polished_deepslate_stairs", "minecraft:polished_deepslate_wall", "minecraft:polished_diorite", "minecraft:polished_diorite_slab", "minecraft:polished_diorite_stairs", "minecraft:polished_granite", "minecraft:polished_granite_slab", "minecraft:polished_granite_stairs", "minecraft:popped_chorus_fruit", "minecraft:poppy", "minecraft:porkchop", "minecraft:potato", "minecraft:potion", "minecraft:powder_snow_bucket", "minecraft:powered_rail", "minecraft:prismarine", "minecraft:prismarine_brick_slab", "minecraft:prismarine_brick_stairs", "minecraft:prismarine_bricks", "minecraft:prismarine_crystals", "minecraft:prismarine_shard", "minecraft:prismarine_slab", "minecraft:prismarine_stairs", "minecraft:prismarine_wall", "minecraft:prize_pottery_sherd", "minecraft:pufferfish", "minecraft:pufferfish_bucket", "minecraft:pufferfish_spawn_egg", "minecraft:pumpkin", "minecraft:pumpkin_pie", "minecraft:pumpkin_seeds", "minecraft:purple_banner", "minecraft:purple_bed", "minecraft:purple_candle", "minecraft:purple_carpet", "minecraft:purple_concrete", "minecraft:purple_concrete_powder", "minecraft:purple_dye", "minecraft:purple_glazed_terracotta", "minecraft:purple_shulker_box", "minecraft:purple_stained_glass", "minecraft:purple_stained_glass_pane", "minecraft:purple_terracotta", "minecraft:purple_wool", "minecraft:purpur_block", "minecraft:purpur_pillar", "minecraft:purpur_slab", "minecraft:purpur_stairs", "minecraft:quartz", "minecraft:quartz_block", "minecraft:quartz_bricks", "minecraft:quartz_pillar", "minecraft:quartz_slab", "minecraft:quartz_stairs", "minecraft:rabbit", "minecraft:rabbit_foot", "minecraft:rabbit_hide", "minecraft:rabbit_spawn_egg", "minecraft:rabbit_stew", "minecraft:rail", "minecraft:raiser_armor_trim_smithing_template", "minecraft:ravager_spawn_egg", "minecraft:raw_copper", "minecraft:raw_copper_block", "minecraft:raw_gold", "minecraft:raw_gold_block", "minecraft:raw_iron", "minecraft:raw_iron_block", "minecraft:recovery_compass", "minecraft:red_banner", "minecraft:red_bed", "minecraft:red_candle", "minecraft:red_carpet", "minecraft:red_concrete", "minecraft:red_concrete_powder", "minecraft:red_dye", "minecraft:red_glazed_terracotta", "minecraft:red_mushroom", "minecraft:red_mushroom_block", "minecraft:red_nether_brick_slab", "minecraft:red_nether_brick_stairs", "minecraft:red_nether_brick_wall", "minecraft:red_nether_bricks", "minecraft:red_sand", "minecraft:red_sandstone", "minecraft:red_sandstone_slab", "minecraft:red_sandstone_stairs", "minecraft:red_sandstone_wall", "minecraft:red_shulker_box", "minecraft:red_stained_glass", "minecraft:red_stained_glass_pane", "minecraft:red_terracotta", "minecraft:red_tulip", "minecraft:red_wool", "minecraft:redstone", "minecraft:redstone_block", "minecraft:redstone_lamp", "minecraft:redstone_ore", "minecraft:redstone_torch", "minecraft:reinforced_deepslate", "minecraft:repeater", "minecraft:repeating_command_block", "minecraft:respawn_anchor", "minecraft:rib_armor_trim_smithing_template", "minecraft:rooted_dirt", "minecraft:rose_bush", "minecraft:rotten_flesh", "minecraft:saddle", "minecraft:salmon", "minecraft:salmon_bucket", "minecraft:salmon_spawn_egg", "minecraft:sand", "minecraft:sandstone", "minecraft:sandstone_slab", "minecraft:sandstone_stairs", "minecraft:sandstone_wall", "minecraft:scaffolding", "minecraft:sculk", "minecraft:sculk_catalyst", "minecraft:sculk_sensor", "minecraft:sculk_shrieker", "minecraft:sculk_vein", "minecraft:scute", "minecraft:sea_lantern", "minecraft:sea_pickle", "minecraft:seagrass", "minecraft:sentry_armor_trim_smithing_template", "minecraft:shaper_armor_trim_smithing_template", "minecraft:sheaf_pottery_sherd", "minecraft:shears", "minecraft:sheep_spawn_egg", "minecraft:shelter_pottery_sherd", "minecraft:shield", "minecraft:shroomlight", "minecraft:shulker_box", "minecraft:shulker_shell", "minecraft:shulker_spawn_egg", "minecraft:silence_armor_trim_smithing_template", "minecraft:silverfish_spawn_egg", "minecraft:skeleton_horse_spawn_egg", "minecraft:skeleton_skull", "minecraft:skeleton_spawn_egg", "minecraft:skull_banner_pattern", "minecraft:skull_pottery_sherd", "minecraft:slime_ball", "minecraft:slime_block", "minecraft:slime_spawn_egg", "minecraft:small_amethyst_bud", "minecraft:small_dripleaf", "minecraft:smithing_table", "minecraft:smoker", "minecraft:smooth_basalt", "minecraft:smooth_quartz", "minecraft:smooth_quartz_slab", "minecraft:smooth_quartz_stairs", "minecraft:smooth_red_sandstone", "minecraft:smooth_red_sandstone_slab", "minecraft:smooth_red_sandstone_stairs", "minecraft:smooth_sandstone", "minecraft:smooth_sandstone_slab", "minecraft:smooth_sandstone_stairs", "minecraft:smooth_stone", "minecraft:smooth_stone_slab", "minecraft:sniffer_egg", "minecraft:sniffer_spawn_egg", "minecraft:snort_pottery_sherd", "minecraft:snout_armor_trim_smithing_template", "minecraft:snow", "minecraft:snow_block", "minecraft:snow_golem_spawn_egg", "minecraft:snowball", "minecraft:soul_campfire", "minecraft:soul_lantern", "minecraft:soul_sand", "minecraft:soul_soil", "minecraft:soul_torch", "minecraft:spawner", "minecraft:spectral_arrow", "minecraft:spider_eye", "minecraft:spider_spawn_egg", "minecraft:spire_armor_trim_smithing_template", "minecraft:splash_potion", "minecraft:sponge", "minecraft:spore_blossom", "minecraft:spruce_boat", "minecraft:spruce_button", "minecraft:spruce_chest_boat", "minecraft:spruce_door", "minecraft:spruce_fence", "minecraft:spruce_fence_gate", "minecraft:spruce_hanging_sign", "minecraft:spruce_leaves", "minecraft:spruce_log", "minecraft:spruce_planks", "minecraft:spruce_pressure_plate", "minecraft:spruce_sapling", "minecraft:spruce_sign", "minecraft:spruce_slab", "minecraft:spruce_stairs", "minecraft:spruce_trapdoor", "minecraft:spruce_wood", "minecraft:spyglass", "minecraft:squid_spawn_egg", "minecraft:stick", "minecraft:sticky_piston", "minecraft:stone", "minecraft:stone_axe", "minecraft:stone_brick_slab", "minecraft:stone_brick_stairs", "minecraft:stone_brick_wall", "minecraft:stone_bricks", "minecraft:stone_button", "minecraft:stone_hoe", "minecraft:stone_pickaxe", "minecraft:stone_pressure_plate", "minecraft:stone_shovel", "minecraft:stone_slab", "minecraft:stone_stairs", "minecraft:stone_sword", "minecraft:stonecutter", "minecraft:stray_spawn_egg", "minecraft:strider_spawn_egg", "minecraft:string", "minecraft:stripped_acacia_log", "minecraft:stripped_acacia_wood", "minecraft:stripped_bamboo_block", "minecraft:stripped_birch_log", "minecraft:stripped_birch_wood", "minecraft:stripped_cherry_log", "minecraft:stripped_cherry_wood", "minecraft:stripped_crimson_hyphae", "minecraft:stripped_crimson_stem", "minecraft:stripped_dark_oak_log", "minecraft:stripped_dark_oak_wood", "minecraft:stripped_jungle_log", "minecraft:stripped_jungle_wood", "minecraft:stripped_mangrove_log", "minecraft:stripped_mangrove_wood", "minecraft:stripped_oak_log", "minecraft:stripped_oak_wood", "minecraft:stripped_spruce_log", "minecraft:stripped_spruce_wood", "minecraft:stripped_warped_hyphae", "minecraft:stripped_warped_stem", "minecraft:structure_block", "minecraft:structure_void", "minecraft:sugar", "minecraft:sugar_cane", "minecraft:sunflower", "minecraft:suspicious_gravel", "minecraft:suspicious_sand", "minecraft:suspicious_stew", "minecraft:sweet_berries", "minecraft:tadpole_bucket", "minecraft:tadpole_spawn_egg", "minecraft:tall_grass", "minecraft:target", "minecraft:terracotta", "minecraft:tide_armor_trim_smithing_template", "minecraft:tinted_glass", "minecraft:tipped_arrow", "minecraft:tnt", "minecraft:tnt_minecart", "minecraft:torch", "minecraft:torchflower", "minecraft:torchflower_seeds", "minecraft:totem_of_undying", "minecraft:trader_llama_spawn_egg", "minecraft:trapped_chest", "minecraft:trident", "minecraft:tripwire_hook", "minecraft:tropical_fish", "minecraft:tropical_fish_bucket", "minecraft:tropical_fish_spawn_egg", "minecraft:tube_coral", "minecraft:tube_coral_block", "minecraft:tube_coral_fan", "minecraft:tuff", "minecraft:turtle_egg", "minecraft:turtle_helmet", "minecraft:turtle_spawn_egg", "minecraft:twisting_vines", "minecraft:verdant_froglight", "minecraft:vex_armor_trim_smithing_template", "minecraft:vex_spawn_egg", "minecraft:villager_spawn_egg", "minecraft:vindicator_spawn_egg", "minecraft:vine", "minecraft:wandering_trader_spawn_egg", "minecraft:ward_armor_trim_smithing_template", "minecraft:warden_spawn_egg", "minecraft:warped_button", "minecraft:warped_door", "minecraft:warped_fence", "minecraft:warped_fence_gate", "minecraft:warped_fungus", "minecraft:warped_fungus_on_a_stick", "minecraft:warped_hanging_sign", "minecraft:warped_hyphae", "minecraft:warped_nylium", "minecraft:warped_planks", "minecraft:warped_pressure_plate", "minecraft:warped_roots", "minecraft:warped_sign", "minecraft:warped_slab", "minecraft:warped_stairs", "minecraft:warped_stem", "minecraft:warped_trapdoor", "minecraft:warped_wart_block", "minecraft:water_bucket", "minecraft:waxed_copper_block", "minecraft:waxed_cut_copper", "minecraft:waxed_cut_copper_slab", "minecraft:waxed_cut_copper_stairs", "minecraft:waxed_exposed_copper", "minecraft:waxed_exposed_cut_copper", "minecraft:waxed_exposed_cut_copper_slab", "minecraft:waxed_exposed_cut_copper_stairs", "minecraft:waxed_oxidized_copper", "minecraft:waxed_oxidized_cut_copper", "minecraft:waxed_oxidized_cut_copper_slab", "minecraft:waxed_oxidized_cut_copper_stairs", "minecraft:waxed_weathered_copper", "minecraft:waxed_weathered_cut_copper", "minecraft:waxed_weathered_cut_copper_slab", "minecraft:waxed_weathered_cut_copper_stairs", "minecraft:wayfinder_armor_trim_smithing_template", "minecraft:weathered_copper", "minecraft:weathered_cut_copper", "minecraft:weathered_cut_copper_slab", "minecraft:weathered_cut_copper_stairs", "minecraft:weeping_vines", "minecraft:wet_sponge", "minecraft:wheat", "minecraft:wheat_seeds", "minecraft:white_banner", "minecraft:white_bed", "minecraft:white_candle", "minecraft:white_carpet", "minecraft:white_concrete", "minecraft:white_concrete_powder", "minecraft:white_dye", "minecraft:white_glazed_terracotta", "minecraft:white_shulker_box", "minecraft:white_stained_glass", "minecraft:white_stained_glass_pane", "minecraft:white_terracotta", "minecraft:white_tulip", "minecraft:white_wool", "minecraft:wild_armor_trim_smithing_template", "minecraft:witch_spawn_egg", "minecraft:wither_rose", "minecraft:wither_skeleton_skull", "minecraft:wither_skeleton_spawn_egg", "minecraft:wither_spawn_egg", "minecraft:wolf_spawn_egg", "minecraft:wooden_axe", "minecraft:wooden_hoe", "minecraft:wooden_pickaxe", "minecraft:wooden_shovel", "minecraft:wooden_sword", "minecraft:writable_book", "minecraft:written_book", "minecraft:yellow_banner", "minecraft:yellow_bed", "minecraft:yellow_candle", "minecraft:yellow_carpet", "minecraft:yellow_concrete", "minecraft:yellow_concrete_powder", "minecraft:yellow_dye", "minecraft:yellow_glazed_terracotta", "minecraft:yellow_shulker_box", "minecraft:yellow_stained_glass", "minecraft:yellow_stained_glass_pane", "minecraft:yellow_terracotta", "minecraft:yellow_wool", "minecraft:zoglin_spawn_egg", "minecraft:zombie_head", "minecraft:zombie_horse_spawn_egg", "minecraft:zombie_spawn_egg", "minecraft:zombie_villager_spawn_egg", "minecraft:zombified_piglin_spawn_egg", "netmusic:cd_burner", "netmusic:computer", "netmusic:music_cd", "netmusic:music_player", "netmusic:music_player_backpack", "cataclysm:purpur_tiles", "cataclysm:void_purpur_tiles", "cataclysm:purpur_tile_pillar", "cataclysm:purpur_tile_slab", "cataclysm:purpur_tile_stairs", "cataclysm:purpur_tile_wall", "cataclysm:void_crystal", "cataclysm:polished_obsidian", "cataclysm:polished_obsidian_slab", "cataclysm:polished_obsidian_stairs", "cataclysm:polished_obsidian_wall", "cataclysm:obsidian_pillar", "cataclysm:chorus_trapdoor", "cataclysm:prismarine_brick_fence", "cataclysm:prismarine_brick_wall", "cataclysm:azure_seastone", "cataclysm:azure_seastone_slab", "cataclysm:azure_seastone_stairs", "cataclysm:azure_seastone_wall", "cataclysm:azure_seastone_fence", "cataclysm:azure_seastone_tiles", "cataclysm:chiseled_azure_seastone", "cataclysm:azure_seastone_bricks", "cataclysm:azure_seastone_brick_slab", "cataclysm:azure_seastone_brick_stairs", "cataclysm:azure_seastone_brick_wall", "cataclysm:azure_seastone_mural_empty", "cataclysm:azure_seastone_mural_urchinkin", "cataclysm:azure_seastone_mural_cindaria", "cataclysm:azure_seastone_mural_hippocamtus", "cataclysm:azure_seastone_mural_clawdian", "cataclysm:azure_seastone_mural_thunder", "cataclysm:azure_seastone_mural_sea", "cataclysm:azure_seastone_mural_underworld", "cataclysm:azure_seastone_mural_harvest", "cataclysm:azure_seastone_mural_smithing", "cataclysm:azure_seastone_mural_wisdom", "cataclysm:curved_azure_seastone_urchinkin", "cataclysm:curved_azure_seastone_cindaria_1", "cataclysm:curved_azure_seastone_cindaria_2", "cataclysm:curved_azure_seastone_cindaria_3", "cataclysm:curved_azure_seastone_cindaria_4", "cataclysm:curved_azure_seastone_hippocamtus_1", "cataclysm:curved_azure_seastone_hippocamtus_2", "cataclysm:curved_azure_seastone_hippocamtus_3", "cataclysm:curved_azure_seastone_hippocamtus_4", "cataclysm:curved_azure_seastone_clawdian_1", "cataclysm:curved_azure_seastone_clawdian_2", "cataclysm:curved_azure_seastone_clawdian_3", "cataclysm:curved_azure_seastone_clawdian_4", "cataclysm:curved_azure_seastone_scylla_1", "cataclysm:curved_azure_seastone_scylla_2", "cataclysm:curved_azure_seastone_scylla_3", "cataclysm:curved_azure_seastone_scylla_4", "cataclysm:curved_azure_seastone_scylla_5", "cataclysm:curved_azure_seastone_scylla_6", "cataclysm:curved_azure_seastone_scylla_7", "cataclysm:curved_azure_seastone_scylla_8", "cataclysm:curved_azure_seastone_scylla_9", "cataclysm:polished_azure_seastone", "cataclysm:polished_azure_seastone_slab", "cataclysm:polished_azure_seastone_stairs", "cataclysm:polished_azure_seastone_wall", "cataclysm:azure_seastone_pillar", "cataclysm:azure_seastone_pillar_wall", "cataclysm:chiseled_azure_seastone_pillar", "cataclysm:chiseled_azure_seastone_pillar_wall", "cataclysm:lacrima", "cataclysm:essence_of_the_storm", "cataclysm:azure_sea_shield", "cataclysm:gauntlet_of_maelstrom", "cataclysm:blazing_grips", "cataclysm:chitin_claw", "cataclysm:wrath_of_the_desert", "cataclysm:astrape", "cataclysm:cera<PERSON>us", "cataclysm:the_immolator", "cataclysm:netherite_effigy", "cataclysm:strange_key", "cataclysm:lava_power_cell", "cataclysm:music_disc_scylla", "cataclysm:music_disc_the_cataclysmfarer", "cataclysm:storm_eye", "cataclysm:urchin_spike", "cataclysm:blood_clot", "cataclysm:netherite_ministrosity_bucket", "cataclysm:netherite_ministrosity_spawn_egg", "cataclysm:scylla_spawn_egg", "cataclysm:clawdian_spawn_egg", "cataclysm:hippocamtus_spawn_egg", "cataclysm:cindaria_spawn_egg", "cataclysm:octohost_spawn_egg", "cataclysm:symbiocto_spawn_egg", "cataclysm:urchinkin_spawn_egg", "cataclysm:boss_respawner", "cataclysm:goddess_statue", "guideme:guide", "tconstruct:clear_glass", "tconstruct:clear_tinted_glass", "tconstruct:clear_glass_pane", "tconstruct:white_clear_stained_glass", "tconstruct:orange_clear_stained_glass", "tconstruct:magenta_clear_stained_glass", "tconstruct:light_blue_clear_stained_glass", "tconstruct:yellow_clear_stained_glass", "tconstruct:lime_clear_stained_glass", "tconstruct:pink_clear_stained_glass", "tconstruct:gray_clear_stained_glass", "tconstruct:light_gray_clear_stained_glass", "tconstruct:cyan_clear_stained_glass", "tconstruct:purple_clear_stained_glass", "tconstruct:blue_clear_stained_glass", "tconstruct:brown_clear_stained_glass", "tconstruct:green_clear_stained_glass", "tconstruct:red_clear_stained_glass", "tconstruct:black_clear_stained_glass", "tconstruct:white_clear_stained_glass_pane", "tconstruct:orange_clear_stained_glass_pane", "tconstruct:magenta_clear_stained_glass_pane", "tconstruct:light_blue_clear_stained_glass_pane", "tconstruct:yellow_clear_stained_glass_pane", "tconstruct:lime_clear_stained_glass_pane", "tconstruct:pink_clear_stained_glass_pane", "tconstruct:gray_clear_stained_glass_pane", "tconstruct:light_gray_clear_stained_glass_pane", "tconstruct:cyan_clear_stained_glass_pane", "tconstruct:purple_clear_stained_glass_pane", "tconstruct:blue_clear_stained_glass_pane", "tconstruct:brown_clear_stained_glass_pane", "tconstruct:green_clear_stained_glass_pane", "tconstruct:red_clear_stained_glass_pane", "tconstruct:black_clear_stained_glass_pane", "tconstruct:soul_glass", "tconstruct:soul_glass_pane", "tconstruct:gold_bars", "tconstruct:obsidian_pane", "tconstruct:gold_platform", "tconstruct:iron_platform", "tconstruct:cobalt_platform", "tconstruct:copper_platform", "tconstruct:exposed_copper_platform", "tconstruct:weathered_copper_platform", "tconstruct:oxidized_copper_platform", "tconstruct:waxed_copper_platform", "tconstruct:waxed_exposed_copper_platform", "tconstruct:waxed_weathered_copper_platform", "tconstruct:waxed_oxidized_copper_platform", "tconstruct:cheese_block", "tconstruct:cobalt_block", "tconstruct:cobalt_ingot", "tconstruct:cobalt_nugget", "tconstruct:steel_block", "tconstruct:steel_ingot", "tconstruct:steel_nugget", "tconstruct:slimesteel_block", "tconstruct:slimesteel_ingot", "tconstruct:slimesteel_nugget", "tconstruct:amethyst_bronze_block", "tconstruct:amethyst_bronze_ingot", "tconstruct:amethyst_bronze_nugget", "tconstruct:rose_gold_block", "tconstruct:rose_gold_ingot", "tconstruct:rose_gold_nugget", "tconstruct:pig_iron_block", "tconstruct:pig_iron_ingot", "tconstruct:pig_iron_nugget", "tconstruct:cinderslime_block", "tconstruct:cinderslime_ingot", "tconstruct:cinderslime_nugget", "tconstruct:queens_slime_block", "tconstruct:queens_slime_ingot", "tconstruct:queens_slime_nugget", "tconstruct:manyullyn_block", "tconstruct:manyullyn_ingot", "tconstruct:manyullyn_nugget", "tconstruct:hepatizon_block", "tconstruct:hepati<PERSON>_ingot", "tconstruct:hepatizon_nugget", "tconstruct:soulsteel_block", "tconstruct:soulsteel_ingot", "tconstruct:soulsteel_nugget", "tconstruct:knightslime_block", "tconstruct:<PERSON><PERSON><PERSON>_ingot", "tconstruct:<PERSON><PERSON>e_nugget", "tconstruct:nahuatl", "tconstruct:nahuatl_slab", "tconstruct:nahuatl_stairs", "tconstruct:nahuatl_fence", "tconstruct:blazewood", "tconstruct:blazewood_slab", "tconstruct:blazewood_stairs", "tconstruct:blazewood_fence", "tconstruct:punji", "tconstruct:earth_cake", "tconstruct:sky_cake", "tconstruct:ichor_cake", "tconstruct:ender_cake", "tconstruct:blood_cake", "tconstruct:magma_cake", "tconstruct:cobalt_ore", "tconstruct:raw_cobalt_block", "tconstruct:sky_slime", "tconstruct:ichor_slime", "tconstruct:ender_slime", "tconstruct:earth_congealed_slime", "tconstruct:sky_congealed_slime", "tconstruct:ichor_congealed_slime", "tconstruct:ender_congealed_slime", "tconstruct:earth_slime_dirt", "tconstruct:sky_slime_dirt", "tconstruct:ichor_slime_dirt", "tconstruct:ender_slime_dirt", "tconstruct:earth_vanilla_slime_grass", "tconstruct:sky_vanilla_slime_grass", "tconstruct:ichor_vanilla_slime_grass", "tconstruct:ender_vanilla_slime_grass", "tconstruct:blood_vanilla_slime_grass", "tconstruct:earth_earth_slime_grass", "tconstruct:sky_earth_slime_grass", "tconstruct:ichor_earth_slime_grass", "tconstruct:ender_earth_slime_grass", "tconstruct:blood_earth_slime_grass", "tconstruct:earth_sky_slime_grass", "tconstruct:sky_sky_slime_grass", "tconstruct:ichor_sky_slime_grass", "tconstruct:ender_sky_slime_grass", "tconstruct:blood_sky_slime_grass", "tconstruct:earth_ender_slime_grass", "tconstruct:sky_ender_slime_grass", "tconstruct:ichor_ender_slime_grass", "tconstruct:ender_ender_slime_grass", "tconstruct:blood_ender_slime_grass", "tconstruct:earth_ichor_slime_grass", "tconstruct:sky_ichor_slime_grass", "tconstruct:ichor_ichor_slime_grass", "tconstruct:ender_ichor_slime_grass", "tconstruct:blood_ichor_slime_grass", "tconstruct:greenheart_planks", "tconstruct:greenheart_planks_slab", "tconstruct:greenheart_planks_stairs", "tconstruct:greenheart_fence", "tconstruct:stripped_greenheart_log", "tconstruct:stripped_greenheart_wood", "tconstruct:greenheart_log", "tconstruct:greenheart_wood", "tconstruct:greenheart_door", "tconstruct:greenheart_trapdoor", "tconstruct:greenheart_fence_gate", "tconstruct:greenheart_pressure_plate", "tconstruct:greenheart_button", "tconstruct:greenheart_sign", "tconstruct:greenheart_hanging_sign", "tconstruct:skyroot_planks", "tconstruct:skyroot_planks_slab", "tconstruct:skyroot_planks_stairs", "tconstruct:skyroot_fence", "tconstruct:stripped_skyroot_log", "tconstruct:stripped_skyroot_wood", "tconstruct:skyroot_log", "tconstruct:skyroot_wood", "tconstruct:skyroot_door", "tconstruct:skyroot_trapdoor", "tconstruct:skyroot_fence_gate", "tconstruct:skyroot_pressure_plate", "tconstruct:skyroot_button", "tconstruct:skyroot_sign", "tconstruct:skyroot_hanging_sign", "tconstruct:bloodshroom_planks", "tconstruct:bloodshroom_planks_slab", "tconstruct:bloodshroom_planks_stairs", "tconstruct:bloodshroom_fence", "tconstruct:stripped_bloodshroom_log", "tconstruct:stripped_bloodshroom_wood", "tconstruct:bloodshroom_log", "tconstruct:bloodshroom_wood", "tconstruct:bloodshroom_door", "tconstruct:bloodshroom_trapdoor", "tconstruct:bloodshroom_fence_gate", "tconstruct:bloodshroom_pressure_plate", "tconstruct:bloodshroom_button", "tconstruct:bloodshroom_sign", "tconstruct:bloodshroom_hanging_sign", "tconstruct:enderbark_planks", "tconstruct:enderbark_planks_slab", "tconstruct:enderbark_planks_stairs", "tconstruct:enderbark_fence", "tconstruct:stripped_enderbark_log", "tconstruct:stripped_enderbark_wood", "tconstruct:enderbark_log", "tconstruct:enderbark_wood", "tconstruct:enderbark_door", "tconstruct:enderbark_trapdoor", "tconstruct:enderbark_fence_gate", "tconstruct:enderbark_pressure_plate", "tconstruct:enderbark_button", "tconstruct:enderbark_sign", "tconstruct:enderbark_hanging_sign", "tconstruct:enderbark_roots", "tconstruct:earth_enderbark_roots", "tconstruct:sky_enderbark_roots", "tconstruct:ichor_enderbark_roots", "tconstruct:ender_enderbark_roots", "tconstruct:earth_slime_fern", "tconstruct:sky_slime_fern", "tconstruct:ichor_slime_fern", "tconstruct:ender_slime_fern", "tconstruct:blood_slime_fern", "tconstruct:earth_slime_tall_grass", "tconstruct:sky_slime_tall_grass", "tconstruct:ichor_slime_tall_grass", "tconstruct:ender_slime_tall_grass", "tconstruct:blood_slime_tall_grass", "tconstruct:earth_slime_sapling", "tconstruct:sky_slime_sapling", "tconstruct:blood_slime_sapling", "tconstruct:ichor_slime_sapling", "tconstruct:ender_slime_sapling", "tconstruct:earth_slime_leaves", "tconstruct:sky_slime_leaves", "tconstruct:ichor_slime_leaves", "tconstruct:blood_slime_leaves", "tconstruct:ender_slime_leaves", "tconstruct:sky_slime_vine", "tconstruct:ender_slime_vine", "tconstruct:earth_slime_crystal", "tconstruct:earth_slime_crystal_block", "tconstruct:budding_earth_slime_crystal", "tconstruct:earth_slime_crystal_cluster", "tconstruct:small_earth_slime_crystal_bud", "tconstruct:medium_earth_slime_crystal_bud", "tconstruct:large_earth_slime_crystal_bud", "tconstruct:sky_slime_crystal", "tconstruct:sky_slime_crystal_block", "tconstruct:budding_sky_slime_crystal", "tconstruct:sky_slime_crystal_cluster", "tconstruct:small_sky_slime_crystal_bud", "tconstruct:medium_sky_slime_crystal_bud", "tconstruct:large_sky_slime_crystal_bud", "tconstruct:ichor_slime_crystal", "tconstruct:ichor_slime_crystal_block", "tconstruct:budding_ichor_slime_crystal", "tconstruct:ichor_slime_crystal_cluster", "tconstruct:small_ichor_slime_crystal_bud", "tconstruct:medium_ichor_slime_crystal_bud", "tconstruct:large_ichor_slime_crystal_bud", "tconstruct:ender_slime_crystal", "tconstruct:ender_slime_crystal_block", "tconstruct:budding_ender_slime_crystal", "tconstruct:ender_slime_crystal_cluster", "tconstruct:small_ender_slime_crystal_bud", "tconstruct:medium_ender_slime_crystal_bud", "tconstruct:large_ender_slime_crystal_bud", "tconstruct:crafting_station", "tconstruct:tinker_station", "tconstruct:part_builder", "tconstruct:tinkers_chest", "tconstruct:part_chest", "tconstruct:cast_chest", "tconstruct:modifier_worktable", "tconstruct:tinkers_anvil", "tconstruct:scorched_anvil", "tconstruct:grout", "tconstruct:nether_grout", "tconstruct:seared_stone", "tconstruct:seared_stone_slab", "tconstruct:seared_stone_stairs", "tconstruct:seared_cobble", "tconstruct:seared_cobble_slab", "tconstruct:seared_cobble_stairs", "tconstruct:seared_cobble_wall", "tconstruct:seared_paver", "tconstruct:seared_paver_slab", "tconstruct:seared_paver_stairs", "tconstruct:seared_bricks", "tconstruct:seared_bricks_slab", "tconstruct:seared_bricks_stairs", "tconstruct:seared_bricks_wall", "tconstruct:seared_cracked_bricks", "tconstruct:seared_fancy_bricks", "tconstruct:seared_triangle_bricks", "tconstruct:seared_ladder", "tconstruct:seared_glass", "tconstruct:seared_soul_glass", "tconstruct:seared_tinted_glass", "tconstruct:seared_glass_pane", "tconstruct:seared_soul_glass_pane", "tconstruct:seared_drain", "tconstruct:seared_duct", "tconstruct:seared_chute", "tconstruct:scorched_stone", "tconstruct:polished_scorched_stone", "tconstruct:scorched_bricks", "tconstruct:scorched_bricks_slab", "tconstruct:scorched_bricks_stairs", "tconstruct:scorched_bricks_fence", "tconstruct:scorched_road", "tconstruct:scorched_road_slab", "tconstruct:scorched_road_stairs", "tconstruct:chiseled_scorched_bricks", "tconstruct:scorched_ladder", "tconstruct:scorched_glass", "tconstruct:scorched_soul_glass", "tconstruct:scorched_tinted_glass", "tconstruct:scorched_glass_pane", "tconstruct:scorched_soul_glass_pane", "tconstruct:scorched_drain", "tconstruct:scorched_duct", "tconstruct:scorched_chute", "tconstruct:seared_fuel_tank", "tconstruct:seared_fuel_gauge", "tconstruct:seared_ingot_tank", "tconstruct:seared_ingot_gauge", "tconstruct:seared_lantern", "tconstruct:seared_faucet", "tconstruct:seared_channel", "tconstruct:seared_basin", "tconstruct:seared_table", "tconstruct:seared_casting_tank", "tconstruct:scorched_fuel_tank", "tconstruct:scorched_fuel_gauge", "tconstruct:scorched_ingot_tank", "tconstruct:scorched_ingot_gauge", "tconstruct:scorched_lantern", "tconstruct:scorched_faucet", "tconstruct:scorched_channel", "tconstruct:scorched_basin", "tconstruct:scorched_table", "tconstruct:scorched_proxy_tank", "tconstruct:copper_gauge", "tconstruct:obsidian_gauge", "tconstruct:seared_fluid_cannon", "tconstruct:scorched_fluid_cannon", "tconstruct:smeltery_controller", "tconstruct:foundry_controller", "tconstruct:seared_melter", "tconstruct:seared_heater", "tconstruct:scorched_alloyer", "tconstruct:bacon", "tconstruct:jeweled_apple", "tconstruct:cheese_ingot", "tconstruct:materials_and_you", "tconstruct:puny_smelting", "tconstruct:mighty_smelting", "tconstruct:tinkers_gadgetry", "tconstruct:fantastic_foundry", "tconstruct:encyclopedia", "tconstruct:sky_slime_ball", "tconstruct:ichor_slime_ball", "tconstruct:ender_slime_ball", "tconstruct:copper_nugget", "tconstruct:netherite_nugget", "tconstruct:debris_nugget", "tconstruct:necrotic_bone", "tconstruct:venombone", "tconstruct:blazing_bone", "tconstruct:necronium_bone", "tconstruct:piggy_backpack", "tconstruct:reversed_gold_item_frame", "tconstruct:diamond_item_frame", "tconstruct:manyullyn_item_frame", "tconstruct:gold_item_frame", "tconstruct:clear_item_frame", "tconstruct:netherite_item_frame", "tconstruct:glow_ball", "tconstruct:efln_ball", "tconstruct:quartz_shuriken", "tconstruct:flint_shuriken", "tconstruct:raw_cobalt", "tconstruct:earth_slime_grass_seeds", "tconstruct:sky_slime_grass_seeds", "tconstruct:ichor_slime_grass_seeds", "tconstruct:ender_slime_grass_seeds", "tconstruct:blood_slime_grass_seeds", "tconstruct:blaze_head", "tconstruct:end<PERSON>_head", "tconstruct:stray_head", "tconstruct:husk_head", "tconstruct:drowned_head", "tconstruct:spider_head", "tconstruct:cave_spider_head", "tconstruct:piglin_brute_head", "tconstruct:zombified_piglin_head", "tconstruct:pattern", "tconstruct:silky_cloth", "tconstruct:dragon_scale", "tconstruct:emerald_reinforcement", "tconstruct:slimesteel_reinforcement", "tconstruct:iron_reinforcement", "tconstruct:seared_reinforcement", "tconstruct:gold_reinforcement", "tconstruct:cobalt_reinforcement", "tconstruct:obsidian_reinforcement", "tconstruct:modifier_crystal", "tconstruct:creative_slot", "tconstruct:repair_kit", "tconstruct:pick_head", "tconstruct:hammer_head", "tconstruct:small_axe_head", "tconstruct:broad_axe_head", "tconstruct:small_blade", "tconstruct:broad_blade", "tconstruct:adze_head", "tconstruct:large_plate", "tconstruct:bow_limb", "tconstruct:bow_grip", "tconstruct:bowstring", "tconstruct:tool_binding", "tconstruct:tough_binding", "tconstruct:tool_handle", "tconstruct:tough_handle", "tconstruct:helmet_plating", "tconstruct:chestplate_plating", "tconstruct:leggings_plating", "tconstruct:boots_plating", "tconstruct:maille", "tconstruct:shield_core", "tconstruct:pickaxe", "tconstruct:sledge_hammer", "tconstruct:vein_hammer", "tconstruct:mattock", "tconstruct:pickadze", "tconstruct:excavator", "tconstruct:hand_axe", "tconstruct:broad_axe", "tconstruct:kama", "tconstruct:scythe", "tconstruct:dagger", "tconstruct:sword", "tconstruct:cleaver", "tconstruct:crossbow", "tconstruct:longbow", "tconstruct:flint_and_brick", "tconstruct:sky_staff", "tconstruct:earth_staff", "tconstruct:ichor_staff", "tconstruct:ender_staff", "tconstruct:melting_pan", "tconstruct:war_pick", "tconstruct:battlesign", "tconstruct:swasher", "tconstruct:travelers_helmet", "tconstruct:travelers_chestplate", "tconstruct:travelers_leggings", "tconstruct:travelers_boots", "tconstruct:plate_helmet", "tconstruct:plate_chestplate", "tconstruct:plate_leggings", "tconstruct:plate_boots", "tconstruct:slime_boots", "tconstruct:slime_leggings", "tconstruct:slime_chestplate", "tconstruct:slime_helmet", "tconstruct:travelers_shield", "tconstruct:plate_shield", "tconstruct:crystalshot", "tconstruct:seared_brick", "tconstruct:scorched_brick", "tconstruct:copper_can", "tconstruct:blank_sand_cast", "tconstruct:blank_red_sand_cast", "tconstruct:ingot_cast", "tconstruct:ingot_sand_cast", "tconstruct:ingot_red_sand_cast", "tconstruct:nugget_cast", "tconstruct:nugget_sand_cast", "tconstruct:nugget_red_sand_cast", "tconstruct:gem_cast", "tconstruct:gem_sand_cast", "tconstruct:gem_red_sand_cast", "tconstruct:rod_cast", "tconstruct:rod_sand_cast", "tconstruct:rod_red_sand_cast", "tconstruct:repair_kit_cast", "tconstruct:repair_kit_sand_cast", "tconstruct:repair_kit_red_sand_cast", "tconstruct:plate_cast", "tconstruct:plate_sand_cast", "tconstruct:plate_red_sand_cast", "tconstruct:gear_cast", "tconstruct:gear_sand_cast", "tconstruct:gear_red_sand_cast", "tconstruct:coin_cast", "tconstruct:coin_sand_cast", "tconstruct:coin_red_sand_cast", "tconstruct:wire_cast", "tconstruct:wire_sand_cast", "tconstruct:wire_red_sand_cast", "tconstruct:pick_head_cast", "tconstruct:pick_head_sand_cast", "tconstruct:pick_head_red_sand_cast", "tconstruct:small_axe_head_cast", "tconstruct:small_axe_head_sand_cast", "tconstruct:small_axe_head_red_sand_cast", "tconstruct:small_blade_cast", "tconstruct:small_blade_sand_cast", "tconstruct:small_blade_red_sand_cast", "tconstruct:adze_head_cast", "tconstruct:adze_head_sand_cast", "tconstruct:adze_head_red_sand_cast", "tconstruct:hammer_head_cast", "tconstruct:hammer_head_sand_cast", "tconstruct:hammer_head_red_sand_cast", "tconstruct:broad_blade_cast", "tconstruct:broad_blade_sand_cast", "tconstruct:broad_blade_red_sand_cast", "tconstruct:broad_axe_head_cast", "tconstruct:broad_axe_head_sand_cast", "tconstruct:broad_axe_head_red_sand_cast", "tconstruct:large_plate_cast", "tconstruct:large_plate_sand_cast", "tconstruct:large_plate_red_sand_cast", "tconstruct:tool_binding_cast", "tconstruct:tool_binding_sand_cast", "tconstruct:tool_binding_red_sand_cast", "tconstruct:tough_binding_cast", "tconstruct:tough_binding_sand_cast", "tconstruct:tough_binding_red_sand_cast", "tconstruct:tool_handle_cast", "tconstruct:tool_handle_sand_cast", "tconstruct:tool_handle_red_sand_cast", "tconstruct:tough_handle_cast", "tconstruct:tough_handle_sand_cast", "tconstruct:tough_handle_red_sand_cast", "tconstruct:bow_limb_cast", "tconstruct:bow_limb_sand_cast", "tconstruct:bow_limb_red_sand_cast", "tconstruct:bow_grip_cast", "tconstruct:bow_grip_sand_cast", "tconstruct:bow_grip_red_sand_cast", "tconstruct:helmet_plating_cast", "tconstruct:helmet_plating_sand_cast", "tconstruct:helmet_plating_red_sand_cast", "tconstruct:chestplate_plating_cast", "tconstruct:chestplate_plating_sand_cast", "tconstruct:chestplate_plating_red_sand_cast", "tconstruct:leggings_plating_cast", "tconstruct:leggings_plating_sand_cast", "tconstruct:leggings_plating_red_sand_cast", "tconstruct:boots_plating_cast", "tconstruct:boots_plating_sand_cast", "tconstruct:boots_plating_red_sand_cast", "tconstruct:maille_cast", "tconstruct:maille_sand_cast", "tconstruct:maille_red_sand_cast", "tconstruct:helmet_plating_dummy", "tconstruct:chestplate_plating_dummy", "tconstruct:leggings_plating_dummy", "tconstruct:boots_plating_dummy", "tconstruct:venom_bottle", "tconstruct:earth_slime_bottle", "tconstruct:sky_slime_bottle", "tconstruct:ichor_slime_bottle", "tconstruct:ender_slime_bottle", "tconstruct:magma_bottle", "tconstruct:meat_soup", "tconstruct:splash_bottle", "tconstruct:lingering_bottle", "tconstruct:venom_bucket", "tconstruct:earth_slime_bucket", "tconstruct:sky_slime_bucket", "tconstruct:ender_slime_bucket", "tconstruct:magma_bucket", "tconstruct:ichor_bucket", "tconstruct:honey_bucket", "tconstruct:beetroot_soup_bucket", "tconstruct:mushroom_stew_bucket", "tconstruct:rabbit_stew_bucket", "tconstruct:meat_soup_bucket", "tconstruct:potion_bucket", "tconstruct:seared_stone_bucket", "tconstruct:scorched_stone_bucket", "tconstruct:molten_clay_bucket", "tconstruct:molten_glass_bucket", "tconstruct:liquid_soul_bucket", "tconstruct:molten_porcelain_bucket", "tconstruct:molten_obsidian_bucket", "tconstruct:molten_ender_bucket", "tconstruct:blazing_blood_bucket", "tconstruct:molten_emerald_bucket", "tconstruct:molten_quartz_bucket", "tconstruct:molten_amethyst_bucket", "tconstruct:molten_diamond_bucket", "tconstruct:molten_debris_bucket", "tconstruct:molten_iron_bucket", "tconstruct:molten_gold_bucket", "tconstruct:molten_copper_bucket", "tconstruct:molten_cobalt_bucket", "tconstruct:molten_steel_bucket", "tconstruct:molten_slimesteel_bucket", "tconstruct:molten_amethyst_bronze_bucket", "tconstruct:molten_rose_gold_bucket", "tconstruct:molten_pig_iron_bucket", "tconstruct:molten_manyullyn_bucket", "tconstruct:molten_hepatizon_bucket", "tconstruct:molten_cinderslime_bucket", "tconstruct:molten_queens_slime_bucket", "tconstruct:molten_soulsteel_bucket", "tconstruct:molten_netherite_bucket", "tconstruct:molten_knightslime_bucket", "tconstruct:molten_tin_bucket", "tconstruct:molten_aluminum_bucket", "tconstruct:molten_lead_bucket", "tconstruct:molten_silver_bucket", "tconstruct:molten_nickel_bucket", "tconstruct:molten_zinc_bucket", "tconstruct:molten_platinum_bucket", "tconstruct:molten_tungsten_bucket", "tconstruct:molten_osmium_bucket", "tconstruct:molten_uranium_bucket", "tconstruct:molten_bronze_bucket", "tconstruct:molten_brass_bucket", "tconstruct:molten_electrum_bucket", "tconstruct:molten_invar_bucket", "tconstruct:molten_constantan_bucket", "tconstruct:molten_pewter_bucket", "tconstruct:molten_enderium_bucket", "tconstruct:molten_lumium_bucket", "tconstruct:molten_signalum_bucket", "tconstruct:molten_refined_glowstone_bucket", "tconstruct:molten_refined_obsidian_bucket", "tconstruct:molten_nicrosil_bucket", "tconstruct:sky_slime_spawn_egg", "tconstruct:ender_slime_spawn_egg", "tconstruct:terracube_spawn_egg", "create:chain_conveyor", "create:item_hatch", "create:packager", "create:repackager", "create:package_frogport", "create:white_postbox", "create:orange_postbox", "create:magenta_postbox", "create:light_blue_postbox", "create:yellow_postbox", "create:lime_postbox", "create:pink_postbox", "create:gray_postbox", "create:light_gray_postbox", "create:cyan_postbox", "create:purple_postbox", "create:blue_postbox", "create:brown_postbox", "create:green_postbox", "create:red_postbox", "create:black_postbox", "create:stock_link", "create:stock_ticker", "create:redstone_requester", "create:factory_gauge", "create:white_table_cloth", "create:orange_table_cloth", "create:magenta_table_cloth", "create:light_blue_table_cloth", "create:yellow_table_cloth", "create:lime_table_cloth", "create:pink_table_cloth", "create:gray_table_cloth", "create:light_gray_table_cloth", "create:cyan_table_cloth", "create:purple_table_cloth", "create:blue_table_cloth", "create:brown_table_cloth", "create:green_table_cloth", "create:red_table_cloth", "create:black_table_cloth", "create:andesite_table_cloth", "create:brass_table_cloth", "create:copper_table_cloth", "create:pulse_timer", "create:transmitter", "create:pulp", "create:cardboard", "create:cardboard_sword", "create:cardboard_helmet", "create:cardboard_chestplate", "create:cardboard_leggings", "create:cardboard_boots", "create:cardboard_package_12x12", "create:cardboard_package_10x12", "create:cardboard_package_10x8", "create:cardboard_package_12x10", "create:rare_creeper_package", "create:rare_darcy_package", "create:rare_evan_package", "create:rare_jinx_package", "create:rare_kryppers_package", "create:rare_simi_package", "create:rare_starlotte_package", "create:rare_thunder_package", "create:rare_up_package", "create:rare_vector_package", "create:package_filter", "create:shopping_list", "create:desk_bell", "create:weathered_iron_block", "create:cardboard_block", "create:bound_cardboard_block", "create:cherry_window", "create:bamboo_window", "create:industrial_iron_window", "create:weathered_iron_window", "create:cherry_window_pane", "create:bamboo_window_pane", "create:industrial_iron_window_pane", "create:weathered_iron_window_pane", "undefined:bronze_fluid_tank"]}, "typeDimension": {"type": "string", "enum": ["aerlunerpg:iced", "minecraft:overworld", "minecraft:the_end", "aerlunerpg:crowdd", "minecraft:the_nether"]}, "typeEnchantment": {"type": "string", "enum": ["create:capacity", "create:potato_recovery", "minecraft:aqua_affinity", "minecraft:bane_of_arthropods", "minecraft:binding_curse", "minecraft:blast_protection", "minecraft:channeling", "minecraft:depth_strider", "minecraft:efficiency", "minecraft:feather_falling", "minecraft:fire_aspect", "minecraft:fire_protection", "minecraft:flame", "minecraft:fortune", "minecraft:frost_walker", "minecraft:impaling", "minecraft:infinity", "minecraft:knockback", "minecraft:looting", "minecraft:loyalty", "minecraft:luck_of_the_sea", "minecraft:lure", "minecraft:mending", "minecraft:multishot", "minecraft:piercing", "minecraft:power", "minecraft:projectile_protection", "minecraft:protection", "minecraft:punch", "minecraft:quick_charge", "minecraft:respiration", "minecraft:riptide", "minecraft:sharpness", "minecraft:silk_touch", "minecraft:smite", "minecraft:soul_speed", "minecraft:sweeping", "minecraft:swift_sneak", "minecraft:thorns", "minecraft:unbreaking", "minecraft:vanishing_curse"]}, "typeWorldgenBiomeSource": {"type": "string", "enum": ["minecraft:multi_noise", "minecraft:checkerboard", "minecraft:fixed", "minecraft:the_end", "lionfishapi:modded"]}, "typeWorldgenProcessorList": {"type": "string", "enum": ["minecraft:zombie_snowy", "minecraft:roof", "minecraft:bottom_rampart", "minecraft:entrance_replacement", "minecraft:treasure_rooms", "minecraft:farm_desert", "minecraft:fossil_rot", "minecraft:outpost_rot", "minecraft:zombie_plains", "minecraft:farm_plains", "minecraft:zombie_desert", "minecraft:fossil_coal", "minecraft:trail_ruins_houses_archaeology", "minecraft:rampart_degradation", "minecraft:empty", "minecraft:high_rampart", "minecraft:mossify_70_percent", "minecraft:bastion_generic_degradation", "minecraft:mossify_10_percent", "minecraft:street_snowy_or_taiga", "minecraft:farm_taiga", "minecraft:ancient_city_generic_degradation", "minecraft:ancient_city_walls_degradation", "cataclysm:water_logging_fix", "minecraft:stable_degradation", "minecraft:trail_ruins_tower_top_archaeology", "minecraft:zombie_taiga", "minecraft:street_plains", "minecraft:street_savanna", "minecraft:mossify_20_percent", "minecraft:trail_ruins_roads_archaeology", "minecraft:ancient_city_start_degradation", "minecraft:zombie_savanna", "minecraft:farm_snowy", "minecraft:farm_savanna", "minecraft:fossil_diamonds", "minecraft:housing", "minecraft:side_wall_degradation", "minecraft:bridge", "minecraft:high_wall"]}, "typeWorldgenConfiguredFeature": {"type": "string", "enum": ["minecraft:lake_lava", "aerlunerpg:stump_1", "minecraft:azalea_tree", "minecraft:disk_sand", "mekanism:ore_osmium_middle", "minecraft:patch_large_fern", "minecraft:trees_old_growth_pine_taiga", "minecraft:cave_vine", "minecraft:flower_meadow", "mekanism:ore_tin_small_retrogen", "minecraft:patch_red_mushroom", "tconstruct:cobalt_ore_large", "minecraft:blue_ice", "minecraft:blackstone_blobs", "minecraft:end_gateway_delayed", "minecraft:sea_pickle", "aerlunerpg:oldmanst", "minecraft:patch_crimson_roots", "minecraft:end_island", "minecraft:patch_tall_grass", "minecraft:trees_jungle", "minecraft:amethyst_geode", "minecraft:ore_copper_large", "minecraft:ore_soul_sand", "mekanism:ore_uranium_small_retrogen", "minecraft:fancy_oak_bees_0002", "minecraft:tall_mangrove", "minecraft:flower_default", "tconstruct:ichor_geode", "minecraft:seagrass_mid", "minecraft:meadow_trees", "minecraft:patch_cactus", "kubejs:diamond_block_ore_small", "minecraft:patch_grass", "minecraft:trees_savanna", "minecraft:patch_sunflower", "minecraft:trees_flower_forest", "minecraft:spring_lava_overworld", "minecraft:bamboo_vegetation", "mekanism:salt", "mekanism:ore_lead_normal_retrogen", "minecraft:ice_patch", "tconstruct:sky_slime_island_tree", "minecraft:fossil_coal", "minecraft:ore_andesite", "minecraft:basalt_blobs", "minecraft:mangrove_vegetation", "minecraft:moss_patch_bonemeal", "minecraft:crimson_forest_vegetation", "minecraft:huge_red_mushroom", "minecraft:forest_rock", "minecraft:pointed_dripstone", "minecraft:disk_clay", "minecraft:jungle_bush", "minecraft:underwater_magma", "minecraft:birch_bees_002", "minecraft:sculk_patch_ancient_city", "minecraft:birch_bees_005", "minecraft:flower_flower_forest", "minecraft:large_dripstone", "minecraft:lush_caves_clay", "minecraft:void_start_platform", "aerlunerpg:bushsmall", "mekanism:ore_uranium_small", "minecraft:moss_patch", "minecraft:spring_water", "minecraft:ore_diamond_small", "minecraft:ore_blackstone", "minecraft:birch_tall", "minecraft:fossil_diamonds", "minecraft:mega_jungle_tree", "minecraft:crimson_fungus", "aerlunerpg:crystalsvoids", "tconstruct:blood_slime_island_fungus", "minecraft:forest_flowers", "aerlunerpg:icerion", "minecraft:patch_pumpkin", "immersiveengineering:lead", "minecraft:sculk_patch_deep_dark", "minecraft:spring_lava_frozen", "minecraft:disk_gravel", "minecraft:nether_sprouts", "minecraft:birch_bees_0002", "minecraft:oak", "minecraft:dark_oak", "immersiveengineering:deep_nickel", "aerlunerpg:grasssnow", "tconstruct:blood_slime_fungus", "minecraft:swamp_oak", "minecraft:cherry", "aerlunerpg:smalltreeblock_2", "minecraft:super_birch_bees", "minecraft:twisting_vines_bonemeal", "minecraft:crimson_fungus_planted", "minecraft:seagrass_simple", "create:striated_ores_overworld", "minecraft:trees_taiga", "minecraft:ore_granite", "minecraft:ore_clay", "minecraft:ore_gold", "minecraft:mega_spruce", "tconstruct:cobalt_ore_small", "minecraft:ice_spike", "minecraft:bamboo_some_podzol", "minecraft:acacia", "minecraft:ore_diamond_large", "minecraft:dripstone_cluster", "minecraft:pile_ice", "minecraft:trees_grove", "minecraft:ore_infested", "minecraft:cave_vine_in_moss", "minecraft:super_birch_bees_0002", "minecraft:ore_iron", "mekanism:ore_osmium_small", "minecraft:flower_plain", "minecraft:fancy_oak", "mekanism:ore_osmium_small_retrogen", "mekanism:ore_osmium_upper", "minecraft:kelp", "minecraft:ore_iron_small", "minecraft:ore_gravel", "aerlunerpg:smalltreeblock_1", "minecraft:patch_grass_jungle", "minecraft:ore_diamond_buried", "minecraft:bonus_chest", "minecraft:moss_vegetation", "minecraft:pile_snow", "tconstruct:ichor_slime_fungus", "minecraft:ore_gravel_nether", "aerlunerpg:snowdrop", "tconstruct:ender_geode", "minecraft:patch_melon", "aerlunerpg:stonesmall", "mekanism:ore_osmium_middle_retrogen", "immersiveengineering:silver", "minecraft:patch_brown_mushroom", "minecraft:vines", "mekanism:ore_fluorite_buried", "tconstruct:ender_slime_tree_tall", "immersiveengineering:mineral_veins", "minecraft:fancy_oak_bees_002", "minecraft:spring_nether_open", "minecraft:fancy_oak_bees_005", "minecraft:ore_lapis", "mekanism:ore_fluorite_buried_retrogen", "minecraft:huge_brown_mushroom", "minecraft:iceberg_blue", "minecraft:clay_with_dripleaves", "minecraft:ore_magma", "minecraft:bamboo_no_podzol", "minecraft:trees_water", "mekanism:ore_fluorite_normal_retrogen", "minecraft:freeze_top_layer", "minecraft:ore_diorite", "mekanism:ore_lead_normal", "minecraft:seagrass_short", "minecraft:spore_blossom", "mekanism:ore_tin_large_retrogen", "minecraft:end_spike", "immersiveengineering:nickel", "minecraft:oak_bees_005", "minecraft:ore_lapis_buried", "aerlunerpg:rabbitgoles", "minecraft:birch", "minecraft:glowstone_extra", "minecraft:oak_bees_002", "minecraft:seagrass_tall", "immersiveengineering:bauxite", "minecraft:pile_hay", "minecraft:basalt_pillar", "minecraft:dripleaf", "minecraft:monster_room", "minecraft:jungle_tree", "aerlunerpg:caveice_tree", "minecraft:trees_windswept_hills", "minecraft:ore_ancient_debris_small", "minecraft:patch_soul_fire", "minecraft:spruce", "minecraft:warped_forest_vegetation_bonemeal", "minecraft:moss_patch_ceiling", "tconstruct:earth_slime_island_tree", "minecraft:ore_gold_buried", "minecraft:weeping_vines", "minecraft:jungle_tree_no_vine", "minecraft:spring_nether_closed", "minecraft:mangrove", "tconstruct:earth_slime_tree", "minecraft:nether_sprouts_bonemeal", "tconstruct:ender_slime_tree", "minecraft:ore_tuff", "kubejs:diamond_block_ore", "minecraft:spring_lava_nether", "minecraft:end_gateway_return", "minecraft:small_basalt_columns", "minecraft:trees_birch_and_oak", "minecraft:trees_old_growth_spruce_taiga", "tconstruct:sky_slime_tree", "aerlunerpg:caramelore", "tconstruct:sky_geode", "minecraft:chorus_plant", "minecraft:ore_dirt", "minecraft:desert_well", "minecraft:seagrass_slightly_less_short", "minecraft:patch_fire", "minecraft:fancy_oak_bees", "minecraft:warped_fungus_planted", "create:striated_ores_nether", "minecraft:warm_ocean_vegetation", "minecraft:flower_cherry", "minecraft:ore_nether_gold", "minecraft:delta", "create:zinc_ore", "minecraft:ore_ancient_debris_large", "minecraft:flower_swamp", "mekanism:ore_uranium_buried_retrogen", "minecraft:trees_plains", "mekanism:ore_tin_small", "minecraft:patch_taiga_grass", "minecraft:large_basalt_columns", "minecraft:single_piece_of_grass", "minecraft:warped_forest_vegetation", "minecraft:pile_pumpkin", "tconstruct:earth_geode", "minecraft:twisting_vines", "minecraft:ore_copper_small", "minecraft:patch_waterlily", "minecraft:dark_forest_vegetation", "minecraft:patch_sugar_cane", "minecraft:iceberg_packed", "minecraft:ore_quartz", "minecraft:clay_pool_with_dripleaves", "mekanism:ore_osmium_upper_retrogen", "mekanism:ore_fluorite_normal", "minecraft:sculk_vein", "minecraft:mushroom_island_vegetation", "minecraft:ore_emerald", "minecraft:trees_sparse_jungle", "minecraft:rooted_azalea_tree", "aerlunerpg:treesmall", "minecraft:pine", "minecraft:cherry_bees_005", "minecraft:oak_bees_0002", "minecraft:warped_fungus", "minecraft:glow_lichen", "minecraft:patch_dead_bush", "mekanism:ore_uranium_buried", "aerlunerpg:crystalsmithg", "minecraft:mega_pine", "minecraft:disk_grass", "aerlunerpg:bushs", "minecraft:ore_coal_buried", "minecraft:ore_coal", "minecraft:crimson_forest_vegetation_bonemeal", "minecraft:ore_redstone", "mekanism:ore_tin_large", "minecraft:patch_berry_bush", "minecraft:pile_melon", "immersiveengineering:uranium"]}, "typeWorldgenNoiseSettings": {"type": "string", "enum": ["minecraft:nether", "minecraft:floating_islands", "minecraft:overworld", "minecraft:amplified", "minecraft:caves", "minecraft:end", "minecraft:large_biomes"]}, "typeEntityDataSerializers": {"type": "string", "enum": ["tconstruct:fluid", "immersiveengineering:fluid_stack", "create:carriage_data", "mekanism:robit_skin", "mekanism:security", "mekanism:uuid"]}, "typeMobEffect": {"type": "string", "enum": ["aerlunerpg:boarhitp", "aerlunerpg:cannonpoition", "aerlunerpg:cold", "aerlunerpg:housepotiont", "aerlunerpg:iceshield", "aerlunerpg:parasiteef", "aerlunerpg:<PERSON><PERSON><PERSON><PERSON>", "aerlunerpg:stunninggef", "attributeslib:bleeding", "attributeslib:detonation", "attributeslib:flying", "attributeslib:grievous", "attributeslib:knowledge", "attributeslib:sundering", "attributeslib:vitality", "cataclysm:abyssal_burn", "cataclysm:abyssal_curse", "cataclysm:abyssal_fear", "cataclysm:blazing_brand", "cataclysm:blessing_of_amethyst", "cataclysm:bone_fracture", "cataclysm:curse_of_desert", "cataclysm:ghost_form", "cataclysm:ghost_sickness", "cataclysm:monstrous", "cataclysm:stun", "immersiveengineering:concrete_feet", "immersiveengineering:conductive", "immersiveengineering:flammable", "immersiveengineering:flashed", "immersiveengineering:slippery", "immersiveengineering:sticky", "immersiveengineering:stunned", "minecraft:absorption", "minecraft:bad_omen", "minecraft:blindness", "minecraft:conduit_power", "minecraft:darkness", "minecraft:dolphins_grace", "minecraft:fire_resistance", "minecraft:glowing", "minecraft:haste", "minecraft:health_boost", "minecraft:hero_of_the_village", "minecraft:hunger", "minecraft:instant_damage", "minecraft:instant_health", "minecraft:invisibility", "minecraft:jump_boost", "minecraft:levitation", "minecraft:luck", "minecraft:mining_fatigue", "minecraft:nausea", "minecraft:night_vision", "minecraft:poison", "minecraft:regeneration", "minecraft:resistance", "minecraft:saturation", "minecraft:slow_falling", "minecraft:slowness", "minecraft:speed", "minecraft:strength", "minecraft:unluck", "minecraft:water_breathing", "minecraft:weakness", "minecraft:wither", "cataclysm:wetness", "tconstruct:experienced", "tconstruct:ricochet", "tconstruct:enderference", "tconstruct:bouncy", "tconstruct:double_jump", "tconstruct:antigravity", "tconstruct:returning", "tconstruct:bleeding", "tconstruct:magnetic", "tconstruct:self_destructing", "tconstruct:repulsive", "tconstruct:pierce", "tconstruct:carry", "tconstruct:teleport_cooldown", "tconstruct:fireball_cooldown", "tconstruct:calcified", "tconstruct:momentum_harvest", "tconstruct:momentum_ranged", "tconstruct:momentum_armor", "tconstruct:insatiable_melee", "tconstruct:insatiable_ranged", "tconstruct:insatiable_armor"]}, "typeIntProviderType": {"type": "string", "enum": ["minecraft:uniform", "minecraft:biased_to_bottom", "minecraft:clamped", "mekanism:configurable_constant", "minecraft:clamped_normal", "mekanism:configurable_uniform", "minecraft:constant", "minecraft:weighted_list"]}, "typeCustomStat": {"type": "string", "enum": ["minecraft:play_time", "minecraft:fish_caught", "minecraft:walk_one_cm", "minecraft:damage_dealt_resisted", "minecraft:clean_banner", "minecraft:clean_shulker_box", "minecraft:interact_with_loom", "minecraft:interact_with_campfire", "minecraft:target_hit", "minecraft:inspect_hopper", "minecraft:open_shulker_box", "minecraft:pig_one_cm", "minecraft:play_noteblock", "minecraft:tune_noteblock", "minecraft:trigger_trapped_chest", "minecraft:clean_armor", "minecraft:items_extracted", "minecraft:aviate_one_cm", "minecraft:interact_with_smoker", "minecraft:interact_with_smithing_table", "minecraft:crouch_one_cm", "minecraft:open_chest", "minecraft:jump", "minecraft:mob_kills", "minecraft:fall_one_cm", "minecraft:interact_with_furnace", "minecraft:raid_trigger", "minecraft:interact_with_brewingstand", "minecraft:climb_one_cm", "minecraft:talked_to_villager", "minecraft:sleep_in_bed", "minecraft:damage_resisted", "minecraft:interact_with_cartography_table", "minecraft:pot_flower", "minecraft:raid_win", "minecraft:interact_with_anvil", "minecraft:interact_with_crafting_table", "minecraft:use_cauldron", "minecraft:inspect_dropper", "minecraft:open_barrel", "minecraft:interact_with_lectern", "minecraft:damage_absorbed", "minecraft:deaths", "minecraft:damage_taken", "minecraft:play_record", "minecraft:eat_cake_slice", "minecraft:interact_with_beacon", "minecraft:fill_cauldron", "immersiveengineering:wire_deaths", "minecraft:time_since_rest", "minecraft:walk_under_water_one_cm", "minecraft:enchant_item", "minecraft:inspect_dispenser", "minecraft:minecart_one_cm", "minecraft:strider_one_cm", "minecraft:player_kills", "minecraft:swim_one_cm", "minecraft:damage_blocked_by_shield", "minecraft:sneak_time", "minecraft:fly_one_cm", "minecraft:walk_on_water_one_cm", "minecraft:time_since_death", "minecraft:drop", "immersiveengineering:skyhook_distance", "minecraft:interact_with_grindstone", "minecraft:total_world_time", "minecraft:items_inserted", "minecraft:damage_dealt", "minecraft:interact_with_stonecutter", "minecraft:bell_ring", "minecraft:animals_bred", "minecraft:interact_with_blast_furnace", "minecraft:leave_game", "minecraft:traded_with_villager", "minecraft:damage_dealt_absorbed", "minecraft:boat_one_cm", "minecraft:sprint_one_cm", "minecraft:open_enderchest", "minecraft:horse_one_cm"]}, "typeWorldgenBiome": {"type": "string", "enum": ["minecraft:frozen_ocean", "minecraft:savanna_plateau", "minecraft:taiga", "minecraft:savanna", "minecraft:dripstone_caves", "minecraft:swamp", "minecraft:basalt_deltas", "minecraft:ice_spikes", "minecraft:cherry_grove", "minecraft:crimson_forest", "minecraft:frozen_peaks", "aerlunerpg:iceplate", "minecraft:dark_forest", "minecraft:lush_caves", "minecraft:old_growth_spruce_taiga", "minecraft:deep_dark", "minecraft:frozen_river", "minecraft:lukewarm_ocean", "minecraft:mushroom_fields", "minecraft:warm_ocean", "minecraft:forest", "minecraft:end_midlands", "lionfishapi:original_source_marker", "minecraft:windswept_forest", "minecraft:deep_ocean", "minecraft:sunflower_plains", "minecraft:stony_peaks", "minecraft:stony_shore", "minecraft:nether_wastes", "minecraft:deep_lukewarm_ocean", "minecraft:flower_forest", "minecraft:old_growth_birch_forest", "minecraft:desert", "minecraft:snowy_taiga", "minecraft:beach", "minecraft:grove", "minecraft:deep_frozen_ocean", "minecraft:river", "minecraft:old_growth_pine_taiga", "minecraft:the_void", "minecraft:deep_cold_ocean", "minecraft:windswept_gravelly_hills", "minecraft:snowy_plains", "minecraft:end_highlands", "minecraft:jagged_peaks", "minecraft:eroded_badlands", "aerlunerpg:forestice", "minecraft:bamboo_jungle", "aerlunerpg:darkforest", "minecraft:end_barrens", "minecraft:plains", "minecraft:small_end_islands", "minecraft:meadow", "ae2:spatial_storage", "minecraft:the_end", "minecraft:snowy_beach", "aerlunerpg:ice", "minecraft:sparse_jungle", "minecraft:jungle", "minecraft:snowy_slopes", "minecraft:birch_forest", "aerlunerpg:caveice", "minecraft:mangrove_swamp", "minecraft:ocean", "aerlunerpg:crowdb", "minecraft:cold_ocean", "minecraft:soul_sand_valley", "minecraft:warped_forest", "minecraft:badlands", "minecraft:windswept_hills", "minecraft:windswept_savanna", "minecraft:wooded_badlands"]}, "typeSensorType": {"type": "string", "enum": ["minecraft:dummy", "minecraft:nearest_items", "minecraft:nearest_living_entities", "minecraft:nearest_players", "minecraft:nearest_bed", "minecraft:hurt_by", "minecraft:villager_hostiles", "minecraft:villager_babies", "minecraft:secondary_pois", "minecraft:golem_detected", "minecraft:piglin_specific_sensor", "minecraft:piglin_brute_specific_sensor", "minecraft:hoglin_specific_sensor", "minecraft:nearest_adult", "minecraft:axolotl_attackables", "minecraft:axolotl_temptations", "minecraft:goat_temptations", "minecraft:frog_temptations", "minecraft:camel_temptations", "minecraft:frog_attackables", "minecraft:is_in_water", "minecraft:warden_entity_sensor", "minecraft:sniffer_temptations"]}, "typeWorldgenConfiguredCarver": {"type": "string", "enum": ["minecraft:canyon", "minecraft:cave_extra_underground", "minecraft:nether_cave", "minecraft:cave"]}, "typeBiomeModifier": {"type": "string", "enum": ["immersiveengineering:mineral_veins", "aerlunerpg:crystalsmithg_biome_modifier", "tconstruct:sky_geode", "immersiveengineering:lead", "aerlunerpg:grasssnow_biome_modifier", "aerlunerpg:bushs_biome_modifier", "aerlunerpg:bushsmall_biome_modifier", "fabric_biome_api_v1:fabric_biome_modifier_instance", "immersiveengineering:deep_nickel", "create:striated_ores_nether", "kubejs:add_diamond_block_ore", "create:striated_ores_overworld", "create:zinc_ore", "architectury:impl", "aerlunerpg:donut_biome_modifier", "mekanism:fluorite", "tconstruct:ichor_geode", "immersiveengineering:nickel", "aerlunerpg:snowdrop_biome_modifier", "aerlunerpg:caramelore_biome_modifier", "aerlunerpg:rabbitgoles_biome_modifier", "aerlunerpg:smalltreeblock_2_biome_modifier", "tconstruct:earth_geode", "aerlunerpg:ghost_biome_modifier", "tconstruct:cobalt_ore", "aerlunerpg:zombieghost_biome_modifier", "balm:balm", "mekanism:salt", "aerlunerpg:laser_biome_modifier", "immersiveengineering:bauxite", "mekanism:tin", "entityjs:event_based", "mekanism:uranium", "aerlunerpg:deerevil_biome_modifier", "mekanism:lead", "aerlunerpg:smalltreeblock_1_biome_modifier", "mekanism:osmium", "aerlunerpg:stonesmall_biome_modifier", "tconstruct:spawn_end_slime", "aerlunerpg:treesmall_biome_modifier", "aerlunerpg:crystalsvoids_biome_modifier", "aerlunerpg:oldmanst_biome_modifier", "aerlunerpg:icerion_biome_modifier", "tconstruct:spawn_overworld_slime", "aerlunerpg:stump_1_biome_modifier", "tconstruct:ender_geode", "immersiveengineering:silver", "immersiveengineering:uranium", "cataclysm:cataclysm_mob_spawns"]}, "typeWorldgenStructure": {"type": "string", "enum": ["aerlunerpg:bushsmall_3", "minecraft:desert_pyramid", "ae2:meteorite", "minecraft:ruined_portal_swamp", "minecraft:village_taiga", "aerlunerpg:bushsmall_1", "tconstruct:clay_island", "aerlunerpg:bushsmall_2", "aerlunerpg:midletree_2", "minecraft:ruined_portal_jungle", "minecraft:ocean_ruin_warm", "aerlunerpg:midletree_5", "aerlunerpg:midletree_4", "aerlunerpg:midletree_3", "tconstruct:blood_island", "minecraft:buried_treasure", "tconstruct:end_slime_island", "minecraft:mansion", "minecraft:ruined_portal_desert", "cataclysm:ruined_citadel", "minecraft:igloo", "minecraft:ruined_portal_ocean", "cataclysm:burning_arena", "aerlunerpg:midletree", "aerlunerpg:bigtree_2", "aerlunerpg:bigtree_3", "minecraft:shipwreck_beached", "minecraft:stronghold", "cataclysm:ancient_factory", "aerlunerpg:spooketree", "aerlunerpg:bigtree_8", "minecraft:village_desert", "aerlunerpg:bigtree_4", "aerlunerpg:bigtree_5", "minecraft:ocean_ruin_cold", "aerlunerpg:bigtree_6", "aerlunerpg:bigtree_7", "tconstruct:sky_slime_island", "cataclysm:cursed_pyramid", "minecraft:nether_fossil", "aerlunerpg:candyhome", "minecraft:bastion_remnant", "minecraft:pillager_outpost", "minecraft:shipwreck", "minecraft:jungle_pyramid", "minecraft:mineshaft_mesa", "minecraft:swamp_hut", "minecraft:village_plains", "cataclysm:frosted_prison", "minecraft:ruined_portal", "aerlunerpg:bigtree", "minecraft:end_city", "tconstruct:earth_slime_island", "aerlunerpg:spt_3", "minecraft:mineshaft", "minecraft:monument", "aerlunerpg:smallhouse", "minecraft:ruined_portal_mountain", "minecraft:fortress", "cataclysm:soul_black_smith", "minecraft:trail_ruins", "minecraft:village_savanna", "minecraft:ruined_portal_nether", "aerlunerpg:gnoemtown_1", "minecraft:ancient_city", "cataclysm:sunken_city", "aerlunerpg:spt_2", "cataclysm:acropolis", "aerlunerpg:elkas<PERSON><PERSON>", "minecraft:village_snowy", "aerlunerpg:midleetree_6"]}, "typeWorldgenStructureProcessor": {"type": "string", "enum": ["minecraft:lava_submerged_block", "minecraft:block_ignore", "create:schematic", "minecraft:gravity", "minecraft:rule", "minecraft:blackstone_replace", "minecraft:block_rot", "minecraft:nop", "minecraft:jigsaw_replacement", "minecraft:block_age", "cataclysm:water_logging_fix_processor", "minecraft:capped", "minecraft:protected_blocks"]}, "typeBlockPredicateType": {"type": "string", "enum": ["minecraft:any_of", "minecraft:all_of", "minecraft:not", "minecraft:matching_fluids", "minecraft:would_survive", "minecraft:inside_world_bounds", "minecraft:true", "minecraft:solid", "minecraft:matching_blocks", "minecraft:replaceable", "minecraft:matching_block_tag", "minecraft:has_sturdy_face"]}, "typeStructureModifier": {"type": "string", "enum": ["cataclysm:cataclysm_mob_spawns"]}, "typeWorldgenTrunkPlacerType": {"type": "string", "enum": ["minecraft:straight_trunk_placer", "minecraft:giant_trunk_placer", "minecraft:bending_trunk_placer", "minecraft:cherry_trunk_placer", "minecraft:fancy_trunk_placer", "minecraft:upwards_branching_trunk_placer", "minecraft:forking_trunk_placer", "minecraft:dark_oak_trunk_placer", "minecraft:mega_jungle_trunk_placer"]}, "typeParticleType": {"type": "string", "enum": ["minecraft:ambient_entity_effect", "minecraft:angry_villager", "minecraft:block", "minecraft:block_marker", "minecraft:bubble", "minecraft:cloud", "minecraft:crit", "minecraft:damage_indicator", "minecraft:dragon_breath", "minecraft:dripping_lava", "minecraft:falling_lava", "minecraft:landing_lava", "minecraft:dripping_water", "minecraft:falling_water", "minecraft:dust", "minecraft:dust_color_transition", "minecraft:effect", "minecraft:elder_guardian", "minecraft:enchanted_hit", "minecraft:enchant", "minecraft:end_rod", "minecraft:entity_effect", "minecraft:explosion_emitter", "minecraft:explosion", "minecraft:sonic_boom", "minecraft:falling_dust", "minecraft:firework", "minecraft:fishing", "minecraft:flame", "minecraft:cherry_leaves", "minecraft:sculk_soul", "minecraft:sculk_charge", "minecraft:sculk_charge_pop", "minecraft:soul_fire_flame", "minecraft:soul", "minecraft:flash", "minecraft:happy_villager", "minecraft:composter", "minecraft:heart", "minecraft:instant_effect", "minecraft:item", "minecraft:vibration", "minecraft:item_slime", "minecraft:item_snowball", "minecraft:large_smoke", "minecraft:lava", "minecraft:mycelium", "minecraft:note", "minecraft:poof", "minecraft:portal", "minecraft:rain", "minecraft:smoke", "minecraft:sneeze", "minecraft:spit", "minecraft:squid_ink", "minecraft:sweep_attack", "minecraft:totem_of_undying", "minecraft:underwater", "minecraft:splash", "minecraft:witch", "minecraft:bubble_pop", "minecraft:current_down", "minecraft:bubble_column_up", "minecraft:nautilus", "minecraft:dolphin", "minecraft:campfire_cosy_smoke", "minecraft:campfire_signal_smoke", "minecraft:dripping_honey", "minecraft:falling_honey", "minecraft:landing_honey", "minecraft:falling_nectar", "minecraft:falling_spore_blossom", "minecraft:ash", "minecraft:crimson_spore", "minecraft:warped_spore", "minecraft:spore_blossom_air", "minecraft:dripping_obsidian_tear", "minecraft:falling_obsidian_tear", "minecraft:landing_obsidian_tear", "minecraft:reverse_portal", "minecraft:white_ash", "minecraft:small_flame", "minecraft:snowflake", "minecraft:dripping_dripstone_lava", "minecraft:falling_dripstone_lava", "minecraft:dripping_dripstone_water", "minecraft:falling_dripstone_water", "minecraft:glow_squid_ink", "minecraft:glow", "minecraft:wax_on", "minecraft:wax_off", "minecraft:electric_spark", "minecraft:scrape", "minecraft:shriek", "minecraft:egg_crack", "ae2:crafting_fx", "ae2:energy_fx", "ae2:lightning_arc_fx", "ae2:lightning_fx", "ae2:matter_cannon_fx", "ae2:vibrant_fx", "aerlunerpg:mithpar", "aerlunerpg:smallbeatlepar", "aerlunerpg:acidpar", "aerlunerpg:icerodpar", "aerlunerpg:darkflamepar", "aerlunerpg:icefirepar", "aerlunerpg:cloud", "aerlunerpg:blood", "aerlunerpg:elkpar", "aerlunerpg:magicpar", "cataclysm:soul_lava", "cataclysm:sandstorm", "cataclysm:lightning", "cataclysm:storm", "cataclysm:light_trail", "cataclysm:track_lightning", "cataclysm:ring_0", "cataclysm:roar", "cataclysm:circle_lightning", "cataclysm:custom_poof", "cataclysm:rain_fog", "cataclysm:gathering_water", "cataclysm:lightning_zap", "cataclysm:lightning_storm", "cataclysm:scylla_swing", "cataclysm:spin_trail", "cataclysm:spark_trail", "cataclysm:dust_pillar", "cataclysm:not_spin_particle", "cataclysm:spark", "cataclysm:rain_cloud", "cataclysm:cursed_flame", "cataclysm:small_cursed_flame", "cataclysm:phantom_wing_flame", "cataclysm:em_pulse", "cataclysm:shock_wave", "cataclysm:trap_flame", "cataclysm:flame_jet", "cataclysm:flare_explode", "cataclysm:lightning_explode", "cataclysm:ignis_explode", "cataclysm:ignis_abyss_explode", "cataclysm:ignis_soul_explode", "cataclysm:desert_glyph", "attributeslib:apoth_crit", "tconstruct:fluid", "tconstruct:sky_slime", "tconstruct:ender_slime", "tconstruct:terracube", "tconstruct:hammer_attack", "tconstruct:axe_attack", "tconstruct:bonk", "immersiveengineering:fluid_splash", "immersiveengineering:fractal", "immersiveengineering:ie_bubble", "immersiveengineering:sparks", "create:rotation_indicator", "create:air_flow", "create:air", "create:steam_jet", "create:cube", "create:fluid_particle", "create:basin_fluid", "create:fluid_drip", "create:wifi", "create:soul", "create:soul_base", "create:soul_perimeter", "create:soul_expanding_perimeter", "mekanism:laser", "mekanism:jetpack_flame", "mekanism:jetpack_smoke", "mekanism:scuba_bubble", "mekanism:radiation"]}, "typeChunkStatus": {"type": "string", "enum": ["minecraft:empty", "minecraft:structure_starts", "minecraft:structure_references", "minecraft:biomes", "minecraft:noise", "minecraft:surface", "minecraft:carvers", "minecraft:features", "minecraft:initialize_light", "minecraft:light", "minecraft:spawn", "minecraft:full"]}, "typeWorldgenMultiNoiseBiomeSourceParameterList": {"type": "string", "enum": ["minecraft:nether", "minecraft:overworld"]}, "typeRobitSkin": {"type": "string", "enum": ["mekanism:aro", "mekanism:agender", "mekanism:genderfluid", "mekanism:pan", "mekanism:gay", "mekanism:allay", "mekanism:enby", "mekanism:trans", "mekanism:robit", "mekanism:ace", "mekanism:lesbian", "mekanism:bi", "mekanism:pride"]}, "typeWorldgenMaterialRule": {"type": "string", "enum": ["minecraft:bandlands", "minecraft:condition", "minecraft:sequence", "minecraft:block"]}, "typeCommandArgumentType": {"type": "string", "enum": ["brigadier:bool", "brigadier:float", "brigadier:double", "brigadier:integer", "brigadier:long", "brigadier:string", "minecraft:entity", "minecraft:game_profile", "minecraft:block_pos", "minecraft:column_pos", "minecraft:vec3", "minecraft:vec2", "minecraft:block_state", "minecraft:block_predicate", "minecraft:item_stack", "minecraft:item_predicate", "minecraft:color", "minecraft:component", "minecraft:message", "minecraft:nbt_compound_tag", "minecraft:nbt_tag", "minecraft:nbt_path", "minecraft:objective", "minecraft:objective_criteria", "minecraft:operation", "minecraft:particle", "minecraft:angle", "minecraft:rotation", "minecraft:scoreboard_slot", "minecraft:score_holder", "minecraft:swizzle", "minecraft:team", "minecraft:item_slot", "minecraft:resource_location", "minecraft:function", "minecraft:entity_anchor", "minecraft:int_range", "minecraft:float_range", "minecraft:dimension", "minecraft:gamemode", "minecraft:time", "minecraft:resource_or_tag", "minecraft:resource_or_tag_key", "minecraft:resource", "minecraft:resource_key", "minecraft:template_mirror", "minecraft:template_rotation", "minecraft:heightmap", "minecraft:uuid", "minecraft:test_argument", "minecraft:test_class", "curios:slot_type", "ftbteams:team", "ftbteams:team_property", "guideme:guide_id", "guideme:page_anchor", "forge:enum", "forge:modid", "tconstruct:slot_type", "tconstruct:tool_stat", "tconstruct:modifier", "tconstruct:material", "tconstruct:material_variant", "tconstruct:material_stat", "tconstruct:modifier_hook", "ftbteams:change_progress", "ftbteams:quest_object", "immersiveengineering:mineral", "ponder:config_path"]}, "typeRecipeType": {"type": "string", "enum": ["minecraft:crafting", "minecraft:smelting", "minecraft:blasting", "minecraft:smoking", "minecraft:campfire_cooking", "minecraft:stonecutting", "minecraft:smithing", "cataclysm:weapon_fusion", "cataclysm:amethyst_bless", "tconstruct:part_builder", "tconstruct:material", "tconstruct:tinker_station", "tconstruct:modifier_worktable", "tconstruct:casting_basin", "tconstruct:casting_table", "tconstruct:molding_table", "tconstruct:molding_basin", "tconstruct:melting", "tconstruct:entity_melting", "tconstruct:fuel", "tconstruct:alloying", "tconstruct:severing", "tconstruct:data", "immersiveengineering:alloy", "immersiveengineering:arc_furnace", "immersiveengineering:blast_furnace_fuel", "immersiveengineering:blast_furnace", "immersiveengineering:blueprint", "immersiveengineering:bottling_machine", "immersiveengineering:fertilizer", "immersiveengineering:cloche", "immersiveengineering:coke_oven", "immersiveengineering:crusher", "immersiveengineering:fermenter", "immersiveengineering:metal_press", "immersiveengineering:mixer", "immersiveengineering:refinery", "immersiveengineering:sawmill", "immersiveengineering:squeezer", "immersiveengineering:mineral_mix", "immersiveengineering:generator_fuel", "immersiveengineering:thermoelectric_source", "immersiveengineering:windmill_biome", "createoreexcavation:drilling", "createoreexcavation:extracting", "createoreexcavation:vein", "create:conversion", "create:crushing", "create:cutting", "create:milling", "create:basin", "create:mixing", "create:compacting", "create:pressing", "create:sandpaper_polishing", "create:splashing", "create:haunting", "create:deploying", "create:filling", "create:emptying", "create:item_application", "create:mechanical_crafting", "create:sequenced_assembly", "almostunified:client_recipe_tracker", "ae2:transform", "ae2:entropy", "ae2:inscriber", "ae2:charger", "ae2:matter_cannon", "mekanism:crushing", "mekanism:enriching", "mekanism:smelting", "mekanism:chemical_infusing", "mekanism:combining", "mekanism:separating", "mekanism:washing", "mekanism:evaporating", "mekanism:activating", "mekanism:centrifuging", "mekanism:crystallizing", "mekanism:dissolution", "mekanism:compressing", "mekanism:purifying", "mekanism:injecting", "mekanism:nucleosynthesizing", "mekanism:energy_conversion", "mekanism:gas_conversion", "mekanism:oxidizing", "mekanism:infusion_conversion", "mekanism:pigment_extracting", "mekanism:pigment_mixing", "mekanism:metallurgic_infusing", "mekanism:painting", "mekanism:reaction", "mekanism:rotary", "mekanism:sawing"]}, "typeSoundEvent": {"type": "string", "enum": ["ae2:guide.click", "aerlunerpg:air", "aerlunerpg:armitem", "aerlunerpg:bighit", "aerlunerpg:bosses", "aerlunerpg:cannon", "aerlunerpg:darkmagic", "aerlunerpg:deerhit", "aerlunerpg:demonhit", "aerlunerpg:demonhit2", "aerlunerpg:ent3", "aerlunerpg:ent_big1", "aerlunerpg:ent_big2", "aerlunerpg:ent_hirt", "aerlunerpg:entbigdie", "aerlunerpg:entsmall", "aerlunerpg:epic", "aerlunerpg:epic2", "aerlunerpg:fireball", "aerlunerpg:freeze", "aerlunerpg:ghostdie", "aerlunerpg:ghosthit", "aerlunerpg:gnomehit", "aerlunerpg:golem_hit", "aerlunerpg:gui", "aerlunerpg:gun", "aerlunerpg:gun2", "aerlunerpg:househit", "aerlunerpg:icerod_shoot", "aerlunerpg:money", "aerlunerpg:ossfight", "aerlunerpg:scrollcast", "aerlunerpg:smallbeatle_laser", "aerlunerpg:spawn", "aerlunerpg:speak", "aerlunerpg:speak2", "aerlunerpg:toilet", "aerlunerpg:walkbig", "attributeslib:dodge", "bettercombat:anchor_slam", "bettercombat:axe_slash", "bettercombat:claymore_slam", "bettercombat:claymore_stab", "bettercombat:claymore_swing", "bettercombat:dagger_slash", "bettercombat:double_axe_swing", "bettercombat:fist_punch", "bettercombat:glaive_slash_quick", "bettercombat:glaive_slash_slow", "bettercombat:hammer_slam", "bettercombat:katana_slash", "bettercombat:mace_slam", "bettercombat:mace_slash", "bettercombat:pickaxe_swing", "bettercombat:rapier_slash", "bettercombat:rapier_stab", "bettercombat:scythe_slash", "bettercombat:sickle_slash", "bettercombat:spear_stab", "bettercombat:staff_slam", "bettercombat:staff_slash", "bettercombat:staff_spin", "bettercombat:staff_stab", "bettercombat:sword_slash", "bettercombat:wand_swing", "cataclysm:abyss_blast", "cataclysm:abyss_blast_only_charge", "cataclysm:abyss_blast_only_shoot", "cataclysm:aptrgangr_death", "cataclysm:aptrgangr_hurt", "cataclysm:aptrgangr_idle", "cataclysm:axe_swing", "cataclysm:black_hole_closing", "cataclysm:black_hole_loop", "cataclysm:black_hole_opening", "cataclysm:coral_golem_death", "cataclysm:coral_golem_hurt", "cataclysm:coralssus_ambient", "cataclysm:coralssus_death", "cataclysm:coral<PERSON><PERSON>_hurt", "cataclysm:coralssus_roar", "cataclysm:crab_bite", "cataclysm:crab_death", "cataclysm:crab_hurt", "cataclysm:death_laser", "cataclysm:deepling_death", "cataclysm:deepling_hurt", "cataclysm:deepling_idle", "cataclysm:deepling_light", "cataclysm:deepling_swing", "cataclysm:door_of_seal_open", "cataclysm:draugr_death", "cataclysm:draugr_hurt", "cataclysm:draugr_idle", "cataclysm:emp_activated", "cataclysm:enderguardian_music", "cataclysm:end<PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysm:ender<PERSON><PERSON><PERSON><PERSON>", "cataclysm:end<PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysm:endermaptera_ambient", "cataclysm:endermaptera_death", "cataclysm:ender<PERSON><PERSON>_hurt", "cataclysm:endermaptera_step", "cataclysm:flame_burst", "cataclysm:flame_trap", "cataclysm:flamethrower", "cataclysm:golemattack", "cataclysm:golem<PERSON>th", "cataclysm:go<PERSON><PERSON><PERSON>", "cataclysm:hammertime", "cataclysm:harbinger_charge", "cataclysm:harbinger_charge_prepare", "cataclysm:harbinger_deathlaser_prepare", "cataclysm:harbinger_hurt", "cataclysm:harbinger_idle", "cataclysm:harbinger_laser", "cataclysm:harbinger_mode_change", "cataclysm:harbinger_music", "cataclysm:harbinger_prepare", "cataclysm:harbinger_stun", "cataclysm:ignis_ambient", "cataclysm:ignis_armor_break", "cataclysm:ignis_death", "cataclysm:ignis_earthquake", "cataclysm:ignis_hurt", "cataclysm:ignis_impact", "cataclysm:ignis_music", "cataclysm:ignis_poke", "cataclysm:ignisshieldbreak", "cataclysm:kobolediator_ambient", "cataclysm:kobolediator_death", "cataclysm:kobolediator_hurt", "cataclysm:koboleton_ambient", "cataclysm:koboleton_death", "cataclysm:koboleton_hurt", "cataclysm:koboleton_step", "cataclysm:leviathan_bite", "cataclysm:leviathan_defeat", "cataclysm:leviathan_hurt", "cataclysm:leviathan_idle", "cataclysm:leviathan_music", "cataclysm:leviathan_music_1", "cataclysm:leviathan_music_2", "cataclysm:leviathan_roar", "cataclysm:leviathan_stun_roar", "cataclysm:leviathan_tentacle_strike", "cataclysm:maledictus_battle_cry", "cataclysm:maledictus_bow_pull", "cataclysm:maledictus_death", "cataclysm:maledictus_hurt", "cataclysm:maledictus_idle", "cataclysm:maledictus_jump", "cataclysm:maledictus_leap", "cataclysm:maledictus_mace_swing", "cataclysm:maledictus_music", "cataclysm:maledictus_music_disc", "cataclysm:maledictus_short_roar", "cataclysm:male<PERSON>us_spear", "cataclysm:modern_remnant_bite", "cataclysm:modern_remnant_death", "cataclysm:modern_remnant_fill_bucket", "cataclysm:monstrosity_music", "cataclysm:monstrosityawaken", "cataclysm:monstrositydeath", "cataclysm:monstrositygrowl", "cataclysm:monstrosity<PERSON>t", "cataclysm:monstrosityland", "cataclysm:monstrosityshoot", "cataclysm:monstrositystep", "cataclysm:portal_abyss_blast", "cataclysm:prowler_death", "cataclysm:prowler_hurt", "cataclysm:prowler_idle", "cataclysm:prowler_saw_attack", "cataclysm:prowler_saw_spin_attack", "cataclysm:remnant_bite", "cataclysm:remnant_breathing", "cataclysm:remnant_charge_prepare", "cataclysm:remnant_charge_roar", "cataclysm:remnant_charge_step", "cataclysm:remnant_death", "cataclysm:remnant_hurt", "cataclysm:remnant_idle", "cataclysm:remnant_music", "cataclysm:remnant_roar", "cataclysm:remnant_shockwave", "cataclysm:remnant_stomp", "cataclysm:remnant_tail_slam", "cataclysm:remnant_tail_slam_1", "cataclysm:remnant_tail_slam_2", "cataclysm:remnant_tail_slam_3", "cataclysm:remnant_tail_swing", "cataclysm:revenant_breath", "cataclysm:revenant_death", "cataclysm:revenant_hurt", "cataclysm:revenant_idle", "cataclysm:rocket_launch", "cataclysm:sandstorm", "cataclysm:shredder_end", "cataclysm:shredder_loop", "cataclysm:shredder_start", "cataclysm:strongswing", "cataclysm:swing", "cataclysm:sword_stomp", "cataclysm:tidal_hook_hit", "cataclysm:tidal_hook_loop", "cataclysm:tidal_tentacle", "cataclysm:voidrunerising", "cataclysm:wadjet_ambient", "cataclysm:wadjet_death", "cataclysm:wadjet_hurt", "cataclysm:watcher_death", "cataclysm:watcher_hurt", "create:blaze_munch", "create:chiff", "create:clipboard_check", "create:clipboard_erase", "create:cogs", "create:confirm", "create:contraption_assemble", "create:contraption_assemble_compounded_1", "create:contraption_disassemble", "create:controller_click", "create:controller_put", "create:controller_take", "create:copper_armor_equip", "create:crafter_click", "create:crafter_click_compounded_1", "create:crafter_craft", "create:cranking", "create:cranking_compounded_1", "create:crushing_1", "create:crushing_2", "create:crushing_3", "create:deny", "create:depot_plop", "create:depot_slide", "create:funnel_flap", "create:funnel_flap_compounded_1", "create:fwoomp", "create:haunted_bell_convert", "create:haunted_bell_use", "create:mechanical_press_activation", "create:mechanical_press_activation_belt", "create:mechanical_press_activation_belt_compounded_1", "create:mechanical_press_activation_compounded_1", "create:mixing", "create:mixing_compounded_1", "create:peculiar_bell_use", "create:potato_hit", "create:potato_hit_compounded_1", "create:sanding_long", "create:sanding_short", "create:saw_activate_stone", "create:saw_activate_wood", "create:schematicannon_finish", "create:schematicannon_launch_block", "create:scroll_value", "create:slime_added", "create:spout", "create:steam", "create:train", "create:train2", "create:train3", "create:whistle", "create:whistle_high", "create:whistle_low", "create:whistle_train", "create:whistle_train_low", "create:whistle_train_manual", "create:whistle_train_manual_end", "create:whistle_train_manual_low", "create:whistle_train_manual_low_end", "create:worldshaper_place", "create:wrench_remove", "create:wrench_remove_compounded_1", "create:wrench_rotate", "immersiveengineering:arc_furnace", "immersiveengineering:assembler", "immersiveengineering:birthday_party", "immersiveengineering:bottling", "immersiveengineering:charge_fast", "immersiveengineering:charge_slow", "immersiveengineering:chute", "immersiveengineering:crusher", "immersiveengineering:diesel_generator", "immersiveengineering:dire_switch", "immersiveengineering:electromagnet", "immersiveengineering:fermenter", "immersiveengineering:glider", "immersiveengineering:metal_press_piston", "immersiveengineering:metal_press_smash", "immersiveengineering:mixer", "immersiveengineering:ore_conveyor", "immersiveengineering:ore_dump", "immersiveengineering:preheater", "immersiveengineering:process_1", "immersiveengineering:process_1_lift", "immersiveengineering:process_2", "immersiveengineering:process_2_lift", "immersiveengineering:railgun_fire", "immersiveengineering:refinery", "immersiveengineering:revolver_fire", "immersiveengineering:revolver_fire_thump", "immersiveengineering:revolver_reload", "immersiveengineering:saw_empty", "immersiveengineering:saw_full", "immersiveengineering:saw_shutdown", "immersiveengineering:saw_startup", "immersiveengineering:spark", "immersiveengineering:spray", "immersiveengineering:spray_fire", "immersiveengineering:tesla", "mekanism:gui.digital_beep", "mekanism:item.flamethrower.active", "mekanism:item.flamethrower.idle", "mekanism:item.geiger_elevated", "mekanism:item.geiger_fast", "mekanism:item.geiger_medium", "mekanism:item.geiger_slow", "mekanism:item.gravitational_modulation_unit", "mekanism:item.hydraulic", "mekanism:item.jetpack", "mekanism:item.scuba_mask", "mekanism:tile.christmas.1", "mekanism:tile.christmas.2", "mekanism:tile.christmas.3", "mekanism:tile.christmas.4", "mekanism:tile.christmas.5", "mekanism:tile.machine.antiprotonic_nucleosynthesizer", "mekanism:tile.machine.chargepad", "mekanism:tile.machine.chemical_crystallizer", "mekanism:tile.machine.chemical_dissolution_chamber", "mekanism:tile.machine.chemical_infuser", "mekanism:tile.machine.chemical_injection_chamber", "mekanism:tile.machine.chemical_oxidizer", "mekanism:tile.machine.chemical_washer", "mekanism:tile.machine.combiner", "mekanism:tile.machine.compressor", "mekanism:tile.machine.crusher", "mekanism:tile.machine.electrolytic_separator", "mekanism:tile.machine.energized_smelter", "mekanism:tile.machine.enrichment_chamber", "mekanism:tile.machine.industrial_alarm", "mekanism:tile.machine.isotopic_centrifuge", "mekanism:tile.machine.laser", "mekanism:tile.machine.logistical_sorter", "mekanism:tile.machine.metallurgic_infuser", "mekanism:tile.machine.nutritional_liquifier", "mekanism:tile.machine.painting_machine", "mekanism:tile.machine.pigment_extractor", "mekanism:tile.machine.pigment_mixer", "mekanism:tile.machine.precision_sawmill", "mekanism:tile.machine.pressurized_reaction_chamber", "mekanism:tile.machine.purification_chamber", "mekanism:tile.machine.resistive_heater", "mekanism:tile.machine.rotary_condensentrator", "mekanism:tile.machine.sps", "minecraft:ambient.basalt_deltas.additions", "minecraft:ambient.basalt_deltas.loop", "minecraft:ambient.basalt_deltas.mood", "minecraft:ambient.cave", "minecraft:ambient.crimson_forest.additions", "minecraft:ambient.crimson_forest.loop", "minecraft:ambient.crimson_forest.mood", "minecraft:ambient.nether_wastes.additions", "minecraft:ambient.nether_wastes.loop", "minecraft:ambient.nether_wastes.mood", "minecraft:ambient.soul_sand_valley.additions", "minecraft:ambient.soul_sand_valley.loop", "minecraft:ambient.soul_sand_valley.mood", "minecraft:ambient.underwater.enter", "minecraft:ambient.underwater.exit", "minecraft:ambient.underwater.loop", "minecraft:ambient.underwater.loop.additions", "minecraft:ambient.underwater.loop.additions.rare", "minecraft:ambient.underwater.loop.additions.ultra_rare", "minecraft:ambient.warped_forest.additions", "minecraft:ambient.warped_forest.loop", "minecraft:ambient.warped_forest.mood", "minecraft:block.amethyst_block.break", "minecraft:block.amethyst_block.chime", "minecraft:block.amethyst_block.fall", "minecraft:block.amethyst_block.hit", "minecraft:block.amethyst_block.place", "minecraft:block.amethyst_block.resonate", "minecraft:block.amethyst_block.step", "minecraft:block.amethyst_cluster.break", "minecraft:block.amethyst_cluster.fall", "minecraft:block.amethyst_cluster.hit", "minecraft:block.amethyst_cluster.place", "minecraft:block.amethyst_cluster.step", "minecraft:block.ancient_debris.break", "minecraft:block.ancient_debris.fall", "minecraft:block.ancient_debris.hit", "minecraft:block.ancient_debris.place", "minecraft:block.ancient_debris.step", "minecraft:block.anvil.break", "minecraft:block.anvil.destroy", "minecraft:block.anvil.fall", "minecraft:block.anvil.hit", "minecraft:block.anvil.land", "minecraft:block.anvil.place", "minecraft:block.anvil.step", "minecraft:block.anvil.use", "minecraft:block.azalea.break", "minecraft:block.azalea.fall", "minecraft:block.azalea.hit", "minecraft:block.azalea.place", "minecraft:block.azalea.step", "minecraft:block.azalea_leaves.break", "minecraft:block.azalea_leaves.fall", "minecraft:block.azalea_leaves.hit", "minecraft:block.azalea_leaves.place", "minecraft:block.azalea_leaves.step", "minecraft:block.bamboo.break", "minecraft:block.bamboo.fall", "minecraft:block.bamboo.hit", "minecraft:block.bamboo.place", "minecraft:block.bamboo.step", "minecraft:block.bamboo_sapling.break", "minecraft:block.bamboo_sapling.hit", "minecraft:block.bamboo_sapling.place", "minecraft:block.bamboo_wood.break", "minecraft:block.bamboo_wood.fall", "minecraft:block.bamboo_wood.hit", "minecraft:block.bamboo_wood.place", "minecraft:block.bamboo_wood.step", "minecraft:block.bamboo_wood_button.click_off", "minecraft:block.bamboo_wood_button.click_on", "minecraft:block.bamboo_wood_door.close", "minecraft:block.bamboo_wood_door.open", "minecraft:block.bamboo_wood_fence_gate.close", "minecraft:block.bamboo_wood_fence_gate.open", "minecraft:block.bamboo_wood_hanging_sign.break", "minecraft:block.bamboo_wood_hanging_sign.fall", "minecraft:block.bamboo_wood_hanging_sign.hit", "minecraft:block.bamboo_wood_hanging_sign.place", "minecraft:block.bamboo_wood_hanging_sign.step", "minecraft:block.bamboo_wood_pressure_plate.click_off", "minecraft:block.bamboo_wood_pressure_plate.click_on", "minecraft:block.bamboo_wood_trapdoor.close", "minecraft:block.bamboo_wood_trapdoor.open", "minecraft:block.barrel.close", "minecraft:block.barrel.open", "minecraft:block.basalt.break", "minecraft:block.basalt.fall", "minecraft:block.basalt.hit", "minecraft:block.basalt.place", "minecraft:block.basalt.step", "minecraft:block.beacon.activate", "minecraft:block.beacon.ambient", "minecraft:block.beacon.deactivate", "minecraft:block.beacon.power_select", "minecraft:block.beehive.drip", "minecraft:block.beehive.enter", "minecraft:block.beehive.exit", "minecraft:block.beehive.shear", "minecraft:block.beehive.work", "minecraft:block.bell.resonate", "minecraft:block.bell.use", "minecraft:block.big_dripleaf.break", "minecraft:block.big_dripleaf.fall", "minecraft:block.big_dripleaf.hit", "minecraft:block.big_dripleaf.place", "minecraft:block.big_dripleaf.step", "minecraft:block.big_dripleaf.tilt_down", "minecraft:block.big_dripleaf.tilt_up", "minecraft:block.blastfurnace.fire_crackle", "minecraft:block.bone_block.break", "minecraft:block.bone_block.fall", "minecraft:block.bone_block.hit", "minecraft:block.bone_block.place", "minecraft:block.bone_block.step", "minecraft:block.brewing_stand.brew", "minecraft:block.bubble_column.bubble_pop", "minecraft:block.bubble_column.upwards_ambient", "minecraft:block.bubble_column.upwards_inside", "minecraft:block.bubble_column.whirlpool_ambient", "minecraft:block.bubble_column.whirlpool_inside", "minecraft:block.cake.add_candle", "minecraft:block.calcite.break", "minecraft:block.calcite.fall", "minecraft:block.calcite.hit", "minecraft:block.calcite.place", "minecraft:block.calcite.step", "minecraft:block.campfire.crackle", "minecraft:block.candle.ambient", "minecraft:block.candle.break", "minecraft:block.candle.extinguish", "minecraft:block.candle.fall", "minecraft:block.candle.hit", "minecraft:block.candle.place", "minecraft:block.candle.step", "minecraft:block.cave_vines.break", "minecraft:block.cave_vines.fall", "minecraft:block.cave_vines.hit", "minecraft:block.cave_vines.pick_berries", "minecraft:block.cave_vines.place", "minecraft:block.cave_vines.step", "minecraft:block.chain.break", "minecraft:block.chain.fall", "minecraft:block.chain.hit", "minecraft:block.chain.place", "minecraft:block.chain.step", "minecraft:block.cherry_leaves.break", "minecraft:block.cherry_leaves.fall", "minecraft:block.cherry_leaves.hit", "minecraft:block.cherry_leaves.place", "minecraft:block.cherry_leaves.step", "minecraft:block.cherry_sapling.break", "minecraft:block.cherry_sapling.fall", "minecraft:block.cherry_sapling.hit", "minecraft:block.cherry_sapling.place", "minecraft:block.cherry_sapling.step", "minecraft:block.cherry_wood.break", "minecraft:block.cherry_wood.fall", "minecraft:block.cherry_wood.hit", "minecraft:block.cherry_wood.place", "minecraft:block.cherry_wood.step", "minecraft:block.cherry_wood_button.click_off", "minecraft:block.cherry_wood_button.click_on", "minecraft:block.cherry_wood_door.close", "minecraft:block.cherry_wood_door.open", "minecraft:block.cherry_wood_fence_gate.close", "minecraft:block.cherry_wood_fence_gate.open", "minecraft:block.cherry_wood_hanging_sign.break", "minecraft:block.cherry_wood_hanging_sign.fall", "minecraft:block.cherry_wood_hanging_sign.hit", "minecraft:block.cherry_wood_hanging_sign.place", "minecraft:block.cherry_wood_hanging_sign.step", "minecraft:block.cherry_wood_pressure_plate.click_off", "minecraft:block.cherry_wood_pressure_plate.click_on", "minecraft:block.cherry_wood_trapdoor.close", "minecraft:block.cherry_wood_trapdoor.open", "minecraft:block.chest.close", "minecraft:block.chest.locked", "minecraft:block.chest.open", "minecraft:block.chiseled_bookshelf.break", "minecraft:block.chiseled_bookshelf.fall", "minecraft:block.chiseled_bookshelf.hit", "minecraft:block.chiseled_bookshelf.insert", "minecraft:block.chiseled_bookshelf.insert.enchanted", "minecraft:block.chiseled_bookshelf.pickup", "minecraft:block.chiseled_bookshelf.pickup.enchanted", "minecraft:block.chiseled_bookshelf.place", "minecraft:block.chiseled_bookshelf.step", "minecraft:block.chorus_flower.death", "minecraft:block.chorus_flower.grow", "minecraft:block.comparator.click", "minecraft:block.composter.empty", "minecraft:block.composter.fill", "minecraft:block.composter.fill_success", "minecraft:block.composter.ready", "minecraft:block.conduit.activate", "minecraft:block.conduit.ambient", "minecraft:block.conduit.ambient.short", "minecraft:block.conduit.attack.target", "minecraft:block.conduit.deactivate", "minecraft:block.copper.break", "minecraft:block.copper.fall", "minecraft:block.copper.hit", "minecraft:block.copper.place", "minecraft:block.copper.step", "minecraft:block.coral_block.break", "minecraft:block.coral_block.fall", "minecraft:block.coral_block.hit", "minecraft:block.coral_block.place", "minecraft:block.coral_block.step", "minecraft:block.crop.break", "minecraft:block.decorated_pot.break", "minecraft:block.decorated_pot.fall", "minecraft:block.decorated_pot.hit", "minecraft:block.decorated_pot.place", "minecraft:block.decorated_pot.shatter", "minecraft:block.decorated_pot.step", "minecraft:block.deepslate.break", "minecraft:block.deepslate.fall", "minecraft:block.deepslate.hit", "minecraft:block.deepslate.place", "minecraft:block.deepslate.step", "minecraft:block.deepslate_bricks.break", "minecraft:block.deepslate_bricks.fall", "minecraft:block.deepslate_bricks.hit", "minecraft:block.deepslate_bricks.place", "minecraft:block.deepslate_bricks.step", "minecraft:block.deepslate_tiles.break", "minecraft:block.deepslate_tiles.fall", "minecraft:block.deepslate_tiles.hit", "minecraft:block.deepslate_tiles.place", "minecraft:block.deepslate_tiles.step", "minecraft:block.dispenser.dispense", "minecraft:block.dispenser.fail", "minecraft:block.dispenser.launch", "minecraft:block.dripstone_block.break", "minecraft:block.dripstone_block.fall", "minecraft:block.dripstone_block.hit", "minecraft:block.dripstone_block.place", "minecraft:block.dripstone_block.step", "minecraft:block.enchantment_table.use", "minecraft:block.end_gateway.spawn", "minecraft:block.end_portal.spawn", "minecraft:block.end_portal_frame.fill", "minecraft:block.ender_chest.close", "minecraft:block.ender_chest.open", "minecraft:block.fence_gate.close", "minecraft:block.fence_gate.open", "minecraft:block.fire.ambient", "minecraft:block.fire.extinguish", "minecraft:block.flowering_azalea.break", "minecraft:block.flowering_azalea.fall", "minecraft:block.flowering_azalea.hit", "minecraft:block.flowering_azalea.place", "minecraft:block.flowering_azalea.step", "minecraft:block.froglight.break", "minecraft:block.froglight.fall", "minecraft:block.froglight.hit", "minecraft:block.froglight.place", "minecraft:block.froglight.step", "minecraft:block.frogspawn.break", "minecraft:block.frogspawn.fall", "minecraft:block.frogspawn.hatch", "minecraft:block.frogspawn.hit", "minecraft:block.frogspawn.place", "minecraft:block.frogspawn.step", "minecraft:block.fungus.break", "minecraft:block.fungus.fall", "minecraft:block.fungus.hit", "minecraft:block.fungus.place", "minecraft:block.fungus.step", "minecraft:block.furnace.fire_crackle", "minecraft:block.gilded_blackstone.break", "minecraft:block.gilded_blackstone.fall", "minecraft:block.gilded_blackstone.hit", "minecraft:block.gilded_blackstone.place", "minecraft:block.gilded_blackstone.step", "minecraft:block.glass.break", "minecraft:block.glass.fall", "minecraft:block.glass.hit", "minecraft:block.glass.place", "minecraft:block.glass.step", "minecraft:block.grass.break", "minecraft:block.grass.fall", "minecraft:block.grass.hit", "minecraft:block.grass.place", "minecraft:block.grass.step", "minecraft:block.gravel.break", "minecraft:block.gravel.fall", "minecraft:block.gravel.hit", "minecraft:block.gravel.place", "minecraft:block.gravel.step", "minecraft:block.grindstone.use", "minecraft:block.growing_plant.crop", "minecraft:block.hanging_roots.break", "minecraft:block.hanging_roots.fall", "minecraft:block.hanging_roots.hit", "minecraft:block.hanging_roots.place", "minecraft:block.hanging_roots.step", "minecraft:block.hanging_sign.break", "minecraft:block.hanging_sign.fall", "minecraft:block.hanging_sign.hit", "minecraft:block.hanging_sign.place", "minecraft:block.hanging_sign.step", "minecraft:block.honey_block.break", "minecraft:block.honey_block.fall", "minecraft:block.honey_block.hit", "minecraft:block.honey_block.place", "minecraft:block.honey_block.slide", "minecraft:block.honey_block.step", "minecraft:block.iron_door.close", "minecraft:block.iron_door.open", "minecraft:block.iron_trapdoor.close", "minecraft:block.iron_trapdoor.open", "minecraft:block.ladder.break", "minecraft:block.ladder.fall", "minecraft:block.ladder.hit", "minecraft:block.ladder.place", "minecraft:block.ladder.step", "minecraft:block.lantern.break", "minecraft:block.lantern.fall", "minecraft:block.lantern.hit", "minecraft:block.lantern.place", "minecraft:block.lantern.step", "minecraft:block.large_amethyst_bud.break", "minecraft:block.large_amethyst_bud.place", "minecraft:block.lava.ambient", "minecraft:block.lava.extinguish", "minecraft:block.lava.pop", "minecraft:block.lever.click", "minecraft:block.lily_pad.place", "minecraft:block.lodestone.break", "minecraft:block.lodestone.fall", "minecraft:block.lodestone.hit", "minecraft:block.lodestone.place", "minecraft:block.lodestone.step", "minecraft:block.mangrove_roots.break", "minecraft:block.mangrove_roots.fall", "minecraft:block.mangrove_roots.hit", "minecraft:block.mangrove_roots.place", "minecraft:block.mangrove_roots.step", "minecraft:block.medium_amethyst_bud.break", "minecraft:block.medium_amethyst_bud.place", "minecraft:block.metal.break", "minecraft:block.metal.fall", "minecraft:block.metal.hit", "minecraft:block.metal.place", "minecraft:block.metal.step", "minecraft:block.metal_pressure_plate.click_off", "minecraft:block.metal_pressure_plate.click_on", "minecraft:block.moss.break", "minecraft:block.moss.fall", "minecraft:block.moss.hit", "minecraft:block.moss.place", "minecraft:block.moss.step", "minecraft:block.moss_carpet.break", "minecraft:block.moss_carpet.fall", "minecraft:block.moss_carpet.hit", "minecraft:block.moss_carpet.place", "minecraft:block.moss_carpet.step", "minecraft:block.mud.break", "minecraft:block.mud.fall", "minecraft:block.mud.hit", "minecraft:block.mud.place", "minecraft:block.mud.step", "minecraft:block.mud_bricks.break", "minecraft:block.mud_bricks.fall", "minecraft:block.mud_bricks.hit", "minecraft:block.mud_bricks.place", "minecraft:block.mud_bricks.step", "minecraft:block.muddy_mangrove_roots.break", "minecraft:block.muddy_mangrove_roots.fall", "minecraft:block.muddy_mangrove_roots.hit", "minecraft:block.muddy_mangrove_roots.place", "minecraft:block.muddy_mangrove_roots.step", "minecraft:block.nether_bricks.break", "minecraft:block.nether_bricks.fall", "minecraft:block.nether_bricks.hit", "minecraft:block.nether_bricks.place", "minecraft:block.nether_bricks.step", "minecraft:block.nether_gold_ore.break", "minecraft:block.nether_gold_ore.fall", "minecraft:block.nether_gold_ore.hit", "minecraft:block.nether_gold_ore.place", "minecraft:block.nether_gold_ore.step", "minecraft:block.nether_ore.break", "minecraft:block.nether_ore.fall", "minecraft:block.nether_ore.hit", "minecraft:block.nether_ore.place", "minecraft:block.nether_ore.step", "minecraft:block.nether_sprouts.break", "minecraft:block.nether_sprouts.fall", "minecraft:block.nether_sprouts.hit", "minecraft:block.nether_sprouts.place", "minecraft:block.nether_sprouts.step", "minecraft:block.nether_wart.break", "minecraft:block.nether_wood.break", "minecraft:block.nether_wood.fall", "minecraft:block.nether_wood.hit", "minecraft:block.nether_wood.place", "minecraft:block.nether_wood.step", "minecraft:block.nether_wood_button.click_off", "minecraft:block.nether_wood_button.click_on", "minecraft:block.nether_wood_door.close", "minecraft:block.nether_wood_door.open", "minecraft:block.nether_wood_fence_gate.close", "minecraft:block.nether_wood_fence_gate.open", "minecraft:block.nether_wood_hanging_sign.break", "minecraft:block.nether_wood_hanging_sign.fall", "minecraft:block.nether_wood_hanging_sign.hit", "minecraft:block.nether_wood_hanging_sign.place", "minecraft:block.nether_wood_hanging_sign.step", "minecraft:block.nether_wood_pressure_plate.click_off", "minecraft:block.nether_wood_pressure_plate.click_on", "minecraft:block.nether_wood_trapdoor.close", "minecraft:block.nether_wood_trapdoor.open", "minecraft:block.netherite_block.break", "minecraft:block.netherite_block.fall", "minecraft:block.netherite_block.hit", "minecraft:block.netherite_block.place", "minecraft:block.netherite_block.step", "minecraft:block.netherrack.break", "minecraft:block.netherrack.fall", "minecraft:block.netherrack.hit", "minecraft:block.netherrack.place", "minecraft:block.netherrack.step", "minecraft:block.note_block.banjo", "minecraft:block.note_block.basedrum", "minecraft:block.note_block.bass", "minecraft:block.note_block.bell", "minecraft:block.note_block.bit", "minecraft:block.note_block.chime", "minecraft:block.note_block.cow_bell", "minecraft:block.note_block.didgeridoo", "minecraft:block.note_block.flute", "minecraft:block.note_block.guitar", "minecraft:block.note_block.harp", "minecraft:block.note_block.hat", "minecraft:block.note_block.imitate.creeper", "minecraft:block.note_block.imitate.ender_dragon", "minecraft:block.note_block.imitate.piglin", "minecraft:block.note_block.imitate.skeleton", "minecraft:block.note_block.imitate.wither_skeleton", "minecraft:block.note_block.imitate.zombie", "minecraft:block.note_block.iron_xylophone", "minecraft:block.note_block.pling", "minecraft:block.note_block.snare", "minecraft:block.note_block.xylophone", "minecraft:block.nylium.break", "minecraft:block.nylium.fall", "minecraft:block.nylium.hit", "minecraft:block.nylium.place", "minecraft:block.nylium.step", "minecraft:block.packed_mud.break", "minecraft:block.packed_mud.fall", "minecraft:block.packed_mud.hit", "minecraft:block.packed_mud.place", "minecraft:block.packed_mud.step", "minecraft:block.pink_petals.break", "minecraft:block.pink_petals.fall", "minecraft:block.pink_petals.hit", "minecraft:block.pink_petals.place", "minecraft:block.pink_petals.step", "minecraft:block.piston.contract", "minecraft:block.piston.extend", "minecraft:block.pointed_dripstone.break", "minecraft:block.pointed_dripstone.drip_lava", "minecraft:block.pointed_dripstone.drip_lava_into_cauldron", "minecraft:block.pointed_dripstone.drip_water", "minecraft:block.pointed_dripstone.drip_water_into_cauldron", "minecraft:block.pointed_dripstone.fall", "minecraft:block.pointed_dripstone.hit", "minecraft:block.pointed_dripstone.land", "minecraft:block.pointed_dripstone.place", "minecraft:block.pointed_dripstone.step", "minecraft:block.polished_deepslate.break", "minecraft:block.polished_deepslate.fall", "minecraft:block.polished_deepslate.hit", "minecraft:block.polished_deepslate.place", "minecraft:block.polished_deepslate.step", "minecraft:block.portal.ambient", "minecraft:block.portal.travel", "minecraft:block.portal.trigger", "minecraft:block.powder_snow.break", "minecraft:block.powder_snow.fall", "minecraft:block.powder_snow.hit", "minecraft:block.powder_snow.place", "minecraft:block.powder_snow.step", "minecraft:block.pumpkin.carve", "minecraft:block.redstone_torch.burnout", "minecraft:block.respawn_anchor.ambient", "minecraft:block.respawn_anchor.charge", "minecraft:block.respawn_anchor.deplete", "minecraft:block.respawn_anchor.set_spawn", "minecraft:block.rooted_dirt.break", "minecraft:block.rooted_dirt.fall", "minecraft:block.rooted_dirt.hit", "minecraft:block.rooted_dirt.place", "minecraft:block.rooted_dirt.step", "minecraft:block.roots.break", "minecraft:block.roots.fall", "minecraft:block.roots.hit", "minecraft:block.roots.place", "minecraft:block.roots.step", "minecraft:block.sand.break", "minecraft:block.sand.fall", "minecraft:block.sand.hit", "minecraft:block.sand.place", "minecraft:block.sand.step", "minecraft:block.scaffolding.break", "minecraft:block.scaffolding.fall", "minecraft:block.scaffolding.hit", "minecraft:block.scaffolding.place", "minecraft:block.scaffolding.step", "minecraft:block.sculk.break", "minecraft:block.sculk.charge", "minecraft:block.sculk.fall", "minecraft:block.sculk.hit", "minecraft:block.sculk.place", "minecraft:block.sculk.spread", "minecraft:block.sculk.step", "minecraft:block.sculk_catalyst.bloom", "minecraft:block.sculk_catalyst.break", "minecraft:block.sculk_catalyst.fall", "minecraft:block.sculk_catalyst.hit", "minecraft:block.sculk_catalyst.place", "minecraft:block.sculk_catalyst.step", "minecraft:block.sculk_sensor.break", "minecraft:block.sculk_sensor.clicking", "minecraft:block.sculk_sensor.clicking_stop", "minecraft:block.sculk_sensor.fall", "minecraft:block.sculk_sensor.hit", "minecraft:block.sculk_sensor.place", "minecraft:block.sculk_sensor.step", "minecraft:block.sculk_shrieker.break", "minecraft:block.sculk_shrieker.fall", "minecraft:block.sculk_shrieker.hit", "minecraft:block.sculk_shrieker.place", "minecraft:block.sculk_shrieker.shriek", "minecraft:block.sculk_shrieker.step", "minecraft:block.sculk_vein.break", "minecraft:block.sculk_vein.fall", "minecraft:block.sculk_vein.hit", "minecraft:block.sculk_vein.place", "minecraft:block.sculk_vein.step", "minecraft:block.shroomlight.break", "minecraft:block.shroomlight.fall", "minecraft:block.shroomlight.hit", "minecraft:block.shroomlight.place", "minecraft:block.shroomlight.step", "minecraft:block.shulker_box.close", "minecraft:block.shulker_box.open", "minecraft:block.sign.waxed_interact_fail", "minecraft:block.slime_block.break", "minecraft:block.slime_block.fall", "minecraft:block.slime_block.hit", "minecraft:block.slime_block.place", "minecraft:block.slime_block.step", "minecraft:block.small_amethyst_bud.break", "minecraft:block.small_amethyst_bud.place", "minecraft:block.small_dripleaf.break", "minecraft:block.small_dripleaf.fall", "minecraft:block.small_dripleaf.hit", "minecraft:block.small_dripleaf.place", "minecraft:block.small_dripleaf.step", "minecraft:block.smithing_table.use", "minecraft:block.smoker.smoke", "minecraft:block.sniffer_egg.crack", "minecraft:block.sniffer_egg.hatch", "minecraft:block.sniffer_egg.plop", "minecraft:block.snow.break", "minecraft:block.snow.fall", "minecraft:block.snow.hit", "minecraft:block.snow.place", "minecraft:block.snow.step", "minecraft:block.soul_sand.break", "minecraft:block.soul_sand.fall", "minecraft:block.soul_sand.hit", "minecraft:block.soul_sand.place", "minecraft:block.soul_sand.step", "minecraft:block.soul_soil.break", "minecraft:block.soul_soil.fall", "minecraft:block.soul_soil.hit", "minecraft:block.soul_soil.place", "minecraft:block.soul_soil.step", "minecraft:block.spore_blossom.break", "minecraft:block.spore_blossom.fall", "minecraft:block.spore_blossom.hit", "minecraft:block.spore_blossom.place", "minecraft:block.spore_blossom.step", "minecraft:block.stem.break", "minecraft:block.stem.fall", "minecraft:block.stem.hit", "minecraft:block.stem.place", "minecraft:block.stem.step", "minecraft:block.stone.break", "minecraft:block.stone.fall", "minecraft:block.stone.hit", "minecraft:block.stone.place", "minecraft:block.stone.step", "minecraft:block.stone_button.click_off", "minecraft:block.stone_button.click_on", "minecraft:block.stone_pressure_plate.click_off", "minecraft:block.stone_pressure_plate.click_on", "minecraft:block.suspicious_gravel.break", "minecraft:block.suspicious_gravel.fall", "minecraft:block.suspicious_gravel.hit", "minecraft:block.suspicious_gravel.place", "minecraft:block.suspicious_gravel.step", "minecraft:block.suspicious_sand.break", "minecraft:block.suspicious_sand.fall", "minecraft:block.suspicious_sand.hit", "minecraft:block.suspicious_sand.place", "minecraft:block.suspicious_sand.step", "minecraft:block.sweet_berry_bush.break", "minecraft:block.sweet_berry_bush.pick_berries", "minecraft:block.sweet_berry_bush.place", "minecraft:block.tripwire.attach", "minecraft:block.tripwire.click_off", "minecraft:block.tripwire.click_on", "minecraft:block.tripwire.detach", "minecraft:block.tuff.break", "minecraft:block.tuff.fall", "minecraft:block.tuff.hit", "minecraft:block.tuff.place", "minecraft:block.tuff.step", "minecraft:block.vine.break", "minecraft:block.vine.fall", "minecraft:block.vine.hit", "minecraft:block.vine.place", "minecraft:block.vine.step", "minecraft:block.wart_block.break", "minecraft:block.wart_block.fall", "minecraft:block.wart_block.hit", "minecraft:block.wart_block.place", "minecraft:block.wart_block.step", "minecraft:block.water.ambient", "minecraft:block.weeping_vines.break", "minecraft:block.weeping_vines.fall", "minecraft:block.weeping_vines.hit", "minecraft:block.weeping_vines.place", "minecraft:block.weeping_vines.step", "minecraft:block.wet_grass.break", "minecraft:block.wet_grass.fall", "minecraft:block.wet_grass.hit", "minecraft:block.wet_grass.place", "minecraft:block.wet_grass.step", "minecraft:block.wood.break", "minecraft:block.wood.fall", "minecraft:block.wood.hit", "minecraft:block.wood.place", "minecraft:block.wood.step", "minecraft:block.wooden_button.click_off", "minecraft:block.wooden_button.click_on", "minecraft:block.wooden_door.close", "minecraft:block.wooden_door.open", "minecraft:block.wooden_pressure_plate.click_off", "minecraft:block.wooden_pressure_plate.click_on", "minecraft:block.wooden_trapdoor.close", "minecraft:block.wooden_trapdoor.open", "minecraft:block.wool.break", "minecraft:block.wool.fall", "minecraft:block.wool.hit", "minecraft:block.wool.place", "minecraft:block.wool.step", "minecraft:enchant.thorns.hit", "minecraft:entity.allay.ambient_with_item", "minecraft:entity.allay.ambient_without_item", "minecraft:entity.allay.death", "minecraft:entity.allay.hurt", "minecraft:entity.allay.item_given", "minecraft:entity.allay.item_taken", "minecraft:entity.allay.item_thrown", "minecraft:entity.armor_stand.break", "minecraft:entity.armor_stand.fall", "minecraft:entity.armor_stand.hit", "minecraft:entity.armor_stand.place", "minecraft:entity.arrow.hit", "minecraft:entity.arrow.hit_player", "minecraft:entity.arrow.shoot", "minecraft:entity.axolotl.attack", "minecraft:entity.axolotl.death", "minecraft:entity.axolotl.hurt", "minecraft:entity.axolotl.idle_air", "minecraft:entity.axolotl.idle_water", "minecraft:entity.axolotl.splash", "minecraft:entity.axolotl.swim", "minecraft:entity.bat.ambient", "minecraft:entity.bat.death", "minecraft:entity.bat.hurt", "minecraft:entity.bat.loop", "minecraft:entity.bat.takeoff", "minecraft:entity.bee.death", "minecraft:entity.bee.hurt", "minecraft:entity.bee.loop", "minecraft:entity.bee.loop_aggressive", "minecraft:entity.bee.pollinate", "minecraft:entity.bee.sting", "minecraft:entity.blaze.ambient", "minecraft:entity.blaze.burn", "minecraft:entity.blaze.death", "minecraft:entity.blaze.hurt", "minecraft:entity.blaze.shoot", "minecraft:entity.boat.paddle_land", "minecraft:entity.boat.paddle_water", "minecraft:entity.camel.ambient", "minecraft:entity.camel.dash", "minecraft:entity.camel.dash_ready", "minecraft:entity.camel.death", "minecraft:entity.camel.eat", "minecraft:entity.camel.hurt", "minecraft:entity.camel.saddle", "minecraft:entity.camel.sit", "minecraft:entity.camel.stand", "minecraft:entity.camel.step", "minecraft:entity.camel.step_sand", "minecraft:entity.cat.ambient", "minecraft:entity.cat.beg_for_food", "minecraft:entity.cat.death", "minecraft:entity.cat.eat", "minecraft:entity.cat.hiss", "minecraft:entity.cat.hurt", "minecraft:entity.cat.purr", "minecraft:entity.cat.purreow", "minecraft:entity.cat.stray_ambient", "minecraft:entity.chicken.ambient", "minecraft:entity.chicken.death", "minecraft:entity.chicken.egg", "minecraft:entity.chicken.hurt", "minecraft:entity.chicken.step", "minecraft:entity.cod.ambient", "minecraft:entity.cod.death", "minecraft:entity.cod.flop", "minecraft:entity.cod.hurt", "minecraft:entity.cow.ambient", "minecraft:entity.cow.death", "minecraft:entity.cow.hurt", "minecraft:entity.cow.milk", "minecraft:entity.cow.step", "minecraft:entity.creeper.death", "minecraft:entity.creeper.hurt", "minecraft:entity.creeper.primed", "minecraft:entity.dolphin.ambient", "minecraft:entity.dolphin.ambient_water", "minecraft:entity.dolphin.attack", "minecraft:entity.dolphin.death", "minecraft:entity.dolphin.eat", "minecraft:entity.dolphin.hurt", "minecraft:entity.dolphin.jump", "minecraft:entity.dolphin.play", "minecraft:entity.dolphin.splash", "minecraft:entity.dolphin.swim", "minecraft:entity.donkey.ambient", "minecraft:entity.donkey.angry", "minecraft:entity.donkey.chest", "minecraft:entity.donkey.death", "minecraft:entity.donkey.eat", "minecraft:entity.donkey.hurt", "minecraft:entity.dragon_fireball.explode", "minecraft:entity.drowned.ambient", "minecraft:entity.drowned.ambient_water", "minecraft:entity.drowned.death", "minecraft:entity.drowned.death_water", "minecraft:entity.drowned.hurt", "minecraft:entity.drowned.hurt_water", "minecraft:entity.drowned.shoot", "minecraft:entity.drowned.step", "minecraft:entity.drowned.swim", "minecraft:entity.egg.throw", "minecraft:entity.elder_guardian.ambient", "minecraft:entity.elder_guardian.ambient_land", "minecraft:entity.elder_guardian.curse", "minecraft:entity.elder_guardian.death", "minecraft:entity.elder_guardian.death_land", "minecraft:entity.elder_guardian.flop", "minecraft:entity.elder_guardian.hurt", "minecraft:entity.elder_guardian.hurt_land", "minecraft:entity.ender_dragon.ambient", "minecraft:entity.ender_dragon.death", "minecraft:entity.ender_dragon.flap", "minecraft:entity.ender_dragon.growl", "minecraft:entity.ender_dragon.hurt", "minecraft:entity.ender_dragon.shoot", "minecraft:entity.ender_eye.death", "minecraft:entity.ender_eye.launch", "minecraft:entity.ender_pearl.throw", "minecraft:entity.enderman.ambient", "minecraft:entity.enderman.death", "minecraft:entity.enderman.hurt", "minecraft:entity.enderman.scream", "minecraft:entity.enderman.stare", "minecraft:entity.enderman.teleport", "minecraft:entity.endermite.ambient", "minecraft:entity.endermite.death", "minecraft:entity.endermite.hurt", "minecraft:entity.endermite.step", "minecraft:entity.evoker.ambient", "minecraft:entity.evoker.cast_spell", "minecraft:entity.evoker.celebrate", "minecraft:entity.evoker.death", "minecraft:entity.evoker.hurt", "minecraft:entity.evoker.prepare_attack", "minecraft:entity.evoker.prepare_summon", "minecraft:entity.evoker.prepare_wololo", "minecraft:entity.evoker_fangs.attack", "minecraft:entity.experience_bottle.throw", "minecraft:entity.experience_orb.pickup", "minecraft:entity.firework_rocket.blast", "minecraft:entity.firework_rocket.blast_far", "minecraft:entity.firework_rocket.large_blast", "minecraft:entity.firework_rocket.large_blast_far", "minecraft:entity.firework_rocket.launch", "minecraft:entity.firework_rocket.shoot", "minecraft:entity.firework_rocket.twinkle", "minecraft:entity.firework_rocket.twinkle_far", "minecraft:entity.fish.swim", "minecraft:entity.fishing_bobber.retrieve", "minecraft:entity.fishing_bobber.splash", "minecraft:entity.fishing_bobber.throw", "minecraft:entity.fox.aggro", "minecraft:entity.fox.ambient", "minecraft:entity.fox.bite", "minecraft:entity.fox.death", "minecraft:entity.fox.eat", "minecraft:entity.fox.hurt", "minecraft:entity.fox.screech", "minecraft:entity.fox.sleep", "minecraft:entity.fox.sniff", "minecraft:entity.fox.spit", "minecraft:entity.fox.teleport", "minecraft:entity.frog.ambient", "minecraft:entity.frog.death", "minecraft:entity.frog.eat", "minecraft:entity.frog.hurt", "minecraft:entity.frog.lay_spawn", "minecraft:entity.frog.long_jump", "minecraft:entity.frog.step", "minecraft:entity.frog.tongue", "minecraft:entity.generic.big_fall", "minecraft:entity.generic.burn", "minecraft:entity.generic.death", "minecraft:entity.generic.drink", "minecraft:entity.generic.eat", "minecraft:entity.generic.explode", "minecraft:entity.generic.extinguish_fire", "minecraft:entity.generic.hurt", "minecraft:entity.generic.small_fall", "minecraft:entity.generic.splash", "minecraft:entity.generic.swim", "minecraft:entity.ghast.ambient", "minecraft:entity.ghast.death", "minecraft:entity.ghast.hurt", "minecraft:entity.ghast.scream", "minecraft:entity.ghast.shoot", "minecraft:entity.ghast.warn", "minecraft:entity.glow_item_frame.add_item", "minecraft:entity.glow_item_frame.break", "minecraft:entity.glow_item_frame.place", "minecraft:entity.glow_item_frame.remove_item", "minecraft:entity.glow_item_frame.rotate_item", "minecraft:entity.glow_squid.ambient", "minecraft:entity.glow_squid.death", "minecraft:entity.glow_squid.hurt", "minecraft:entity.glow_squid.squirt", "minecraft:entity.goat.ambient", "minecraft:entity.goat.death", "minecraft:entity.goat.eat", "minecraft:entity.goat.horn_break", "minecraft:entity.goat.hurt", "minecraft:entity.goat.long_jump", "minecraft:entity.goat.milk", "minecraft:entity.goat.prepare_ram", "minecraft:entity.goat.ram_impact", "minecraft:entity.goat.screaming.ambient", "minecraft:entity.goat.screaming.death", "minecraft:entity.goat.screaming.eat", "minecraft:entity.goat.screaming.horn_break", "minecraft:entity.goat.screaming.hurt", "minecraft:entity.goat.screaming.long_jump", "minecraft:entity.goat.screaming.milk", "minecraft:entity.goat.screaming.prepare_ram", "minecraft:entity.goat.screaming.ram_impact", "minecraft:entity.goat.step", "minecraft:entity.guardian.ambient", "minecraft:entity.guardian.ambient_land", "minecraft:entity.guardian.attack", "minecraft:entity.guardian.death", "minecraft:entity.guardian.death_land", "minecraft:entity.guardian.flop", "minecraft:entity.guardian.hurt", "minecraft:entity.guardian.hurt_land", "minecraft:entity.hoglin.ambient", "minecraft:entity.hoglin.angry", "minecraft:entity.hoglin.attack", "minecraft:entity.hoglin.converted_to_zombified", "minecraft:entity.hoglin.death", "minecraft:entity.hoglin.hurt", "minecraft:entity.hoglin.retreat", "minecraft:entity.hoglin.step", "minecraft:entity.horse.ambient", "minecraft:entity.horse.angry", "minecraft:entity.horse.armor", "minecraft:entity.horse.breathe", "minecraft:entity.horse.death", "minecraft:entity.horse.eat", "minecraft:entity.horse.gallop", "minecraft:entity.horse.hurt", "minecraft:entity.horse.jump", "minecraft:entity.horse.land", "minecraft:entity.horse.saddle", "minecraft:entity.horse.step", "minecraft:entity.horse.step_wood", "minecraft:entity.hostile.big_fall", "minecraft:entity.hostile.death", "minecraft:entity.hostile.hurt", "minecraft:entity.hostile.small_fall", "minecraft:entity.hostile.splash", "minecraft:entity.hostile.swim", "minecraft:entity.husk.ambient", "minecraft:entity.husk.converted_to_zombie", "minecraft:entity.husk.death", "minecraft:entity.husk.hurt", "minecraft:entity.husk.step", "minecraft:entity.illusioner.ambient", "minecraft:entity.illusioner.cast_spell", "minecraft:entity.illusioner.death", "minecraft:entity.illusioner.hurt", "minecraft:entity.illusioner.mirror_move", "minecraft:entity.illusioner.prepare_blindness", "minecraft:entity.illusioner.prepare_mirror", "minecraft:entity.iron_golem.attack", "minecraft:entity.iron_golem.damage", "minecraft:entity.iron_golem.death", "minecraft:entity.iron_golem.hurt", "minecraft:entity.iron_golem.repair", "minecraft:entity.iron_golem.step", "minecraft:entity.item.break", "minecraft:entity.item.pickup", "minecraft:entity.item_frame.add_item", "minecraft:entity.item_frame.break", "minecraft:entity.item_frame.place", "minecraft:entity.item_frame.remove_item", "minecraft:entity.item_frame.rotate_item", "minecraft:entity.leash_knot.break", "minecraft:entity.leash_knot.place", "minecraft:entity.lightning_bolt.impact", "minecraft:entity.lightning_bolt.thunder", "minecraft:entity.lingering_potion.throw", "minecraft:entity.llama.ambient", "minecraft:entity.llama.angry", "minecraft:entity.llama.chest", "minecraft:entity.llama.death", "minecraft:entity.llama.eat", "minecraft:entity.llama.hurt", "minecraft:entity.llama.spit", "minecraft:entity.llama.step", "minecraft:entity.llama.swag", "minecraft:entity.magma_cube.death", "minecraft:entity.magma_cube.death_small", "minecraft:entity.magma_cube.hurt", "minecraft:entity.magma_cube.hurt_small", "minecraft:entity.magma_cube.jump", "minecraft:entity.magma_cube.squish", "minecraft:entity.magma_cube.squish_small", "minecraft:entity.minecart.inside", "minecraft:entity.minecart.inside.underwater", "minecraft:entity.minecart.riding", "minecraft:entity.mooshroom.convert", "minecraft:entity.mooshroom.eat", "minecraft:entity.mooshroom.milk", "minecraft:entity.mooshroom.shear", "minecraft:entity.mooshroom.suspicious_milk", "minecraft:entity.mule.ambient", "minecraft:entity.mule.angry", "minecraft:entity.mule.chest", "minecraft:entity.mule.death", "minecraft:entity.mule.eat", "minecraft:entity.mule.hurt", "minecraft:entity.ocelot.ambient", "minecraft:entity.ocelot.death", "minecraft:entity.ocelot.hurt", "minecraft:entity.painting.break", "minecraft:entity.painting.place", "minecraft:entity.panda.aggressive_ambient", "minecraft:entity.panda.ambient", "minecraft:entity.panda.bite", "minecraft:entity.panda.cant_breed", "minecraft:entity.panda.death", "minecraft:entity.panda.eat", "minecraft:entity.panda.hurt", "minecraft:entity.panda.pre_sneeze", "minecraft:entity.panda.sneeze", "minecraft:entity.panda.step", "minecraft:entity.panda.worried_ambient", "minecraft:entity.parrot.ambient", "minecraft:entity.parrot.death", "minecraft:entity.parrot.eat", "minecraft:entity.parrot.fly", "minecraft:entity.parrot.hurt", "minecraft:entity.parrot.imitate.blaze", "minecraft:entity.parrot.imitate.creeper", "minecraft:entity.parrot.imitate.drowned", "minecraft:entity.parrot.imitate.elder_guardian", "minecraft:entity.parrot.imitate.ender_dragon", "minecraft:entity.parrot.imitate.endermite", "minecraft:entity.parrot.imitate.evoker", "minecraft:entity.parrot.imitate.ghast", "minecraft:entity.parrot.imitate.guardian", "minecraft:entity.parrot.imitate.hoglin", "minecraft:entity.parrot.imitate.husk", "minecraft:entity.parrot.imitate.illusioner", "minecraft:entity.parrot.imitate.magma_cube", "minecraft:entity.parrot.imitate.phantom", "minecraft:entity.parrot.imitate.piglin", "minecraft:entity.parrot.imitate.piglin_brute", "minecraft:entity.parrot.imitate.pillager", "minecraft:entity.parrot.imitate.ravager", "minecraft:entity.parrot.imitate.shulker", "minecraft:entity.parrot.imitate.silverfish", "minecraft:entity.parrot.imitate.skeleton", "minecraft:entity.parrot.imitate.slime", "minecraft:entity.parrot.imitate.spider", "minecraft:entity.parrot.imitate.stray", "minecraft:entity.parrot.imitate.vex", "minecraft:entity.parrot.imitate.vindicator", "minecraft:entity.parrot.imitate.warden", "minecraft:entity.parrot.imitate.witch", "minecraft:entity.parrot.imitate.wither", "minecraft:entity.parrot.imitate.wither_skeleton", "minecraft:entity.parrot.imitate.zoglin", "minecraft:entity.parrot.imitate.zombie", "minecraft:entity.parrot.imitate.zombie_villager", "minecraft:entity.parrot.step", "minecraft:entity.phantom.ambient", "minecraft:entity.phantom.bite", "minecraft:entity.phantom.death", "minecraft:entity.phantom.flap", "minecraft:entity.phantom.hurt", "minecraft:entity.phantom.swoop", "minecraft:entity.pig.ambient", "minecraft:entity.pig.death", "minecraft:entity.pig.hurt", "minecraft:entity.pig.saddle", "minecraft:entity.pig.step", "minecraft:entity.piglin.admiring_item", "minecraft:entity.piglin.ambient", "minecraft:entity.piglin.angry", "minecraft:entity.piglin.celebrate", "minecraft:entity.piglin.converted_to_zombified", "minecraft:entity.piglin.death", "minecraft:entity.piglin.hurt", "minecraft:entity.piglin.jealous", "minecraft:entity.piglin.retreat", "minecraft:entity.piglin.step", "minecraft:entity.piglin_brute.ambient", "minecraft:entity.piglin_brute.angry", "minecraft:entity.piglin_brute.converted_to_zombified", "minecraft:entity.piglin_brute.death", "minecraft:entity.piglin_brute.hurt", "minecraft:entity.piglin_brute.step", "minecraft:entity.pillager.ambient", "minecraft:entity.pillager.celebrate", "minecraft:entity.pillager.death", "minecraft:entity.pillager.hurt", "minecraft:entity.player.attack.crit", "minecraft:entity.player.attack.knockback", "minecraft:entity.player.attack.nodamage", "minecraft:entity.player.attack.strong", "minecraft:entity.player.attack.sweep", "minecraft:entity.player.attack.weak", "minecraft:entity.player.big_fall", "minecraft:entity.player.breath", "minecraft:entity.player.burp", "minecraft:entity.player.death", "minecraft:entity.player.hurt", "minecraft:entity.player.hurt_drown", "minecraft:entity.player.hurt_freeze", "minecraft:entity.player.hurt_on_fire", "minecraft:entity.player.hurt_sweet_berry_bush", "minecraft:entity.player.levelup", "minecraft:entity.player.small_fall", "minecraft:entity.player.splash", "minecraft:entity.player.splash.high_speed", "minecraft:entity.player.swim", "minecraft:entity.polar_bear.ambient", "minecraft:entity.polar_bear.ambient_baby", "minecraft:entity.polar_bear.death", "minecraft:entity.polar_bear.hurt", "minecraft:entity.polar_bear.step", "minecraft:entity.polar_bear.warning", "minecraft:entity.puffer_fish.ambient", "minecraft:entity.puffer_fish.blow_out", "minecraft:entity.puffer_fish.blow_up", "minecraft:entity.puffer_fish.death", "minecraft:entity.puffer_fish.flop", "minecraft:entity.puffer_fish.hurt", "minecraft:entity.puffer_fish.sting", "minecraft:entity.rabbit.ambient", "minecraft:entity.rabbit.attack", "minecraft:entity.rabbit.death", "minecraft:entity.rabbit.hurt", "minecraft:entity.rabbit.jump", "minecraft:entity.ravager.ambient", "minecraft:entity.ravager.attack", "minecraft:entity.ravager.celebrate", "minecraft:entity.ravager.death", "minecraft:entity.ravager.hurt", "minecraft:entity.ravager.roar", "minecraft:entity.ravager.step", "minecraft:entity.ravager.stunned", "minecraft:entity.salmon.ambient", "minecraft:entity.salmon.death", "minecraft:entity.salmon.flop", "minecraft:entity.salmon.hurt", "minecraft:entity.sheep.ambient", "minecraft:entity.sheep.death", "minecraft:entity.sheep.hurt", "minecraft:entity.sheep.shear", "minecraft:entity.sheep.step", "minecraft:entity.shulker.ambient", "minecraft:entity.shulker.close", "minecraft:entity.shulker.death", "minecraft:entity.shulker.hurt", "minecraft:entity.shulker.hurt_closed", "minecraft:entity.shulker.open", "minecraft:entity.shulker.shoot", "minecraft:entity.shulker.teleport", "minecraft:entity.shulker_bullet.hit", "minecraft:entity.shulker_bullet.hurt", "minecraft:entity.silverfish.ambient", "minecraft:entity.silverfish.death", "minecraft:entity.silverfish.hurt", "minecraft:entity.silverfish.step", "minecraft:entity.skeleton.ambient", "minecraft:entity.skeleton.converted_to_stray", "minecraft:entity.skeleton.death", "minecraft:entity.skeleton.hurt", "minecraft:entity.skeleton.shoot", "minecraft:entity.skeleton.step", "minecraft:entity.skeleton_horse.ambient", "minecraft:entity.skeleton_horse.ambient_water", "minecraft:entity.skeleton_horse.death", "minecraft:entity.skeleton_horse.gallop_water", "minecraft:entity.skeleton_horse.hurt", "minecraft:entity.skeleton_horse.jump_water", "minecraft:entity.skeleton_horse.step_water", "minecraft:entity.skeleton_horse.swim", "minecraft:entity.slime.attack", "minecraft:entity.slime.death", "minecraft:entity.slime.death_small", "minecraft:entity.slime.hurt", "minecraft:entity.slime.hurt_small", "minecraft:entity.slime.jump", "minecraft:entity.slime.jump_small", "minecraft:entity.slime.squish", "minecraft:entity.slime.squish_small", "minecraft:entity.sniffer.death", "minecraft:entity.sniffer.digging", "minecraft:entity.sniffer.digging_stop", "minecraft:entity.sniffer.drop_seed", "minecraft:entity.sniffer.eat", "minecraft:entity.sniffer.happy", "minecraft:entity.sniffer.hurt", "minecraft:entity.sniffer.idle", "minecraft:entity.sniffer.scenting", "minecraft:entity.sniffer.searching", "minecraft:entity.sniffer.sniffing", "minecraft:entity.sniffer.step", "minecraft:entity.snow_golem.ambient", "minecraft:entity.snow_golem.death", "minecraft:entity.snow_golem.hurt", "minecraft:entity.snow_golem.shear", "minecraft:entity.snow_golem.shoot", "minecraft:entity.snowball.throw", "minecraft:entity.spider.ambient", "minecraft:entity.spider.death", "minecraft:entity.spider.hurt", "minecraft:entity.spider.step", "minecraft:entity.splash_potion.break", "minecraft:entity.splash_potion.throw", "minecraft:entity.squid.ambient", "minecraft:entity.squid.death", "minecraft:entity.squid.hurt", "minecraft:entity.squid.squirt", "minecraft:entity.stray.ambient", "minecraft:entity.stray.death", "minecraft:entity.stray.hurt", "minecraft:entity.stray.step", "minecraft:entity.strider.ambient", "minecraft:entity.strider.death", "minecraft:entity.strider.eat", "minecraft:entity.strider.happy", "minecraft:entity.strider.hurt", "minecraft:entity.strider.retreat", "minecraft:entity.strider.saddle", "minecraft:entity.strider.step", "minecraft:entity.strider.step_lava", "minecraft:entity.tadpole.death", "minecraft:entity.tadpole.flop", "minecraft:entity.tadpole.grow_up", "minecraft:entity.tadpole.hurt", "minecraft:entity.tnt.primed", "minecraft:entity.tropical_fish.ambient", "minecraft:entity.tropical_fish.death", "minecraft:entity.tropical_fish.flop", "minecraft:entity.tropical_fish.hurt", "minecraft:entity.turtle.ambient_land", "minecraft:entity.turtle.death", "minecraft:entity.turtle.death_baby", "minecraft:entity.turtle.egg_break", "minecraft:entity.turtle.egg_crack", "minecraft:entity.turtle.egg_hatch", "minecraft:entity.turtle.hurt", "minecraft:entity.turtle.hurt_baby", "minecraft:entity.turtle.lay_egg", "minecraft:entity.turtle.shamble", "minecraft:entity.turtle.shamble_baby", "minecraft:entity.turtle.swim", "minecraft:entity.vex.ambient", "minecraft:entity.vex.charge", "minecraft:entity.vex.death", "minecraft:entity.vex.hurt", "minecraft:entity.villager.ambient", "minecraft:entity.villager.celebrate", "minecraft:entity.villager.death", "minecraft:entity.villager.hurt", "minecraft:entity.villager.no", "minecraft:entity.villager.trade", "minecraft:entity.villager.work_armorer", "minecraft:entity.villager.work_butcher", "minecraft:entity.villager.work_cartographer", "minecraft:entity.villager.work_cleric", "minecraft:entity.villager.work_farmer", "minecraft:entity.villager.work_fisherman", "minecraft:entity.villager.work_fletcher", "minecraft:entity.villager.work_leatherworker", "minecraft:entity.villager.work_librarian", "minecraft:entity.villager.work_mason", "minecraft:entity.villager.work_shepherd", "minecraft:entity.villager.work_toolsmith", "minecraft:entity.villager.work_weaponsmith", "minecraft:entity.villager.yes", "minecraft:entity.vindicator.ambient", "minecraft:entity.vindicator.celebrate", "minecraft:entity.vindicator.death", "minecraft:entity.vindicator.hurt", "minecraft:entity.wandering_trader.ambient", "minecraft:entity.wandering_trader.death", "minecraft:entity.wandering_trader.disappeared", "minecraft:entity.wandering_trader.drink_milk", "minecraft:entity.wandering_trader.drink_potion", "minecraft:entity.wandering_trader.hurt", "minecraft:entity.wandering_trader.no", "minecraft:entity.wandering_trader.reappeared", "minecraft:entity.wandering_trader.trade", "minecraft:entity.wandering_trader.yes", "minecraft:entity.warden.agitated", "minecraft:entity.warden.ambient", "minecraft:entity.warden.angry", "minecraft:entity.warden.attack_impact", "minecraft:entity.warden.death", "minecraft:entity.warden.dig", "minecraft:entity.warden.emerge", "minecraft:entity.warden.heartbeat", "minecraft:entity.warden.hurt", "minecraft:entity.warden.listening", "minecraft:entity.warden.listening_angry", "minecraft:entity.warden.nearby_close", "minecraft:entity.warden.nearby_closer", "minecraft:entity.warden.nearby_closest", "minecraft:entity.warden.roar", "minecraft:entity.warden.sniff", "minecraft:entity.warden.sonic_boom", "minecraft:entity.warden.sonic_charge", "minecraft:entity.warden.step", "minecraft:entity.warden.tendril_clicks", "minecraft:entity.witch.ambient", "minecraft:entity.witch.celebrate", "minecraft:entity.witch.death", "minecraft:entity.witch.drink", "minecraft:entity.witch.hurt", "minecraft:entity.witch.throw", "minecraft:entity.wither.ambient", "minecraft:entity.wither.break_block", "minecraft:entity.wither.death", "minecraft:entity.wither.hurt", "minecraft:entity.wither.shoot", "minecraft:entity.wither.spawn", "minecraft:entity.wither_skeleton.ambient", "minecraft:entity.wither_skeleton.death", "minecraft:entity.wither_skeleton.hurt", "minecraft:entity.wither_skeleton.step", "minecraft:entity.wolf.ambient", "minecraft:entity.wolf.death", "minecraft:entity.wolf.growl", "minecraft:entity.wolf.howl", "minecraft:entity.wolf.hurt", "minecraft:entity.wolf.pant", "minecraft:entity.wolf.shake", "minecraft:entity.wolf.step", "minecraft:entity.wolf.whine", "minecraft:entity.zoglin.ambient", "minecraft:entity.zoglin.angry", "minecraft:entity.zoglin.attack", "minecraft:entity.zoglin.death", "minecraft:entity.zoglin.hurt", "minecraft:entity.zoglin.step", "minecraft:entity.zombie.ambient", "minecraft:entity.zombie.attack_iron_door", "minecraft:entity.zombie.attack_wooden_door", "minecraft:entity.zombie.break_wooden_door", "minecraft:entity.zombie.converted_to_drowned", "minecraft:entity.zombie.death", "minecraft:entity.zombie.destroy_egg", "minecraft:entity.zombie.hurt", "minecraft:entity.zombie.infect", "minecraft:entity.zombie.step", "minecraft:entity.zombie_horse.ambient", "minecraft:entity.zombie_horse.death", "minecraft:entity.zombie_horse.hurt", "minecraft:entity.zombie_villager.ambient", "minecraft:entity.zombie_villager.converted", "minecraft:entity.zombie_villager.cure", "minecraft:entity.zombie_villager.death", "minecraft:entity.zombie_villager.hurt", "minecraft:entity.zombie_villager.step", "minecraft:entity.zombified_piglin.ambient", "minecraft:entity.zombified_piglin.angry", "minecraft:entity.zombified_piglin.death", "minecraft:entity.zombified_piglin.hurt", "minecraft:event.raid.horn", "minecraft:intentionally_empty", "minecraft:item.armor.equip_chain", "minecraft:item.armor.equip_diamond", "minecraft:item.armor.equip_elytra", "minecraft:item.armor.equip_generic", "minecraft:item.armor.equip_gold", "minecraft:item.armor.equip_iron", "minecraft:item.armor.equip_leather", "minecraft:item.armor.equip_netherite", "minecraft:item.armor.equip_turtle", "minecraft:item.axe.scrape", "minecraft:item.axe.strip", "minecraft:item.axe.wax_off", "minecraft:item.bone_meal.use", "minecraft:item.book.page_turn", "minecraft:item.book.put", "minecraft:item.bottle.empty", "minecraft:item.bottle.fill", "minecraft:item.bottle.fill_dragonbreath", "minecraft:item.brush.brushing.generic", "minecraft:item.brush.brushing.gravel", "minecraft:item.brush.brushing.gravel.complete", "minecraft:item.brush.brushing.sand", "minecraft:item.brush.brushing.sand.complete", "minecraft:item.bucket.empty", "minecraft:item.bucket.empty_axolotl", "minecraft:item.bucket.empty_fish", "minecraft:item.bucket.empty_lava", "minecraft:item.bucket.empty_milk", "minecraft:item.bucket.empty_powder_snow", "minecraft:item.bucket.empty_tadpole", "minecraft:item.bucket.fill", "minecraft:item.bucket.fill_axolotl", "minecraft:item.bucket.fill_fish", "minecraft:item.bucket.fill_lava", "minecraft:item.bucket.fill_milk", "minecraft:item.bucket.fill_powder_snow", "minecraft:item.bucket.fill_tadpole", "minecraft:item.bundle.drop_contents", "minecraft:item.bundle.insert", "minecraft:item.bundle.remove_one", "minecraft:item.chorus_fruit.teleport", "minecraft:item.crop.plant", "minecraft:item.crossbow.hit", "minecraft:item.crossbow.loading_end", "minecraft:item.crossbow.loading_middle", "minecraft:item.crossbow.loading_start", "minecraft:item.crossbow.quick_charge_1", "minecraft:item.crossbow.quick_charge_2", "minecraft:item.crossbow.quick_charge_3", "minecraft:item.crossbow.shoot", "minecraft:item.dye.use", "minecraft:item.elytra.flying", "minecraft:item.firecharge.use", "minecraft:item.flintandsteel.use", "minecraft:item.glow_ink_sac.use", "minecraft:item.goat_horn.play", "minecraft:item.goat_horn.sound.0", "minecraft:item.goat_horn.sound.1", "minecraft:item.goat_horn.sound.2", "minecraft:item.goat_horn.sound.3", "minecraft:item.goat_horn.sound.4", "minecraft:item.goat_horn.sound.5", "minecraft:item.goat_horn.sound.6", "minecraft:item.goat_horn.sound.7", "minecraft:item.hoe.till", "minecraft:item.honey_bottle.drink", "minecraft:item.honeycomb.wax_on", "minecraft:item.ink_sac.use", "minecraft:item.lodestone_compass.lock", "minecraft:item.nether_wart.plant", "minecraft:item.shield.block", "minecraft:item.shield.break", "minecraft:item.shovel.flatten", "minecraft:item.spyglass.stop_using", "minecraft:item.spyglass.use", "minecraft:item.totem.use", "minecraft:item.trident.hit", "minecraft:item.trident.hit_ground", "minecraft:item.trident.return", "minecraft:item.trident.riptide_1", "minecraft:item.trident.riptide_2", "minecraft:item.trident.riptide_3", "minecraft:item.trident.throw", "minecraft:item.trident.thunder", "minecraft:music.creative", "minecraft:music.credits", "minecraft:music.dragon", "minecraft:music.end", "minecraft:music.game", "minecraft:music.menu", "minecraft:music.nether.basalt_deltas", "minecraft:music.nether.crimson_forest", "minecraft:music.nether.nether_wastes", "minecraft:music.nether.soul_sand_valley", "minecraft:music.nether.warped_forest", "minecraft:music.overworld.badlands", "minecraft:music.overworld.bamboo_jungle", "minecraft:music.overworld.cherry_grove", "minecraft:music.overworld.deep_dark", "minecraft:music.overworld.desert", "minecraft:music.overworld.dripstone_caves", "minecraft:music.overworld.flower_forest", "minecraft:music.overworld.forest", "minecraft:music.overworld.frozen_peaks", "minecraft:music.overworld.grove", "minecraft:music.overworld.jagged_peaks", "minecraft:music.overworld.jungle", "minecraft:music.overworld.lush_caves", "minecraft:music.overworld.meadow", "minecraft:music.overworld.old_growth_taiga", "minecraft:music.overworld.snowy_slopes", "minecraft:music.overworld.sparse_jungle", "minecraft:music.overworld.stony_peaks", "minecraft:music.overworld.swamp", "minecraft:music.under_water", "minecraft:music_disc.11", "minecraft:music_disc.13", "minecraft:music_disc.5", "minecraft:music_disc.blocks", "minecraft:music_disc.cat", "minecraft:music_disc.chirp", "minecraft:music_disc.far", "minecraft:music_disc.mall", "minecraft:music_disc.mellohi", "minecraft:music_disc.otherside", "minecraft:music_disc.pigstep", "minecraft:music_disc.relic", "minecraft:music_disc.stal", "minecraft:music_disc.strad", "minecraft:music_disc.wait", "minecraft:music_disc.ward", "minecraft:particle.soul_escape", "minecraft:ui.button.click", "minecraft:ui.cartography_table.take_result", "minecraft:ui.loom.select_pattern", "minecraft:ui.loom.take_result", "minecraft:ui.stonecutter.select_recipe", "minecraft:ui.stonecutter.take_result", "minecraft:ui.toast.challenge_complete", "minecraft:ui.toast.in", "minecraft:ui.toast.out", "minecraft:weather.rain", "minecraft:weather.rain.above", "netmusic:net_music", "cataclysm:ministrosity_fill_bucket", "cataclysm:ministrosity_hurt", "cataclysm:cindaria_hurt", "cataclysm:cindaria_death", "cataclysm:hippo<PERSON><PERSON>_hurt", "cataclysm:hippocamtus_death", "cataclysm:hippocam<PERSON>_idle", "cataclysm:scylla_hurt", "cataclysm:scylla_death", "cataclysm:scylla_roar", "cataclysm:scylla_music", "cataclysm:scylla_music_disc", "cataclysm:parry", "cataclysm:heavy_smash", "cataclysm:super_lightning", "cataclysm:the_cataclysmfarer", "guideme:guide.click", "tconstruct:little_saw", "tconstruct:item_frame_click", "tconstruct:casting_cools", "tconstruct:casting_clicks", "tconstruct:block.earth_crystal.chime", "tconstruct:block.sky_crystal.chime", "tconstruct:block.ichor_crystal.chime", "tconstruct:block.ender_crystal.chime", "tconstruct:slime_sling", "tconstruct:slime_sling.teleport", "tconstruct:throw.throwball", "tconstruct:throw.shuriken", "tconstruct:longbow.charge", "tconstruct:crystalshot", "tconstruct:bonk", "tconstruct:spit", "tconstruct:necrotic_heal", "tconstruct:enderporting", "tconstruct:extra_jump", "tconstruct:slime_teleport", "tconstruct:slimy_bounce", "tconstruct:equip.slime", "tconstruct:equip.travelers", "tconstruct:equip.plate", "tconstruct:toy_squeak", "tconstruct:crossbow_reload", "tconstruct:stone_hit", "tconstruct:wood_hit", "tconstruct:charged", "tconstruct:discharge", "create:frogport_catch", "create:confirm_2", "create:desk_bell", "create:stock_ticker_request", "create:item_hatch", "create:item_hatch_compounded_1", "create:cardboard_bonk", "create:frogport_close", "create:frogport_deposit", "create:package_pop", "create:package_pop_compounded_1", "create:stock_ticker_trade", "create:frogport_open", "create:stock_link", "create:packager"]}}