{"format_version": "1.8.0", "animations": {"static_idle": {"animation_length": 0.1, "bones": {"lefthand": {"rotation": [107.69108, -32.75736, -156.00452], "position": [8.2, -11.9, -2.2], "scale": [1, 1.5, 1]}, "righthand": {"rotation": [89.49625, 6.99973, -180.06139], "position": [-6.85, -14.075, 8.775], "scale": [1, 1.5, 1]}, "refit_view": {"position": [0, -1, 11]}, "constraint": {"rotation": [0.2, 0.1, 0.2], "position": [0.2, 0.2, 0.4]}}}, "static_bolt_caught": {"loop": true, "bones": {"holdrelease": {"rotation": [0, 0, 15]}, "bolt": {"position": [0, 0, 4.7]}, "bullet": {"scale": 0}}}, "draw": {"animation_length": 0.73333, "bones": {"lefthand": {"rotation": [107.69108, -32.75736, -156.00452], "position": [8.2, -11.9, -2.2], "scale": [1, 1.5, 1]}, "righthand": {"rotation": [89.49625, 6.99973, -180.06139], "position": [-6.85, -14.075, 8.775], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0.811, -32.64455, 33.95463], "lerp_mode": "catmullrom"}, "0.1": {"post": [5.88301, -29.75023, 5.08656], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0.28514, -4.44535, -0.52252], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.8526, -0.39013, -4.13439], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-0.01, -0.435, -0.76], "lerp_mode": "catmullrom"}, "0.5": {"post": [1.01337, -0.38036, -1.29904], "lerp_mode": "catmullrom"}, "0.6333": {"post": [0, 0.25, -1.25], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-3.18, -5.58, 13.525], "lerp_mode": "catmullrom"}, "0.1": {"post": [-0.9, -2.45, 2.7], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.25, -0.9425, -1.14], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.03, -0.3025, -0.8], "lerp_mode": "catmullrom"}, "0.4": {"post": [-0.03, -0.1475, -0.145], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.01, -0.2, 0.065], "lerp_mode": "catmullrom"}, "0.5667": {"post": [0, -0.08125, 0], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0.45, -0.175, 0.6], "lerp_mode": "catmullrom"}, "0.4": {"post": [-0.1, 0.05, -0.175], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.05, 0, 0.05], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0667": {"effect": "tacz:scar_h/p05_ar_schotel_reload_raise"}}}, "put_away": {"animation_length": 0.43333, "bones": {"lefthand": {"rotation": {"0.0": [107.69108, -32.75736, -156.00452], "0.1333": [169.13843, 3.15772, -219.72112], "0.2667": [168.76095, 1.2363, -229.54249]}, "position": {"0.0": [8.2, -11.9, -2.2], "0.0667": [8.66185, -10.53957, -1.25509], "0.1333": [8.08317, -8.39052, 0.52034], "0.2667": [8.08317, -8.39052, 0.52034]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": [89.49625, 6.99973, -180.06139], "position": [-6.85, -14.075, 8.775], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.51171, -3.6801, -4.19401], "lerp_mode": "catmullrom"}, "0.1667": {"post": [4.36882, -11.73179, -10.29382], "lerp_mode": "catmullrom"}, "0.2333": {"post": [11.60471, -21.56305, -3.66288], "lerp_mode": "catmullrom"}, "0.3333": {"post": [16.97, -33.62, 3.38], "lerp_mode": "catmullrom"}, "0.4333": {"post": [34.73, -44.04, -3.09], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, -0.49, -0.02], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.52, -1.47, -0.56], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.36, -2.92, -0.84], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0.13, -5.86, -0.16], "lerp_mode": "catmullrom"}, "0.4333": {"post": [1.72, -9.68, 0.16], "lerp_mode": "catmullrom"}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.35, 0.025, -0.225], "lerp_mode": "catmullrom"}, "0.3": {"post": [0.1, 0.025, 0.075], "lerp_mode": "catmullrom"}, "0.4333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0333": {"effect": "tacz:scar_h/p05_ar_schotel_ubgl_drop_rattle"}}}, "reload_tactical": {"animation_length": 2.33333, "bones": {"lefthand": {"rotation": {"0.0": [107.69, -32.76, -156], "0.0667": [101.5, -26.45, -142.36], "0.1333": [95.31, -20.14, -128.72], "0.2": [89.12, -13.83, -115.08], "0.2667": [82.93, -7.52, -101.43], "0.3": [79.84, -4.36, -94.61], "0.3667": [79.84, -4.36, -94.61], "0.4333": [79.84, -4.36, -94.61], "0.5": [79.84, -4.36, -94.61], "0.5667": [79.84, -4.36, -94.61], "0.6333": [79.84, -4.36, -94.61], "0.7": [79.84, -4.36, -94.61], "0.8": [79.84, -4.36, -94.61], "0.9": [79.84, -4.36, -94.61], "1.0": [79.84, -4.36, -94.61], "1.1": [79.84, -4.36, -94.61], "1.2": [79.84, -4.36, -94.61], "1.3": [79.84, -4.36, -94.61], "1.3333": [79.84, -4.36, -94.61], "1.4": [108.59, -27.55, -135.68], "1.4667": [137.34, -50.75, -176.75], "1.5333": [108.59, -27.56, -135.68], "1.6": [79.84, -4.36, -94.61], "1.6667": [87.8, -12.47, -112.15], "1.7333": [95.75, -20.59, -129.69], "1.8": [103.71, -28.7, -147.23], "1.8333": [107.69, -32.76, -156]}, "position": {"0.0": [8.2, -11.9, -2.2], "0.0667": [7.76, -13.25, -1.23], "0.1333": [7.31, -14.6, -0.25], "0.2": [6.87, -15.95, 0.72], "0.2667": [6.42, -17.3, 1.69], "0.3": [6.2, -17.98, 2.18], "0.3667": [6.2, -17.98, 2.18], "0.4333": [6.2, -17.98, 2.18], "0.5": [6.2, -17.98, 2.18], "0.5667": [6.2, -17.98, 2.18], "0.6333": [6.2, -17.98, 2.18], "0.7": [6.2, -17.98, 2.18], "0.8": [6.2, -17.98, 2.18], "0.9": [6.2, -17.98, 2.18], "1.0": [6.2, -17.98, 2.18], "1.1": [6.2, -17.98, 2.18], "1.2": [6.2, -17.98, 2.18], "1.3": [6.2, -17.98, 2.18], "1.3333": [6.2, -17.98, 2.18], "1.4": [6.60625, -21.53, 2.20375], "1.4667": [11.54954, -26.8681, 9.99255], "1.6": [11.54954, -26.8681, 12.36755], "1.7": [13.9, -19.15, 5.08], "1.7667": [10.63, -12.75, -1.695], "1.8": [9.42, -12.33, -1.95], "1.8333": [8.2, -11.9, -2.2]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": [89.49625, 6.99973, -180.06139], "position": [-6.85, -14.075, 8.775], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-2.99197, -2.34076, -3.81083], "lerp_mode": "catmullrom"}, "0.2": {"post": [-12.10904, -5.51675, -10.10819], "lerp_mode": "catmullrom"}, "0.3": {"post": [-23.11357, -19.36321, -23.07043], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-25.9588, -26.06981, -30.67374], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-28.86716, -26.78215, -27.32846], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-27.96077, -27.73393, -29.30727], "lerp_mode": "catmullrom"}, "0.7": {"post": [-25.99921, -27.39747, -30.9734], "lerp_mode": "catmullrom"}, "0.8": {"post": [-30.05831, -26.67402, -29.62769], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-17.73794, -34.20182, -46.06376], "lerp_mode": "catmullrom"}, "1.0333": {"post": [-20.57138, -31.83872, -45.59599], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-17.23483, -33.49224, -49.92316], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-26.34197, -30.74158, -41.61953], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-15.4835, -30.54489, -49.46293], "lerp_mode": "catmullrom"}, "1.5": {"post": [-21.97418, -28.22094, -40.20536], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-23.59477, -10.21438, -16.52437], "lerp_mode": "catmullrom"}, "1.7333": {"post": [-15.31958, -2.96368, -5.03831], "lerp_mode": "catmullrom"}, "1.8": {"post": [-9.07234, -1.20567, 6.00913], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-2.94771, -1.4232, 3.47003], "lerp_mode": "catmullrom"}, "1.9333": {"post": [0.57324, -1.68438, -0.35369], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-0.78429, -0.96449, -3.00002], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-0.01963, 0.49961, 0.62491], "lerp_mode": "catmullrom"}, "2.2333": {"post": [0, 0.375, -0.125], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-0.4, -0.6, 0.775], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.595, -0.05, 0.74], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.79, 1.435, 0.53], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.8, 2.275, 0.38], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-0.67, 2.415, 0.365], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-0.85, 1.95, 0.105], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-0.85, 1.925, 0.095], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-0.695, 1.68, 0.105], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-0.95, 2.235, 0.205], "lerp_mode": "catmullrom"}, "0.7": {"post": [-1.04, 2.505, 0.365], "lerp_mode": "catmullrom"}, "0.8": {"post": [-1.12, 2.63, 0.255], "lerp_mode": "catmullrom"}, "0.9667": {"post": [-1.39, 1.95625, -0.265], "lerp_mode": "catmullrom"}, "1.1": {"post": [-1.175, 2.025, -0.265], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-1.165, 1.99875, -0.265], "lerp_mode": "catmullrom"}, "1.2": {"post": [-0.88, 1.215, -0.045], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-0.64, 1, 0.085], "lerp_mode": "catmullrom"}, "1.3": {"post": [-0.92, 1.13, -0.315], "lerp_mode": "catmullrom"}, "1.4": {"post": [-1.16, 0.995, -0.27], "lerp_mode": "catmullrom"}, "1.5": {"post": [-0.905, 1.2, -0.165], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-0.995, 0.77, -0.05], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-0.845, 0.225, 0.135], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-0.69, -0.77, -0.775], "lerp_mode": "catmullrom"}, "1.9333": {"post": [-0.265, -0.8, -1.135], "lerp_mode": "catmullrom"}, "2.0": {"post": [0.11, -0.73, -0.325], "lerp_mode": "catmullrom"}, "2.0667": {"post": [0.225, -0.25, 0.35], "lerp_mode": "catmullrom"}, "2.1333": {"post": [0, 0.025, 0], "lerp_mode": "catmullrom"}, "2.2333": {"post": [0, 0, 0.03125], "lerp_mode": "catmullrom"}, "2.3": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lh": {"rotation": {"0.0": [0, 0, 0], "0.4333": [-76.1, -20.46, -9.17], "0.5": [-69.74946, -21.91786, -9.82227], "0.6333": [-69.74946, -21.91786, -9.82227], "0.7": [-69.75, -21.92, -9.82], "0.7667": [-53.31, -16.44, -7.37], "0.9333": [-5.96737, -0.62604, -5.96737], "1.0": [-3.99454, -0.20917, -2.99271], "1.0333": [-2.67, 0, 0], "1.1333": [-1.34, 0, 0], "1.1667": [0, 0, 0], "1.2333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0667": [2.26, -1.28, 1.67], "0.1667": [4.975, -5.29, 4.17], "0.2667": [5.06, -9.31, 6.67], "0.4333": [2.455, -3.965, 2.135], "0.5": [1.175, -2.35, 2.05], "0.6333": [1.175, -2.35, 2.05], "0.7": [1.18, -5.375, 1.58], "0.7333": [1.03, -6.345, 0.73], "0.7667": [0.88, -6.14, -0.13], "0.8333": [0.29, -4.555, -1.18], "0.9333": [0.35, -3.025, -0.85], "1.0": [0.225, -2.725, -0.4], "1.0333": [0, -2.58, -0.27], "1.1333": [0, -2.44, -0.14], "1.1667": [0, -2.3, 0], "1.2333": [0, 0, 0]}}, "mag_and_bullet": {"scale": {"0.0": [0, 0, 0], "0.2667": [0, 0, 0], "0.3": [1, 1, 1]}}, "additional_magazine": {"rotation": {"0.6333": [0, 0, 0], "0.7": [0, 0, 0], "0.7667": [17, 0, 0], "0.8333": [37.995, 0.65, 4.905], "0.9333": [70.93432, 8.72056, 22.45912], "1.1667": [70.89, 8.455, 21.255], "1.2333": [85.44247, 8.68175, 24.2797], "1.3333": [85.44, 8.685, 24.275], "1.4667": [17.46117, -1.49957, -44.69095]}, "position": {"0.6333": [0, 0, 0], "0.7": [0, -3, 0], "0.7333": [-0.26, -4.625, -1.15], "0.7667": [-0.525, -5.175, -2.3], "0.8333": [-1.07, -5.295, -3.53], "0.9333": [-1.99, -4.775, -3.4], "1.0": [-1.94, -4.66, -3.045], "1.1667": [-1.865, -4.495, -2.875], "1.2333": [-2.065, -2.045, -3.1], "1.3333": [-2.07, -2.05, -3.1], "1.4": [-1.65, -5.33, -2.29], "1.4667": [3.29329, -10.9681, 7.2238]}, "scale": {"1.4333": [1, 1, 1], "1.4667": {"pre": [1, 1, 1], "post": [0, 0, 0]}}}, "magrelease": {"position": {"0.5": [0, 0, 0], "0.5333": [0.125, 0, 0], "0.8667": [0.125, 0, 0], "0.9": [0, 0, 0], "1.1667": [0, 0, 0], "1.2": [0.125, 0, 0], "1.2667": [0.125, 0, 0], "1.3": [0, 0, 0]}}, "bullet": {"scale": 1}, "constraint": {"rotation": {"0.0": [0.2, 0.1, 0.2], "0.2": [0.01, 0.01, 0.04], "1.6333": [0.01, 0.01, 0.04], "2.0333": [0.2, 0.1, 0.2]}, "position": {"0.0": [0.2, 0.2, 0.4], "0.2": [0.05, 0.05, 0.1], "1.6333": [0.05, 0.05, 0.1], "2.0333": [0.2, 0.2, 0.4]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-0.32, 0, -0.125], "lerp_mode": "catmullrom"}, "0.4": {"post": [2.1, 0.05, -0.8], "lerp_mode": "catmullrom"}, "0.6333": {"post": [1.625, 0.1, -0.75], "lerp_mode": "catmullrom"}, "0.7": {"post": [2.45, 0.1, 0.04], "lerp_mode": "catmullrom"}, "0.7667": {"post": [2.64, 0.1, 0.175], "lerp_mode": "catmullrom"}, "0.9": {"post": [1.81, 0.21, -1.36], "lerp_mode": "catmullrom"}, "1.0333": {"post": [2.225, 0.35, -0.82], "lerp_mode": "catmullrom"}, "1.1667": {"post": [1.87, 0.475, -1.6], "lerp_mode": "catmullrom"}, "1.2333": {"post": [3.06, 0.48, -0.62], "lerp_mode": "catmullrom"}, "1.2667": {"post": [3.185, 0.475, -0.7], "lerp_mode": "catmullrom"}, "1.4": {"post": [1.925, 0.44, -1.795], "lerp_mode": "catmullrom"}, "1.5667": {"post": [2.655, 0.31, -0.975], "lerp_mode": "catmullrom"}, "1.7": {"post": [2.35, 0.175, -1.525], "lerp_mode": "catmullrom"}, "2.0": {"post": [-0.2, 0, 0.35], "lerp_mode": "catmullrom"}, "2.1": {"post": [0.3, 0, -0.375], "lerp_mode": "catmullrom"}, "2.2": {"post": [-0.05, 0, 0.1], "lerp_mode": "catmullrom"}, "2.3": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "tacz:scar_h/p05_ar_schotel_reload_raise"}, "0.5333": {"effect": "tacz:scar_h/p05_ar_schotel_reload_ubgl_magout"}, "0.8667": {"effect": "tacz:scar_h/p05_ar_schotel_branched_reload_magin"}, "1.3667": {"effect": "tacz:scar_h/p05_ar_schotel_reload_end"}}}, "reload_empty": {"animation_length": 2.86667, "bones": {"lefthand": {"rotation": {"0.0": [107.69, -32.76, -156], "0.0667": [105.46, -30.93, -146.07], "0.1333": [103.23, -29.11, -136.13], "0.2": [101, -27.29, -126.19], "0.2667": [98.77, -25.47, -116.26], "0.3": [97.65, -24.56, -111.29], "0.3333": [97.65, -24.56, -111.29], "0.4": [97.65, -24.56, -111.29], "0.4667": [97.65, -24.56, -111.29], "0.5333": [97.65, -24.56, -111.29], "0.6": [97.65, -24.56, -111.29], "0.6667": [97.65, -24.56, -111.29], "0.7333": [97.65, -24.56, -111.29], "0.8": [97.65, -24.56, -111.29], "0.8667": [97.65, -24.56, -111.29], "0.9333": [97.65, -24.56, -111.29], "1.0": [97.65, -24.56, -111.29], "1.0333": [97.65, -24.56, -111.29], "1.0667": [97.65, -24.56, -111.29], "1.1333": [97.65, -24.56, -111.29], "1.2": [97.65, -24.56, -111.29], "1.2667": [97.65, -24.56, -111.29], "1.3333": [97.65, -24.56, -111.29], "1.4": [97.65, -24.56, -111.29], "1.4667": [97.65, -24.56, -111.29], "1.5333": [97.65, -24.56, -111.29], "1.5667": [97.65, -24.56, -111.29], "1.6": [109.2, -26.65, -126.96], "1.6333": [125.54, -29.15, -149.09], "1.6667": [140.75, -28.17, -169.46], "1.7": [153.39, -20.41, -186.63], "1.7333": [164.9, -9.18, -202.04], "1.7667": [173.62, -0.53, -211.75], "1.8": [175.56, 0.57, -209.51], "1.8333": [177.51, 1.68, -207.26], "1.8667": [179.45, 2.79, -205.02], "1.9": [181.39, 3.9, -202.78], "1.9667": [184.94, 5.56, -201.34], "2.0333": [188.49765, 7.22189, -199.90728], "2.1": [155.38573, 2.26831, -225.03827], "2.1667": [137.13, -21.42, -219.99], "2.2333": [121.31242, -43.18487, -205.60127], "2.3": [113.51, -39.33, -179.9], "2.3667": [107.69108, -32.75736, -156.00452]}, "position": {"0.0": [8.2, -11.9, -2.2], "0.0667": [7.97, -13.15, -1.01], "0.1333": [7.73, -14.41, 0.19], "0.2": [7.5, -15.66, 1.38], "0.2667": [7.27, -16.92, 2.58], "0.3": [7.15, -17.55, 3.18], "0.3333": [7.15, -17.55, 3.18], "0.4": [7.15, -17.55, 3.18], "0.4667": [7.15, -17.55, 3.18], "0.5333": [7.15, -17.55, 3.18], "0.6": [7.15, -17.55, 3.18], "0.6667": [7.15, -17.55, 3.18], "0.7333": [7.15, -17.55, 3.18], "0.8": [7.15, -17.55, 3.18], "0.8667": [7.15, -17.55, 3.18], "0.9333": [7.15, -17.55, 3.18], "1.0": [7.15, -17.55, 3.18], "1.0333": [7.15, -17.55, 3.18], "1.0667": [7.15, -17.55, 3.18], "1.1333": [7.15, -17.55, 3.18], "1.2": [7.15, -17.55, 3.18], "1.2667": [7.15, -17.55, 3.18], "1.3333": [7.15, -17.55, 3.18], "1.4": [7.15, -17.55, 3.18], "1.4667": [7.15, -17.55, 3.18], "1.5333": [7.15, -17.55, 3.18], "1.5667": [7.15, -17.55, 3.18], "1.6667": {"pre": [9.39, -14, 1.89], "post": [9.39, -14, 1.89], "lerp_mode": "catmullrom"}, "1.7667": [9.175, -8.4, 2.955], "1.9": [9.175, -8.6, 2.955], "1.9667": [9.095, -8.6, 4.61], "2.0333": [9.095, -8.6, 4.61], "2.1": {"pre": [9.705, -11.87, 8.885], "post": [9.705, -11.87, 8.885], "lerp_mode": "catmullrom"}, "2.2333": {"post": [9.9, -15.175, 5.455], "lerp_mode": "catmullrom"}, "2.3667": [8.2, -11.9, -2.2]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": [89.49625, 6.99973, -180.06139], "position": [-6.85, -14.075, 8.775], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-3.49555, -0.81508, 3.05538], "lerp_mode": "catmullrom"}, "0.3": {"post": [-6.77915, -2.53345, -7.84739], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-7.87862, -3.64818, -10.52572], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-8.8603, -3.86241, -6.62741], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-8.73621, -2.74727, -1.87501], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-13.09623, -8.67173, -8.11656], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-20.6171, -14.2888, -17.82616], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-25.76586, -33.00324, -29.70148], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-22.19264, -39.63697, -39.91232], "lerp_mode": "catmullrom"}, "1.1": {"post": [-21.42948, -38.12554, -39.74042], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-21.29159, -38.96497, -41.45368], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-23.69501, -38.86025, -37.53157], "lerp_mode": "catmullrom"}, "1.4": {"post": [-21.33661, -40.67129, -43.33577], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-17.51248, -42.17814, -48.55021], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-19.96251, -40.11132, -38.41785], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-21.88778, -30.58079, -29.63565], "lerp_mode": "catmullrom"}, "1.7333": {"post": [-23.1908, -12.26892, -2.49657], "lerp_mode": "catmullrom"}, "1.8333": {"post": [-11.61388, -5.41404, 2.48103], "lerp_mode": "catmullrom"}, "1.9": {"post": [-8.61, -3.465, 1.86], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-8.44778, -2.97641, 5.51303], "lerp_mode": "catmullrom"}, "2.0": {"post": [-10.70316, -3.33396, 3.61274], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-8.8364, -0.09288, 10.91901], "lerp_mode": "catmullrom"}, "2.1": {"post": [-4.85391, -0.71359, 6.38627], "lerp_mode": "catmullrom"}, "2.1667": {"post": [-1.92838, -2.00642, 0.99896], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-2.86394, -2.24803, 4.50799], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-1.17724, -0.52456, -3.81208], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-1.53169, -1.64259, -5.70459], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-0.27794, 0.48501, -1.00445], "lerp_mode": "catmullrom"}, "2.6667": {"post": [0, 0, 1.25], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0, -0.025, 0.35], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.1, 0.6, 0.425], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-0.225, -0.725, 0.42], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-0.505, 0.03125, 0.12], "lerp_mode": "catmullrom"}, "0.7": {"post": [-1.075, 0.315, -0.43], "lerp_mode": "catmullrom"}, "0.8667": {"post": [-1.48, 2.11, -0.08], "lerp_mode": "catmullrom"}, "0.9667": {"post": [-1.255, 2.635, -0.005], "lerp_mode": "catmullrom"}, "1.1": {"post": [-1.155, 2.34, -0.13], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-1.28, 2.04, -0.13], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-1.33, 2.165, -0.13], "lerp_mode": "catmullrom"}, "1.4": {"post": [-0.81, 1.13, -0.03], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-0.88, 0.965, 0.045], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-1.185, 0.97, -0.09], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-1.275, 0.075, -0.34], "lerp_mode": "catmullrom"}, "1.7": {"post": [-1.29, -0.135, -0.34], "lerp_mode": "catmullrom"}, "1.8333": {"post": [-0.935, 0.185, -0.335], "lerp_mode": "catmullrom"}, "1.9": {"post": [-1.005, -0.355, -0.14], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-1, 0.115, 0.65], "lerp_mode": "catmullrom"}, "2.0": {"post": [-1, 0.24, 0.375], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-0.985, 0.445, -0.165], "lerp_mode": "catmullrom"}, "2.1333": {"post": [-0.965, -0.18, -0.375], "lerp_mode": "catmullrom"}, "2.2333": {"post": [-0.87, -0.68, -1.47], "lerp_mode": "catmullrom"}, "2.3333": {"post": [-0.25, -0.73, -1.27], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0.095, -0.15, -1.225], "lerp_mode": "catmullrom"}, "2.5667": {"post": [0, -0.175, 0.5], "lerp_mode": "catmullrom"}, "2.6667": {"post": [0, 0, -0.175], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lh": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.7061, 20.27135, -25.65768], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-0.7061, 20.27135, -25.65768], "lerp_mode": "catmullrom"}, "0.9": {"post": [9.33, 1.83, -0.24], "lerp_mode": "catmullrom"}, "0.9667": {"post": [7.38011, -2.97237, -2.71019], "lerp_mode": "catmullrom"}, "1.0333": [-3, 0, -11], "1.1333": [0, 0, -10], "1.1667": [0, 0, -6.67], "1.2": [0, 0, 0], "1.3667": [0, 0, 0], "1.4": [0, 0, 0]}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [5.22, -1.29, 1.71], "lerp_mode": "catmullrom"}, "0.2333": {"post": [8.19702, -13.17286, 5.1424], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-0.25298, -13.17286, 5.1424], "lerp_mode": "catmullrom"}, "0.9": {"post": [0.75881, -5.98048, -0.23633], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0.39861, -4.5891, -0.99821], "lerp_mode": "catmullrom"}, "1.0333": [0.775, -3.1, -1.025], "1.1333": [0.775, -3, -0.8], "1.1667": [0.52, -2.83, -0.53], "1.2": [0, -2.7, -0.175], "1.3667": [0, -2.7, -0.1], "1.4": [0, 0, 0]}}, "lefthand_pos": {"rotation": {"0.0": [0, 0, 0], "2.1333": [0, 0, 0]}}, "mag_and_bullet": {"scale": {"0.0": [0, 0, 0], "0.7": [0, 0, 0], "0.7333": [1, 1, 1]}}, "additional_magazine": {"rotation": [0, 0, 0], "position": {"0.3333": [0, 0, 0], "0.4333": [0, -11, 0]}, "scale": {"0.4667": [1, 1, 1], "0.5": [0, 0, 0]}}, "holdrelease": {"rotation": {"0.0": [0, 0, 15], "2.0333": [0, 0, 15], "2.0667": [0, 0, 0]}}, "bolt": {"position": {"0.0": [0, 0, 4.7], "1.9": [0, 0, 4.7], "1.9667": [0, 0, 6.025], "2.0333": [0, 0, 6.025], "2.0667": [0, 0, 0], "2.1": [0, 0, 0.475], "2.1333": [0, 0, 0], "2.2": [0, 0, 0.25], "2.2667": [0, 0, 0]}}, "magrelease": {"position": {"0.2667": [0, 0, 0], "0.3": [0.125, 0, 0], "0.6333": [0.125, 0, 0], "0.6667": [0, 0, 0], "1.3667": [0, 0, 0], "1.4": [0.125, 0, 0], "1.4667": [0.125, 0, 0], "1.5": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-0.575, 0, 0.475], "lerp_mode": "catmullrom"}, "0.4": {"post": [0.305, 0.125, -0.4], "lerp_mode": "catmullrom"}, "0.5333": {"post": [0.65, 0.125, 0.825], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0.51, 0.04, -0.07], "lerp_mode": "catmullrom"}, "0.8": {"post": [0.455, 0, 0.275], "lerp_mode": "catmullrom"}, "1.1333": {"post": [1.51, 0.35, -0.6], "lerp_mode": "catmullrom"}, "1.3667": {"post": [1.435, 0.5, -0.725], "lerp_mode": "catmullrom"}, "1.4667": {"post": [2.49, 0.5, 0.125], "lerp_mode": "catmullrom"}, "1.6": {"post": [1.51, 0.5, -1], "lerp_mode": "catmullrom"}, "1.7333": {"post": [1.86, 0.35, -0.6], "lerp_mode": "catmullrom"}, "1.8667": {"post": [1.17, 0.24, -0.73], "lerp_mode": "catmullrom"}, "1.9667": {"post": [0.76, 0.175, -0.45], "lerp_mode": "catmullrom"}, "2.0667": {"post": [1.585, 0.175, 0.35], "lerp_mode": "catmullrom"}, "2.2": {"post": [0.49, 0.1, -0.725], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0.94, 0.1, -0.15], "lerp_mode": "catmullrom"}, "2.4667": {"post": [0.27, 0.04, -0.215], "lerp_mode": "catmullrom"}, "2.5667": {"post": [-0.2, 0, 0.15], "lerp_mode": "catmullrom"}, "2.6667": {"post": [0.15, 0, -0.225], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-0.1, 0, 0.125], "lerp_mode": "catmullrom"}, "2.8667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bullet": {"scale": {"0.0": [0, 0, 0], "0.3667": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "constraint": {"rotation": {"0.3": [0.2, 0.1, 0.2], "0.7333": [0.01, 0.02, 0.04], "2.2": [0.01, 0.02, 0.04], "2.5667": [0.2, 0.1, 0.2]}, "position": {"0.3": [0.2, 0.2, 0.4], "0.7333": [0.05, 0.05, 0.1], "2.2": [0.05, 0.05, 0.1], "2.5667": [0.2, 0.2, 0.4]}}}, "sound_effects": {"0.0": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_raise"}, "0.0333": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_magout"}, "0.8667": {"effect": "tacz:mag_drop_sound/iw8_phys_mag_drop_ar_metal_dirt_04"}, "1.0": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_magin"}, "1.7667": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_charge"}, "2.2": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_end"}}}, "inspect": {"animation_length": 6.66667, "bones": {"lefthand": {"rotation": {"0.0": [107.69108, -32.75736, -156.00452], "0.0667": [113.84399, -28.84087, -167.98195], "0.2": [115.13774, -50.87624, -183.69084], "2.4333": [115.13774, -50.87624, -183.69084], "2.6667": [96.05402, -23.77546, -135.39325], "2.7333": [96.05402, -23.77546, -135.39325], "4.3667": [96.05, -23.78, -135.39], "4.4": [95.4, -23.62, -134.02], "4.4333": [96.05, -23.78, -135.39], "4.4667": [100.53, -24.81, -144.41], "4.5": [106.5, -26.34, -157.28], "4.5333": [112.38, -27.79, -169.63], "4.5667": [118.43, -29.4, -182.96], "4.6": [118.43, -30.33, -188.08], "4.6333": [104.29, -29.63, -173.68], "4.6667": [90.15, -28.94, -159.29], "4.7": [76.01, -28.24, -144.89], "4.7667": [76.01297, -28.23693, -144.89294], "5.6333": [76.01297, -28.23693, -144.89294], "5.8667": [69.48475, -24.48895, -163.5452], "6.0333": [76.01297, -28.23693, -144.89294], "6.1333": [107.69108, -32.75736, -156.00452]}, "position": {"0.0": [8.2, -11.9, -2.2], "0.0667": [8.55, -11.425, -2.2], "0.2": [7.675, -12.82, 0.275], "2.4333": [7.675, -12.82, 0.275], "2.5667": [8.755, -15.77, 1.64], "2.6667": [8.2, -16.97, 3], "2.7333": [8.175, -16.62, 3], "4.3667": [8.175, -16.62, 3], "4.4333": {"pre": [9, -17.495, 2.1], "post": [9, -17.495, 2.1], "lerp_mode": "catmullrom"}, "4.5": {"post": [10.875, -12.92, 0.71], "lerp_mode": "catmullrom"}, "4.6": [9.455, -8.345, -0.675], "4.7": [8.63, -8.35, -0.67], "4.7667": [8.63, -8.345, -0.675], "4.9": [8.805, -8.35, 1.78], "5.6333": [8.805, -8.35, 1.78], "5.7333": [11.825, -8.425, 3.68], "5.8667": [12.02938, -9.54272, 4.58733], "6.0333": [11.63919, -12.76962, 3.40214], "6.1333": [8.2, -11.9, -2.2]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [89.49625, 6.99973, -180.06139], "0.2": [89.5, 7, -180.06], "0.3667": [89.5, 7, -180.06], "2.3667": [89.5, 7, -180.06], "2.5333": [89.5, 7, -180.06]}, "position": {"0.0": [-6.85, -14.075, 8.775], "0.2": [-6.85, -14.07, 8.78], "0.3667": [-8.325, -14.07, 8.78], "2.3667": [-8.325, -14.07, 8.78], "2.5333": [-6.85, -14.07, 8.78]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-1.57552, -4.18517, -5.87258], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-4.02414, -13.28009, -10.40473], "lerp_mode": "catmullrom"}, "0.5": {"post": [-12.85629, -43.05513, -11.98818], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-14.81181, -47.20625, -10.13088], "lerp_mode": "catmullrom"}, "0.8": {"post": [-14.99109, -47.47374, -9.10282], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-17.06722, -46.91834, -6.27058], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-11.77121, -50.35486, -8.4078], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-2.48632, -55.49611, -13.34944], "lerp_mode": "catmullrom"}, "1.8333": {"post": [0.79794, -56.84607, -14.39354], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-1.48956, -56.78982, -13.34354], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-2.98956, -53.78982, -13.33104], "lerp_mode": "catmullrom"}, "2.4": {"post": [-10.67, -46.585, -14.92], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-30.11122, -26.09675, -11.89334], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-34.29063, -30.84703, -23.03454], "lerp_mode": "catmullrom"}, "2.8": {"post": [-33.63149, -29.74839, -27.2996], "lerp_mode": "catmullrom"}, "3.0667": [-33.63, -29.75, -24.72], "3.7667": [-33.58, -29.81, -24.69], "4.1": {"pre": [-33.63, -29.75, -24.72], "post": [-33.63, -29.75, -24.72], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-31.13018, -37.2508, -35.73132], "lerp_mode": "catmullrom"}, "4.2667": {"post": [-39.13662, -28.77784, -21.5149], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-32.41298, -28.48824, -24.12616], "lerp_mode": "catmullrom"}, "4.5": {"post": [-32.35, -13.84, -3.68], "lerp_mode": "catmullrom"}, "4.7": {"post": [-24.44887, 33.69261, 57.72468], "lerp_mode": "catmullrom"}, "4.8333": {"post": [-24.44887, 37.43011, 65.20593], "lerp_mode": "catmullrom"}, "5.0": {"post": [-21.77341, 39.9934, 71.11487], "lerp_mode": "catmullrom"}, "5.2": {"post": [-22.39217, 38.36309, 67.78261], "lerp_mode": "catmullrom"}, "5.4333": {"post": [-20.91319, 37.91021, 71.44428], "lerp_mode": "catmullrom"}, "5.6333": {"post": [-20.84, 38.06, 71.73], "lerp_mode": "catmullrom"}, "5.7": {"post": [-21.44549, 33.44573, 65.10345], "lerp_mode": "catmullrom"}, "5.7667": {"post": [-12.11178, 33.21632, 75.57421], "lerp_mode": "catmullrom"}, "5.8667": {"post": [-14.16619, 29.72082, 65.11199], "lerp_mode": "catmullrom"}, "5.9333": {"post": [-9.81809, 14.52297, 60.27799], "lerp_mode": "catmullrom"}, "6.0333": {"post": [-7.6537, -0.57338, 19.91169], "lerp_mode": "catmullrom"}, "6.2": {"post": [-2.89977, -0.56617, 0.01026], "lerp_mode": "catmullrom"}, "6.3": {"post": [-0.81567, -0.58037, -3.89535], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-0.97803, -1.26726, 0.24991], "lerp_mode": "catmullrom"}, "6.4667": {"post": [0.25, -0.5, -1.25], "lerp_mode": "catmullrom"}, "6.5667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "6.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [0.2, -0.725, 0.7], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0.725, -0.47, 0.55], "lerp_mode": "catmullrom"}, "0.5": {"post": [1.55, 1.955, -0.10625], "lerp_mode": "catmullrom"}, "0.7667": {"post": [1.95, 1.83, -0.125], "lerp_mode": "catmullrom"}, "1.0667": {"post": [2.025, 1.88, -0.16], "lerp_mode": "catmullrom"}, "1.3333": {"post": [2.05, 1.83, -0.2], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.705, 1.625, -0.39], "lerp_mode": "catmullrom"}, "1.6667": {"post": [3.805, 1.625, -0.58], "lerp_mode": "catmullrom"}, "1.8333": {"post": [4.58625, 1.91125, -0.77], "lerp_mode": "catmullrom"}, "2.0333": {"post": [4.705, 2.0125, -0.77], "lerp_mode": "catmullrom"}, "2.2": {"post": [4.53, 2.01125, -0.81], "lerp_mode": "catmullrom"}, "2.3": {"post": [3.8175, 1.68125, -0.67], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.95, 0.98, -0.245], "lerp_mode": "catmullrom"}, "2.5333": {"post": [0.555, 1.38, 0.075], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.625, 2.655, 0.78], "lerp_mode": "catmullrom"}, "2.8": {"post": [-0.945, 2.58, 0.78], "lerp_mode": "catmullrom"}, "2.9": {"post": [-0.99, 2.465, 0.78], "lerp_mode": "catmullrom"}, "3.0": {"post": [-0.97625, 2.4175, 0.78], "lerp_mode": "catmullrom"}, "3.0667": [-0.76, 1.985, 0.78], "3.7667": [-1, 2.04, 0.78], "3.8667": [-1.025, 1.76, 0.78], "4.0667": {"pre": [-0.79375, 1.775, 0.78], "post": [-0.79375, 1.775, 0.78], "lerp_mode": "catmullrom"}, "4.1333": {"post": [-0.3425, 0.92, 0.78], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-0.82, 0.98625, 0.78], "lerp_mode": "catmullrom"}, "4.5333": {"post": [-0.82, 0.73, 0.78], "lerp_mode": "catmullrom"}, "4.7": {"post": [-0.72, 1.03, 0.68], "lerp_mode": "catmullrom"}, "4.8333": {"post": [-0.64, 1.38, 0.68], "lerp_mode": "catmullrom"}, "4.9": {"post": [-0.66, 1.79, 0.76], "lerp_mode": "catmullrom"}, "5.0": {"post": [-0.69, 1.655, 0.88], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-0.765, 1.455, 0.88], "lerp_mode": "catmullrom"}, "5.4333": {"post": [-0.68, 1.64, 0.88], "lerp_mode": "catmullrom"}, "5.6667": {"post": [-0.69, 1.455, 0.88], "lerp_mode": "catmullrom"}, "5.7": {"post": [-0.69, 0.78, 0.14875], "lerp_mode": "catmullrom"}, "5.7667": {"post": [-0.65, 0.965, 0.2], "lerp_mode": "catmullrom"}, "5.8667": {"post": [-0.64, 0.65, 0.3125], "lerp_mode": "catmullrom"}, "5.9333": {"post": [-0.89, 0.06875, 0.135], "lerp_mode": "catmullrom"}, "6.0667": {"post": [-0.68, -0.625, -0.46], "lerp_mode": "catmullrom"}, "6.1667": {"post": [-0.44, -0.86, -1.05], "lerp_mode": "catmullrom"}, "6.3": {"post": [-0.075, -0.15625, 0.325], "lerp_mode": "catmullrom"}, "6.3667": {"post": [0, -0.125, 0.55], "lerp_mode": "catmullrom"}, "6.4667": {"post": [0, 0, -0.125], "lerp_mode": "catmullrom"}, "6.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lh": {"rotation": {"2.9667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.2": {"post": [-9.98938, -1.44898, 15.30138], "lerp_mode": "catmullrom"}, "3.6": {"post": [-9.98938, -1.44898, 15.30138], "lerp_mode": "catmullrom"}, "3.7667": [-4.92442, -0.86717, -9.96271], "3.8": [-4.92442, -0.86717, -9.96271], "3.8667": [-1.93953, -0.69374, -7.97017], "3.9333": [-1.93953, -0.69374, -7.97017], "4.0333": [0, 0, 0], "4.1": [0, 0, 0], "4.1333": [0, 0, 0]}, "position": {"2.9667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0, -2.85, 0], "lerp_mode": "catmullrom"}, "3.2": {"post": [1.15, -3.65, -0.475], "lerp_mode": "catmullrom"}, "3.3667": {"post": [1.375, -3.45, -0.645], "lerp_mode": "catmullrom"}, "3.5": {"post": [1.25, -3.56, -0.725], "lerp_mode": "catmullrom"}, "3.6": {"post": [1.15, -3.65, -0.475], "lerp_mode": "catmullrom"}, "3.6667": {"post": [0.63, -4.055, -0.56], "lerp_mode": "catmullrom"}, "3.7667": [0.6, -3.15, -0.8], "3.8": [0.6, -3.325, -0.8], "3.8667": [0.53, -3.325, -0.605], "3.9333": [0.53, -3.025, -0.23], "4.0333": [0, -2.75, 0], "4.1": [0, -2.6, 0], "4.1333": [0, 0, 0]}}, "bolt": {"position": {"4.7667": [0, 0, 0], "4.9": [0, 0, 2.625], "5.6333": [0, 0, 2.625], "5.6667": [0, 0, 0]}}, "gun_and_rh": {"rotation": {"3.0667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.1": {"post": [0.24769, 0.05884, -4.49962], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-3.25006, -0.00982, 0.74994], "lerp_mode": "catmullrom"}, "3.4": {"post": [-3.07127, -1.97943, -3.14676], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-0.27223, -0.23014, -3.09702], "lerp_mode": "catmullrom"}, "3.7333": {"post": [0.02917, -0.08324, -3.23879], "lerp_mode": "catmullrom"}, "3.8": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"3.0667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-0.48875, 0.46625, 0.005], "lerp_mode": "catmullrom"}, "3.4333": {"post": [-0.75875, 0.235, 0.005], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-0.4575, 0.22, 0.005], "lerp_mode": "catmullrom"}, "3.8": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "additional_magazine": {"scale": 0}, "magrelease": {"position": {"2.7667": [0, 0, 0], "2.8333": [0.125, 0, 0], "3.2667": [0.125, 0, 0], "3.3333": [0, 0, 0], "4.0667": [0, 0, 0], "4.1": [0.125, 0, 0], "4.1667": [0.125, 0, 0], "4.2": [0, 0, 0]}}, "bullet": {"scale": 0}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.455, -0.05, -0.4], "lerp_mode": "catmullrom"}, "0.5333": {"post": [2.75, -1.15, -1.9], "lerp_mode": "catmullrom"}, "1.0": {"post": [3.4, -1.2, -2.1], "lerp_mode": "catmullrom"}, "1.4667": {"post": [3.25, -0.425, -2.35], "lerp_mode": "catmullrom"}, "1.8": {"post": [5.8, 2.35, -2.075], "lerp_mode": "catmullrom"}, "2.3667": {"post": [6.2, 2.875, -2.15], "lerp_mode": "catmullrom"}, "2.6667": {"post": [1.24, 0.84, -1.33], "lerp_mode": "catmullrom"}, "2.8": {"post": [1.025, 0.35, -1.15], "lerp_mode": "catmullrom"}, "2.9": {"post": [1.02, 0.18, -1.14], "lerp_mode": "catmullrom"}, "3.0": {"post": [1.435, 0.07, -1.16], "lerp_mode": "catmullrom"}, "3.0667": {"post": [2.97, 0, 0.175], "lerp_mode": "catmullrom"}, "3.2": {"post": [2.785, -0.33, -1.48], "lerp_mode": "catmullrom"}, "3.3333": {"post": [4.17, -0.825, -0.125], "lerp_mode": "catmullrom"}, "3.5667": {"post": [3.75, -1, -0.775], "lerp_mode": "catmullrom"}, "3.8667": {"post": [2.025, -0.25, -1.325], "lerp_mode": "catmullrom"}, "4.1": {"post": [1.48, 0.06, -1.71], "lerp_mode": "catmullrom"}, "4.2": {"post": [3.43, 0.425, -0.35], "lerp_mode": "catmullrom"}, "4.3": {"post": [1.825, 0.71, -2.015], "lerp_mode": "catmullrom"}, "4.4333": {"post": [3, 1.14, -0.295], "lerp_mode": "catmullrom"}, "4.6": {"post": [2.84, 1.8, 0.3], "lerp_mode": "catmullrom"}, "4.8333": {"post": [3.405, 2.775, 1.625], "lerp_mode": "catmullrom"}, "4.9333": {"post": [3.89, 2.86, 2.4], "lerp_mode": "catmullrom"}, "5.0667": {"post": [3.12, 2.92, 1.2], "lerp_mode": "catmullrom"}, "5.2333": {"post": [3.735, 3.01, 2], "lerp_mode": "catmullrom"}, "5.6333": {"post": [3.46, 3.2, 2.36], "lerp_mode": "catmullrom"}, "5.7333": {"post": [5.21, 3.175, 3.85], "lerp_mode": "catmullrom"}, "5.8333": {"post": [2.62, 2.88, 0.8], "lerp_mode": "catmullrom"}, "5.9667": {"post": [2.185, 2.325, 1.425], "lerp_mode": "catmullrom"}, "6.2": {"post": [-0.525, 0.175, -0.35], "lerp_mode": "catmullrom"}, "6.3": {"post": [0.5, 0, 0.575], "lerp_mode": "catmullrom"}, "6.4333": {"post": [-0.25, 0.025, -0.3], "lerp_mode": "catmullrom"}, "6.6": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_raise"}, "1.4333": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_magrattle"}, "2.7": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_magout"}, "3.7667": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_magin"}, "4.3": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_rotate"}, "4.4667": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_boltback"}, "5.6": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_boltclose"}, "5.7": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_end"}}}, "inspect_empty": {"animation_length": 10.2, "bones": {"lefthand": {"rotation": {"0.0": [107.69108, -32.75736, -156.00452], "0.0667": [113.84399, -28.84087, -167.98195], "0.2": [115.13774, -50.87624, -183.69084], "2.4333": [115.13774, -50.87624, -183.69084], "2.6667": [96.05402, -23.77546, -135.39325], "2.7333": [96.05402, -23.77546, -135.39325], "3.6": [96.05, -23.78, -135.39], "3.7": [91.62359, -18.85515, -117.94239], "3.8333": [96.05, -23.78, -135.39], "3.9667": [96.05, -23.78, -135.39], "7.2333": [96.05, -23.78, -135.39], "7.2667": [108.45, -26.11, -144.22], "7.3": [125.81, -28.93, -156.83], "7.3333": [140.75, -28.17, -169.46], "7.3667": [150.84, -20.29, -182.26], "7.4": [158.51, -8.82, -195.07], "7.4333": [163.64, -0.33, -203.66], "7.5": [163.65145, 0.79994, -199.82343], "7.6": [158.86058, 2.76029, -193.09995], "7.7": [188.76524, 2.95546, -195.54852], "7.8333": [181.55609, -3.92605, -199.74227], "7.9333": [188.77, 2.96, -195.55], "8.0": [168.5, -5.97, -185.66], "8.0667": [148.23, -14.9, -175.78], "8.1333": [127.96, -23.83, -165.89], "8.2": [107.69, -32.76, -156], "8.2333": [107.69, -32.76, -156], "8.4333": [63.69, -32.76, -156], "8.9667": [63.69, -32.76, -156], "9.2": [107.69108, -32.75736, -156.00452]}, "position": {"0.0": [8.2, -11.9, -2.2], "0.0667": [8.55, -11.425, -2.2], "0.2": [7.675, -12.82, 0.275], "2.4333": [7.675, -12.82, 0.275], "2.5667": [8.755, -15.77, 1.64], "2.6667": [8.2, -16.97, 3], "2.7333": [8.175, -16.62, 3], "3.6": [8.18, -16.62, 3], "3.7": [7.98441, -16.46383, 2.93995], "3.8333": [8.31348, -16.31027, 2.5343], "3.9667": [8.18, -16.62, 3], "7.2333": [8.175, -16.62, 3], "7.3333": {"pre": [9.39, -14, 1.89], "post": [9.39, -14, 1.89], "lerp_mode": "catmullrom"}, "7.4333": [9.5, -8.15, -1.57], "7.5": [8.9, -8.4, -1.57], "7.6": [8.9, -8.4, -1.57], "7.7": [9.175, -8.4, 4.68], "7.9333": [9.175, -8.4, 4.68], "8.2": [8.2, -11.9, -2.2], "8.2333": [8.2, -11.9, -2.2], "8.4333": [8.2, -9.775, -1.125], "8.9667": [8.2, -9.775, -1.125], "9.2": [8.2, -11.9, -2.2]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [89.49625, 6.99973, -180.06139], "0.2": [89.5, 7, -180.06], "0.3667": [89.5, 7, -180.06], "2.3667": [89.5, 7, -180.06], "2.5333": [89.5, 7, -180.06]}, "position": {"0.0": [-6.85, -14.075, 8.775], "0.2": [-6.85, -14.07, 8.78], "0.3667": [-8.325, -14.07, 8.78], "2.3667": [-8.325, -14.07, 8.78], "2.5333": [-6.85, -14.07, 8.78]}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-1.57552, -4.18517, -5.87258], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-4.02414, -13.28009, -10.40473], "lerp_mode": "catmullrom"}, "0.5": {"post": [-12.85629, -43.05513, -11.98818], "lerp_mode": "catmullrom"}, "0.6333": {"post": [-14.81181, -47.20625, -10.13088], "lerp_mode": "catmullrom"}, "0.8": {"post": [-14.99109, -47.47374, -9.10282], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-17.06722, -46.91834, -6.27058], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-11.77121, -50.35486, -8.4078], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-2.48632, -55.49611, -13.34944], "lerp_mode": "catmullrom"}, "1.8333": {"post": [0.79794, -56.84607, -14.39354], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-1.48956, -56.78982, -13.34354], "lerp_mode": "catmullrom"}, "2.2667": {"post": [-2.98956, -53.78982, -13.33104], "lerp_mode": "catmullrom"}, "2.4": {"post": [-10.67, -46.585, -14.92], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-30.11122, -26.09675, -11.89334], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-34.29063, -30.84703, -23.03454], "lerp_mode": "catmullrom"}, "2.8": {"post": [-33.63149, -29.74839, -27.2996], "lerp_mode": "catmullrom"}, "3.0667": [-33.63, -29.75, -24.72], "3.8667": {"pre": [-32.41298, -28.48824, -24.12616], "post": [-32.41298, -28.48824, -24.12616], "lerp_mode": "catmullrom"}, "4.0": {"post": [-32.35, -13.84, -3.68], "lerp_mode": "catmullrom"}, "4.2": {"post": [-24.44887, 33.69261, 57.72468], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-24.44887, 37.43011, 65.20593], "lerp_mode": "catmullrom"}, "4.5": {"post": [-21.77341, 39.9934, 71.11487], "lerp_mode": "catmullrom"}, "4.6": {"post": [-19.1759, 42.32263, 74.38865], "lerp_mode": "catmullrom"}, "4.7667": {"post": [-22.39217, 38.36309, 67.78261], "lerp_mode": "catmullrom"}, "4.8667": {"post": [-21.52, 37.88, 69.73], "lerp_mode": "catmullrom"}, "4.9333": {"post": [-17.6696, 41.26733, 77.42593], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-20.88, 37.95, 71.59], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-23.33234, 36.8907, 68.42593], "lerp_mode": "catmullrom"}, "5.2667": {"post": [-17.19522, 36.60408, 74.84594], "lerp_mode": "catmullrom"}, "5.5": {"post": [-20.52672, 41.30297, 72.54405], "lerp_mode": "catmullrom"}, "5.7667": {"post": [-20.84, 38.06, 71.73], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-21.44549, 33.44573, 65.10345], "lerp_mode": "catmullrom"}, "5.9": {"post": [-12.11178, 33.21632, 75.57421], "lerp_mode": "catmullrom"}, "6.0": {"post": [-14.16619, 29.72082, 65.11199], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-15.1588, 31.72784, 67.47333], "lerp_mode": "catmullrom"}, "6.2667": {"post": [-19.99288, 27.14386, 60.59523], "lerp_mode": "catmullrom"}, "6.4667": {"post": [-32.35, -13.84, -3.68], "lerp_mode": "catmullrom"}, "6.6667": {"post": [-33.63, -29.75, -24.72], "lerp_mode": "catmullrom"}, "6.8667": {"post": [-33.63, -29.75, -24.72], "lerp_mode": "catmullrom"}, "6.9333": {"post": [-31.13018, -37.2508, -35.73132], "lerp_mode": "catmullrom"}, "7.0667": {"post": [-39.13662, -28.77784, -21.5149], "lerp_mode": "catmullrom"}, "7.2": {"post": [-32.41298, -28.48824, -24.12616], "lerp_mode": "catmullrom"}, "7.4333": {"post": [-20.48201, -14.0597, -15.60999], "lerp_mode": "catmullrom"}, "7.6": {"post": [-24.55485, -15.33258, -8.27582], "lerp_mode": "catmullrom"}, "7.6333": {"post": [-23.088, -15.05408, -8.82379], "lerp_mode": "catmullrom"}, "7.7": {"post": [-17.3097, -16.22103, -17.76034], "lerp_mode": "catmullrom"}, "7.8333": {"post": [-20.84843, -12.43702, -12.54088], "lerp_mode": "catmullrom"}, "8.0333": {"post": [-12.18, -8.99, -10.03], "lerp_mode": "catmullrom"}, "8.1667": {"post": [-12.07638, -4.63111, 4.9991], "lerp_mode": "catmullrom"}, "8.3": {"post": [-22.50437, 0.13036, 40.00681], "lerp_mode": "catmullrom"}, "8.4333": {"post": [-17.54817, 31.19085, 97.25827], "lerp_mode": "catmullrom"}, "8.5667": {"post": [-19.16287, 36.81684, 102.70025], "lerp_mode": "catmullrom"}, "8.9": {"post": [-18.71251, 37.4368, 104.89253], "lerp_mode": "catmullrom"}, "9.0": {"post": [-17.23654, 29.07261, 97.39959], "lerp_mode": "catmullrom"}, "9.1333": {"post": [-16.95645, 0.7663, 62.12915], "lerp_mode": "catmullrom"}, "9.2667": {"post": [-7.56995, -3.48573, 6.45849], "lerp_mode": "catmullrom"}, "9.4": {"post": [-2.90525, -3.56232, 0.16225], "lerp_mode": "catmullrom"}, "9.5": {"post": [-0.81567, -0.58037, -3.89535], "lerp_mode": "catmullrom"}, "9.5667": {"post": [-0.97803, -1.26726, 0.24991], "lerp_mode": "catmullrom"}, "9.6667": {"post": [0.25, -0.5, -1.25], "lerp_mode": "catmullrom"}, "9.7667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "9.8667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [0.2, -0.725, 0.7], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0.725, -0.47, 0.55], "lerp_mode": "catmullrom"}, "0.5": {"post": [1.55, 1.955, -0.10625], "lerp_mode": "catmullrom"}, "0.7667": {"post": [1.95, 1.83, -0.125], "lerp_mode": "catmullrom"}, "1.0667": {"post": [2.025, 1.88, -0.16], "lerp_mode": "catmullrom"}, "1.3333": {"post": [2.05, 1.83, -0.2], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.705, 1.625, -0.39], "lerp_mode": "catmullrom"}, "1.6667": {"post": [3.805, 1.625, -0.58], "lerp_mode": "catmullrom"}, "1.8333": {"post": [4.58625, 1.91125, -0.77], "lerp_mode": "catmullrom"}, "2.0333": {"post": [4.705, 2.0125, -0.77], "lerp_mode": "catmullrom"}, "2.2": {"post": [4.53, 2.01125, -0.81], "lerp_mode": "catmullrom"}, "2.3": {"post": [3.8175, 1.68125, -0.67], "lerp_mode": "catmullrom"}, "2.4": {"post": [1.95, 0.98, -0.245], "lerp_mode": "catmullrom"}, "2.5333": {"post": [0.555, 1.38, 0.075], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.625, 2.655, 0.78], "lerp_mode": "catmullrom"}, "2.8": {"post": [-0.945, 2.58, 0.78], "lerp_mode": "catmullrom"}, "2.9": {"post": [-0.99, 2.465, 0.78], "lerp_mode": "catmullrom"}, "3.0": {"post": [-0.97625, 2.4175, 0.78], "lerp_mode": "catmullrom"}, "3.0667": [-0.76, 1.985, 0.78], "3.8333": {"pre": [-1.195, 0.98625, 0.78], "post": [-1.195, 0.98625, 0.78], "lerp_mode": "catmullrom"}, "4.0333": {"post": [-0.82, 0.205, 0.78], "lerp_mode": "catmullrom"}, "4.2": {"post": [-1.37, 0.755, 0.68], "lerp_mode": "catmullrom"}, "4.3333": {"post": [-1.09, 1.38, 0.68], "lerp_mode": "catmullrom"}, "4.4": {"post": [-0.835, 1.79, 0.76], "lerp_mode": "catmullrom"}, "4.5": {"post": [-0.765, 1.655, 0.88], "lerp_mode": "catmullrom"}, "4.6": {"post": [-0.87, 1.77, 0.465], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-0.9, 1.48, 0.88], "lerp_mode": "catmullrom"}, "4.8667": {"post": [-0.675, 1.4, 0.535], "lerp_mode": "catmullrom"}, "4.9667": {"post": [-0.605, 1.32, 1], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-0.635, 1.435, 0.735], "lerp_mode": "catmullrom"}, "5.2": {"post": [-0.775, 1.16, 0.76], "lerp_mode": "catmullrom"}, "5.3": {"post": [-0.645, 1.64, 0.365], "lerp_mode": "catmullrom"}, "5.5333": {"post": [-0.7, 1.215, 0.6], "lerp_mode": "catmullrom"}, "5.8": {"post": [-0.69, 1.455, 0.88], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-0.69, 0.78, 0.14875], "lerp_mode": "catmullrom"}, "5.9": {"post": [-0.65, 0.965, 0.2], "lerp_mode": "catmullrom"}, "6.0": {"post": [-0.64, 0.65, 0.3125], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-0.64, 1.38, 0.68], "lerp_mode": "catmullrom"}, "6.2667": {"post": [-0.72, 0.83, 0.68], "lerp_mode": "catmullrom"}, "6.4333": {"post": [-0.82, 0.73, 0.78], "lerp_mode": "catmullrom"}, "6.6333": {"post": [-0.79375, 1.775, 0.78], "lerp_mode": "catmullrom"}, "6.8333": {"post": [-0.79375, 1.775, 0.78], "lerp_mode": "catmullrom"}, "6.9": {"post": [-0.3425, 0.92, 0.78], "lerp_mode": "catmullrom"}, "7.0": {"post": [-0.53, 0.73, 0.73], "lerp_mode": "catmullrom"}, "7.1667": {"post": [-0.82, 0.98625, 0.78], "lerp_mode": "catmullrom"}, "7.2667": {"post": [-0.7, 0.705, 1.2], "lerp_mode": "catmullrom"}, "7.4333": {"post": [0.475, -0.125, 1.955], "lerp_mode": "catmullrom"}, "7.6": {"post": [0.55, -0.25, 1.655], "lerp_mode": "catmullrom"}, "7.7": {"post": [0.355, -0.625, 1.84], "lerp_mode": "catmullrom"}, "7.8333": {"post": [0.24, -0.83, 1.365], "lerp_mode": "catmullrom"}, "8.0": {"post": [0.23, -0.74, 0.995], "lerp_mode": "catmullrom"}, "8.1667": {"post": [0.22, -2.565, 1.28], "lerp_mode": "catmullrom"}, "8.4333": {"post": [-0.405, 1.035, 1.58], "lerp_mode": "catmullrom"}, "8.6333": {"post": [-0.4, 1.265, 1.58], "lerp_mode": "catmullrom"}, "8.8": {"post": [-0.4, 1.17875, 1.58], "lerp_mode": "catmullrom"}, "9.0": {"post": [-0.405, 1.20375, 1.58], "lerp_mode": "catmullrom"}, "9.2": {"post": [-0.425, -1.05, -1.95], "lerp_mode": "catmullrom"}, "9.3667": {"post": [-0.44, -0.86, -1.05], "lerp_mode": "catmullrom"}, "9.5": {"post": [-0.075, -0.15625, 0.325], "lerp_mode": "catmullrom"}, "9.6": {"post": [0, -0.125, 0.55], "lerp_mode": "catmullrom"}, "9.7": {"post": [0, 0, -0.125], "lerp_mode": "catmullrom"}, "9.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lh": {"rotation": {"2.9667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.2": {"post": [-9.98938, -1.44898, 15.30138], "lerp_mode": "catmullrom"}, "3.5667": [-12.97, -0.29, 15.87], "3.6667": [-11.896, -3.24064, 2.43642], "3.8": [1.0113, -3.53812, 16.09031], "3.9667": {"pre": [-17.45741, -4.25574, -7.21939], "post": [-17.45741, -4.25574, -7.21939], "lerp_mode": "catmullrom"}, "4.0667": {"post": [-3.42154, -8.17748, -51.3687], "lerp_mode": "catmullrom"}, "4.1667": {"post": [11.94631, -32.69074, -112.66479], "lerp_mode": "catmullrom"}, "4.2667": [-1.55232, -7.33104, -142.62808], "4.4": [1.4, -13.88, -142.42], "4.5": [7.30732, -26.97697, -142.0095], "4.6667": [7.31, -26.98, -142.01], "4.8": [3.55283, -26.34286, -143.97436], "4.9": [1.35952, -18.23676, -139.21462], "5.0": [4.0784, -19.14411, -137.16281], "5.1667": [3.55283, -26.34286, -143.97436], "5.2667": [1.35952, -18.23676, -139.21462], "5.7333": [1.35952, -18.23676, -139.21462], "5.9333": [8.27389, -25.78234, -158.71247], "6.2667": [8.27389, -25.78234, -158.71247], "6.5333": [-4.92442, -0.86717, -9.96271], "6.5667": [-4.92442, -0.86717, -9.96271], "6.6333": [-1.93953, -0.69374, -7.97017], "6.7": [-1.93953, -0.69374, -7.97017], "6.8": [0, 0, 0], "6.8667": [0, 0, 0], "6.9": [0, 0, 0]}, "position": {"2.9667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0, -2.85, 0], "lerp_mode": "catmullrom"}, "3.2": {"post": [1.15, -3.65, -0.475], "lerp_mode": "catmullrom"}, "3.3667": {"post": [1.66195, -3.78673, -0.62223], "lerp_mode": "catmullrom"}, "3.5667": [1.75386, -3.91975, -0.75821], "3.6667": [1.85929, -5.67558, 0.0674], "3.7667": [3.06986, -4.38612, -0.86391], "3.8667": [3.23585, -5.03739, -0.607], "4.0": {"pre": [5.28411, -3.71784, -0.36591], "post": [5.28411, -3.71784, -0.36591], "lerp_mode": "catmullrom"}, "4.1": {"post": [6.73909, -0.04001, -0.71288], "lerp_mode": "catmullrom"}, "4.1667": {"post": [3.70352, 3.89074, -0.48332], "lerp_mode": "catmullrom"}, "4.2667": [2.925, 3.64, -0.18], "4.4333": [2.55, 3.64, -0.18], "4.5": [2.525, 3.65, 1.075], "4.6": [2.525, 3.65, 1.075], "4.6667": [2.525, 3.65, -4.95], "4.8": [2.525, 3.65, -4.95], "4.8667": [2.525, 3.65, 1.075], "4.9667": [2.525, 3.65, 1.075], "5.0333": [2.525, 3.65, -4.95], "5.2": [2.525, 3.65, -4.95], "5.2667": [2.525, 3.65, 1.075], "5.7333": [2.525, 3.65, 1.075], "5.8333": [5.21, 3.66, 3.75], "5.9333": [7.905, 3.675, 1.215], "6.2667": [11.08, 1.95, 1.215], "6.4333": {"pre": [4.73, -4.055, -0.56], "post": [4.73, -4.055, -0.56], "lerp_mode": "catmullrom"}, "6.5333": [0.6, -3.15, -0.8], "6.5667": [0.6, -3.325, -0.8], "6.6333": [0.53, -3.325, -0.605], "6.7": [0.53, -3.025, -0.23], "6.8": [0, -2.75, 0], "6.8667": [0, -2.6, 0], "6.9": [0, 0, 0]}}, "bolt": {"position": {"4.4333": [0, 0, 4.7], "4.5": [0, 0, 6.025], "4.6": [0, 0, 6.025], "4.6667": [0, 0, 0], "4.8": [0, 0, 0], "4.8667": [0, 0, 6.025], "4.9667": [0, 0, 6.025], "5.0333": [0, 0, 0], "5.2": [0, 0, 0], "5.2667": [0, 0, 6.025], "5.7333": [0, 0, 6.025], "5.7667": [0, 0, 0], "5.9333": [0, 0, 0], "6.1": [0, 0, 0], "7.6": [0, 0, 0], "7.7": [0, 0, 6.025], "7.9": [0, 0, 6.025], "8.0": [0, 0, 4.7]}}, "gun_and_rh": {"rotation": {"3.0667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.1": {"post": [0.24769, 0.05884, -4.49962], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-3.25006, -0.00982, 0.74994], "lerp_mode": "catmullrom"}, "3.4": {"post": [-3.07127, -1.97943, -3.14676], "lerp_mode": "catmullrom"}, "3.7333": {"post": [-0.27223, -0.23014, -3.09702], "lerp_mode": "catmullrom"}, "3.9333": {"post": [0.02917, -0.08324, -3.23879], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"3.0667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-0.48875, 0.46625, 0.005], "lerp_mode": "catmullrom"}, "3.4333": {"post": [-0.75875, 0.235, 0.005], "lerp_mode": "catmullrom"}, "3.7333": {"post": [-0.4575, 0.22, 0.005], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "additional_magazine": {"scale": 0}, "magrelease": {"position": {"2.7667": [0, 0, 0], "2.8333": [0.125, 0, 0], "3.2667": [0.125, 0, 0], "3.3333": [0, 0, 0], "4.0667": [0, 0, 0], "4.1": [0.125, 0, 0], "4.1667": [0.125, 0, 0], "4.2": [0, 0, 0]}}, "mag_and_bullet": {"rotation": {"3.7": [0, 0, 0], "3.9667": [0, 0, -25], "6.2667": [0, 0, -25], "6.5333": [0, 0, 0]}, "position": {"3.7": [0, 0, 0], "3.9667": [-1.475, 0, 0], "6.2667": [-1.475, 0, 0], "6.5333": [0, 0, 0]}}, "holdrelease": {"rotation": {"0.0": [0, 0, 15], "4.4667": [0, 0, 15], "4.5": [0, 0, 0], "7.6333": [0, 0, 0], "7.7": [0, 0, 15]}}, "bullet": {"scale": 0}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0.455, -0.05, -0.4], "lerp_mode": "catmullrom"}, "0.5333": {"post": [2.75, -1.15, -1.9], "lerp_mode": "catmullrom"}, "1.0": {"post": [3.4, -1.2, -2.1], "lerp_mode": "catmullrom"}, "1.4667": {"post": [3.25, -0.425, -2.35], "lerp_mode": "catmullrom"}, "1.8": {"post": [5.8, 2.35, -2.075], "lerp_mode": "catmullrom"}, "2.3667": {"post": [6.2, 2.875, -2.15], "lerp_mode": "catmullrom"}, "2.6667": {"post": [1.24, 0.84, -1.33], "lerp_mode": "catmullrom"}, "2.8": {"post": [1.025, 0.35, -1.15], "lerp_mode": "catmullrom"}, "2.9": {"post": [1.02, 0.18, -1.14], "lerp_mode": "catmullrom"}, "3.0": {"post": [1.435, 0.07, -1.16], "lerp_mode": "catmullrom"}, "3.0667": {"post": [2.97, 0, 0.175], "lerp_mode": "catmullrom"}, "3.2": {"post": [3.78, 0.1, -1.11], "lerp_mode": "catmullrom"}, "3.3667": {"post": [3.66, 0.1, -0.07], "lerp_mode": "catmullrom"}, "3.5667": {"post": [3.34, 0.105, -0.615], "lerp_mode": "catmullrom"}, "3.9667": {"post": [3, 0.34, -0.295], "lerp_mode": "catmullrom"}, "4.1333": {"post": [2.84, 1.8, 0.3], "lerp_mode": "catmullrom"}, "4.3667": {"post": [3.405, 2.775, 1.625], "lerp_mode": "catmullrom"}, "4.4667": {"post": [3.19, 2.86, 2.4], "lerp_mode": "catmullrom"}, "4.5333": {"post": [3.75, 2.89, 1.8], "lerp_mode": "catmullrom"}, "4.6": {"post": [3.12, 2.92, 2.525], "lerp_mode": "catmullrom"}, "4.7667": {"post": [4.085, 3.01, 2], "lerp_mode": "catmullrom"}, "4.8667": {"post": [3.015, 3.03, 2.555], "lerp_mode": "catmullrom"}, "4.9333": {"post": [4.195, 3.04, 1.62], "lerp_mode": "catmullrom"}, "5.0667": {"post": [3.27, 3.06, 2.325], "lerp_mode": "catmullrom"}, "5.1667": {"post": [2.98, 3.08, 1.75], "lerp_mode": "catmullrom"}, "5.2667": {"post": [3.725, 3.1, 2.635], "lerp_mode": "catmullrom"}, "5.4333": {"post": [3.1, 3.14, 1.79], "lerp_mode": "catmullrom"}, "5.6": {"post": [3.75, 3.18, 2.56], "lerp_mode": "catmullrom"}, "5.7333": {"post": [3.985, 3.2, 2.36], "lerp_mode": "catmullrom"}, "5.8333": {"post": [5.21, 3.175, 3.85], "lerp_mode": "catmullrom"}, "5.9333": {"post": [3.07, 2.88, 0.8], "lerp_mode": "catmullrom"}, "6.0667": {"post": [2.635, 2.325, 1.425], "lerp_mode": "catmullrom"}, "6.3": {"post": [2.475, 2.175, 1.325], "lerp_mode": "catmullrom"}, "6.5667": {"post": [4.56, 0.335, -0.09], "lerp_mode": "catmullrom"}, "6.7333": {"post": [4.22, 0.24, -0.16], "lerp_mode": "catmullrom"}, "6.8667": {"post": [4.13, 0.3, -0.11], "lerp_mode": "catmullrom"}, "6.9333": {"post": [4.925, 0.33, 0.845], "lerp_mode": "catmullrom"}, "7.0333": {"post": [3.64, 0.4, -0.795], "lerp_mode": "catmullrom"}, "7.1667": {"post": [4.46, 0.415, 0.22], "lerp_mode": "catmullrom"}, "7.3333": {"post": [4.185, 0.11, -0.42], "lerp_mode": "catmullrom"}, "7.6333": {"post": [4.1, -0.125, -0.715], "lerp_mode": "catmullrom"}, "7.7": {"post": [5.07, -0.17, -0.035], "lerp_mode": "catmullrom"}, "7.8667": {"post": [3.825, -0.23, -1.445], "lerp_mode": "catmullrom"}, "8.0333": {"post": [4.3, -0.34, -0.72], "lerp_mode": "catmullrom"}, "8.2": {"post": [4.7, -0.125, -0.715], "lerp_mode": "catmullrom"}, "8.4333": {"post": [5.88, 2.75, 1.93], "lerp_mode": "catmullrom"}, "8.7": {"post": [5.84, 3.09, 2.06], "lerp_mode": "catmullrom"}, "9.1": {"post": [5.57, 2.88, 1.725], "lerp_mode": "catmullrom"}, "9.2667": {"post": [2.635, 1.1, 1.425], "lerp_mode": "catmullrom"}, "9.5": {"post": [-0.525, 0.175, -0.35], "lerp_mode": "catmullrom"}, "9.6": {"post": [0.5, 0, 0.575], "lerp_mode": "catmullrom"}, "9.7333": {"post": [-0.25, 0.025, -0.3], "lerp_mode": "catmullrom"}, "9.9": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_raise"}, "1.4333": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_magrattle"}, "2.7": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_magout"}, "3.6667": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_magrattle"}, "4.4": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_empty_charge"}, "4.7667": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_empty_charge2"}, "4.9333": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_empty_boltback"}, "5.6667": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_empty_boltclose"}, "6.5": {"effect": "tacz:scar_h/p05_ar_schotel_reload_empty_magin"}, "7.0333": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_rotate"}, "7.3": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_empty_boltback2"}, "7.5": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_handling"}, "8.9333": {"effect": "tacz:scar_h/p05_ar_schotel_inspect_end"}}}, "shoot": {"animation_length": 0.66667, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0.16986, -0.11208, 2.14881], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.96137, -0.23295, -1.21862], "lerp_mode": "catmullrom"}, "0.1": {"post": [-0.99135, 0.12866, 1.12246], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0.77838, 0.0674, -0.52417], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0.27498, -0.0036, 0.17499], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0.425, -0.2, 0.525], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.07, -0.075, -0.05], "lerp_mode": "catmullrom"}, "0.6333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, -0.025, 1.7], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, -0.075, 1.555], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, -0.175, -0.12], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0, -0.025, 0.1], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"0.0": [0, 0, 0], "0.0333": [0, 0, 6], "0.1": [0, 0, 0], "0.1667": [0, 0, 0.45], "0.2333": [0, 0, 0], "0.3": [0, 0, 0.175], "0.3667": [0, 0, 0]}}, "bullet_in_barrel": {"scale": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.2": [1, 1, 1]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, 0, 0.6], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, 0, -0.4], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0, 0, 0.325], "lerp_mode": "catmullrom"}, "0.2": {"post": [0, 0, -0.225], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "constraint": {"rotation": {"0.1667": [0.002, 0.05, 0.006], "0.3333": [0, 0, 0]}, "position": {"0.1667": [0.01, 0.01, 0.25], "0.3333": [0, 0, 0]}}}}}}