
#Client Config. This config only exists on the client
[client]
	#Play sounds for Jetpack/Gas Mask/Flamethrower/Radiation (all players).
	enablePlayerSounds = true
	#If enabled machines play their sounds while running.
	enableMachineSounds = true
	#If enabled tries to force all radial menu text to be white.
	whiteRadialText = false
	#Should holiday greetings and easter eggs play for holidays (ex: Christmas and New Years).
	holidays = true
	#Adjust Mekanism sounds' base volume. < 1 is softer, higher is louder.
	#Range: 0.0 ~ 10.0
	baseSoundVolume = 1.0
	#If true, don't render Cables/Pipes/Tubes as transparent and don't render their contents.
	opaqueTransmitters = false
	#Allow sneak + scroll to change item modes.
	allowModeScroll = true
	#If true will move HUD text alignment and compass rendering to the right side of the screen, and move the MekaSuit module rendering to the left side.
	reverseHUD = false
	#Scale of the text displayed on the HUD.
	#Range: 0.25 ~ 1.0
	hudScale = 0.6
	#Enable item information HUD during gameplay
	enableHUD = false
	#Color of energy in item durability display.
	energyColor = 3997338
	#Range at which Tile Entity Renderer's added by Mekanism can render at, for example the contents of multiblocks. Vanilla defaults the rendering range for TERs to 64 for most blocks, but uses a range of 256 for beacons and end gateways.
	#Range: 1 ~ 1024
	terRange = 256

	#Particle Config
	[client.particle]
		#Set to false to prevent particle spam when loading multiblocks (notification message will display instead).
		enableMultiblockFormationParticles = true
		#Show particles when machines active.
		machineEffects = true
		#How far (in blocks) from the player radiation particles can spawn.
		#Range: 2 ~ 64
		radiationParticleRadius = 30
		#How many particles spawn when rendering radiation effects (scaled by radiation level).
		#Range: 0 ~ 1000
		radiationParticleCount = 100
		#Show bolts when the Magnetic Attraction Unit is pulling items.
		magneticAttraction = true
		#Show bolts for various AOE tool behaviors such as tilling, debarking, and vein mining.
		toolAOE = true

	#GUI Config
	[client.gui]
		#Opacity of HUD used by MekaSuit.
		#Range: 0.0 ~ 1.0
		hudOpacity = 0.4000000059604645
		#Color (RGB) of HUD used by MekaSuit.
		#Range: 0 ~ 16777215
		hudColor = 4257264
		#Color (RGB) of warning HUD elements used by MekaSuit.
		#Range: 0 ~ 16777215
		hudWarningColor = 16768335
		#Color (RGB) of danger HUD elements used by MekaSuit.
		#Range: 0 ~ 16777215
		hudDangerColor = 16726076
		#Visual jitter of MekaSuit HUD, seen when moving the player's head. Bigger value = more jitter.
		#Range: 1.0 ~ 100.0
		hudJitter = 6.0
		#Display a fancy compass when the MekaSuit is worn.
		mekaSuitHelmetCompass = true

		#Last Window Positions. In general these values should not be modified manually.
		[client.gui.window]

			[client.gui.window.color]
				x = 2147483647
				y = 2147483647

			[client.gui.window.confirmation]
				x = 2147483647
				y = 2147483647

			[client.gui.window.crafting0]
				x = 2147483647
				y = 2147483647

			[client.gui.window.crafting1]
				x = 2147483647
				y = 2147483647

			[client.gui.window.crafting2]
				x = 2147483647
				y = 2147483647

			[client.gui.window.mekaSuitHelmet]
				x = 2147483647
				y = 2147483647

			[client.gui.window.rename]
				x = 2147483647
				y = 2147483647

			[client.gui.window.skinSelect]
				x = 2147483647
				y = 2147483647

			[client.gui.window.sideConfig]
				x = 2147483647
				y = 2147483647

			[client.gui.window.transporterConfig]
				x = 2147483647
				y = 2147483647

			[client.gui.window.upgrade]
				x = 2147483647
				y = 2147483647

	#QIO Config
	[client.qio]
		#Sorting strategy when viewing items in a QIO Item Viewer.
		#Allowed Values: NAME, SIZE, MOD
		itemViewerSortType = "NAME"
		#Sorting direction when viewing items in a QIO Item Viewer.
		#Allowed Values: ASCENDING, DESCENDING
		itemViewerSortDirection = "ASCENDING"
		#Number of slots to view horizontally on a QIO Item Viewer.
		#Range: 8 ~ 16
		itemViewerSlotsX = 8
		#Number of slots to view vertically on a QIO Item Viewer.
		#Range: 2 ~ 48
		itemViewerSlotsY = 4

