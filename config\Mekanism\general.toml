
#General Config. This config is synced from server to client.
[general]
	#Log Mekanism packet names. Debug setting.
	logPackets = false
	#Disable to make the anchor upgrade not do anything.
	allowChunkloading = true
	#Enable this to allow dragging items from JEI into the target slot of Digital Miner filters.
	easyMinerFilters = false
	#How many ticks must pass until a block's active state is synced with the client, if it has been rapidly changing.
	#Range: 0 ~ 1200
	blockDeactivationDelay = 60
	#Any mod ids added to this list will not be able to have any of their blocks, picked up by the cardboard box. For example: ["mekanism"]
	cardboardModBlacklist = []
	#Allow right clicking on Cables/Pipes/Tubes with alloys to upgrade the tier.
	transmitterAlloyUpgrade = true
	#Base factor for working out machine performance with upgrades - UpgradeModifier * (UpgradesInstalled/UpgradesPossible).
	#Range: > 1
	maxUpgradeMultiplier = 10
	#How much Boiler heat is immediately usable to convert water to steam.
	#Range: 0.01 ~ 1.0
	boilerWaterConductivity = 0.7
	#Amount of heat produced per fuel tick of a fuel's burn time in the Fuelwood Heater.
	#Range: 0.1 ~ 4000000.0
	heatPerFuelTick = 400.0
	#Number of ticks to burn an item at in a Fuelwood Heater. Use this config option to effectively make Fuelwood Heater's burn faster but produce the same amount of heat per item.
	#Range: 1 ~ 1000
	fuelwoodTickMultiplier = 1
	#How much heat energy is created from one Joule of regular energy in the Resistive Heater.
	#Range: 0.0 ~ 1.0
	resistiveHeaterEfficiency = 0.6
	#Amount of heat each Boiler heating element produces.
	#Range: 0.1 ~ 1.024E9
	superheatingHeatTransfer = 1.6E7
	#Peak processing rate for the Solar Neutron Activator. Note: It can go higher than this value in some extreme environments.
	#Range: 1 ~ 1024
	maxSolarNeutronActivatorRate = 64

	#Dynamic Tank Settings
	[general.dynamic_tank]
		#Amount of fluid (mB) that each block of the dynamic tank contributes to the volume. Max = volume * fluidPerTank
		#Range: 1 ~ 368224
		fluidPerTank = 350000
		#Amount of chemical (mB) that each block of the dynamic tank contributes to the volume. Max = volume * chemicalPerTank
		#Range: 1 ~ 1581510980256305
		chemicalPerTank = 16000000

	#Auto Eject Settings
	[general.auto_eject]
		#Rate at which fluid gets auto ejected from tiles.
		#Range: > 1
		fluid = 1024
		#Rate at which chemicals gets auto ejected from tiles.
		#Range: 1 ~ 9223372036854775807
		chemical = 1024
		#The percentage of a tank's capacity to leave contents in when set to dumping excess.
		#Range: 0.001 ~ 1.0
		dumpExcessKeepRatio = 0.9

	#Prefilled Tanks
	[general.prefilled]
		#Add filled creative fluid tanks to creative/JEI.
		fluidTanks = true
		#Add filled creative gas tanks to creative/JEI.
		gasTanks = true
		#Add filled creative infusion tanks to creative/JEI.
		infusionTanks = true
		#Add filled creative pigment tanks to creative/JEI.
		pigmentTanks = true
		#Add filled creative slurry tanks to creative/JEI.
		slurryTanks = true

	#Energy Conversion Rate Settings
	[general.energy_conversion]
		#Disables IC2 power integration. Requires world restart (server-side option in SMP).
		blacklistIC2 = false
		#Conversion multiplier from EU to Joules (EU * euConversionRate = Joules)
		euConversionRate = "10"
		#Disables Forge Energy (FE,RF,IF,uF,CF) power integration. Requires world restart (server-side option in SMP).
		blacklistForge = false
		#Conversion multiplier from Forge Energy to Joules (FE * feConversionRate = Joules)
		feConversionRate = "2.5000"
		#Disables Flux Networks higher throughput Forge Energy (FE,RF,IF,uF,CF) power integration. Requires world restart (server-side option in SMP). Note: Disabling Forge Energy integration also disables this.
		blacklistFluxNetworks = false
		#How much energy is produced per mB of Hydrogen, also affects Electrolytic Separator usage, Ethylene burn rate and Gas generator energy capacity.
		HydrogenEnergyDensity = "200"
		#Maximum Joules per mB of Steam. Also affects Thermoelectric Boiler.
		maxEnergyPerSteam = "10"

	#Radiation Settings
	[general.radiation]
		#Enable worldwide radiation effects. Don't be a downer and disable this.
		radiationEnabled = true
		#The radius of chunks checked when running radiation calculations. The algorithm is efficient, but don't abuse it by making this crazy high.
		#Range: 1 ~ 100
		chunkCheckRadius = 5
		#Radiation sources are multiplied by this constant roughly once per second to represent their emission decay. At the default rate, it takes roughly 10 hours to remove a 1,000 Sv/h (crazy high) source.
		#Range: 0.0 ~ 1.0
		sourceDecayRate = 0.9995
		#Radiated objects and entities are multiplied by this constant roughly once per second to represent their dosage decay.
		#Range: 0.0 ~ 1.0
		targetDecayRate = 0.9995
		#Defines the minimum severity radiation dosage severity (scale of 0 to 1) for which negative effects can take place. Set to 1 to disable negative effects completely.
		#Range: 0.0 ~ 1.0
		negativeEffectsMinSeverity = 0.1
		#Amount of gas (mB) that can be stored in a Radioactive Waste Barrel.
		#Range: 1 ~ 9223372036854775807
		radioactiveWasteBarrelMaxGas = 512000
		#Number of ticks required for radioactive gas stored in a Radioactive Waste Barrel to decay radioactiveWasteBarrelDecayAmount mB.
		#Range: > 1
		radioactiveWasteBarrelProcessTicks = 20
		#Number of mB of gas that decay every radioactiveWasteBarrelProcessTicks ticks when stored in a Radioactive Waste Barrel. Set to zero to disable decay all together. (Gases in the mekanism:waste_barrel_decay_blacklist tag will not decay).
		#Range: 0 ~ 9223372036854775807
		radioactiveWasteBarrelDecayAmount = 1

	#Digital Miner Settings
	[general.digital_miner]
		#Energy multiplier for using silk touch mode with the Digital Miner.
		#Range: > 1
		silkMultiplier = 12
		#Maximum radius in blocks that the Digital Miner can reach. (Increasing this may have negative effects on stability and/or performance. We strongly recommend you leave it at the default value).
		#Range: > 1
		maxRadius = 32
		#Number of ticks required to mine a single block with a Digital Miner (without any upgrades).
		#Range: > 1
		ticksPerMine = 80

	#Laser Settings
	[general.laser]
		#If enabled, lasers can break blocks and the flamethrower starts fires.
		aestheticWorldDamage = true
		#How far (in blocks) a laser can travel.
		#Range: 1 ~ 1024
		range = 64
		#Energy needed to destroy or attract blocks with a Laser (per block hardness level).
		energyNeededPerHardness = "100000"
		#Energy used per half heart of damage being transferred to entities.
		energyPerDamage = "2500"

	#Oredictionificator Settings
	[general.oredictionificator]
		#The list of valid tag prefixes for the Oredictionificator. Note: It is highly recommended to only include well known/defined tag prefixes otherwise it is very easy to potentially add in accidental conversions of things that are not actually equivalent.
		validItemFilters = ["forge:dusts/", "forge:ingots/", "forge:nuggets/", "forge:ores/", "forge:raw_materials/", "forge:storage_blocks/"]

	#Pump Settings
	[general.pump]
		#Maximum block distance to pull fluid from for the Electric Pump.
		#Range: 1 ~ 512
		maxPumpRange = 80
		#If enabled makes Water and Heavy Water blocks be removed from the world on pump.
		pumpWaterSources = false
		#mB of Heavy Water that is extracted per block of Water by the Electric Pump with a Filter Upgrade.
		#Range: 1 ~ 1000
		pumpHeavyWaterAmount = 10
		#Fluidic Plenisher stops after this many blocks.
		#Range: 1 ~ 1000000
		maxPlenisherNodes = 4000

	#Quantum Entangloporter Settings
	[general.quantum_entangloporter]
		#Maximum energy buffer (Mekanism Joules) of an Entangoloporter frequency - i.e. the maximum transfer per tick per frequency. Default is ultimate tier energy cube capacity.
		energyBuffer = "256000000"
		#Maximum fluid buffer (mb) of an Entangoloporter frequency - i.e. the maximum transfer per tick per frequency. Default is ultimate tier tank capacity.
		#Range: > 1
		fluidBuffer = 256000
		#Maximum chemical buffer (mb) of an Entangoloporter frequency - i.e. the maximum transfer per tick per frequency. Default is ultimate tier tank capacity.
		#Range: 1 ~ 9223372036854775807
		chemicalBuffer = 8192000

	#Block security/protection Settings
	[general.security]
		#Enable the security system for players to prevent others from accessing their machines. Does NOT affect Frequencies.
		allowProtection = true
		#If this is enabled then players with the 'mekanism.bypass_security' permission (default ops) can bypass the block and item security restrictions.
		opsBypassRestrictions = false

	#Nutritional Paste Settings
	[general.nutritional_paste]
		#Saturation level of Nutritional Paste when eaten.
		#Range: 0.0 ~ 100.0
		saturation = 0.8
		#How much mB of Nutritional Paste equates to one 'half-food.'
		#Range: > 1
		mbPerFood = 50

	#Boiler Settings
	[general.boiler]
		#Amount of fluid (mB) that each block of the boiler's water portion contributes to the volume. Max = volume * waterPerTank
		#Range: 1 ~ 368224
		waterPerTank = 16000
		#Amount of steam (mB) that each block of the boiler's steam portion contributes to the volume. Max = volume * steamPerTank
		#Range: 10 ~ 1581510980256305
		steamPerTank = 160000
		#Amount of steam (mB) that each block of the boiler's heated coolant portion contributes to the volume. Max = volume * heatedCoolantPerTank
		#Range: 1 ~ 1581510980256305
		heatedCoolantPerTank = 256000
		#Amount of steam (mB) that each block of the boiler's cooled coolant portion contributes to the volume. Max = volume * cooledCoolantPerTank
		#Range: 1 ~ 1581510980256305
		cooledCoolantPerTank = 256000

	#Thermal Evaporation Plant Settings
	[general.thermal_evaporation]
		#Thermal Evaporation Tower heat loss per tick.
		#Range: 0.001 ~ 1000.0
		heatDissipation = 0.02
		#Temperature to amount produced ratio for Thermal Evaporation Tower.
		#Range: 0.001 ~ 1000000.0
		tempMultiplier = 0.4
		#Heat to absorb per Solar Panel array of Thermal Evaporation Tower.
		#Range: 0.001 ~ 1000000.0
		solarMultiplier = 0.2
		#Heat capacity of Thermal Evaporation Tower layers (increases amount of energy needed to increase temperature).
		#Range: 1.0 ~ 1000000.0
		heatCapacity = 100.0
		#Amount of fluid (mB) that each block of the evaporation plant contributes to the input tank capacity. Max = volume * fluidPerTank
		#Range: 1 ~ 29826161
		fluidPerTank = 64000
		#Amount of output fluid (mB) that the evaporation plant can store.
		#Range: > 1
		outputTankCapacity = 10000

	#SPS Settings
	[general.sps]
		#How much input gas (polonium) in mB must be processed to make 1 mB of antimatter. Input tank capacity is 2x this value.
		#Range: > 1
		inputPerAntimatter = 1000
		#Amount of output gas (mB, antimatter) that the SPS can store.
		#Range: 1 ~ 9223372036854775807
		outputTankCapacity = 1000
		#Energy needed to process 1 mB of input (inputPerAntimatter * energyPerInput = energy to produce 1 mB of antimatter).
		energyPerInput = "1000000"

