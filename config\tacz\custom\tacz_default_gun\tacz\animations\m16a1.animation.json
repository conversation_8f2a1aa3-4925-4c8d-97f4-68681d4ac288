{"format_version": "1.8.0", "animations": {"static_idle": {"animation_length": 0.1, "bones": {"lefthand": {"rotation": [99.10245, -28.39169, -137.40361], "position": [8.025, -11.85, -4.95], "scale": [1, 1.5, 1]}, "righthand": {"rotation": [92.4939, 3.99619, -180.17476], "position": [-6.9, -14.125, 8.425], "scale": [1, 1.5, 1]}, "bolt_release": {"rotation": [0, 0, 0]}, "constraint": {"rotation": [0.1, 0.1, 0.1], "position": [0.1, 0.1, 0.35]}}}, "static_bolt_caught": {"animation_length": 0.1, "bones": {"lefthand": {"rotation": [99.10245, -28.39169, -137.40361], "position": [8.025, -11.85, -4.95], "scale": [1, 1.5, 1]}, "righthand": {"rotation": [92.4939, 3.99619, -180.17476], "position": [-6.9, -14.125, 8.425], "scale": [1, 1.5, 1]}, "bullet": {"scale": 0}, "bullet_in_mag": {"scale": 0}, "bolt": {"position": [0, 0, 5]}, "bolt_release": {"rotation": [0, 0, 15]}}}, "draw": {"animation_length": 0.78431, "bones": {"lefthand": {"rotation": {"0.0": [92.44585, -22.23047, -119.50882], "0.1961": [99.10245, -28.39169, -137.40361]}, "position": {"0.0": [15.57, -23.23, -4.95], "0.1961": [8.025, -11.85, -4.95]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": [92.4939, 3.99619, -180.17476], "position": [-6.9, -14.125, 8.425], "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": [11.65263, -27.91875, 35.6415], "0.3137": {"pre": [0.82459, -2.8871, 7.79742], "post": [0.82459, -2.8871, 7.79742], "lerp_mode": "catmullrom"}, "0.549": {"post": [-0.065, -0.095, 0.79], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, -0.03, 0.1], "lerp_mode": "catmullrom"}, "0.7843": [0, 0, 0]}, "position": {"0.0": [0.0625, -3.14062, 13.32813], "0.1961": {"pre": [0.275, -1.45, -0.38438], "post": [0.275, -1.45, -0.38438], "lerp_mode": "catmullrom"}, "0.3529": {"post": [0.09, -0.56, -1.2], "lerp_mode": "catmullrom"}, "0.4706": {"post": [-0.05, -0.075, 0.265], "lerp_mode": "catmullrom"}, "0.5882": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.7451": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1961": {"post": [0.225, -0.2, -0.375], "lerp_mode": "catmullrom"}, "0.4314": {"post": [-0.22, 0.15, 0.1], "lerp_mode": "catmullrom"}, "0.5882": {"post": [0.03, 0, -0.02], "lerp_mode": "catmullrom"}, "0.7451": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.0": {"effect": "m4a1_raise_v2"}}}, "put_away": {"animation_length": 0.53333, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.51171, -3.6801, -4.19401], "lerp_mode": "catmullrom"}, "0.1333": {"post": [4.36882, -11.73179, -10.29382], "lerp_mode": "catmullrom"}, "0.2333": {"post": [12.74519, -20.93019, -6.8093], "lerp_mode": "catmullrom"}, "0.4": {"post": [32.04283, -33.29116, -11.84847], "lerp_mode": "catmullrom"}, "0.5": {"post": [49.81196, -45.49555, -15.70446], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.45, -0.49, 0.28], "lerp_mode": "catmullrom"}, "0.1333": {"post": [-0.67, -1.47, 0.34], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.435, -3.42, 0.06], "lerp_mode": "catmullrom"}, "0.4": {"post": [0.92, -7.375, -0.335], "lerp_mode": "catmullrom"}, "0.5333": {"post": [4.125, -13.425, -0.35], "lerp_mode": "catmullrom"}}}, "lefthand": {"rotation": {"0.0": [99.10245, -28.39169, -137.40361], "0.2": [148.89318, -7.54994, -212.09431]}, "position": {"0.0": [8.025, -11.85, -4.95], "0.1": [9.045, -11.335, -4.995], "0.2": [8.42, -9.395, -4.695]}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": [92.4939, 3.99619, -180.17476], "position": [-6.9, -14.125, 8.425], "scale": [1, 1.5, 1]}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.3, 0, -0.125], "lerp_mode": "catmullrom"}, "0.2333": {"post": [0.64, 0, 0.3], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0.5, 0, 0.15], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.1": {"effect": "p01_ar_mike4_drop"}}}, "reload_tactical": {"animation_length": 2.33333, "bones": {"lefthand": {"rotation": {"0.0": [99.10245, -28.39169, -137.40361], "0.1": {"pre": [99.1, -28.39, -137.4], "post": [99.1, -28.39, -137.4], "lerp_mode": "catmullrom"}, "0.2": {"post": [100.31175, -38.9876, -139.57508], "lerp_mode": "catmullrom"}, "0.4333": {"post": [85.17283, 2.63043, -178.59904], "lerp_mode": "catmullrom"}, "0.9": {"post": [85.17283, 2.63043, -178.59904], "lerp_mode": "catmullrom"}, "0.9333": [86.01133, -17.67975, -99.41687], "1.2333": [86.01133, -17.67975, -99.41687], "1.5": [86.01133, -17.67975, -99.41687], "1.6": {"pre": [95.87634, -23.82565, -123.30512], "post": [95.87634, -23.82565, -123.30512], "lerp_mode": "catmullrom"}, "1.7": {"post": [99.10245, -28.39169, -137.40361], "lerp_mode": "catmullrom"}}, "position": {"0.0": [8.025, -11.85, -4.95], "0.1": [8.01827, -11.87966, -5.47403], "0.2": [18.63567, -20.33154, 1.41755], "0.4333": [11.305, -28.675, 23.45], "0.9": [11.305, -28.675, 23.45], "0.9333": [6.05, -17.205, 2.61], "1.2333": [6.575, -16.405, 2.61], "1.4333": [6.575, -16.405, 2.61], "1.5": [6.26, -17.205, 2.61], "1.5667": {"pre": [9.05, -17.205, 2.61], "post": [9.05, -17.205, 2.61], "lerp_mode": "catmullrom"}, "1.6333": {"post": [9.5, -13.95, -0.19], "lerp_mode": "catmullrom"}, "1.7": {"post": [8.025, -11.85, -4.95], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [92.4939, 3.99619, -180.17476], "1.7333": [92.49, 4, -180.17]}, "position": {"0.0": [-6.9, -14.125, 8.425], "1.7333": {"pre": [-6.9, -14.12, 8.43], "post": [-6.9, -14.12, 8.43], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-6.9, -14.125, 8.425], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-3.24, -1.28, -1.66], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-1.22602, -1.98699, 9.54848], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.83433, -2.37009, 18.91092], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.58444, -2.70769, 28.33281], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.73487, -3.45148, 26.82664], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-9.85, -0.28, -24.255], "lerp_mode": "catmullrom"}, "0.4": {"post": [-13.92455, 0.79241, -38.84884], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-16.37264, -2.00707, -43.27927], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-17.54955, -4.52082, -36.08161], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-18.70151, -4.40429, -32.66848], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-19.08127, -4.06214, -28.29379], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-15.63, -4.18, -33.175], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-15.72172, -2.86761, -33.25168], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-17.86625, -6.70691, -42.9336], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-15.97554, -4.4902, -43.45699], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-14.34425, -3.09249, -35.49442], "lerp_mode": "catmullrom"}, "1.4": {"post": [-16.06862, -4.66605, -37.57407], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-14.69313, -3.11145, -32.3039], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-10.88, -4.24, -29.045], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-7.95046, -3.54216, -0.80782], "lerp_mode": "catmullrom"}, "1.7667": {"post": [3.11693, -1.05943, 3.30619], "lerp_mode": "catmullrom"}, "1.8667": {"post": [0.57, 0.55, -2.295], "lerp_mode": "catmullrom"}, "1.9333": {"post": [-0.5, -1.225, 1.2], "lerp_mode": "catmullrom"}, "2.0667": {"post": [0, 0, 0.05], "lerp_mode": "catmullrom"}, "2.2": {"post": [0, 0, -0.09], "lerp_mode": "catmullrom"}, "2.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [0.12, -0.17, 0.225], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.645, -0.61, 0.74], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-1.335, -0.63, 1.225], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-1.035, 0.195, 1.475], "lerp_mode": "catmullrom"}, "0.4": {"post": [-0.16, 0.52, 1.1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.075, 0.345, 0.645], "lerp_mode": "catmullrom"}, "0.5667": {"post": [0.28, 0.23, 0.42], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.475, 0.095, 0.47], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0.77, -0.525, 0.655], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0.855, -0.59, 0.81], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0.62, -0.49, 0.655], "lerp_mode": "catmullrom"}, "1.0667": {"post": [0.405, -0.075, 0.38], "lerp_mode": "catmullrom"}, "1.2333": [0.26, 0.235, 0.185], "1.3333": [0.115, 0.23, 0.185], "1.4": {"pre": [0.485, -0.83, 0.185], "post": [0.485, -0.83, 0.185], "lerp_mode": "catmullrom"}, "1.5": {"post": [0.255, -0.68, 0.185], "lerp_mode": "catmullrom"}, "1.5667": {"post": [0.375, -0.62, -0.04], "lerp_mode": "catmullrom"}, "1.7333": {"post": [0.27, -1.72, -0.9], "lerp_mode": "catmullrom"}, "1.8667": {"post": [-0.07, -0.12, 0.595], "lerp_mode": "catmullrom"}, "1.9667": {"post": [-0.07, 0.07, -0.05625], "lerp_mode": "catmullrom"}, "2.1": {"post": [0, 0.055, 0.055], "lerp_mode": "catmullrom"}, "2.3": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"0.0": [0, 0, 0], "0.9": {"pre": [0, 0, 0], "post": [-1.5, 0, 0]}, "1.0333": [-35.53512, -24.39375, -13.65478], "1.1667": [-10.13685, -1.53119, -1.58329], "1.3333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.9": {"pre": [0, 0, 0], "post": [0, -16.12, 2.26]}, "1.0333": [2.42, -8.24, -2.9], "1.1667": [0.295, -3.495, -0.89], "1.2333": [0.17, -3.395, -0.465], "1.3333": [0, -3.325, 0], "1.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_bullet": {"rotation": {"0.3667": [0, 0, 0], "0.5": [-12.5, -5, -143.94], "0.6667": [6.71216, -0.48552, -228.01498], "0.7667": [0, 0, -275.24], "0.9": {"pre": [0, 0, 2.26], "post": [0, 0, 0]}}, "position": {"0.3": [0, 0, 0], "0.3667": [0.05, -2.83, -0.01], "0.5": [4.20825, -10.97516, -5.06901], "0.5667": [7.55006, -14.96268, -5.96782], "0.6333": [8.77993, -21.77805, -6.21685], "0.8333": [6.96066, -62.13778, -0.01632], "0.9": {"pre": [0, -58.38, -0.5], "post": [0, 0, 0]}}}, "mag_release": {"position": {"0.2667": [0, 0, 0], "0.3": [0.13125, 0, 0], "0.6": [0.13125, 0, 0], "0.6667": [0, 0, 0], "1.3333": [0, 0, 0], "1.3667": [0.13125, 0, 0], "1.4333": [0.13125, 0, 0], "1.4667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2333": {"post": [1.7, 0.425, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [1.445, -0.14, -2.145], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-1.075, -0.975, -0.975], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-0.76, -0.91, -1.85], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0.6, -0.575, -1.225], "lerp_mode": "catmullrom"}, "0.8667": {"post": [1.2, 0.12, -0.975], "lerp_mode": "catmullrom"}, "1.0333": {"post": [1.555, 0.275, -0.275], "lerp_mode": "catmullrom"}, "1.3333": {"post": [1.46, -0.08, -0.56], "lerp_mode": "catmullrom"}, "1.4": {"post": [2.235, -0.145, 0.575], "lerp_mode": "catmullrom"}, "1.5": {"post": [1.41, -0.16, -1.285], "lerp_mode": "catmullrom"}, "1.6333": {"post": [1.435, -0.17, 0.28], "lerp_mode": "catmullrom"}, "1.7667": {"post": [0.815, -0.18, 0.11], "lerp_mode": "catmullrom"}, "1.9": {"post": [-0.325, 0, -0.225], "lerp_mode": "catmullrom"}, "1.9667": {"post": [0.12, 0.01, 0.33], "lerp_mode": "catmullrom"}, "2.0667": {"post": [-0.125, 0, -0.175], "lerp_mode": "catmullrom"}, "2.1667": {"post": [0.06, 0, 0.05], "lerp_mode": "catmullrom"}, "2.3": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.1333": {"effect": "p01_ar_mike16_reload_empty_fast_rattle"}, "0.3": {"effect": "p01_ar_mike16_reload_fast_magout"}, "0.9333": {"effect": "iw8_phys_mag_drop_ar_poly_dirt_02"}, "1.0": {"effect": "p01_ar_mike16_reload_empty_fast_end"}, "1.1333": {"effect": "p01_ar_mike16_reload_magin"}}}, "reload_empty": {"animation_length": 3.1, "bones": {"lefthand": {"rotation": {"0.0": [99.10245, -28.39169, -137.40361], "0.1": {"pre": [99.1, -28.39, -137.4], "post": [99.1, -28.39, -137.4], "lerp_mode": "catmullrom"}, "0.2": {"post": [100.31175, -38.9876, -139.57508], "lerp_mode": "catmullrom"}, "0.4333": {"post": [85.17283, 2.63043, -178.59904], "lerp_mode": "catmullrom"}, "0.9": {"post": [85.17283, 2.63043, -178.59904], "lerp_mode": "catmullrom"}, "0.9333": [86.01133, -17.67975, -99.41687], "1.2333": [86.01133, -17.67975, -99.41687], "1.5": [86.01133, -17.67975, -99.41687], "1.6": {"pre": [95.87634, -23.82565, -123.30512], "post": [95.87634, -23.82565, -123.30512], "lerp_mode": "catmullrom"}, "1.7": {"post": [99.10245, -28.39169, -137.40361], "lerp_mode": "catmullrom"}}, "position": {"0.0": [8.025, -11.85, -4.95], "0.1": [8.01827, -11.87966, -5.47403], "0.2": [18.63567, -20.33154, 1.41755], "0.4333": [11.305, -28.675, 23.45], "0.9": [11.305, -28.675, 23.45], "0.9333": [6.05, -17.205, 2.61], "1.2333": [6.575, -16.405, 2.61], "1.4333": [6.575, -16.405, 2.61], "1.5": [6.26, -17.205, 2.61], "1.5667": {"pre": [9.05, -17.205, 2.61], "post": [9.05, -17.205, 2.61], "lerp_mode": "catmullrom"}, "1.6333": {"post": [9.5, -13.95, -0.19], "lerp_mode": "catmullrom"}, "1.7": {"post": [8.025, -11.85, -4.95], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "righthand": {"rotation": {"0.0": [92.4939, 3.99619, -180.17476], "1.7333": [92.49, 4, -180.17], "1.8333": [120.50449, 2.47405, -98.74171], "1.9667": [118.87373, -10.57327, -76.14209], "2.0": [118.87373, -10.57327, -76.14209], "2.1": [118.87373, -10.57327, -76.14209], "2.1667": [128.70408, 25.91809, -148.67814], "2.3333": [134.75972, 28.43589, -146.36771], "2.5": [92.4939, 3.99619, -180.17476]}, "position": {"0.0": [-6.9, -14.125, 8.425], "1.7333": {"pre": [-6.9, -14.12, 8.43], "post": [-6.9, -14.12, 8.43], "lerp_mode": "catmullrom"}, "1.8333": {"post": [-11.1, -11.79, 9.86], "lerp_mode": "catmullrom"}, "1.9": {"post": [-9.55, -9.46, 11.29], "lerp_mode": "catmullrom"}, "1.9667": [-7.64385, -8.51607, 10.56676], "2.0": [-7.64385, -8.51607, 10.56676], "2.1": [-7.925, -8.51, 15.14], "2.1667": [-11.715, -10.1, 18.27], "2.3667": {"pre": [-11.465, -14.64, 12.925], "post": [-11.465, -14.64, 12.925], "lerp_mode": "catmullrom"}, "2.5": {"post": [-6.87, -14.67, 7.335], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-6.9, -14.125, 8.425], "lerp_mode": "catmullrom"}}, "scale": [1, 1.5, 1]}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-3.24, -1.28, -1.66], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-1.22602, -1.98699, 9.54848], "lerp_mode": "catmullrom"}, "0.2": {"post": [-0.83433, -2.37009, 18.91092], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.58444, -2.70769, 28.33281], "lerp_mode": "catmullrom"}, "0.3": {"post": [-0.73487, -3.45148, 26.82664], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-9.85, -0.28, -24.255], "lerp_mode": "catmullrom"}, "0.4": {"post": [-13.92455, 0.79241, -38.84884], "lerp_mode": "catmullrom"}, "0.4667": {"post": [-16.37264, -2.00707, -43.27927], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-17.54955, -4.52082, -36.08161], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-18.70151, -4.40429, -32.66848], "lerp_mode": "catmullrom"}, "0.7667": {"post": [-19.08127, -4.06214, -28.29379], "lerp_mode": "catmullrom"}, "0.9333": {"post": [-15.63, -4.18, -33.175], "lerp_mode": "catmullrom"}, "1.0667": {"post": [-15.72172, -2.86761, -33.25168], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-17.86625, -6.70691, -42.9336], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-15.97554, -4.4902, -43.45699], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-14.34425, -3.09249, -35.49442], "lerp_mode": "catmullrom"}, "1.4": {"post": [-16.06862, -4.66605, -37.57407], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-14.69313, -3.11145, -32.3039], "lerp_mode": "catmullrom"}, "1.5667": {"post": [-10.88, -4.24, -29.045], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-7.95046, -3.54216, -0.80782], "lerp_mode": "catmullrom"}, "1.7667": {"post": [2.81208, -8.30626, 4.01644], "lerp_mode": "catmullrom"}, "1.9333": {"post": [7.28088, -8.33013, 5.61688], "lerp_mode": "catmullrom"}, "2.0333": {"post": [10.72465, -8.27132, 6.36668], "lerp_mode": "catmullrom"}, "2.1": {"post": [9.46254, -6.43066, -2.89125], "lerp_mode": "catmullrom"}, "2.1667": {"post": [8.4356, -7.45221, 0.63854], "lerp_mode": "catmullrom"}, "2.2333": {"post": [12.85386, -8.05388, 5.41473], "lerp_mode": "catmullrom"}, "2.3": {"post": [13.191, -6.78448, -0.62876], "lerp_mode": "catmullrom"}, "2.3667": {"post": [10.7074, -7.54448, 0.95859], "lerp_mode": "catmullrom"}, "2.4667": {"post": [3.24432, -7.52563, -0.52333], "lerp_mode": "catmullrom"}, "2.6": {"post": [0.57, 0.55, -2.295], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.5, -1.225, 1.2], "lerp_mode": "catmullrom"}, "2.8": {"post": [0, 0, 0.05], "lerp_mode": "catmullrom"}, "2.9667": {"post": [0, 0, -0.09], "lerp_mode": "catmullrom"}, "3.1": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [0.12, -0.17, 0.225], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-0.645, -0.61, 0.74], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-1.335, -0.63, 1.225], "lerp_mode": "catmullrom"}, "0.3333": {"post": [-1.035, 0.195, 1.475], "lerp_mode": "catmullrom"}, "0.4": {"post": [-0.16, 0.52, 1.1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.075, 0.345, 0.645], "lerp_mode": "catmullrom"}, "0.5667": {"post": [0.28, 0.23, 0.42], "lerp_mode": "catmullrom"}, "0.6": {"post": [0.475, 0.095, 0.47], "lerp_mode": "catmullrom"}, "0.7333": {"post": [0.77, -0.525, 0.655], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0.855, -0.59, 0.81], "lerp_mode": "catmullrom"}, "0.9667": {"post": [0.62, -0.49, 0.655], "lerp_mode": "catmullrom"}, "1.0667": {"post": [0.405, -0.075, 0.38], "lerp_mode": "catmullrom"}, "1.2333": [0.26, 0.235, 0.185], "1.3333": [0.115, 0.23, 0.185], "1.4": {"pre": [0.485, -0.83, 0.185], "post": [0.485, -0.83, 0.185], "lerp_mode": "catmullrom"}, "1.5": {"post": [0.255, -0.68, 0.185], "lerp_mode": "catmullrom"}, "1.6": {"post": [0.375, -0.62, -0.04], "lerp_mode": "catmullrom"}, "1.7": {"post": [0.27, -1.72, -0.9], "lerp_mode": "catmullrom"}, "1.8333": {"post": [0.47, -2.31, -1.155], "lerp_mode": "catmullrom"}, "1.9333": {"post": [0.47, -2.045, -0.705], "lerp_mode": "catmullrom"}, "2.0333": {"post": [0.47, -2.045, -0.705], "lerp_mode": "catmullrom"}, "2.1333": {"post": [0.47, -2.265, -0.465], "lerp_mode": "catmullrom"}, "2.2": {"post": [0.33, -1.77, -0.41], "lerp_mode": "catmullrom"}, "2.3667": {"post": [0.45, -2.37, -0.36], "lerp_mode": "catmullrom"}, "2.5333": {"post": [0.055, -0.705, -0.245], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.07, -0.12, 0.695], "lerp_mode": "catmullrom"}, "2.8": {"post": [0, 0.055, 0.055], "lerp_mode": "catmullrom"}, "3.0333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"0.0": [0, 0, 0], "0.9": {"pre": [0, 0, 0], "post": [-1.5, 0, 0]}, "1.0333": [-35.53512, -24.39375, -13.65478], "1.1667": [-10.13685, -1.53119, -1.58329], "1.3333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.9": {"pre": [0, 0, 0], "post": [0, -16.12, 2.26]}, "1.0333": [2.42, -8.24, -2.9], "1.1667": [0.295, -3.495, -0.89], "1.2333": [0.17, -3.395, -0.465], "1.3333": [0, -3.325, 0], "1.3667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_bullet": {"rotation": {"0.3667": [0, 0, 0], "0.5": [-12.5, -5, -143.94], "0.6667": [6.71216, -0.48552, -228.01498], "0.7667": [0, 0, -275.24], "0.9": {"pre": [0, 0, 2.26], "post": [0, 0, 0]}}, "position": {"0.3": [0, 0, 0], "0.3667": [0.05, -2.83, -0.01], "0.5": [4.20825, -10.97516, -5.06901], "0.5667": [7.55006, -14.96268, -5.96782], "0.6333": [8.77993, -21.77805, -6.21685], "0.8333": [6.96066, -62.13778, -0.01632], "0.9": {"pre": [0, -58.38, -0.5], "post": [0, 0, 0]}}}, "bolt": {"position": {"2.1333": [0, 0, 5], "2.2": [0, 0, 0]}}, "pull2": {"position": {"2.0": [0, 0, 0], "2.0667": [0, 0, 5], "2.1": [0, 0, 5], "2.2": [0, 0, 0]}}, "bullet": {"scale": {"0.0": [0, 0, 0], "0.9333": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "bolt_release": {"rotation": {"0.0": [0, 0, 15], "2.0333": [0, 0, 15], "2.0667": [0, 0, 0]}}, "mag_release": {"position": {"0.2667": [0, 0, 0], "0.3": [0.13125, 0, 0], "0.6": [0.13125, 0, 0], "0.6667": [0, 0, 0], "1.3333": [0, 0, 0], "1.3667": [0.13125, 0, 0], "1.4333": [0.13125, 0, 0], "1.4667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2333": {"post": [1.7, 0.425, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [1.445, -0.14, -2.145], "lerp_mode": "catmullrom"}, "0.4333": {"post": [-1.075, -0.975, -0.975], "lerp_mode": "catmullrom"}, "0.5667": {"post": [-0.76, -0.91, -1.85], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0.6, -0.575, -1.225], "lerp_mode": "catmullrom"}, "0.8667": {"post": [1.2, 0.12, -0.975], "lerp_mode": "catmullrom"}, "1.0333": {"post": [1.555, 0.275, -0.275], "lerp_mode": "catmullrom"}, "1.3333": {"post": [1.46, -0.08, -0.56], "lerp_mode": "catmullrom"}, "1.4": {"post": [2.235, -0.145, 0.575], "lerp_mode": "catmullrom"}, "1.5": {"post": [1.41, -0.16, -1.285], "lerp_mode": "catmullrom"}, "1.6333": {"post": [1.96, -0.17, 0.28], "lerp_mode": "catmullrom"}, "1.7667": {"post": [1.94, -0.18, 0.11], "lerp_mode": "catmullrom"}, "1.9": {"post": [2.455, -0.195, 0.4], "lerp_mode": "catmullrom"}, "2.0": {"post": [2.51, -0.2, 0.49], "lerp_mode": "catmullrom"}, "2.0667": {"post": [3.095, -0.2, 1.54], "lerp_mode": "catmullrom"}, "2.1667": {"post": [1.77, -0.21, -0.08], "lerp_mode": "catmullrom"}, "2.3": {"post": [2.25, -0.195, 0.65], "lerp_mode": "catmullrom"}, "2.4333": {"post": [1.41, -0.13, 0.215], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.325, 0, -0.225], "lerp_mode": "catmullrom"}, "2.7333": {"post": [0.12, 0.01, 0.33], "lerp_mode": "catmullrom"}, "2.8333": {"post": [-0.125, 0, -0.175], "lerp_mode": "catmullrom"}, "2.9333": {"post": [0.06, 0, 0.05], "lerp_mode": "catmullrom"}, "3.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.1333": {"effect": "p01_ar_mike16_reload_empty_fast_rattle"}, "0.3": {"effect": "p01_ar_mike16_reload_fast_magout"}, "0.9333": {"effect": "iw8_phys_mag_drop_ar_poly_dirt_02"}, "1.1333": {"effect": "p01_ar_mike16_reload_magin"}, "1.6667": {"effect": "p01_ar_mike16_reload_empty_fast_end"}, "1.9333": {"effect": "p01_ar_mike4_inspect_empty_handle"}, "2.1667": {"effect": "p01_ar_mike16_inspect_boltclose"}}}, "inspect": {"animation_length": 6.16667, "bones": {"root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-16.47921, -9.81655, -15.02475], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-18.40736, -26.44069, -37.68703], "lerp_mode": "catmullrom"}, "0.4": {"post": [-15.96715, -36.09364, -52.92512], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-17.36928, -38.19063, -51.80192], "lerp_mode": "catmullrom"}, "0.7": {"post": [-16.11701, -39.38218, -53.47447], "lerp_mode": "catmullrom"}, "1.0333": {"post": [-18.99466, -38.25032, -48.88535], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-22.69787, -19.70601, -35.21184], "lerp_mode": "catmullrom"}, "1.4": {"post": [-23.17927, -17.49128, -30.20424], "lerp_mode": "catmullrom"}, "1.4667": {"post": [-24.06, -14.85125, -31.8525], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-24.12927, -13.96628, -27.46674], "lerp_mode": "catmullrom"}, "1.6667": {"post": [-24.13, -14.02, -29.91], "lerp_mode": "catmullrom"}, "2.3667": {"post": [-24.13, -14.62, -30.13], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-22.38653, -15.5048, -32.02257], "lerp_mode": "catmullrom"}, "2.5": {"post": [-23.13877, -14.34824, -29.13691], "lerp_mode": "catmullrom"}, "2.6": {"post": [-21.90123, -15.05985, -30.7463], "lerp_mode": "catmullrom"}, "2.7333": {"post": [-23.53151, -13.97825, -29.21103], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-24.29958, -12.78629, -26.38132], "lerp_mode": "catmullrom"}, "2.8333": {"post": [-23.87842, -13.97919, -28.80487], "lerp_mode": "catmullrom"}, "2.9333": {"post": [-24.43904, -13.77489, -27.70404], "lerp_mode": "catmullrom"}, "3.0333": {"post": [-24.13, -14.92, -29.84], "lerp_mode": "catmullrom"}, "3.1": {"post": [-24.12927, -13.78503, -28.05424], "lerp_mode": "catmullrom"}, "3.2333": {"post": [-24.19, -14.28, -27.61875], "lerp_mode": "catmullrom"}, "3.4": {"post": [-24.22, -13.8475, -27.5575], "lerp_mode": "catmullrom"}, "3.4667": {"post": [-24.29, -17.34625, -31.2525], "lerp_mode": "catmullrom"}, "3.5333": {"post": [-23.85154, -12.50636, -26.39656], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-20.81, -13.10125, -27.02625], "lerp_mode": "catmullrom"}, "3.7333": {"post": [-20.58, -5.66, -11.65], "lerp_mode": "catmullrom"}, "3.9333": {"post": [-27.50449, 20.26323, 35.23224], "lerp_mode": "catmullrom"}, "4.0667": {"post": [-29.845, 29.5325, 56.5125], "lerp_mode": "catmullrom"}, "4.2333": {"post": [-30.9525, 30.24, 56.56375], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-29.02324, 30.06323, 58.84474], "lerp_mode": "catmullrom"}, "4.4333": {"post": [-27.4675, 31.795, 60.56125], "lerp_mode": "catmullrom"}, "4.5333": {"post": [-29.76875, 30.67875, 56.52], "lerp_mode": "catmullrom"}, "4.6667": {"post": [-29.56373, 29.96896, 57.57586], "lerp_mode": "catmullrom"}, "5.0": {"post": [-29.225, 29.555, 55.63], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-32.11369, 26.47641, 50.45753], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-29.9871, 29.19678, 56.23653], "lerp_mode": "catmullrom"}, "5.1333": {"post": [-31.2741, 26.7239, 50.30799], "lerp_mode": "catmullrom"}, "5.2": {"post": [-25.24, 23.75, 47.38], "lerp_mode": "catmullrom"}, "5.4": {"post": [-9.2, 3.79, 12.45], "lerp_mode": "catmullrom"}, "5.4667": {"post": [-5.15505, -0.93208, 3.01761], "lerp_mode": "catmullrom"}, "5.6": {"post": [-3.63, 2.24125, -3.92], "lerp_mode": "catmullrom"}, "5.7": {"post": [-0.79712, 1.52875, -1.1995], "lerp_mode": "catmullrom"}, "5.7667": {"post": [0.595, 0.64, 2.28], "lerp_mode": "catmullrom"}, "5.8": {"post": [0.835, 0.19, -0.9525], "lerp_mode": "catmullrom"}, "5.9": {"post": [0, 0, 0.73125], "lerp_mode": "catmullrom"}, "6.0333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1": {"post": [-0.13, -0.34125, -0.285], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.315, 0.6675, -0.6875], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.6, -0.04375, -0.75625], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.6, 0.38125, -0.97], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-0.7, 0.33, -0.965], "lerp_mode": "catmullrom"}, "1.0": {"post": [-0.725, 0.33, -0.965], "lerp_mode": "catmullrom"}, "1.2": {"post": [-0.68, -0.245, -0.79], "lerp_mode": "catmullrom"}, "1.3667": {"post": [-0.59, -0.01, -0.19], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-0.475, -0.36, -0.19], "lerp_mode": "catmullrom"}, "1.5": {"post": [-0.61, -0.34, -0.2], "lerp_mode": "catmullrom"}, "1.7333": {"post": [-0.69375, -0.14, -0.21], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-0.74, -0.1, -0.2], "lerp_mode": "catmullrom"}, "2.3": {"post": [-0.895, -0.49, -0.2], "lerp_mode": "catmullrom"}, "2.4667": {"post": [-0.79, -0.24, -0.2], "lerp_mode": "catmullrom"}, "2.5333": {"post": [-0.525, -0.555, -0.2], "lerp_mode": "catmullrom"}, "2.6667": {"post": [-0.69, -0.295, -0.2], "lerp_mode": "catmullrom"}, "2.7667": {"post": [-0.785, -0.27, -0.2], "lerp_mode": "catmullrom"}, "2.8": {"post": [-0.615, -0.585, -0.2], "lerp_mode": "catmullrom"}, "2.9333": {"post": [-0.65, -0.735, -0.4], "lerp_mode": "catmullrom"}, "3.1333": {"post": [-0.73, -0.185, -0.2], "lerp_mode": "catmullrom"}, "3.4": {"post": [-0.91, -0.1, -0.19], "lerp_mode": "catmullrom"}, "3.4333": {"post": [-0.91, -0.75, -0.19], "lerp_mode": "catmullrom"}, "3.5": {"post": [-0.92, -0.88, -0.17], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-0.91, -0.8, -0.19], "lerp_mode": "catmullrom"}, "3.8333": {"post": [-0.61, -1.28, -0.69], "lerp_mode": "catmullrom"}, "4.0": {"post": [-0.31, 0.9375, -1.19], "lerp_mode": "catmullrom"}, "4.1": {"post": [-0.19, 1.37, -1.22], "lerp_mode": "catmullrom"}, "4.2": {"post": [-0.11, 1.3, -1.19], "lerp_mode": "catmullrom"}, "4.3": {"post": [-0.11, 1.2875, -1.19], "lerp_mode": "catmullrom"}, "4.3667": {"post": [-0.09, 1.165, -1.32], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-0.06, 1.55, -1.515], "lerp_mode": "catmullrom"}, "4.6": {"post": [-0.06, 1.61, -1.48], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-0.06, 1.53625, -1.42875], "lerp_mode": "catmullrom"}, "5.0": {"post": [-0.06, 1.51, -1.43], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-0.06, 1.54125, -1.58], "lerp_mode": "catmullrom"}, "5.0667": {"post": [-0.06, 1.37875, -1.705], "lerp_mode": "catmullrom"}, "5.1667": {"post": [-0.06, 1.13, -1.42875], "lerp_mode": "catmullrom"}, "5.4667": {"post": [-0.07, -1.21, -1.71], "lerp_mode": "catmullrom"}, "5.6333": {"post": [0, -0.5, -1.75], "lerp_mode": "catmullrom"}, "5.7667": {"post": [0, 0, 0.5], "lerp_mode": "catmullrom"}, "5.8667": {"post": [0, 0, -0.10625], "lerp_mode": "catmullrom"}, "6.0": {"post": [0, 0, 0.025], "lerp_mode": "catmullrom"}, "6.1333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"1.4": [0, 0, 0], "1.4667": [0, 0, 0], "1.7": {"pre": [-16.31807, -6.39323, 24.5888], "post": [-16.31807, -6.39323, 24.5888], "lerp_mode": "catmullrom"}, "1.8333": {"post": [-18.23927, -7.11526, 29.32601], "lerp_mode": "catmullrom"}, "2.2": [-18.39542, -2.76376, 30.37655], "2.3": [-8.55608, -0.74679, 9.95623], "2.3667": [6.75025, 9.31909, 26.309], "2.4333": [16.2816, 0.91382, 39.83396], "2.5": [7.8633, 5.28097, -1.27484], "2.5333": [8.29054, 4.57802, 3.69392], "2.5667": [6.06, 3.62, 9.62], "2.6333": [4.25816, 1.96595, 20.52115], "2.7": [5.77499, -3.09517, 34.26413], "2.7333": [6.73382, -1.53493, 38.33922], "2.7667": [7.8633, 5.28097, -1.27484], "2.8": [8.21004, 4.72152, 2.70101], "2.9": {"pre": [2.83504, -4.78324, 4.28282], "post": [2.83504, -4.78324, 4.28282], "lerp_mode": "catmullrom"}, "2.9667": {"post": [-3.04117, -7.51575, 2.45303], "lerp_mode": "catmullrom"}, "3.0667": [-1.99907, -0.06106, -1.74893], "3.1667": [-2, 0, 0], "3.2333": [0, 0, 0], "3.3667": [0, 0, 0], "3.4667": [0, 0, 0]}, "position": {"1.4": [0, 0, 0], "1.4667": [0, -3, 0], "1.5667": {"pre": [1.98, -7.145, -0.02], "post": [1.98, -7.145, -0.02], "lerp_mode": "catmullrom"}, "1.7": {"post": [3.65575, -4.57278, 0.30502], "lerp_mode": "catmullrom"}, "1.8333": {"post": [4.37382, -3.82136, 0.47888], "lerp_mode": "catmullrom"}, "2.2": [4.23, -3.63, 0.49], "2.3333": [2.21758, -4.47143, 2.13077], "2.4333": [3.2344, -1.60348, 0.56431], "2.5": [1.14276, -2.70841, 0.92041], "2.5333": [1.14276, -2.70841, 0.92041], "2.5667": [1.3, -2.93, 0.97], "2.6333": [2.64904, -3.00883, 0.62594], "2.7333": [2.83956, -2.50325, 0.3564], "2.7667": [1.14276, -2.70841, 0.92041], "2.8": [1.14276, -2.70841, 0.92041], "2.9": [0.96578, -3.18458, 0.59936], "2.9667": [1.26414, -4.27124, 0.12997], "3.0667": [0, -4.425, -0.225], "3.1667": [0, -3.7, -0.225], "3.2333": [0, -3, 0], "3.3667": [0, -3, 0], "3.4667": [0, 0, 0]}}, "bullet": {"scale": 0}, "lefthand": {"rotation": {"0.0": [99.10245, -28.39169, -137.40361], "0.0667": [125.30509, -38.28542, -211.01416], "0.2": [139.42303, -45.8578, -221.30543], "1.2": [121.50986, -18.03004, -196.84229], "1.3": [112.61194, -11.13329, -182.12726], "3.7": [112.61194, -11.13329, -182.12726], "3.9667": [119.17409, -0.96827, -257.06444], "4.2667": [119.17409, -0.96827, -257.06444], "5.0": [119.17409, -0.96827, -257.06444], "5.0667": [124.11777, -4.72958, -284.99142], "5.3": [119.17409, -0.96827, -257.06444], "5.4333": [101.85565, -31.6009, -145.88105], "5.5333": [99.10245, -28.39169, -137.40361]}, "position": {"0.0": [8.025, -11.85, -4.95], "0.0667": [10.5, -14.36, -2.685], "0.1333": [12.22251, -16.43964, 3.93451], "0.2": [12.79981, -22.00224, 7.10336], "0.3667": [4.79865, -22.54859, 12.29824], "1.0333": [6.82365, -22.54859, 12.29824], "1.1333": [8.145, -18.6, 6.56], "1.2": [7.315, -15.96, 2.74], "1.3": [7.0025, -15.24, 3.46125], "3.7": [7.0025, -15.24, 3.46125], "3.8333": [9.28495, -12.60594, 6.46817], "3.9667": [8.7075, -7.845, 10.18625], "4.2667": [8.7075, -7.845, 10.18625], "4.4": [8.7075, -7.85, 13.19125], "4.5": [8.7075, -7.85, 13.36625], "4.6667": [8.7075, -7.85, 13.23625], "5.0": [8.7075, -7.85, 13.31625], "5.0667": [9.37348, -6.84595, 14.2499], "5.3": [11.96372, -9.47086, 14.03675], "5.4333": [8.9575, -12.46, -1.01375], "5.5333": [8.025, -11.85, -4.95]}, "scale": [1, 1.5, 1]}, "gun_and_righthand": {"rotation": {"1.4667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5333": {"post": [-1.00009, 0.74989, 0.41191], "lerp_mode": "catmullrom"}, "1.6667": {"post": [1.00009, 0.74989, -0.05691], "lerp_mode": "catmullrom"}, "2.1": {"post": [0, 0, -0.32], "lerp_mode": "catmullrom"}, "2.7667": {"post": [0, 0, -0.32], "lerp_mode": "catmullrom"}, "3.0": {"post": [0, 0, -0.32], "lerp_mode": "catmullrom"}, "3.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"1.4667": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5667": {"post": [-0.125, 0.2, 0], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-0.175, -0.025, 0], "lerp_mode": "catmullrom"}, "2.6333": {"post": [-0.1, -0.08, 0], "lerp_mode": "catmullrom"}, "2.9333": {"post": [-0.1, 0.02, 0], "lerp_mode": "catmullrom"}, "3.1333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_release": {"position": {"1.2667": [0, 0, 0], "1.3333": [0.125, 0, 0], "1.6": [0.125, 0, 0], "1.6667": [0, 0, 0], "3.4": [0, 0, 0], "3.4667": [0.125, 0, 0], "3.5667": [0.125, 0, 0], "3.6333": [0, 0, 0]}}, "pull2": {"position": {"4.2667": [0, 0, 0], "4.4": [0, 0, 3], "4.5": [0, 0, 3.1], "4.6667": [0, 0, 2.945], "4.8667": [0, 0, 2.88], "5.0": [0, 0, 3], "5.0333": [0, 0, 0]}}, "righthand": {"rotation": [92.4939, 3.99619, -180.17476], "position": [-6.9, -14.125, 8.425], "scale": [1, 1.5, 1]}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.42, 0.12, -0.16], "lerp_mode": "catmullrom"}, "0.3": {"post": [2, 0.275, -0.375], "lerp_mode": "catmullrom"}, "0.4333": {"post": [2.7, 0.33, -0.2], "lerp_mode": "catmullrom"}, "0.5667": {"post": [2.345, 0.37, -0.695], "lerp_mode": "catmullrom"}, "0.7333": {"post": [2.23, 0.4, -0.575], "lerp_mode": "catmullrom"}, "1.0667": {"post": [2.4, 0.49, -0.79], "lerp_mode": "catmullrom"}, "1.4": {"post": [2.16, 0.35, -0.925], "lerp_mode": "catmullrom"}, "1.5": {"post": [3.415, -0.27, -0.125], "lerp_mode": "catmullrom"}, "1.6": {"post": [3.375, -1.03, -1.25], "lerp_mode": "catmullrom"}, "1.7333": {"post": [3.66, -1.75, -0.625], "lerp_mode": "catmullrom"}, "2.1333": {"post": [3.65, -1.88, -0.3], "lerp_mode": "catmullrom"}, "2.3667": {"post": [3.61, 0.26, -0.15], "lerp_mode": "catmullrom"}, "2.4667": {"post": [3.62, 0.73, -0.1], "lerp_mode": "catmullrom"}, "2.5333": {"post": [3.795, 0.82, 0.08], "lerp_mode": "catmullrom"}, "2.6333": {"post": [3.445, 0.85, -0.17], "lerp_mode": "catmullrom"}, "2.7333": {"post": [3.565, 0.86, 0.09], "lerp_mode": "catmullrom"}, "2.8": {"post": [3.925, 0.86, -0.195], "lerp_mode": "catmullrom"}, "2.8667": {"post": [3.805, 0.84, -0.04], "lerp_mode": "catmullrom"}, "2.9667": {"post": [4.255, 0.79, -0.335], "lerp_mode": "catmullrom"}, "3.0667": {"post": [4.24, 0.775, -0.32], "lerp_mode": "catmullrom"}, "3.1333": {"post": [4.545, 0.985, -0.365], "lerp_mode": "catmullrom"}, "3.2": {"post": [4.56, 1.125, -0.55], "lerp_mode": "catmullrom"}, "3.4": {"post": [4.73, 1.13, -0.76], "lerp_mode": "catmullrom"}, "3.4667": {"post": [5.465, 1.12, -0.255], "lerp_mode": "catmullrom"}, "3.5": {"post": [5.53, 1.135, -0.39], "lerp_mode": "catmullrom"}, "3.5667": {"post": [4.565, 1.1, -1.505], "lerp_mode": "catmullrom"}, "3.7": {"post": [4.71, 1.275, -0.525], "lerp_mode": "catmullrom"}, "3.8333": {"post": [4.16, 1.68, -0.51], "lerp_mode": "catmullrom"}, "4.1667": {"post": [3.265, 2.9, 0.575], "lerp_mode": "catmullrom"}, "4.2667": {"post": [3.3, 2.96, 0.66], "lerp_mode": "catmullrom"}, "4.3667": {"post": [3.37, 2.97, 1.205], "lerp_mode": "catmullrom"}, "4.4667": {"post": [4.16, 2.96, 0.27], "lerp_mode": "catmullrom"}, "4.8": {"post": [3.66, 2.96, 0.82], "lerp_mode": "catmullrom"}, "5.0": {"post": [3.535, 2.85, 0.975], "lerp_mode": "catmullrom"}, "5.0667": {"post": [4.09, 2.75, 1.895], "lerp_mode": "catmullrom"}, "5.1667": {"post": [2.74, 2.27, 0.1], "lerp_mode": "catmullrom"}, "5.3667": {"post": [2.405, 0.185, 1.37], "lerp_mode": "catmullrom"}, "5.6": {"post": [-0.33, -0.04, 0.51], "lerp_mode": "catmullrom"}, "5.8": {"post": [-0.125, 0, -0.275], "lerp_mode": "catmullrom"}, "5.9": {"post": [0.44, 0.04, 0.25], "lerp_mode": "catmullrom"}, "6.0333": {"post": [-0.065, 0.02, -0.095], "lerp_mode": "catmullrom"}, "6.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.1": {"effect": "p01_ar_mike4_inspect_lift"}, "1.4": {"effect": "p01_ar_mike4_reload_magout"}, "2.4333": {"effect": "p06_ar_falpha_inspect_maghit1"}, "2.7": {"effect": "p06_ar_falpha_inspect_maghit2"}, "3.1667": {"effect": "p01_ar_mike4_inspect_maghit"}, "3.3667": {"effect": "p01_ar_mike4_inspect_magin"}, "3.9333": {"effect": "p01_ar_mike4_inspect_boltpull"}, "4.9333": {"effect": "p01_ar_mike4_inspect_end"}, "4.9667": {"effect": "p01_ar_mike4_inspect_boltclose"}, "5.2667": {"effect": "p01_ar_mike4_hybrid01_off_arm"}}}, "inspect_empty": {"animation_length": 10.16667, "bones": {"additional_magzine": {"scale": 0}, "m4a1_pull": {"position": {"5.7": [0, 0, 0], "5.8": [0, 0, 4.85], "5.8667": [0, 0, 4.85], "5.9333": [0, 0, 0], "6.0333": [0, 0, 0], "6.1": [0, 0, 4.85], "6.1667": [0, 0, 4.85], "6.2667": [0, 0, 0], "6.3667": [0, 0, 4.85], "6.4333": [0, 0, 4.85], "6.5333": [0, 0, 0], "8.6333": [0, 0, 0], "8.7333": [0, 0, 4.85], "9.0667": [0, 0, 4.85], "9.1667": [0, 0, 0]}}, "m4a1_bolt": {"position": {"0.0": [0, 0, 4.7], "5.7": [0, 0, 4.7], "5.8": [0, 0, 0], "9.0667": [0, 0, 0], "9.1667": [0, 0, 4.7]}}, "root": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-16.47921, -9.81655, -15.02475], "lerp_mode": "catmullrom"}, "0.2667": {"post": [-18.40736, -26.44069, -37.68703], "lerp_mode": "catmullrom"}, "0.4": {"post": [-15.96715, -36.09364, -52.92512], "lerp_mode": "catmullrom"}, "0.5333": {"post": [-17.36928, -38.19063, -51.80192], "lerp_mode": "catmullrom"}, "0.6667": {"post": [-15.12281, -39.71877, -55.03575], "lerp_mode": "catmullrom"}, "0.9": {"post": [-15.16, -38.71875, -55.3], "lerp_mode": "catmullrom"}, "1.1333": {"post": [-15.41875, -38.58375, -54.40375], "lerp_mode": "catmullrom"}, "1.2667": {"post": [-14.94875, -39.875, -48.3275], "lerp_mode": "catmullrom"}, "1.4333": {"post": [-10.51666, -44.82354, -34.1567], "lerp_mode": "catmullrom"}, "1.6333": {"post": [-4.82865, -47.9127, -27.29554], "lerp_mode": "catmullrom"}, "1.7667": {"post": [-3.12873, -48.01389, -25.31554], "lerp_mode": "catmullrom"}, "2.0333": {"post": [-2.67, -47.77, -24.54], "lerp_mode": "catmullrom"}, "2.3": {"post": [-1.98677, -48.03605, -22.80776], "lerp_mode": "catmullrom"}, "2.5333": {"post": [1.45, -52.285, -13.105], "lerp_mode": "catmullrom"}, "2.6333": {"post": [3.34125, -54.835, -11.295], "lerp_mode": "catmullrom"}, "3.0333": {"post": [3.205, -55.825, -11.27], "lerp_mode": "catmullrom"}, "3.1333": {"post": [-1.815, -52.26, -10.42], "lerp_mode": "catmullrom"}, "3.2667": {"post": [-12.54545, -33.12446, -8.68107], "lerp_mode": "catmullrom"}, "3.4667": {"post": [-27.47952, -9.86127, -16.55015], "lerp_mode": "catmullrom"}, "3.6": {"post": [-28.53798, -9.07643, -20.15424], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-28.2, -9.11, -17.875], "lerp_mode": "catmullrom"}, "3.7667": {"post": [-27.67, -9.23, -22], "lerp_mode": "catmullrom"}, "3.8667": {"post": [-26.54069, -10.34474, -23.99245], "lerp_mode": "catmullrom"}, "4.0": {"post": [-25.26, -12.73, -24.89], "lerp_mode": "catmullrom"}, "4.1333": {"post": [-24.12927, -13.41628, -26.45424], "lerp_mode": "catmullrom"}, "4.2": {"post": [-24.06, -14.85125, -30.6025], "lerp_mode": "catmullrom"}, "4.2667": {"post": [-24.12927, -13.96628, -27.46674], "lerp_mode": "catmullrom"}, "4.4": {"post": [-24.13, -14.02, -29.91], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-24.19, -14.28, -27.61875], "lerp_mode": "catmullrom"}, "5.2667": {"post": [-27.50449, 20.26323, 35.23224], "lerp_mode": "catmullrom"}, "5.4667": {"post": [-29.845, 28.53875, 55.025], "lerp_mode": "catmullrom"}, "5.6": {"post": [-29.56373, 29.96896, 57.57586], "lerp_mode": "catmullrom"}, "5.7333": {"post": [-29.02324, 30.06323, 58.84474], "lerp_mode": "catmullrom"}, "5.8": {"post": [-29.30998, 32.16448, 61.3307], "lerp_mode": "catmullrom"}, "5.9": {"post": [-27.94694, 32.48368, 60.37904], "lerp_mode": "catmullrom"}, "5.9667": {"post": [-30.26565, 30.16774, 55.4556], "lerp_mode": "catmullrom"}, "6.0333": {"post": [-30.02769, 30.4145, 55.94489], "lerp_mode": "catmullrom"}, "6.1": {"post": [-28.83883, 31.9124, 59.07239], "lerp_mode": "catmullrom"}, "6.2": {"post": [-30.43011, 30.04981, 55.22071], "lerp_mode": "catmullrom"}, "6.2667": {"post": [-29.28567, 31.15137, 57.44079], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-28.91161, 31.56009, 58.2915], "lerp_mode": "catmullrom"}, "6.4": {"post": [-28.74813, 32.35437, 60.0253], "lerp_mode": "catmullrom"}, "6.4667": {"post": [-29.46004, 31.07145, 57.28422], "lerp_mode": "catmullrom"}, "6.5667": {"post": [-29.3152, 32.25841, 59.90051], "lerp_mode": "catmullrom"}, "6.7": {"post": [-30.17656, 31.59507, 58.41255], "lerp_mode": "catmullrom"}, "6.8": {"post": [-29.76875, 30.67875, 56.52], "lerp_mode": "catmullrom"}, "6.9333": {"post": [-29.76875, 27.75375, 51.295], "lerp_mode": "catmullrom"}, "7.0333": {"post": [-28.09, 15.74, 28.36], "lerp_mode": "catmullrom"}, "7.1667": {"post": [-22.46381, -9.54439, -20.54664], "lerp_mode": "catmullrom"}, "7.2667": {"post": [-21.24967, -13.86767, -28.13263], "lerp_mode": "catmullrom"}, "7.3667": {"post": [-23.21543, -14.17989, -27.70465], "lerp_mode": "catmullrom"}, "7.5333": {"post": [-25.68161, -12.74235, -25.00147], "lerp_mode": "catmullrom"}, "7.6667": {"post": [-23.70727, -12.91563, -25.45236], "lerp_mode": "catmullrom"}, "7.7333": {"post": [-24.29, -17.34625, -31.2525], "lerp_mode": "catmullrom"}, "7.8": {"post": [-23.85154, -12.50636, -26.39656], "lerp_mode": "catmullrom"}, "7.9333": {"post": [-20.81, -13.10125, -27.02625], "lerp_mode": "catmullrom"}, "8.0333": {"post": [-20.58, -5.66, -11.65], "lerp_mode": "catmullrom"}, "8.2333": {"post": [-23.09522, 16.30292, 48.7728], "lerp_mode": "catmullrom"}, "8.3667": {"post": [-23.10799, 15.78335, 57.09159], "lerp_mode": "catmullrom"}, "8.5": {"post": [-21.52812, 14.28906, 56.78712], "lerp_mode": "catmullrom"}, "8.6333": {"post": [-20.95674, 14.4977, 57.5374], "lerp_mode": "catmullrom"}, "8.7": {"post": [-20.28337, 17.31405, 63.01229], "lerp_mode": "catmullrom"}, "8.7667": {"post": [-21.19549, 16.64619, 59.78647], "lerp_mode": "catmullrom"}, "8.8667": {"post": [-20.91185, 15.9142, 56.62889], "lerp_mode": "catmullrom"}, "9.1333": {"post": [-20.38673, 17.90897, 57.55593], "lerp_mode": "catmullrom"}, "9.3333": {"post": [-21.26, 15.95, 51.46], "lerp_mode": "catmullrom"}, "9.4": {"post": [-15.905, 5.98, 34.265], "lerp_mode": "catmullrom"}, "9.5333": {"post": [-5.15505, -0.93208, 3.01761], "lerp_mode": "catmullrom"}, "9.6333": {"post": [-3.63, 2.24125, -3.92], "lerp_mode": "catmullrom"}, "9.7333": {"post": [-0.79712, 1.52875, -1.1995], "lerp_mode": "catmullrom"}, "9.8": {"post": [0.595, 0.64, 2.28], "lerp_mode": "catmullrom"}, "9.8333": {"post": [0.835, 0.19, -0.9525], "lerp_mode": "catmullrom"}, "9.9333": {"post": [0, 0, 0.73125], "lerp_mode": "catmullrom"}, "10.0667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0667": {"post": [-0.13, -0.34125, -0.41], "lerp_mode": "catmullrom"}, "0.2333": {"post": [-0.29, 0.6675, -1.2125], "lerp_mode": "catmullrom"}, "0.3667": {"post": [-0.5, -0.04375, -1.28125], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.5, 0.38125, -1.495], "lerp_mode": "catmullrom"}, "0.7333": {"post": [-0.5, 0.33, -1.49], "lerp_mode": "catmullrom"}, "1.1": {"post": [-0.48625, 0.29125, -1.5], "lerp_mode": "catmullrom"}, "1.2333": {"post": [-0.2575, 0.075, -1.48], "lerp_mode": "catmullrom"}, "1.3667": {"post": [0.57375, -0.99125, -1.4], "lerp_mode": "catmullrom"}, "1.5": {"post": [1.4075, -1.1925, -1.37], "lerp_mode": "catmullrom"}, "1.6333": {"post": [1.8625, -0.28875, -1.315], "lerp_mode": "catmullrom"}, "1.7667": {"post": [1.95, 0.06125, -1.315], "lerp_mode": "catmullrom"}, "1.9667": {"post": [2, 0.12125, -1.33], "lerp_mode": "catmullrom"}, "2.1667": {"post": [2.07875, 0.1875, -1.35], "lerp_mode": "catmullrom"}, "2.3333": {"post": [2.45, 0.50375, -1.3825], "lerp_mode": "catmullrom"}, "2.5333": {"post": [3.64, 1.26, -1.565], "lerp_mode": "catmullrom"}, "2.6667": {"post": [4.04375, 1.66, -1.495], "lerp_mode": "catmullrom"}, "3.0333": {"post": [4.05375, 1.69, -1.515], "lerp_mode": "catmullrom"}, "3.1333": {"post": [3.68375, 1.28625, -1.415], "lerp_mode": "catmullrom"}, "3.2667": {"post": [2.01, 0.03875, -0.84125], "lerp_mode": "catmullrom"}, "3.4": {"post": [0.045, -0.27125, -1.38875], "lerp_mode": "catmullrom"}, "3.5333": {"post": [-0.49375, 0.1, -1.12], "lerp_mode": "catmullrom"}, "3.6333": {"post": [-0.56, -0.21875, -0.045], "lerp_mode": "catmullrom"}, "3.6667": {"post": [-0.56, -0.19, -0.15375], "lerp_mode": "catmullrom"}, "3.7667": {"post": [-0.5325, -0.13125, -0.26625], "lerp_mode": "catmullrom"}, "3.8667": {"post": [-0.54, -0.1, -0.1575], "lerp_mode": "catmullrom"}, "4.1": {"post": [-0.59, -0.01, -0.19], "lerp_mode": "catmullrom"}, "4.1667": {"post": [-0.475, -0.36, -0.19], "lerp_mode": "catmullrom"}, "4.2333": {"post": [-0.61, -0.34, -0.2], "lerp_mode": "catmullrom"}, "4.4667": {"post": [-0.69375, -0.14, -0.21], "lerp_mode": "catmullrom"}, "5.0": {"post": [-0.65, -0.125, -0.53], "lerp_mode": "catmullrom"}, "5.1333": {"post": [-0.64, -0.25, -0.59], "lerp_mode": "catmullrom"}, "5.3": {"post": [-0.61, -0.655, -0.69], "lerp_mode": "catmullrom"}, "5.4667": {"post": [-0.31, 0.6625, -1.19], "lerp_mode": "catmullrom"}, "5.6": {"post": [-0.19, 1.195, -1.395], "lerp_mode": "catmullrom"}, "5.7": {"post": [-0.06, 1.3175, -1.48], "lerp_mode": "catmullrom"}, "5.8": {"post": [-0.06, 1.96125, -1.42875], "lerp_mode": "catmullrom"}, "5.8333": {"post": [-0.06, 1.91625, -1.42], "lerp_mode": "catmullrom"}, "5.8667": {"post": [-0.06, 1.91625, -1.43], "lerp_mode": "catmullrom"}, "6.0": {"post": [-0.05, 1.505, -1.45], "lerp_mode": "catmullrom"}, "6.1": {"post": [-0.04, 1.795, -1.46], "lerp_mode": "catmullrom"}, "6.1333": {"post": [-0.04, 1.7, -1.46], "lerp_mode": "catmullrom"}, "6.2": {"post": [-0.04, 1.405, -1.47], "lerp_mode": "catmullrom"}, "6.2667": {"post": [-0.03, 1.44, -1.48], "lerp_mode": "catmullrom"}, "6.3667": {"post": [-0.02, 1.725, -1.49], "lerp_mode": "catmullrom"}, "6.4": {"post": [-0.01, 1.675, -1.5], "lerp_mode": "catmullrom"}, "6.4333": {"post": [0, 1.685, -1.51], "lerp_mode": "catmullrom"}, "6.5333": {"post": [0.01, 1.725, -1.52], "lerp_mode": "catmullrom"}, "6.6": {"post": [0.01, 1.795, -1.52], "lerp_mode": "catmullrom"}, "6.7": {"post": [0.02, 1.36625, -1.53], "lerp_mode": "catmullrom"}, "6.7667": {"post": [0.01, 1.28125, -1.53], "lerp_mode": "catmullrom"}, "6.8667": {"post": [-0.06, 0.93, -1.42875], "lerp_mode": "catmullrom"}, "7.0333": {"post": [-0.49, -0.745, -0.8], "lerp_mode": "catmullrom"}, "7.2": {"post": [-0.91, -0.1, -0.19], "lerp_mode": "catmullrom"}, "7.4333": {"post": [-0.94, -0.12, -0.15], "lerp_mode": "catmullrom"}, "7.6667": {"post": [-0.91, -0.1, -0.19], "lerp_mode": "catmullrom"}, "7.7": {"post": [-0.91, -0.75, -0.19], "lerp_mode": "catmullrom"}, "7.7667": {"post": [-0.92, -0.88, -0.17], "lerp_mode": "catmullrom"}, "7.9": {"post": [-0.91, -0.8, -0.19], "lerp_mode": "catmullrom"}, "8.0": {"post": [-0.71, -1.71, -0.8], "lerp_mode": "catmullrom"}, "8.1333": {"post": [-0.63, -1.5175, -1.89], "lerp_mode": "catmullrom"}, "8.3": {"post": [-0.725, 0.39, -1.65], "lerp_mode": "catmullrom"}, "8.5": {"post": [-0.61, 0.475, -1.435], "lerp_mode": "catmullrom"}, "8.6333": {"post": [-0.485, 0.41, -1.39], "lerp_mode": "catmullrom"}, "8.7333": {"post": [-0.46, 0.655, -1.935], "lerp_mode": "catmullrom"}, "8.9": {"post": [-0.4, 0.57, -1.82], "lerp_mode": "catmullrom"}, "9.1333": {"post": [-0.41, 0.39375, -1.585], "lerp_mode": "catmullrom"}, "9.3": {"post": [-0.31, 0.275, -1.86], "lerp_mode": "catmullrom"}, "9.4": {"post": [-0.15, -0.68125, -1.51], "lerp_mode": "catmullrom"}, "9.5333": {"post": [-0.07, -1.21, -1.71], "lerp_mode": "catmullrom"}, "9.6667": {"post": [0, -0.5, -1.75], "lerp_mode": "catmullrom"}, "9.8": {"post": [0, 0, 0.5], "lerp_mode": "catmullrom"}, "9.9": {"post": [0, 0, -0.10625], "lerp_mode": "catmullrom"}, "10.0333": {"post": [0, 0, 0.025], "lerp_mode": "catmullrom"}, "10.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mag_and_lefthand": {"rotation": {"4.1333": [0, 0, 0], "4.2": [0, 0, 0], "4.4333": {"pre": [-16.31807, -6.39323, 24.5888], "post": [-16.31807, -6.39323, 24.5888], "lerp_mode": "catmullrom"}, "4.5667": {"post": [-18.23927, -7.11526, 29.32601], "lerp_mode": "catmullrom"}, "4.9": [-18.37, -7.17, 29.65], "5.0667": {"pre": [-11.9351, -15.75636, -1.85346], "post": [-11.9351, -15.75636, -1.85346], "lerp_mode": "catmullrom"}, "5.2667": {"post": [30.77415, -27.99851, -177.98573], "lerp_mode": "catmullrom"}, "5.3667": [30.77415, -27.99851, -177.98573], "5.6667": [30.77415, -27.99851, -177.98573], "6.6667": [30.77415, -27.99851, -177.98573], "6.7667": [30.77415, -27.99851, -177.98573], "7.1333": {"pre": [16.0963, -11.77394, -49.72181], "post": [16.0963, -11.77394, -49.72181], "lerp_mode": "catmullrom"}, "7.3333": [4.96292, 0.60859, -6.97363], "7.4333": [1, 0, 0], "7.5": [0, 0, 0], "7.6333": [0, 0, 0], "7.7": [0, 0, 0], "8.7": [0, 0, 0]}, "position": {"4.1333": [0, 0, 0], "4.2": [0, -3, 0], "4.3": {"pre": [1.98, -5.27, -0.02], "post": [1.98, -5.27, -0.02], "lerp_mode": "catmullrom"}, "4.4333": {"post": [3.66159, -3.88317, 0.30066], "lerp_mode": "catmullrom"}, "4.5667": {"post": [4.16402, -3.68544, 0.47573], "lerp_mode": "catmullrom"}, "4.9": [4.2, -3.67, 0.49], "5.0667": {"pre": [4.41494, -3.68316, 0.62013], "post": [4.41494, -3.68316, 0.62013], "lerp_mode": "catmullrom"}, "5.2": {"post": [5.73924, -1.86755, 4.36575], "lerp_mode": "catmullrom"}, "5.3667": [2.9456, 1.77333, 5.14749], "5.6667": [2.9456, 1.77333, 5.14749], "5.7": [3.05, 1.83, 5.24], "5.8": [3.05, 1.83, 10.24], "5.8667": [3.05, 1.83, 10.24], "5.9333": [3.05, 1.83, 5.24], "6.0333": [3.05, 1.83, 5.24], "6.1": [3.05, 1.83, 10.24], "6.1667": [3.05, 1.83, 10.24], "6.2667": [3.05, 1.83, 5.24], "6.3667": [3.05, 1.83, 10.24], "6.4333": [3.05, 1.83, 10.24], "6.5333": [3.05, 1.83, 5.24], "6.6667": [3.05, 1.83, 5.24], "6.7667": [3.05, 1.83, 5.24], "7.0333": {"pre": [6.51079, -1.88762, 2.99415], "post": [6.51079, -1.88762, 2.99415], "lerp_mode": "catmullrom"}, "7.2": {"post": [2.411, -4.23452, -0.18541], "lerp_mode": "catmullrom"}, "7.3333": [0.675, -3.7, -0.225], "7.4333": [0, -3.7, -0.225], "7.5": [0, -3, 0], "7.6333": [0, -3, 0], "7.7": [0, 0, 0]}}, "bullet": {"scale": 0}, "bullet_in_mag": {"scale": 0}, "lefthand": {"rotation": {"0.0": [99.10245, -28.39169, -137.40361], "0.0667": [125.30509, -38.28542, -211.01416], "0.2": [139.42303, -45.8578, -221.30543], "1.1667": [139.42303, -45.8578, -221.30543], "1.4": [102.04722, -55.94603, -151.57459], "1.4667": [122.38365, -70.39351, -160.83871], "3.2": [122.38365, -70.39351, -160.83871], "3.5333": [103.66705, -57.72377, -146.79633], "3.7333": [103.66705, -57.72377, -146.79633], "4.0": [96.25158, -30.31773, -122.39148], "6.8667": [96.25158, -30.31773, -122.39148], "8.0333": [96.25, -30.32, -122.39], "8.1667": [112.89443, 5.28376, -242.20503], "8.3": [119.17409, -0.96827, -257.06444], "8.6333": [119.17409, -0.96827, -257.06444], "9.1667": [119.17409, -0.96827, -257.06444], "9.3667": [119.17409, -0.96827, -257.06444], "9.5667": [101.85565, -31.6009, -145.88105], "9.6667": [99.10245, -28.39169, -137.40361]}, "position": {"0.0": [8.025, -11.85, -4.95], "0.0667": [10.13075, -13.22284, 1.2365], "0.1333": [10.78501, -17.76464, 4.32826], "0.2": [9.76231, -22.00224, 7.49711], "0.3667": [5.01115, -22.54859, 12.69199], "1.1667": [5.01115, -22.54859, 12.69199], "1.3": [9.2539, -17.7654, 4.60507], "1.4": [8.07339, -13.47075, 1.02171], "1.4667": [7.64375, -12.245, 1.005], "3.7333": [7.64375, -12.245, 1.005], "4.0": [7.215, -16.64, 3.155], "6.8667": [7.215, -16.64, 3.155], "8.0333": [7.22, -16.645, 3.155], "8.1667": [10.67245, -12.60594, 6.86192], "8.3": [8.92, -7.845, 10.58], "8.6333": [8.92, -7.845, 10.58], "8.7333": [8.92, -7.85, 15.435], "9.0667": [8.92, -7.85, 15.435], "9.1667": [8.92, -7.845, 10.58], "9.3667": [8.92, -7.845, 10.58], "9.4667": [9.8709, -10.86689, 6.68925], "9.5667": [9.17, -12.46, -0.62], "9.6667": [8.025, -11.85, -4.95]}, "scale": [1, 1.5, 1]}, "gun_and_righthand": {"rotation": {"4.2": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "4.2667": {"post": [-1.00009, 0.74989, 0.41191], "lerp_mode": "catmullrom"}, "4.4": {"post": [1.00009, 0.74989, -0.05691], "lerp_mode": "catmullrom"}, "4.6333": {"post": [0, 0, -0.32], "lerp_mode": "catmullrom"}, "4.8667": {"post": [0, 0, -0.32], "lerp_mode": "catmullrom"}, "5.1": {"post": [0, 0, -0.32], "lerp_mode": "catmullrom"}, "5.3": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"4.2": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "4.3": {"post": [-0.125, 0.2, 0], "lerp_mode": "catmullrom"}, "4.5": {"post": [-0.175, -0.025, 0], "lerp_mode": "catmullrom"}, "4.7333": {"post": [-0.1, -0.08, 0], "lerp_mode": "catmullrom"}, "5.0333": {"post": [-0.1, 0.02, 0], "lerp_mode": "catmullrom"}, "5.2333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "bolt": {"position": {"0.0": [0, 0, 4.7], "5.7": [0, 0, 4.7], "5.8": [0, 0, 0], "9.0667": [0, 0, 0], "9.1667": [0, 0, 4.7]}}, "bullet_in_barrel": {"scale": 0}, "bolt_release": {"rotation": {"0.0": [0, 0, 12.5], "5.8333": [0, 0, 12.5], "5.8667": [0, 0, 0], "8.7": [0, 0, 0], "8.7333": [0, 0, 12.5]}}, "mag_release": {"position": {"4.0": [0, 0, 0], "4.0667": [0.125, 0, 0], "4.3333": [0.125, 0, 0], "4.4": [0, 0, 0], "7.6": [0, 0, 0], "7.6667": [0.125, 0, 0], "7.7667": [0.125, 0, 0], "7.8333": [0, 0, 0]}}, "pull2": {"position": {"5.7": [0, 0, 0], "5.8": [0, 0, 4.85], "5.8667": [0, 0, 4.85], "5.9333": [0, 0, 0], "6.0333": [0, 0, 0], "6.1": [0, 0, 4.85], "6.1667": [0, 0, 4.85], "6.2667": [0, 0, 0], "6.3667": [0, 0, 4.85], "6.4333": [0, 0, 4.85], "6.5333": [0, 0, 0], "8.6333": [0, 0, 0], "8.7333": [0, 0, 4.85], "9.0667": [0, 0, 4.85], "9.1667": [0, 0, 0]}}, "righthand": {"rotation": [92.4939, 3.99619, -180.17476], "position": {"0.0": [-6.9, -14.125, 8.425], "1.3667": [-6.9, -14.125, 8.425], "1.6333": [-8.4, -14.12, 8.43], "3.1333": [-8.4, -14.12, 8.43], "3.3667": [-6.9, -14.125, 8.425]}, "scale": [1, 1.5, 1]}, "camera": {"relative_to": {"rotation": "entity"}, "rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0.42, 0.12, -0.16], "lerp_mode": "catmullrom"}, "0.3": {"post": [2, 0.275, -0.375], "lerp_mode": "catmullrom"}, "0.4333": {"post": [2.7, 0.33, -0.2], "lerp_mode": "catmullrom"}, "0.5667": {"post": [2.345, 0.37, -0.695], "lerp_mode": "catmullrom"}, "0.7333": {"post": [2.23, 0.4, -0.575], "lerp_mode": "catmullrom"}, "1.1": {"post": [2.755, 0.4, -0.575], "lerp_mode": "catmullrom"}, "1.4333": {"post": [3.775, 0.375, -0.375], "lerp_mode": "catmullrom"}, "1.5333": {"post": [4.09, 0.36, -0.195], "lerp_mode": "catmullrom"}, "1.7333": {"post": [3.845, 0.28, -0.39], "lerp_mode": "catmullrom"}, "2.2333": {"post": [4.07, 0.575, -0.375], "lerp_mode": "catmullrom"}, "2.6333": {"post": [5.495, 4.925, 0.675], "lerp_mode": "catmullrom"}, "3.1": {"post": [5.69, 5.45, 1.05], "lerp_mode": "catmullrom"}, "3.4": {"post": [3.75, 2.35, 0.11], "lerp_mode": "catmullrom"}, "3.5667": {"post": [2.815, 0.8, -0.425], "lerp_mode": "catmullrom"}, "3.6667": {"post": [2.915, 0.49, -0.395], "lerp_mode": "catmullrom"}, "3.8": {"post": [2.4, 0.49, -0.79], "lerp_mode": "catmullrom"}, "4.1333": {"post": [2.16, 0.35, -0.925], "lerp_mode": "catmullrom"}, "4.2333": {"post": [3.415, -0.27, -0.125], "lerp_mode": "catmullrom"}, "4.3333": {"post": [3.375, -1.03, -1.25], "lerp_mode": "catmullrom"}, "4.4667": {"post": [3.66, -1.75, -0.625], "lerp_mode": "catmullrom"}, "4.9333": {"post": [3.655, -2.05, 0.05], "lerp_mode": "catmullrom"}, "5.1667": {"post": [4.24, 0.05, -0.32], "lerp_mode": "catmullrom"}, "5.2333": {"post": [4.545, 0.71, -0.365], "lerp_mode": "catmullrom"}, "5.3667": {"post": [4.56, 1.125, -0.55], "lerp_mode": "catmullrom"}, "5.7": {"post": [4.58, 1.12, -0.59], "lerp_mode": "catmullrom"}, "5.7333": {"post": [4.78, 1.12, -0.865], "lerp_mode": "catmullrom"}, "5.8": {"post": [5.155, 1.11, -0.215], "lerp_mode": "catmullrom"}, "5.9333": {"post": [4.265, 1.1, -1.275], "lerp_mode": "catmullrom"}, "6.0667": {"post": [4.085, 1.09, -0.76], "lerp_mode": "catmullrom"}, "6.1333": {"post": [4.625, 1.09, -1.235], "lerp_mode": "catmullrom"}, "6.2333": {"post": [3.95, 1.08, -0.145], "lerp_mode": "catmullrom"}, "6.3667": {"post": [3.82, 1.05, -0.705], "lerp_mode": "catmullrom"}, "6.4333": {"post": [4.535, 1.04, 0.045], "lerp_mode": "catmullrom"}, "6.5333": {"post": [3.735, 1.02, -1.065], "lerp_mode": "catmullrom"}, "6.6667": {"post": [4.115, 0.97, -0.095], "lerp_mode": "catmullrom"}, "6.8333": {"post": [3.96, 1.125, -0.55], "lerp_mode": "catmullrom"}, "7.0": {"post": [3.82, 1.95, 0.255], "lerp_mode": "catmullrom"}, "7.2667": {"post": [3.56, 2.885, 0.82], "lerp_mode": "catmullrom"}, "7.3333": {"post": [3.56, 3.05, 0.71], "lerp_mode": "catmullrom"}, "7.4333": {"post": [3.62, 2.98, 1.09], "lerp_mode": "catmullrom"}, "7.5333": {"post": [3.66, 2.86, 0.82], "lerp_mode": "catmullrom"}, "7.7": {"post": [3.535, 2.85, 0.975], "lerp_mode": "catmullrom"}, "7.7667": {"post": [4.09, 2.75, 1.895], "lerp_mode": "catmullrom"}, "7.8667": {"post": [3.24, 2.57, 0.1], "lerp_mode": "catmullrom"}, "8.0667": {"post": [3.53, 2.26, 1.37], "lerp_mode": "catmullrom"}, "8.4": {"post": [4.1, 3.78, 1.425], "lerp_mode": "catmullrom"}, "8.6667": {"post": [4.27, 4.01, 1.46], "lerp_mode": "catmullrom"}, "8.7333": {"post": [3.865, 4.02, 2.01], "lerp_mode": "catmullrom"}, "8.8333": {"post": [4.88, 4.04, 1.02], "lerp_mode": "catmullrom"}, "8.9667": {"post": [4.605, 4.07, 1.63], "lerp_mode": "catmullrom"}, "9.1333": {"post": [4.59, 3.95, 1.56], "lerp_mode": "catmullrom"}, "9.2": {"post": [4.875, 3.9, 1.75], "lerp_mode": "catmullrom"}, "9.2667": {"post": [4.525, 3.78, 1.175], "lerp_mode": "catmullrom"}, "9.3667": {"post": [3.86, 3.13, 1.24], "lerp_mode": "catmullrom"}, "9.4667": {"post": [1.75, 1.655, 0.75], "lerp_mode": "catmullrom"}, "9.5667": {"post": [0.75, 0.6, 0.5], "lerp_mode": "catmullrom"}, "9.7667": {"post": [-0.125, 0, -0.275], "lerp_mode": "catmullrom"}, "9.8667": {"post": [0.44, 0.04, 0.25], "lerp_mode": "catmullrom"}, "10.0": {"post": [-0.065, 0.02, -0.095], "lerp_mode": "catmullrom"}, "10.2": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}, "sound_effects": {"0.1": {"effect": "p01_ar_mike4_inspect_lift"}, "1.3": {"effect": "p01_ar_mike4_reload_empty_rattle"}, "3.2333": {"effect": "p01_ar_mike4_drop"}, "4.1333": {"effect": "p01_ar_mike4_reload_magout"}, "5.0667": {"effect": "p01_ar_mike4_reload_empty_fast_xmag_rattle"}, "5.7": {"effect": "p01_ar_mike4_inspect_empty_charge"}, "6.9667": {"effect": "p01_ar_mike4_reload_empty_fast_xmag_lift"}, "7.3333": {"effect": "p01_ar_mike4_inspect_maghit"}, "7.6": {"effect": "p01_ar_mike4_inspect_magin"}, "8.1667": {"effect": "p01_ar_mike4_reload_empty_lift"}, "8.5667": {"effect": "p01_ar_mike4_inspect_empty_rack"}, "9.2667": {"effect": "p01_ar_mike4_reload_empty_end"}}}, "shoot": {"animation_length": 0.63333, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0333": [1.225, 0, 0.725], "0.0667": [-0.595, 0, -1.175], "0.1": [-0.72, 0, -1.225], "0.1667": [0.18, 0, 0.525], "0.2667": [0.45, 0, -0.25], "0.3667": [0.225, -0.2, 0.25], "0.5": [0.055, -0.075, -0.075], "0.6333": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0333": [0, -0.025, 1.725], "0.0667": [0, -0.05, 1.38], "0.1333": [0, -0.05, -0.045], "0.2667": [0, -0.025, 0.1], "0.3667": [0, 0, 0]}}, "bolt": {"position": {"0.0": [0, 0, 0], "0.0333": [0, 0, 5], "0.0667": [0, 0, 0]}}, "camera": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0333": {"post": [0, 0, 0.6], "lerp_mode": "catmullrom"}, "0.0667": {"post": [0, 0, -0.4], "lerp_mode": "catmullrom"}, "0.1333": {"post": [0, 0, 0.325], "lerp_mode": "catmullrom"}, "0.2": {"post": [0, 0, -0.225], "lerp_mode": "catmullrom"}, "0.2667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}}}