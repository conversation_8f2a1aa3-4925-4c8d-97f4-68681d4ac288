{
  
  "ammo": "lradd:fusion_cell",

  "ammo_amount": 100,
  "bolt": "open_bolt",
  "rpm": 600,
  "bullet": {
    // 寿命，单位秒
    "life": 10,
    // 用于霰弹，默认为 1，每发的伤害 / bullet_amount，每次射击扣除一发子弹
    "bullet_amount": 1,
    // 伤害
    "damage": 9,
    // 曳光弹间隔数量，没有此字段则不发射曳光弹
    // 设置为 0 则是每发都是曳光弹
    "tracer_count_interval": 0,
    // 额外伤害的内容，为空则表示没有任何额外伤害计算内容
    "extra_damage": {
      // 护甲穿透率，默认为 0，也就是没有穿甲伤害
      "armor_ignore": 0.3,
      // 爆头伤害 x1.5
      "head_shot_multiplier": 1.75,
      // 距离-伤害分段常函数
      "damage_adjust": [
        // 这样就能写抵近伤害了
        // 如果你忘记写这个无穷，那么超过上述距离，我就认为是 0
        {"distance": "infinite", "damage": 9}
      ]
    },
    // 速度 m/s
    "speed": 500,
    // 重力
    "gravity": 0.0245,
    // 击退效果
    "knockback": 0,
    // 阻力
    "friction": 0.01,
    // 点燃目标
    "ignite": false,
    // 穿透数
    "pierce": 1
    // 是否爆炸，没有此字段时为 false
    //"explosion": {
    //  "radius": 5
    //}
  },
  "reload": {
    "type": "magazine",
    "feed": {
      "empty": 4,
      "tactical": 3
    },
    "cooldown": {
      "empty": 4.25,
      "tactical": 3.5833
    }
  },
  "draw_time": 1.25,
  "put_away_time": 0.8333,
  "aim_time": 0.2,
  "sprint_time": 0.3,
  "fire_mode": [
    "semi",
    "burst"
  ],
  "burst_data": {
    // 是否连续射击
    "continuous_shoot": true,
    // 连发数
    "count": 10,
    // 组内连发的射速
    "bpm": 1200,
    // 每组连发之间的时间间隔（上一组结束时间到下一组开始的时间间隔），单位秒
    "min_interval": 1.25
  },
  "recoil": {
    "pitch": [
      {"time": 0, "value": [0.15, 0.15]},
      {"time": 0.15, "value": [0.15, 0.15]},
      {"time": 0.3, "value": [0, 0]}
    ],
    "yaw": [
      {"time": 0, "value": [0.001, 0.001]},
      {"time": 0.1, "value": [0.001,0.001]},
      {"time": 0.25, "value": [0, 0]}
    ]
  },
  "inaccuracy": {
    "stand": 0.01,
    "move": 0.01,
    "sneak": 0.01,
    "lie": 0.01,
    "aim": 0.01
  },
  "move_speed": {
    "base": 0.75,
    // 瞄准情况
    "aim": 0.6
  },
  // 开放的配件槽。未指定的槽位默认关闭。全部配件槽类型有:
  // scope, stock, muzzle, grip, laser, extended_mag
  "allow_attachment_types": [
    "scope"
  ]
}